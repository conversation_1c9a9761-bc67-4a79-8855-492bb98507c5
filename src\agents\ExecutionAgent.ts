import { AGENT_TOOLCALLING_SYSTEM_PROMPT } from "../ToolCalling";
import type { AgentContext } from "./ContextAgent";
import { t_grep } from "../tools/t_grep";
import { t_read_file } from "../tools/t_read_file";
import { t_edit_file } from "../tools/t_edit_file";

export type ExecutionResult =
  | { type: "final"; text: string }
  | { type: "tool"; name: string; justification: string };

export class ExecutionAgent {
  private _lastSig: string | null = null;
  private _repeatCount = 0;
  private _lastRead?: { file: string; start: number; end: number; total: number };
  private _prevTool: string | null = null;

  constructor(private apiKey: string, private model: unknown) {}

  async decideNextAction(context: AgentContext, signal?: AbortSignal): Promise<ExecutionResult> {
    console.log("[ExecutionAgent] decideNextAction start", { next_step: context?.next_step, intent: context?.intent });
    const userPrompt = [
      "Contexte JSON (utilise-le pour choisir l'action):",
      JSON.stringify(context, null, 2),
      "Consigne: Si tu proposes un appel d'outil, renvoie un JSON STRICT du type {\"call\":{\"name\":\"t_grep|t_read_file|t_edit_file\",\"arguments\":{...}}}.",
      "Inclure une justification brève dans arguments.justification.",
      "Si aucune action n'est nécessaire, renvoie {\"final_answer\":\"texte\"}.",
      "Ne PAS repeter le meme appel d'outil avec les memes arguments sur deux tours consecutifs. Dans ce cas: produis la reponse finale.",
    ].join("\n\n");

    const content = await this._callLLM(AGENT_TOOLCALLING_SYSTEM_PROMPT, userPrompt, signal);
    console.log("[ExecutionAgent] LLM decision content", content);

    // Try to parse as execution decision
    if (typeof content === "object" && content) {
      const anyContent = content as any;
      if (anyContent.final_answer && typeof anyContent.final_answer === "string") {
        return { type: "final", text: anyContent.final_answer };
      }
      if (anyContent.call && anyContent.call.name) {
        const name = String(anyContent.call.name);
        const args = anyContent.call.arguments ?? {};
        let justification = String(args.justification ?? "");

        // De-dup guard: detect repeated identical tool+args (normalized)
        const sig = this._normalizeSig(name, args);
        if (this._lastSig === sig) {
          this._repeatCount++;
        } else {
          this._lastSig = sig;
          this._repeatCount = 0;
        }

        // Early stop to avoid infinite loops of identical tool+args
        if (this._repeatCount >= 1) {
          return { type: "final", text: "Arrêt sécurité: même outil et mêmes arguments répétés. Change d'approche (autre fichier/plage) ou passe à t_edit_file si prêt." };
        }

        try {
          if (name === "t_grep") {
            const pattern = String(args.pattern ?? "");
            const basePath = args.path ? String(args.path) : undefined;
            const result = await t_grep({ pattern, path: basePath });
            const files = new Set(result.matches.map((m: any) => m.file));
            const top = result.matches.slice(0, 3).map((m: any) => `${m.file}:${m.line}`).join(", ");
            justification = `${justification} | ${result.matches.length} correspondance(s) dans ${files.size} fichier(s). Top: ${top}`.trim();
          } else if (name === "t_read_file") {
            const target_file = String(args.target_file ?? "");
            const start_line = args.start_line != null ? Number(args.start_line) : undefined;
            const end_line = args.end_line != null ? Number(args.end_line) : undefined;

            const result = await this._withTimeout(t_read_file({ target_file, start_line, end_line }), 30000, "t_read_file");
            console.log("[ExecutionAgent] t_read_file result", { file: result.file, range: [result.start_line, result.end_line], total: result.total_lines });

            // Remember last read window (to help the planner choose next steps)
            const isDuplicateRead = this._lastRead && this._lastRead.file === result.file &&
              this._lastRead.start === result.start_line && this._lastRead.end === result.end_line;
            this._lastRead = { file: result.file, start: result.start_line, end: result.end_line, total: result.total_lines };

            if (isDuplicateRead) {
              console.log("[ExecutionAgent] Lecture identique d\u00e9tect\u00e9e; \u00e9viter la boucle et laisser l'agent planifier une autre action.");
            }

            const preview = result.content.split(/\r?\n/).slice(0, 3).join(" ").slice(0, 200);
            justification = `${justification} | Lecture ${result.file}:${result.start_line}-${result.end_line} (${result.total_lines} lignes). Apercu: ${preview}`.trim();
          } else if (name === "t_edit_file") {
            const target_file = String(args.target_file ?? "");
            const instructions = String(args.instructions ?? "");
            const code_edit = String(args.code_edit ?? "");
            const result = await t_edit_file({ target_file, instructions, code_edit });
            justification = `${justification} | ${result.summary}`.trim();
          }
        } catch (e: any) {
          const msg = e?.message ?? String(e);
          justification = `${justification} | Erreur outil: ${msg}`.trim();
        }
        this._prevTool = name;
        return { type: "tool", name, justification };
      }
    }

    // Fallback: treat as final text string
    if (typeof content === "string") {
      return { type: "final", text: content };
    }

    return { type: "final", text: "Aucune action supplementaire n'est necessaire." };
  }

  private _normalizeSig(name: string, args: any): string {
    if (name === "t_read_file") {
      return JSON.stringify({ name, target_file: String(args.target_file ?? ""), start_line: Number(args.start_line ?? 1), end_line: Number(args.end_line ?? -1) });
    }
    if (name === "t_grep") {
      return JSON.stringify({ name, pattern: String(args.pattern ?? ""), path: String(args.path ?? ".") });
    }
    if (name === "t_edit_file") {
      return JSON.stringify({ name, target_file: String(args.target_file ?? "") });
    }
    return JSON.stringify({ name });
  }

  private _isExplanationLike(context: AgentContext): boolean {
    const hay = `${(context.intent||"").toLowerCase()} ${ (context.next_step||"").toLowerCase() }`;
    return /a quoi sert|à quoi sert|what does|purpose|explain|expliquer|role|rôle/.test(hay);
  }

  private _shouldFinalizeAfterRead(context: AgentContext, file: string, total: number, start: number, end: number): boolean {
    const intent = (context.intent || "").toLowerCase();
    const next = (context.next_step || "").toLowerCase();
    const isExplainIntent = /rôle|role|expliq|purpose|what does|a quoi sert/.test(intent);
    const readFully = start <= 1 && (end >= total || total <= 1200 /*lines*/);
    return isExplainIntent && readFully;
  }

  private async _callLLM(systemPrompt: string, userContent: string, signal?: AbortSignal): Promise<unknown> {
    const body = JSON.stringify({
      model: this.model,
      messages: [
        { role: "system", content: systemPrompt },
        { role: "user", content: userContent },
      ],
      stream: false,
      temperature: 0,
    });
    const timeoutMs = 30000;
    const ctrl = new AbortController();
    const onAbort = () => ctrl.abort();
    if (signal) signal.addEventListener('abort', onAbort, { once: true });
    const timer = setTimeout(() => ctrl.abort(), timeoutMs);
    let resp: Response;
    try {
      resp = await fetch("https://openrouter.ai/api/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${this.apiKey}`,
          "HTTP-Referer": "https://github.com/RedTheFoxx/dinootoo-companion",
          "X-Title": "Dinootoo Companion",
        },
        body,
        signal: ctrl.signal,
      } as RequestInit);
    } finally {
      clearTimeout(timer);
      if (signal) signal.removeEventListener('abort', onAbort);
    }
    if (!resp.ok) {
      const t = await resp.text();
      throw new Error(`OpenRouter error: ${resp.status} ${t}`);
    }
    const data = (await resp.json()) as { choices?: { message?: { content?: string } }[] };
    const content = data.choices?.[0]?.message?.content ?? "{}";
    try {
      return JSON.parse(content);
    } catch (e) {
      // Try to salvage JSON code block if present
      const m = content.match(/\{[\s\S]*\}/);
      if (m) {
        return JSON.parse(m[0]);
      }
      return content; // return raw text
    }
  }

  private async _summarizeFilePurpose(filePath: string, content: string, signal?: AbortSignal): Promise<string> {
    const sys = "Tu es un assistant qui resume le role d'un fichier TypeScript d'extension VS Code.";
    const user = [
      `Fichier: ${filePath}`,
      "Tache: Expliquer brievement et clairement a quoi sert ce fichier, ses responsabilites principales, et ou il s'integre dans l'extension.",
      "Contenu (tronque si besoin):",
      content.slice(0, 12000),
      "Reponds en 3-6 puces maximum, en francais.",
    ].join("\n\n");
    const result = await this._callLLM(sys, user, signal);
    if (typeof result === "string") return result;
    try {
      return JSON.stringify(result);
    } catch {
      return String(result);
    }
  }

  private _withTimeout<T>(p: Promise<T>, ms: number, label: string): Promise<T> {
    return new Promise<T>((resolve, reject) => {
      const to = setTimeout(() => {
        console.warn(`[ExecutionAgent] Timeout on ${label} after ${ms}ms`);
        reject(new Error(`Timeout: ${label}`));
      }, ms);
      p.then(v => { clearTimeout(to); resolve(v); })
       .catch(err => { clearTimeout(to); reject(err); });
    });
  }

}

