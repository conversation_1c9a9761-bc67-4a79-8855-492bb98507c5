import * as vscode from 'vscode';
import * as fs from 'fs/promises';
import * as path from 'path';

export const description = `Recherche ciblée dans le dépôt. Utilise des patterns précis (ex: ^export .*MaFonction). Par défaut path='.'. Préfère les dossiers probables (ex: src/, test/). Sert à formuler/valider des hypothèses à confirmer via t_read_file avant toute édition.

Conseils regex:
- Pour chercher un fichier par nom, échappez les points. Ex: Edit\.ts (et non Edit.ts)
- Pour faire correspondre exactement le nom: ^Edit\\.ts$
- Évitez les globes (**, *) dans le pattern regex. Si vous avez un nom littéral, utilisez un motif littéral échappé.
- t_grep fait correspondre le contenu des fichiers LIGNE PAR LIGNE et aussi le chemin relatif du fichier (pour la recherche par nom).`;

export type GrepArgs = { pattern: string; path?: string };
export type GrepResult = {
  pattern: string;
  searched_root: string;
  matches: Array<{ file: string; line: number; text: string }>;
};

const IGNORES = new Set(['.git', 'node_modules', 'out', '.vscode']);
const MAX_FILE_SIZE_BYTES = 1_000_000;

function getWorkspaceRoot(): string {
  const root = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
  if (!root) throw new Error('Workspace root not found');
  return root;
}

function escapeRegExp(str: string): string {
  return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

function deriveFilenamePatternFromGlob(input: string): string | null {
  // If pattern looks like a glob path (contains * or ?) and a basename with extension, try filename intent
  const looksLikeGlob = /[\*\?]/.test(input);
  if (!looksLikeGlob) return null;
  // Normalize separators and extract basename
  const norm = input.replace(/\\/g, '/');
  const parts = norm.split('/').filter(Boolean);
  const base = parts[parts.length - 1] || '';
  if (!base) return null;
  const escapedBase = escapeRegExp(base);
  // Match end of path on either POSIX or Windows separator
  return String.raw`(?:^|[\/])${escapedBase}$`;
}

function sanitizePattern(p: string): string {
  // Trim and drop surrounding quotes
  let s = (p ?? '').toString().trim();
  if ((s.startsWith("'") && s.endsWith("'")) || (s.startsWith('"') && s.endsWith('"'))) {
    s = s.slice(1, -1);
  }
  return s;
}

async function* walk(dir: string): AsyncGenerator<string> {
  const entries = await fs.readdir(dir, { withFileTypes: true });
  for (const entry of entries) {
    if (IGNORES.has(entry.name)) continue;
    const full = path.join(dir, entry.name);
    if (entry.isDirectory()) {
      yield* walk(full);
    } else if (entry.isFile()) {
      yield full;
    }
  }
}

export async function t_grep(args: GrepArgs): Promise<GrepResult> {
  const root = getWorkspaceRoot();
  const base = path.resolve(root, args.path ?? '.');

  // Validate and compile regex with helpful fallback
  const raw = sanitizePattern(args.pattern);
  let rx: RegExp | null = null;
  let compileError: any = null;
  try {
    rx = new RegExp(raw, 'm');
  } catch (e) {
    compileError = e;
    // Fallback 1: derive filename intent from glob-like pattern
    const fnamePattern = deriveFilenamePatternFromGlob(raw);
    if (fnamePattern) {
      try {
        rx = new RegExp(fnamePattern, 'm');
      } catch {
        // ignore and try literal
      }
    }
    // Fallback 2: treat as literal search (escape regex meta)
    if (!rx) {
      try {
        const escaped = escapeRegExp(raw);
        rx = new RegExp(escaped, 'm');
      } catch (e2) {
        // Provide helpful error message
        const hint = `Pattern regex invalide. Essayez d'échapper les caractères spéciaux (ex: Edit\\.ts). Erreur: ${String(e)}`;
        throw new Error(hint);
      }
    }
  }

  const matches: GrepResult['matches'] = [];

  async function scanFile(fp: string) {
    try {
      const rel = path.relative(root, fp);

      // Also match on file path (filename search support)
      if (rx && rx.test(rel)) {
        matches.push({ file: rel, line: 0, text: `[match chemin] ${rel}` });
      }

      const stat = await fs.stat(fp);
      if (stat.size > MAX_FILE_SIZE_BYTES) return;
      const buf = await fs.readFile(fp);
      let text: string;
      try {
        text = buf.toString('utf8');
      } catch {
        return;
      }
      const lines = text.split(/\r?\n/);
      for (let i = 0; i < lines.length; i++) {
        if (rx && rx.test(lines[i])) {
          matches.push({ file: rel, line: i + 1, text: lines[i] });
        }
      }
    } catch {
      // ignore per-file errors
    }
  }

  const stat = await fs.stat(base).catch(() => null);
  if (!stat) throw new Error(`Chemin introuvable: ${args.path ?? '.'}`);
  if (stat.isFile()) {
    await scanFile(base);
  } else {
    for await (const fp of walk(base)) {
      await scanFile(fp);
      if (matches.length > 2000) break;
    }
  }

  return { pattern: args.pattern, searched_root: path.relative(root, base), matches };
}
