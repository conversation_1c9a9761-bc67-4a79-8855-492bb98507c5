{
  // See https://go.microsoft.com/fwlink/?LinkId=733558
  // for the documentation about the tasks.json format
  "version": "2.0.0",
  "tasks": [
    {
      "type": "npm",
      "script": "compile",
      "group": "build",
      "presentation": {
        "panel": "shared",
        "reveal": "silent",
        "showReuseMessage": false,
        "clear": false
      },
      "problemMatcher": "$tsc"
    },
    {
      "type": "npm",
      "script": "watch",
      "isBackground": true,
      "group": {
        "kind": "build",
        "isDefault": true
      },
      "presentation": {
        "panel": "shared",
        "reveal": "silent",
        "showReuseMessage": false,
        "clear": false
      },
      "problemMatcher": "$tsc-watch"
    }
  ]
}