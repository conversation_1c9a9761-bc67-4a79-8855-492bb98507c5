{"version": 3, "sources": ["../../scss/mixins/_banner.scss", "../../scss/_root.scss", "boosted-utilities.css", "../../scss/vendor/_rfs.scss", "../../scss/mixins/_color-mode.scss", "../../scss/mixins/_clearfix.scss", "../../scss/helpers/_color-bg.scss", "../../scss/helpers/_colored-links.scss", "../../scss/helpers/_focus-ring.scss", "../../scss/helpers/_chevron-link.scss", "../../scss/_variables.scss", "../../scss/helpers/_icon-link.scss", "../../scss/mixins/_transition.scss", "../../scss/helpers/_ratio.scss", "../../scss/helpers/_position.scss", "../../scss/mixins/_breakpoints.scss", "../../scss/helpers/_stacks.scss", "../../scss/helpers/_visually-hidden.scss", "../../scss/mixins/_visually-hidden.scss", "../../scss/helpers/_stretched-link.scss", "../../scss/helpers/_text-truncation.scss", "../../scss/mixins/_text-truncate.scss", "../../scss/helpers/_vr.scss", "../../scss/mixins/_utilities.scss", "../../scss/utilities/_api.scss"], "names": [], "mappings": "AACE;;;;;;;;;EAAA;ACAF;;EAEE,2BAAA;EACA,mCAAA;ACUF;;ADJA;;EAEE,mBAAA;EASE,kBAAA;EAAA,oBAAA;EAAA,oBAAA;EAAA,kBAAA;EAAA,iBAAA;EAAA,oBAAA;EAAA,iBAAA;EAAA,mBAAA;EAAA,kBAAA;EAAA,kBAAA;EAAA,gBAAA;EAAA,gBAAA;EAAA,eAAA;EAAA,uBAAA;EAIA,sBAAA;EAAA,sBAAA;EAAA,mBAAA;EAAA,mBAAA;EAAA,mBAAA;EAAA,mBAAA;EAAA,mBAAA;EAAA,sBAAA;EAAA,mBAAA;EAAA,sBAAA;EAIA,qBAAA;EAAA,oBAAA;EAAA,qBAAA;EAAA,kBAAA;EAAA,kBAAA;EAAA,oBAAA;EAAA,gBAAA;EAAA,eAAA;EAIA,6BAAA;EAAA,2BAAA;EAAA,6BAAA;EAAA,2BAAA;EAAA,6BAAA;EAAA,4BAAA;EAAA,6BAAA;EAAA,sBAAA;EAIA,mCAAA;EAAA,kCAAA;EAAA,mCAAA;EAAA,gCAAA;EAAA,gCAAA;EAAA,kCAAA;EAAA,8BAAA;EAAA,6BAAA;EAIA,+BAAA;EAAA,8BAAA;EAAA,+BAAA;EAAA,4BAAA;EAAA,4BAAA;EAAA,8BAAA;EAAA,0BAAA;EAAA,yBAAA;EAIA,mCAAA;EAAA,kCAAA;EAAA,mCAAA;EAAA,gCAAA;EAAA,gCAAA;EAAA,kCAAA;EAAA,8BAAA;EAAA,6BAAA;EAGF,6BAAA;EACA,uBAAA;EAIE,mKAAA;EAAA,ojBAAA;EAAA,8KAAA;EAAA,yaAAA;EAAA,gjBAAA;EAQF,uNAAA;EACA,yGAAA;EACA,yFAAA;EAOA,gDAAA;EEyNI,yBALI;EFlNR,0BAAA;EACA,4BAAA;EAKA,qBAAA;EACA,4BAAA;EACA,kBAAA;EACA,+BAAA;EAEA,yBAAA;EACA,gCAAA;EAEA,0BAAA;EACA,uCAAA;EACA,uBAAA;EACA,oCAAA;EAEA,yBAAA;EACA,sCAAA;EACA,yBAAA;EACA,mCAAA;EAGA,2BAAA;EAEA,qBAAA;EACA,4BAAA;EACA,+BAAA;EAEA,8BAAA;EACA,sCAAA;EAMA,qBAAA;EACA,0BAAA;EACA,uBAAA;EACA,6CAAA;EACA,6BAAA;EAGA,2BAAA;EACA,wBAAA;EACA,uBAAA;EACA,8BAAA;EACA,mDAAA;EAEA,4BAAA;EACA,8BAAA;EACA,6BAAA;EACA,2BAAA;EACA,4BAAA;EACA,mDAAA;EACA,8BAAA;EAGA,iBAAA;EACA,oBAAA;EACA,oBAAA;EACA,uBAAA;EAEA,oCAAA;EACA,oCAAA;EAIA,8BAAA;EACA,6BAAA;EACA,8CAAA;EAIA,sDAAA;EACA,+CAAA;EACA,uDAAA;EACA,gDAAA;EAGA,iCAAA;EACA,0CAAA;EACA,wEAAA;EACA,+JAAA;EACA,sLAAA;EACA,gCAAA;EACA,wDAAA;EAIA,kCAAA;EACA,iCAAA;EACA,mCAAA;EAGA,oCAAA;ACXF;;AE/II;EHgKA,kBAAA;EAGA,qBAAA;EACA,kCAAA;EACA,qBAAA;EACA,4BAAA;EAEA,yBAAA;EACA,sCAAA;EAEA,0BAAA;EACA,uCAAA;EACA,uBAAA;EACA,iCAAA;EAEA,yBAAA;EACA,sCAAA;EACA,sBAAA;EACA,6BAAA;EAIE,qBAAA;EAAA,oBAAA;EAAA,kBAAA;EAAA,eAAA;EAAA,kBAAA;EAAA,oBAAA;EAAA,gBAAA;EAAA,eAAA;EAIA,6BAAA;EAAA,iCAAA;EAAA,+BAAA;EAAA,4BAAA;EAAA,6BAAA;EAAA,4BAAA;EAAA,6BAAA;EAAA,sBAAA;EAKA,mCAAA;EAAA,kCAAA;EAAA,gCAAA;EAAA,6BAAA;EAAA,gCAAA;EAAA,kCAAA;EAAA,8BAAA;EAAA,6BAAA;EAIA,+BAAA;EAAA,8BAAA;EAAA,4BAAA;EAAA,yBAAA;EAAA,4BAAA;EAAA,8BAAA;EAAA,0BAAA;EAAA,yBAAA;EAIA,mCAAA;EAAA,kCAAA;EAAA,gCAAA;EAAA,6BAAA;EAAA,gCAAA;EAAA,kCAAA;EAAA,8BAAA;EAAA,6BAAA;EAKA,saAAA;EAAA,gjBAAA;EAIF,2BAAA;EAEA,qBAAA;EACA,8BAAA;EACA,kCAAA;EACA,sCAAA;EAEA,qBAAA;EACA,0BAAA;EACA,uBAAA;EACA,6CAAA;EACA,6BAAA;EAEA,uBAAA;EACA,8BAAA;EACA,wDAAA;EAEA,oCAAA;EACA,oCAAA;EAEA,8CAAA;EAEA,sDAAA;EACA,+CAAA;EACA,uDAAA;EACA,gDAAA;EACA,4BAAA;EACA,0CAAA;EACA,6KAAA;EACA,sLAAA;EACA,wEAAA;EACA,mCAAA;EACA,iEAAA;EAIA,iCAAA;EACA,iCAAA;EACA,+BAAA;EAGA,yCAAA;AChBJ;;AGvPE;EACE,cAAA;EACA,WAAA;EACA,WAAA;AH0PJ;;AI7PE;EASE,sBAAA;EACA,iFAAA;AJwPJ;;AIlQE;EASE,2CAAA;EACA,mFAAA;AJ6PJ;;AIvQE;EASE,2CAAA;EACA,iFAAA;AJkQJ;;AI5QE;EASE,2CAAA;EACA,8EAAA;AJuQJ;;AIjRE;EASE,sBAAA;EACA,iFAAA;AJ4QJ;;AItRE;EASE,2CAAA;EACA,gFAAA;AJiRJ;;AI3RE;EASE,sBAAA;EACA,+EAAA;AJsRJ;;AIhSE;EASE,sBAAA;EACA,8EAAA;AJ2RJ;;AKrSE;EACE,wEAAA;EACA,0GAAA;EAAA,kGAAA;ALwSJ;AKpSM;EAEE,+DAAA;EACA,iGAAA;EAAA,yFAAA;ALqSR;;AK9SE;EACE,0EAAA;EACA,4GAAA;EAAA,oGAAA;ALiTJ;AK7SM;EAEE,0DAAA;EACA,4FAAA;EAAA,oFAAA;AL8SR;;AKvTE;EACE,wEAAA;EACA,0GAAA;EAAA,kGAAA;AL0TJ;AKtTM;EAEE,8DAAA;EACA,gGAAA;EAAA,wFAAA;ALuTR;;AKhUE;EACE,qEAAA;EACA,uGAAA;EAAA,+FAAA;ALmUJ;AK/TM;EAEE,8DAAA;EACA,gGAAA;EAAA,wFAAA;ALgUR;;AKzUE;EACE,wEAAA;EACA,0GAAA;EAAA,kGAAA;AL4UJ;AKxUM;EAEE,+DAAA;EACA,iGAAA;EAAA,yFAAA;ALyUR;;AKlVE;EACE,uEAAA;EACA,yGAAA;EAAA,iGAAA;ALqVJ;AKjVM;EAEE,8DAAA;EACA,gGAAA;EAAA,wFAAA;ALkVR;;AK3VE;EACE,sEAAA;EACA,wGAAA;EAAA,gGAAA;AL8VJ;AK1VM;EAEE,gEAAA;EACA,kGAAA;EAAA,0FAAA;AL2VR;;AKpWE;EACE,qEAAA;EACA,uGAAA;EAAA,+FAAA;ALuWJ;AKnWM;EAEE,0DAAA;EACA,4FAAA;EAAA,oFAAA;ALoWR;;AKxVU;EAEE,+DAAA;EACA,iGAAA;EAAA,yFAAA;AL0VZ;AK7VU;EAEE,gEAAA;EACA,kGAAA;EAAA,0FAAA;AL8VZ;AKjWU;EAEE,gEAAA;EACA,kGAAA;EAAA,0FAAA;ALkWZ;AKrWU;EAEE,gEAAA;EACA,kGAAA;EAAA,0FAAA;ALsWZ;AKzWU;EAEE,+DAAA;EACA,iGAAA;EAAA,yFAAA;AL0WZ;AK7WU;EAEE,gEAAA;EACA,kGAAA;EAAA,0FAAA;AL8WZ;AKjXU;EAEE,gEAAA;EACA,kGAAA;EAAA,0FAAA;ALkXZ;AKrXU;EAEE,0DAAA;EACA,4FAAA;EAAA,oFAAA;ALsXZ;;AK5WA;EACE,+EAAA;EACA,iHAAA;EAAA,yGAAA;AL+WF;AK5WI;EAEE,kFAAA;EACA,oHAAA;EAAA,4GAAA;AL6WN;;AMzZA;EACE,UAAA;EAEA,kJAAA;AN2ZF;;AO7ZA;EACE,gBCkuB4B;EDjuB5B,qBAAA;EACA,6BAAA;APgaF;AO9ZE;EACE,qBAAA;EACA,yBC0gB0B;EDzgB1B,gBC0gB0B;EDzgB1B,sBC2gB0B;ED1gB1B,sBAAA;EACA,WAAA;EACA,8BAAA;EACA,8CAAA;EAAA,sCAAA;EACA,0CCqgB0B;ARrG9B;AO7ZE;EACE,0BCsfsC;ARvF1C;;AShbA;EACE,oBAAA;EACA,cDshB4B;ECrhB5B,mBAAA;EACA,0FAAA;EAAA,kFAAA;EACA,6BDohB4B;ECnhB5B,mCAAA;EAAA,2BAAA;ATmbF;ASjbE;EACE,cAAA;EACA,UDghB0B;EC/gB1B,WD+gB0B;EC9gB1B,kBAAA;ECEE,sCDDF;ATmbJ;AU9aM;EDVJ;ICWM,gBAAA;EVibN;AACF;;ASjbI;EACE,mEAAA;ATobN;;AWzcA;EACE,kBAAA;EACA,WAAA;AX4cF;AW1cE;EACE,cAAA;EACA,mCAAA;EACA,WAAA;AX4cJ;AWzcE;EACE,kBAAA;EACA,MAAA;EACA,OAAA;EACA,WAAA;EACA,YAAA;AX2cJ;;AWtcE;EACE,uBAAA;AXycJ;;AW1cE;EACE,sBAAA;AX6cJ;;AW9cE;EACE,yBAAA;AXidJ;;AWldE;EACE,iCAAA;AXqdJ;;AWtdE;EACE,kCAAA;AXydJ;;AY9eA;EACE,eAAA;EACA,MAAA;EACA,QAAA;EACA,OAAA;EACA,aJq4CkC;ARp5BpC;;AY9eA;EACE,eAAA;EACA,QAAA;EACA,SAAA;EACA,OAAA;EACA,aJ63CkC;AR54BpC;;AYzeI;EACE,wBAAA;EAAA,gBAAA;EACA,MAAA;EACA,aJi3C8B;ARr4BpC;;AYzeI;EACE,wBAAA;EAAA,gBAAA;EACA,SAAA;EACA,aJ22C8B;AR/3BpC;;Aa7cI;EDxCA;IACE,wBAAA;IAAA,gBAAA;IACA,MAAA;IACA,aJi3C8B;ERx3BlC;EYtfE;IACE,wBAAA;IAAA,gBAAA;IACA,SAAA;IACA,aJ22C8B;ERn3BlC;AACF;Aa1dI;EDxCA;IACE,wBAAA;IAAA,gBAAA;IACA,MAAA;IACA,aJi3C8B;ER52BlC;EYlgBE;IACE,wBAAA;IAAA,gBAAA;IACA,SAAA;IACA,aJ22C8B;ERv2BlC;AACF;AateI;EDxCA;IACE,wBAAA;IAAA,gBAAA;IACA,MAAA;IACA,aJi3C8B;ERh2BlC;EY9gBE;IACE,wBAAA;IAAA,gBAAA;IACA,SAAA;IACA,aJ22C8B;ER31BlC;AACF;AalfI;EDxCA;IACE,wBAAA;IAAA,gBAAA;IACA,MAAA;IACA,aJi3C8B;ERp1BlC;EY1hBE;IACE,wBAAA;IAAA,gBAAA;IACA,SAAA;IACA,aJ22C8B;ER/0BlC;AACF;Aa9fI;EDxCA;IACE,wBAAA;IAAA,gBAAA;IACA,MAAA;IACA,aJi3C8B;ERx0BlC;EYtiBE;IACE,wBAAA;IAAA,gBAAA;IACA,SAAA;IACA,aJ22C8B;ERn0BlC;AACF;AcxkBA;EACE,aAAA;EACA,mBAAA;EACA,mBAAA;EACA,mBAAA;Ad0kBF;;AcvkBA;EACE,aAAA;EACA,cAAA;EACA,sBAAA;EACA,mBAAA;Ad0kBF;;AellBA;;ECIE,qBAAA;EACA,sBAAA;EACA,qBAAA;EACA,uBAAA;EACA,2BAAA;EACA,iCAAA;EACA,8BAAA;EACA,oBAAA;AhBmlBF;AgBhlBE;;EACE,6BAAA;AhBmlBJ;AgB/kBE;;EACE,2BAAA;AhBklBJ;;AiBrmBE;EACE,kBAAA;EACA,MAAA;EACA,QAAA;EACA,SAAA;EACA,OAAA;EACA,UTogBsC;ESngBtC,WAAA;AjBwmBJ;;AkBhnBA;ECAE,gBAAA;EACA,uBAAA;EACA,mBAAA;AnBonBF;;AoB1nBA;EACE,qBAAA;EACA,mBAAA;EACA,UZk1B4B;EYj1B5B,eAAA;EACA,8BAAA;ApB6nBF;;AqBhkBQ;EAOI,mCAAA;ArB6jBZ;;AqBpkBQ;EAOI,8BAAA;ArBikBZ;;AqBxkBQ;EAOI,iCAAA;ArBqkBZ;;AqB5kBQ;EAOI,iCAAA;ArBykBZ;;AqBhlBQ;EAOI,sCAAA;ArB6kBZ;;AqBplBQ;EAOI,mCAAA;ArBilBZ;;AqBxlBQ;EAOI,sBAAA;ArBqlBZ;;AqB5lBQ;EAOI,uBAAA;ArBylBZ;;AqBhmBQ;EAOI,sBAAA;ArB6lBZ;;AqBpmBQ;EAOI,iCAAA;EAAA,8BAAA;ArBimBZ;;AqBxmBQ;EAOI,+BAAA;EAAA,4BAAA;ArBqmBZ;;AqB5mBQ;EAOI,8BAAA;EAAA,2BAAA;ArBymBZ;;AqBhnBQ;EAOI,oCAAA;EAAA,iCAAA;ArB6mBZ;;AqBpnBQ;EAOI,8BAAA;EAAA,2BAAA;ArBinBZ;;AqBxnBQ;EAOI,qBAAA;ArBqnBZ;;AqB5nBQ;EAOI,wBAAA;ArBynBZ;;AqBhoBQ;EAOI,uBAAA;ArB6nBZ;;AqBpoBQ;EAOI,wBAAA;ArBioBZ;;AqBxoBQ;EAOI,qBAAA;ArBqoBZ;;AqB5oBQ;EAOI,yBAAA;ArByoBZ;;AqBhpBQ;EAOI,2BAAA;ArB6oBZ;;AqBppBQ;EAOI,4BAAA;ArBipBZ;;AqBxpBQ;EAOI,2BAAA;ArBqpBZ;;AqB5pBQ;EAOI,2BAAA;ArBypBZ;;AqBhqBQ;EAOI,6BAAA;ArB6pBZ;;AqBpqBQ;EAOI,8BAAA;ArBiqBZ;;AqBxqBQ;EAOI,6BAAA;ArBqqBZ;;AqB5qBQ;EAOI,2BAAA;ArByqBZ;;AqBhrBQ;EAOI,6BAAA;ArB6qBZ;;AqBprBQ;EAOI,8BAAA;ArBirBZ;;AqBxrBQ;EAOI,6BAAA;ArBqrBZ;;AqB5rBQ;EAOI,0BAAA;ArByrBZ;;AqBhsBQ;EAOI,gCAAA;ArB6rBZ;;AqBpsBQ;EAOI,yBAAA;ArBisBZ;;AqBxsBQ;EAOI,wBAAA;ArBqsBZ;;AqB5sBQ;EAOI,+BAAA;ArBysBZ;;AqBhtBQ;EAOI,yBAAA;ArB6sBZ;;AqBptBQ;EAOI,6BAAA;ArBitBZ;;AqBxtBQ;EAOI,8BAAA;ArBqtBZ;;AqB5tBQ;EAOI,wBAAA;ArBytBZ;;AqBhuBQ;EAOI,+BAAA;ArB6tBZ;;AqBpuBQ;EAOI,wBAAA;ArBiuBZ;;AqBxuBQ;EAOI,2CAAA;ArBquBZ;;AqB5uBQ;EAOI,8CAAA;ArByuBZ;;AqBhvBQ;EAOI,8CAAA;ArB6uBZ;;AqBpvBQ;EAOI,2BAAA;ArBivBZ;;AqBlwBQ;EACE,gFAAA;ArBqwBV;;AqBtwBQ;EACE,kFAAA;ArBywBV;;AqB1wBQ;EACE,gFAAA;ArB6wBV;;AqB9wBQ;EACE,6EAAA;ArBixBV;;AqBlxBQ;EACE,gFAAA;ArBqxBV;;AqBtxBQ;EACE,+EAAA;ArByxBV;;AqB1xBQ;EACE,8EAAA;ArB6xBV;;AqB9xBQ;EACE,6EAAA;ArBiyBV;;AqBxxBQ;EAOI,2BAAA;ArBqxBZ;;AqB5xBQ;EAOI,6BAAA;ArByxBZ;;AqBhyBQ;EAOI,6BAAA;ArB6xBZ;;AqBpyBQ;EAOI,0BAAA;ArBiyBZ;;AqBxyBQ;EAOI,mCAAA;EAAA,2BAAA;ArBqyBZ;;AqB5yBQ;EAOI,iBAAA;ArByyBZ;;AqBhzBQ;EAOI,mBAAA;ArB6yBZ;;AqBpzBQ;EAOI,oBAAA;ArBizBZ;;AqBxzBQ;EAOI,oBAAA;ArBqzBZ;;AqB5zBQ;EAOI,sBAAA;ArByzBZ;;AqBh0BQ;EAOI,uBAAA;ArB6zBZ;;AqBp0BQ;EAOI,kBAAA;ArBi0BZ;;AqBx0BQ;EAOI,oBAAA;ArBq0BZ;;AqB50BQ;EAOI,qBAAA;ArBy0BZ;;AqBh1BQ;EAOI,mBAAA;ArB60BZ;;AqBp1BQ;EAOI,qBAAA;ArBi1BZ;;AqBx1BQ;EAOI,sBAAA;ArBq1BZ;;AqB51BQ;EAOI,2CAAA;ArBy1BZ;;AqBh2BQ;EAOI,sCAAA;ArB61BZ;;AqBp2BQ;EAOI,sCAAA;ArBi2BZ;;AqBx2BQ;EAOI,uFAAA;ArBq2BZ;;AqB52BQ;EAOI,oBAAA;ArBy2BZ;;AqBh3BQ;EAOI,2FAAA;ArB62BZ;;AqBp3BQ;EAOI,wBAAA;ArBi3BZ;;AqBx3BQ;EAOI,6FAAA;ArBq3BZ;;AqB53BQ;EAOI,0BAAA;ArBy3BZ;;AqBh4BQ;EAOI,8FAAA;ArB63BZ;;AqBp4BQ;EAOI,2BAAA;ArBi4BZ;;AqBx4BQ;EAOI,4FAAA;ArBq4BZ;;AqB54BQ;EAOI,yBAAA;ArBy4BZ;;AqBh5BQ;EAIQ,sBAAA;EAGJ,8EAAA;ArB84BZ;;AqBr5BQ;EAIQ,sBAAA;EAGJ,gFAAA;ArBm5BZ;;AqB15BQ;EAIQ,sBAAA;EAGJ,8EAAA;ArBw5BZ;;AqB/5BQ;EAIQ,sBAAA;EAGJ,2EAAA;ArB65BZ;;AqBp6BQ;EAIQ,sBAAA;EAGJ,8EAAA;ArBk6BZ;;AqBz6BQ;EAIQ,sBAAA;EAGJ,6EAAA;ArBu6BZ;;AqB96BQ;EAIQ,sBAAA;EAGJ,sEAAA;ArB46BZ;;AqBn7BQ;EAIQ,sBAAA;EAGJ,sEAAA;ArBi7BZ;;AqBx7BQ;EAIQ,sBAAA;EAGJ,4EAAA;ArBs7BZ;;AqB77BQ;EAIQ,sBAAA;EAGJ,4EAAA;ArB27BZ;;AqBl8BQ;EAOI,wDAAA;ArB+7BZ;;AqBt8BQ;EAOI,0DAAA;ArBm8BZ;;AqB18BQ;EAOI,wDAAA;ArBu8BZ;;AqB98BQ;EAOI,qDAAA;ArB28BZ;;AqBl9BQ;EAOI,wDAAA;ArB+8BZ;;AqBt9BQ;EAOI,uDAAA;ArBm9BZ;;AqB19BQ;EAOI,sDAAA;ArBu9BZ;;AqB99BQ;EAOI,qDAAA;ArB29BZ;;AqBl+BQ;EAOI,kCAAA;ArB+9BZ;;AqBt+BQ;EAOI,iCAAA;ArBm+BZ;;AqB1+BQ;EAOI,kCAAA;ArBu+BZ;;AqB9+BQ;EAOI,gCAAA;ArB2+BZ;;AqBl/BQ;EAOI,kCAAA;ArB++BZ;;AqBhgCQ;EACE,wBAAA;ArBmgCV;;AqBpgCQ;EACE,yBAAA;ArBugCV;;AqBxgCQ;EACE,wBAAA;ArB2gCV;;AqB5gCQ;EACE,yBAAA;ArB+gCV;;AqBhhCQ;EACE,sBAAA;ArBmhCV;;AqB1gCQ;EAOI,qBAAA;ArBugCZ;;AqB9gCQ;EAOI,qBAAA;ArB2gCZ;;AqBlhCQ;EAOI,qBAAA;ArB+gCZ;;AqBthCQ;EAOI,sBAAA;ArBmhCZ;;AqB1hCQ;EAOI,sBAAA;ArBuhCZ;;AqB9hCQ;EAOI,0BAAA;ArB2hCZ;;AqBliCQ;EAOI,uBAAA;ArB+hCZ;;AqBtiCQ;EAOI,2BAAA;ArBmiCZ;;AqB1iCQ;EAOI,sBAAA;ArBuiCZ;;AqB9iCQ;EAOI,sBAAA;ArB2iCZ;;AqBljCQ;EAOI,sBAAA;ArB+iCZ;;AqBtjCQ;EAOI,uBAAA;ArBmjCZ;;AqB1jCQ;EAOI,uBAAA;ArBujCZ;;AqB9jCQ;EAOI,2BAAA;ArB2jCZ;;AqBlkCQ;EAOI,wBAAA;ArB+jCZ;;AqBtkCQ;EAOI,4BAAA;ArBmkCZ;;AqB1kCQ;EAOI,yBAAA;ArBukCZ;;AqB9kCQ;EAOI,8BAAA;ArB2kCZ;;AqBllCQ;EAOI,iCAAA;ArB+kCZ;;AqBtlCQ;EAOI,sCAAA;ArBmlCZ;;AqB1lCQ;EAOI,yCAAA;ArBulCZ;;AqB9lCQ;EAOI,uBAAA;ArB2lCZ;;AqBlmCQ;EAOI,uBAAA;ArB+lCZ;;AqBtmCQ;EAOI,yBAAA;ArBmmCZ;;AqB1mCQ;EAOI,yBAAA;ArBumCZ;;AqB9mCQ;EAOI,0BAAA;ArB2mCZ;;AqBlnCQ;EAOI,4BAAA;ArB+mCZ;;AqBtnCQ;EAOI,kCAAA;ArBmnCZ;;AqB1nCQ;EAOI,sCAAA;ArBunCZ;;AqB9nCQ;EAOI,oCAAA;ArB2nCZ;;AqBloCQ;EAOI,kCAAA;ArB+nCZ;;AqBtoCQ;EAOI,yCAAA;ArBmoCZ;;AqB1oCQ;EAOI,wCAAA;ArBuoCZ;;AqB9oCQ;EAOI,wCAAA;ArB2oCZ;;AqBlpCQ;EAOI,kCAAA;ArB+oCZ;;AqBtpCQ;EAOI,gCAAA;ArBmpCZ;;AqB1pCQ;EAOI,8BAAA;ArBupCZ;;AqB9pCQ;EAOI,gCAAA;ArB2pCZ;;AqBlqCQ;EAOI,+BAAA;ArB+pCZ;;AqBtqCQ;EAOI,oCAAA;ArBmqCZ;;AqB1qCQ;EAOI,kCAAA;ArBuqCZ;;AqB9qCQ;EAOI,gCAAA;ArB2qCZ;;AqBlrCQ;EAOI,uCAAA;ArB+qCZ;;AqBtrCQ;EAOI,sCAAA;ArBmrCZ;;AqB1rCQ;EAOI,iCAAA;ArBurCZ;;AqB9rCQ;EAOI,2BAAA;ArB2rCZ;;AqBlsCQ;EAOI,iCAAA;ArB+rCZ;;AqBtsCQ;EAOI,+BAAA;ArBmsCZ;;AqB1sCQ;EAOI,6BAAA;ArBusCZ;;AqB9sCQ;EAOI,+BAAA;ArB2sCZ;;AqBltCQ;EAOI,8BAAA;ArB+sCZ;;AqBttCQ;EAOI,oBAAA;ArBmtCZ;;AqB1tCQ;EAOI,mBAAA;ArButCZ;;AqB9tCQ;EAOI,mBAAA;ArB2tCZ;;AqBluCQ;EAOI,mBAAA;ArB+tCZ;;AqBtuCQ;EAOI,mBAAA;ArBmuCZ;;AqB1uCQ;EAOI,mBAAA;ArBuuCZ;;AqB9uCQ;EAOI,mBAAA;ArB2uCZ;;AqBlvCQ;EAOI,mBAAA;ArB+uCZ;;AqBtvCQ;EAOI,oBAAA;ArBmvCZ;;AqB1vCQ;EAOI,4BAAA;ArBuvCZ;;AqB9vCQ;EAOI,2BAAA;ArB2vCZ;;AqBlwCQ;EAOI,0BAAA;ArB+vCZ;;AqBtwCQ;EAOI,2BAAA;ArBmwCZ;;AqB1wCQ;EAOI,0BAAA;ArBuwCZ;;AqB9wCQ;EAOI,uBAAA;ArB2wCZ;;AqBlxCQ;EAOI,0BAAA;EAAA,yBAAA;ArBgxCZ;;AqBvxCQ;EAOI,kCAAA;EAAA,iCAAA;ArBqxCZ;;AqB5xCQ;EAOI,iCAAA;EAAA,gCAAA;ArB0xCZ;;AqBjyCQ;EAOI,gCAAA;EAAA,+BAAA;ArB+xCZ;;AqBtyCQ;EAOI,iCAAA;EAAA,gCAAA;ArBoyCZ;;AqB3yCQ;EAOI,gCAAA;EAAA,+BAAA;ArByyCZ;;AqBhzCQ;EAOI,6BAAA;EAAA,4BAAA;ArB8yCZ;;AqBrzCQ;EAOI,wBAAA;EAAA,2BAAA;ArBmzCZ;;AqB1zCQ;EAOI,gCAAA;EAAA,mCAAA;ArBwzCZ;;AqB/zCQ;EAOI,+BAAA;EAAA,kCAAA;ArB6zCZ;;AqBp0CQ;EAOI,8BAAA;EAAA,iCAAA;ArBk0CZ;;AqBz0CQ;EAOI,+BAAA;EAAA,kCAAA;ArBu0CZ;;AqB90CQ;EAOI,8BAAA;EAAA,iCAAA;ArB40CZ;;AqBn1CQ;EAOI,2BAAA;EAAA,8BAAA;ArBi1CZ;;AqBx1CQ;EAOI,wBAAA;ArBq1CZ;;AqB51CQ;EAOI,gCAAA;ArBy1CZ;;AqBh2CQ;EAOI,+BAAA;ArB61CZ;;AqBp2CQ;EAOI,8BAAA;ArBi2CZ;;AqBx2CQ;EAOI,+BAAA;ArBq2CZ;;AqB52CQ;EAOI,8BAAA;ArBy2CZ;;AqBh3CQ;EAOI,2BAAA;ArB62CZ;;AqBp3CQ;EAOI,0BAAA;ArBi3CZ;;AqBx3CQ;EAOI,kCAAA;ArBq3CZ;;AqB53CQ;EAOI,iCAAA;ArBy3CZ;;AqBh4CQ;EAOI,gCAAA;ArB63CZ;;AqBp4CQ;EAOI,iCAAA;ArBi4CZ;;AqBx4CQ;EAOI,gCAAA;ArBq4CZ;;AqB54CQ;EAOI,6BAAA;ArBy4CZ;;AqBh5CQ;EAOI,2BAAA;ArB64CZ;;AqBp5CQ;EAOI,mCAAA;ArBi5CZ;;AqBx5CQ;EAOI,kCAAA;ArBq5CZ;;AqB55CQ;EAOI,iCAAA;ArBy5CZ;;AqBh6CQ;EAOI,kCAAA;ArB65CZ;;AqBp6CQ;EAOI,iCAAA;ArBi6CZ;;AqBx6CQ;EAOI,8BAAA;ArBq6CZ;;AqB56CQ;EAOI,yBAAA;ArBy6CZ;;AqBh7CQ;EAOI,iCAAA;ArB66CZ;;AqBp7CQ;EAOI,gCAAA;ArBi7CZ;;AqBx7CQ;EAOI,+BAAA;ArBq7CZ;;AqB57CQ;EAOI,gCAAA;ArBy7CZ;;AqBh8CQ;EAOI,+BAAA;ArB67CZ;;AqBp8CQ;EAOI,4BAAA;ArBi8CZ;;AqBx8CQ;EAOI,qBAAA;ArBq8CZ;;AqB58CQ;EAOI,6BAAA;ArBy8CZ;;AqBh9CQ;EAOI,4BAAA;ArB68CZ;;AqBp9CQ;EAOI,2BAAA;ArBi9CZ;;AqBx9CQ;EAOI,4BAAA;ArBq9CZ;;AqB59CQ;EAOI,2BAAA;ArBy9CZ;;AqBh+CQ;EAOI,2BAAA;EAAA,0BAAA;ArB89CZ;;AqBr+CQ;EAOI,mCAAA;EAAA,kCAAA;ArBm+CZ;;AqB1+CQ;EAOI,kCAAA;EAAA,iCAAA;ArBw+CZ;;AqB/+CQ;EAOI,iCAAA;EAAA,gCAAA;ArB6+CZ;;AqBp/CQ;EAOI,kCAAA;EAAA,iCAAA;ArBk/CZ;;AqBz/CQ;EAOI,iCAAA;EAAA,gCAAA;ArBu/CZ;;AqB9/CQ;EAOI,yBAAA;EAAA,4BAAA;ArB4/CZ;;AqBngDQ;EAOI,iCAAA;EAAA,oCAAA;ArBigDZ;;AqBxgDQ;EAOI,gCAAA;EAAA,mCAAA;ArBsgDZ;;AqB7gDQ;EAOI,+BAAA;EAAA,kCAAA;ArB2gDZ;;AqBlhDQ;EAOI,gCAAA;EAAA,mCAAA;ArBghDZ;;AqBvhDQ;EAOI,+BAAA;EAAA,kCAAA;ArBqhDZ;;AqB5hDQ;EAOI,yBAAA;ArByhDZ;;AqBhiDQ;EAOI,iCAAA;ArB6hDZ;;AqBpiDQ;EAOI,gCAAA;ArBiiDZ;;AqBxiDQ;EAOI,+BAAA;ArBqiDZ;;AqB5iDQ;EAOI,gCAAA;ArByiDZ;;AqBhjDQ;EAOI,+BAAA;ArB6iDZ;;AqBpjDQ;EAOI,2BAAA;ArBijDZ;;AqBxjDQ;EAOI,mCAAA;ArBqjDZ;;AqB5jDQ;EAOI,kCAAA;ArByjDZ;;AqBhkDQ;EAOI,iCAAA;ArB6jDZ;;AqBpkDQ;EAOI,kCAAA;ArBikDZ;;AqBxkDQ;EAOI,iCAAA;ArBqkDZ;;AqB5kDQ;EAOI,4BAAA;ArBykDZ;;AqBhlDQ;EAOI,oCAAA;ArB6kDZ;;AqBplDQ;EAOI,mCAAA;ArBilDZ;;AqBxlDQ;EAOI,kCAAA;ArBqlDZ;;AqB5lDQ;EAOI,mCAAA;ArBylDZ;;AqBhmDQ;EAOI,kCAAA;ArB6lDZ;;AqBpmDQ;EAOI,0BAAA;ArBimDZ;;AqBxmDQ;EAOI,kCAAA;ArBqmDZ;;AqB5mDQ;EAOI,iCAAA;ArBymDZ;;AqBhnDQ;EAOI,gCAAA;ArB6mDZ;;AqBpnDQ;EAOI,iCAAA;ArBinDZ;;AqBxnDQ;EAOI,gCAAA;ArBqnDZ;;AqB5nDQ;EAOI,iBAAA;ArBynDZ;;AqBhoDQ;EAOI,yBAAA;ArB6nDZ;;AqBpoDQ;EAOI,wBAAA;ArBioDZ;;AqBxoDQ;EAOI,uBAAA;ArBqoDZ;;AqB5oDQ;EAOI,wBAAA;ArByoDZ;;AqBhpDQ;EAOI,uBAAA;ArB6oDZ;;AqBppDQ;EAOI,qBAAA;ArBipDZ;;AqBxpDQ;EAOI,6BAAA;ArBqpDZ;;AqB5pDQ;EAOI,4BAAA;ArBypDZ;;AqBhqDQ;EAOI,2BAAA;ArB6pDZ;;AqBpqDQ;EAOI,4BAAA;ArBiqDZ;;AqBxqDQ;EAOI,2BAAA;ArBqqDZ;;AqB5qDQ;EAOI,6BAAA;EAAA,wBAAA;ArByqDZ;;AqBhrDQ;EAOI,qCAAA;EAAA,gCAAA;ArB6qDZ;;AqBprDQ;EAOI,oCAAA;EAAA,+BAAA;ArBirDZ;;AqBxrDQ;EAOI,mCAAA;EAAA,8BAAA;ArBqrDZ;;AqB5rDQ;EAOI,oCAAA;EAAA,+BAAA;ArByrDZ;;AqBhsDQ;EAOI,mCAAA;EAAA,8BAAA;ArB6rDZ;;AqBpsDQ;EAOI,gDAAA;ArBisDZ;;AqBxsDQ;EAOI,8BAAA;ArBqsDZ;;AqB5sDQ;EAOI,8BAAA;ArBysDZ;;AqBhtDQ;EAOI,4BAAA;ArB6sDZ;;AqBptDQ;EAOI,6BAAA;ArBitDZ;;AqBxtDQ;EAOI,8BAAA;ArBqtDZ;;AqB5tDQ;EAOI,0BAAA;ArBytDZ;;AqBhuDQ;EAOI,2BAAA;ArB6tDZ;;AqBpuDQ;EAOI,2BAAA;ArBiuDZ;;AqBxuDQ;EAOI,2BAAA;ArBquDZ;;AqB5uDQ;EAOI,2BAAA;ArByuDZ;;AqBhvDQ;EAOI,yBAAA;ArB6uDZ;;AqBpvDQ;EAOI,4BAAA;ArBivDZ;;AqBxvDQ;EAOI,2BAAA;ArBqvDZ;;AqB5vDQ;EAOI,yBAAA;ArByvDZ;;AqBhwDQ;EAOI,0BAAA;ArB6vDZ;;AqBpwDQ;EAOI,0BAAA;ArBiwDZ;;AqBxwDQ;EAOI,2BAAA;ArBqwDZ;;AqB5wDQ;EAOI,4BAAA;ArBywDZ;;AqBhxDQ;EAOI,6BAAA;ArB6wDZ;;AqBpxDQ;EAOI,gCAAA;ArBixDZ;;AqBxxDQ;EAOI,qCAAA;ArBqxDZ;;AqB5xDQ;EAOI,wCAAA;ArByxDZ;;AqBhyDQ;EAOI,oCAAA;ArB6xDZ;;AqBpyDQ;EAOI,oCAAA;ArBiyDZ;;AqBxyDQ;EAOI,qCAAA;ArBqyDZ;;AqB5yDQ;EAOI,8BAAA;ArByyDZ;;AqBhzDQ;EAOI,8BAAA;ArB6yDZ;;AqBl0DQ,qBAAA;AAcA;EAOI,gCAAA;EAAA,iCAAA;ArBmzDZ;;AqBhyDQ,mBAAA;AA1BA;EAIQ,oBAAA;EAGJ,qEAAA;ArByzDZ;;AqBh0DQ;EAIQ,oBAAA;EAGJ,uEAAA;ArB8zDZ;;AqBr0DQ;EAIQ,oBAAA;EAGJ,qEAAA;ArBm0DZ;;AqB10DQ;EAIQ,oBAAA;EAGJ,kEAAA;ArBw0DZ;;AqB/0DQ;EAIQ,oBAAA;EAGJ,qEAAA;ArB60DZ;;AqBp1DQ;EAIQ,oBAAA;EAGJ,oEAAA;ArBk1DZ;;AqBz1DQ;EAIQ,oBAAA;EAGJ,mEAAA;ArBu1DZ;;AqB91DQ;EAIQ,oBAAA;EAGJ,kEAAA;ArB41DZ;;AqBn2DQ;EAIQ,oBAAA;EAGJ,mEAAA;ArBi2DZ;;AqBx2DQ;EAIQ,oBAAA;EAGJ,mEAAA;ArBs2DZ;;AqB72DQ;EAIQ,oBAAA;EAGJ,wEAAA;ArB22DZ;;AqBl3DQ;EAIQ,oBAAA;EAGJ,2CAAA;ArBg3DZ;;AqBv3DQ;EAIQ,oBAAA;EAGJ,oCAAA;ArBq3DZ;;AqB53DQ;EAIQ,oBAAA;EAGJ,0CAAA;ArB03DZ;;AqBj4DQ;EAIQ,oBAAA;EAGJ,2CAAA;ArB+3DZ;;AqBt4DQ;EAIQ,oBAAA;EAGJ,0CAAA;ArBo4DZ;;AqB34DQ;EAIQ,oBAAA;EAGJ,0CAAA;ArBy4DZ;;AqBh5DQ;EAIQ,oBAAA;EAGJ,yBAAA;ArB84DZ;;AqB/5DQ;EACE,uBAAA;ArBk6DV;;AqBn6DQ;EACE,sBAAA;ArBs6DV;;AqBv6DQ;EACE,uBAAA;ArB06DV;;AqB36DQ;EACE,oBAAA;ArB86DV;;AqBr6DQ;EAOI,iDAAA;ArBk6DZ;;AqBz6DQ;EAOI,mDAAA;ArBs6DZ;;AqB76DQ;EAOI,iDAAA;ArB06DZ;;AqBj7DQ;EAOI,8CAAA;ArB86DZ;;AqBr7DQ;EAOI,iDAAA;ArBk7DZ;;AqBz7DQ;EAOI,gDAAA;ArBs7DZ;;AqB77DQ;EAOI,+CAAA;ArB07DZ;;AqBj8DQ;EAOI,8CAAA;ArB87DZ;;AqB/8DQ;EACE,sBAAA;ArBk9DV;;AqB98DU;EACE,sBAAA;ArBi9DZ;;AqBv9DQ;EACE,uBAAA;ArB09DV;;AqBt9DU;EACE,uBAAA;ArBy9DZ;;AqB/9DQ;EACE,sBAAA;ArBk+DV;;AqB99DU;EACE,sBAAA;ArBi+DZ;;AqBv+DQ;EACE,uBAAA;ArB0+DV;;AqBt+DU;EACE,uBAAA;ArBy+DZ;;AqB/+DQ;EACE,oBAAA;ArBk/DV;;AqB9+DU;EACE,oBAAA;ArBi/DZ;;AqB7+DQ;EAOI,yCAAA;ArB0+DZ;;AqBr+DU;EAOI,yCAAA;ArBk+Dd;;AqBr/DQ;EAOI,wCAAA;ArBk/DZ;;AqB7+DU;EAOI,wCAAA;ArB0+Dd;;AqB7/DQ;EAOI,yCAAA;ArB0/DZ;;AqBr/DU;EAOI,yCAAA;ArBk/Dd;;AqBrgEQ;EAIQ,8BAAA;EAGJ,uGAAA;EAAA,+FAAA;ArBmgEZ;;AqB1gEQ;EAIQ,8BAAA;EAGJ,yGAAA;EAAA,iGAAA;ArBwgEZ;;AqB/gEQ;EAIQ,8BAAA;EAGJ,uGAAA;EAAA,+FAAA;ArB6gEZ;;AqBphEQ;EAIQ,8BAAA;EAGJ,oGAAA;EAAA,4FAAA;ArBkhEZ;;AqBzhEQ;EAIQ,8BAAA;EAGJ,uGAAA;EAAA,+FAAA;ArBuhEZ;;AqB9hEQ;EAIQ,8BAAA;EAGJ,sGAAA;EAAA,8FAAA;ArB4hEZ;;AqBniEQ;EAIQ,8BAAA;EAGJ,qGAAA;EAAA,6FAAA;ArBiiEZ;;AqBxiEQ;EAIQ,8BAAA;EAGJ,oGAAA;EAAA,4FAAA;ArBsiEZ;;AqB7iEQ;EAIQ,8BAAA;EAGJ,6GAAA;EAAA,qGAAA;ArB2iEZ;;AqB5jEQ;EACE,8BAAA;ArB+jEV;;AqB3jEU;EACE,8BAAA;ArB8jEZ;;AqBpkEQ;EACE,gCAAA;ArBukEV;;AqBnkEU;EACE,gCAAA;ArBskEZ;;AqB5kEQ;EACE,iCAAA;ArB+kEV;;AqB3kEU;EACE,iCAAA;ArB8kEZ;;AqBplEQ;EACE,gCAAA;ArBulEV;;AqBnlEU;EACE,gCAAA;ArBslEZ;;AqB5lEQ;EACE,iCAAA;ArB+lEV;;AqB3lEU;EACE,iCAAA;ArB8lEZ;;AqBpmEQ;EACE,8BAAA;ArBumEV;;AqBnmEU;EACE,8BAAA;ArBsmEZ;;AqBlmEQ;EAIQ,kBAAA;EAGJ,8EAAA;ArBgmEZ;;AqBvmEQ;EAIQ,kBAAA;EAGJ,gFAAA;ArBqmEZ;;AqB5mEQ;EAIQ,kBAAA;EAGJ,8EAAA;ArB0mEZ;;AqBjnEQ;EAIQ,kBAAA;EAGJ,2EAAA;ArB+mEZ;;AqBtnEQ;EAIQ,kBAAA;EAGJ,8EAAA;ArBonEZ;;AqB3nEQ;EAIQ,kBAAA;EAGJ,6EAAA;ArBynEZ;;AqBhoEQ;EAIQ,kBAAA;EAGJ,4EAAA;ArB8nEZ;;AqBroEQ;EAIQ,kBAAA;EAGJ,2EAAA;ArBmoEZ;;AqB1oEQ;EAIQ,kBAAA;EAGJ,4EAAA;ArBwoEZ;;AqB/oEQ;EAIQ,kBAAA;EAGJ,4EAAA;ArB6oEZ;;AqBppEQ;EAIQ,kBAAA;EAGJ,8EAAA;ArBkpEZ;;AqBzpEQ;EAIQ,kBAAA;EAGJ,wCAAA;ArBupEZ;;AqB9pEQ;EAIQ,kBAAA;EAGJ,mFAAA;ArB4pEZ;;AqBnqEQ;EAIQ,kBAAA;EAGJ,kFAAA;ArBiqEZ;;AqBxqEQ;EAIQ,kBAAA;EAGJ,oCAAA;ArBsqEZ;;AqB7qEQ;EAIQ,kBAAA;EAGJ,oCAAA;ArB2qEZ;;AqBlrEQ;EAIQ,kBAAA;EAGJ,oCAAA;ArBgrEZ;;AqBvrEQ;EAIQ,kBAAA;EAGJ,oCAAA;ArBqrEZ;;AqB5rEQ;EAIQ,kBAAA;EAGJ,oCAAA;ArB0rEZ;;AqBjsEQ;EAIQ,kBAAA;EAGJ,oCAAA;ArB+rEZ;;AqBhtEQ;EACE,oBAAA;ArBmtEV;;AqBptEQ;EACE,qBAAA;ArButEV;;AqBxtEQ;EACE,oBAAA;ArB2tEV;;AqB5tEQ;EACE,qBAAA;ArB+tEV;;AqBhuEQ;EACE,kBAAA;ArBmuEV;;AqB1tEQ;EAOI,wDAAA;ArButEZ;;AqB9tEQ;EAOI,0DAAA;ArB2tEZ;;AqBluEQ;EAOI,wDAAA;ArB+tEZ;;AqBtuEQ;EAOI,qDAAA;ArBmuEZ;;AqB1uEQ;EAOI,wDAAA;ArBuuEZ;;AqB9uEQ;EAOI,uDAAA;ArB2uEZ;;AqBlvEQ;EAOI,sDAAA;ArB+uEZ;;AqBtvEQ;EAOI,qDAAA;ArBmvEZ;;AqB1vEQ;EAOI,+CAAA;ArBuvEZ;;AqB9vEQ;EAOI,mCAAA;EAAA,gCAAA;EAAA,2BAAA;ArB2vEZ;;AqBlwEQ;EAOI,oCAAA;EAAA,iCAAA;EAAA,4BAAA;ArB+vEZ;;AqBtwEQ;EAOI,oCAAA;EAAA,iCAAA;EAAA,4BAAA;ArBmwEZ;;AqB1wEQ;EAOI,+BAAA;ArBuwEZ;;AqB9wEQ;EAOI,+BAAA;ArB2wEZ;;AqBlxEQ;EAOI,iDAAA;ArB+wEZ;;AqBtxEQ;EAOI,2BAAA;ArBmxEZ;;AqB1xEQ;EAOI,oDAAA;ArBuxEZ;;AqB9xEQ;EAOI,iDAAA;ArB2xEZ;;AqBlyEQ;EAOI,oDAAA;ArB+xEZ;;AqBtyEQ;EAOI,oDAAA;ArBmyEZ;;AqB1yEQ;EAOI,qDAAA;ArBuyEZ;;AqB9yEQ;EAOI,6BAAA;ArB2yEZ;;AqBlzEQ;EAOI,sDAAA;ArB+yEZ;;AqBtzEQ;EAOI,0DAAA;EAAA,2DAAA;ArBozEZ;;AqB3zEQ;EAOI,oCAAA;EAAA,qCAAA;ArByzEZ;;AqBh0EQ;EAOI,6DAAA;EAAA,8DAAA;ArB8zEZ;;AqBr0EQ;EAOI,0DAAA;EAAA,2DAAA;ArBm0EZ;;AqB10EQ;EAOI,6DAAA;EAAA,8DAAA;ArBw0EZ;;AqB/0EQ;EAOI,6DAAA;EAAA,8DAAA;ArB60EZ;;AqBp1EQ;EAOI,8DAAA;EAAA,+DAAA;ArBk1EZ;;AqBz1EQ;EAOI,sCAAA;EAAA,uCAAA;ArBu1EZ;;AqB91EQ;EAOI,+DAAA;EAAA,gEAAA;ArB41EZ;;AqBn2EQ;EAOI,2DAAA;EAAA,8DAAA;ArBi2EZ;;AqBx2EQ;EAOI,qCAAA;EAAA,wCAAA;ArBs2EZ;;AqB72EQ;EAOI,8DAAA;EAAA,iEAAA;ArB22EZ;;AqBl3EQ;EAOI,2DAAA;EAAA,8DAAA;ArBg3EZ;;AqBv3EQ;EAOI,8DAAA;EAAA,iEAAA;ArBq3EZ;;AqB53EQ;EAOI,8DAAA;EAAA,iEAAA;ArB03EZ;;AqBj4EQ;EAOI,+DAAA;EAAA,kEAAA;ArB+3EZ;;AqBt4EQ;EAOI,uCAAA;EAAA,0CAAA;ArBo4EZ;;AqB34EQ;EAOI,gEAAA;EAAA,mEAAA;ArBy4EZ;;AqBh5EQ;EAOI,8DAAA;EAAA,6DAAA;ArB84EZ;;AqBr5EQ;EAOI,wCAAA;EAAA,uCAAA;ArBm5EZ;;AqB15EQ;EAOI,iEAAA;EAAA,gEAAA;ArBw5EZ;;AqB/5EQ;EAOI,8DAAA;EAAA,6DAAA;ArB65EZ;;AqBp6EQ;EAOI,iEAAA;EAAA,gEAAA;ArBk6EZ;;AqBz6EQ;EAOI,iEAAA;EAAA,gEAAA;ArBu6EZ;;AqB96EQ;EAOI,kEAAA;EAAA,iEAAA;ArB46EZ;;AqBn7EQ;EAOI,0CAAA;EAAA,yCAAA;ArBi7EZ;;AqBx7EQ;EAOI,mEAAA;EAAA,kEAAA;ArBs7EZ;;AqB77EQ;EAOI,6DAAA;EAAA,0DAAA;ArB27EZ;;AqBl8EQ;EAOI,uCAAA;EAAA,oCAAA;ArBg8EZ;;AqBv8EQ;EAOI,gEAAA;EAAA,6DAAA;ArBq8EZ;;AqB58EQ;EAOI,6DAAA;EAAA,0DAAA;ArB08EZ;;AqBj9EQ;EAOI,gEAAA;EAAA,6DAAA;ArB+8EZ;;AqBt9EQ;EAOI,gEAAA;EAAA,6DAAA;ArBo9EZ;;AqB39EQ;EAOI,iEAAA;EAAA,8DAAA;ArBy9EZ;;AqBh+EQ;EAOI,yCAAA;EAAA,sCAAA;ArB89EZ;;AqBr+EQ;EAOI,kEAAA;EAAA,+DAAA;ArBm+EZ;;AqB1+EQ;EAOI,8BAAA;ArBu+EZ;;AqB9+EQ;EAOI,6BAAA;ArB2+EZ;;AqBl/EQ;EAOI,sBAAA;ArB++EZ;;AqBt/EQ;EAOI,qBAAA;ArBm/EZ;;AqB1/EQ;EAOI,qBAAA;ArBu/EZ;;AqB9/EQ;EAOI,qBAAA;ArB2/EZ;;AqBlgFQ;EAOI,qBAAA;ArB+/EZ;;AazgFI;EQGI;IAOI,sBAAA;ErBogFV;EqB3gFM;IAOI,uBAAA;ErBugFV;EqB9gFM;IAOI,sBAAA;ErB0gFV;EqBjhFM;IAOI,iCAAA;IAAA,8BAAA;ErB6gFV;EqBphFM;IAOI,+BAAA;IAAA,4BAAA;ErBghFV;EqBvhFM;IAOI,8BAAA;IAAA,2BAAA;ErBmhFV;EqB1hFM;IAOI,oCAAA;IAAA,iCAAA;ErBshFV;EqB7hFM;IAOI,8BAAA;IAAA,2BAAA;ErByhFV;EqBhiFM;IAOI,0BAAA;ErB4hFV;EqBniFM;IAOI,gCAAA;ErB+hFV;EqBtiFM;IAOI,yBAAA;ErBkiFV;EqBziFM;IAOI,wBAAA;ErBqiFV;EqB5iFM;IAOI,+BAAA;ErBwiFV;EqB/iFM;IAOI,yBAAA;ErB2iFV;EqBljFM;IAOI,6BAAA;ErB8iFV;EqBrjFM;IAOI,8BAAA;ErBijFV;EqBxjFM;IAOI,wBAAA;ErBojFV;EqB3jFM;IAOI,+BAAA;ErBujFV;EqB9jFM;IAOI,wBAAA;ErB0jFV;EqBjkFM;IAOI,yBAAA;ErB6jFV;EqBpkFM;IAOI,8BAAA;ErBgkFV;EqBvkFM;IAOI,iCAAA;ErBmkFV;EqB1kFM;IAOI,sCAAA;ErBskFV;EqB7kFM;IAOI,yCAAA;ErBykFV;EqBhlFM;IAOI,uBAAA;ErB4kFV;EqBnlFM;IAOI,uBAAA;ErB+kFV;EqBtlFM;IAOI,yBAAA;ErBklFV;EqBzlFM;IAOI,yBAAA;ErBqlFV;EqB5lFM;IAOI,0BAAA;ErBwlFV;EqB/lFM;IAOI,4BAAA;ErB2lFV;EqBlmFM;IAOI,kCAAA;ErB8lFV;EqBrmFM;IAOI,sCAAA;ErBimFV;EqBxmFM;IAOI,oCAAA;ErBomFV;EqB3mFM;IAOI,kCAAA;ErBumFV;EqB9mFM;IAOI,yCAAA;ErB0mFV;EqBjnFM;IAOI,wCAAA;ErB6mFV;EqBpnFM;IAOI,wCAAA;ErBgnFV;EqBvnFM;IAOI,kCAAA;ErBmnFV;EqB1nFM;IAOI,gCAAA;ErBsnFV;EqB7nFM;IAOI,8BAAA;ErBynFV;EqBhoFM;IAOI,gCAAA;ErB4nFV;EqBnoFM;IAOI,+BAAA;ErB+nFV;EqBtoFM;IAOI,oCAAA;ErBkoFV;EqBzoFM;IAOI,kCAAA;ErBqoFV;EqB5oFM;IAOI,gCAAA;ErBwoFV;EqB/oFM;IAOI,uCAAA;ErB2oFV;EqBlpFM;IAOI,sCAAA;ErB8oFV;EqBrpFM;IAOI,iCAAA;ErBipFV;EqBxpFM;IAOI,2BAAA;ErBopFV;EqB3pFM;IAOI,iCAAA;ErBupFV;EqB9pFM;IAOI,+BAAA;ErB0pFV;EqBjqFM;IAOI,6BAAA;ErB6pFV;EqBpqFM;IAOI,+BAAA;ErBgqFV;EqBvqFM;IAOI,8BAAA;ErBmqFV;EqB1qFM;IAOI,oBAAA;ErBsqFV;EqB7qFM;IAOI,mBAAA;ErByqFV;EqBhrFM;IAOI,mBAAA;ErB4qFV;EqBnrFM;IAOI,mBAAA;ErB+qFV;EqBtrFM;IAOI,mBAAA;ErBkrFV;EqBzrFM;IAOI,mBAAA;ErBqrFV;EqB5rFM;IAOI,mBAAA;ErBwrFV;EqB/rFM;IAOI,mBAAA;ErB2rFV;EqBlsFM;IAOI,oBAAA;ErB8rFV;EqBrsFM;IAOI,4BAAA;ErBisFV;EqBxsFM;IAOI,2BAAA;ErBosFV;EqB3sFM;IAOI,0BAAA;ErBusFV;EqB9sFM;IAOI,2BAAA;ErB0sFV;EqBjtFM;IAOI,0BAAA;ErB6sFV;EqBptFM;IAOI,uBAAA;ErBgtFV;EqBvtFM;IAOI,0BAAA;IAAA,yBAAA;ErBotFV;EqB3tFM;IAOI,kCAAA;IAAA,iCAAA;ErBwtFV;EqB/tFM;IAOI,iCAAA;IAAA,gCAAA;ErB4tFV;EqBnuFM;IAOI,gCAAA;IAAA,+BAAA;ErBguFV;EqBvuFM;IAOI,iCAAA;IAAA,gCAAA;ErBouFV;EqB3uFM;IAOI,gCAAA;IAAA,+BAAA;ErBwuFV;EqB/uFM;IAOI,6BAAA;IAAA,4BAAA;ErB4uFV;EqBnvFM;IAOI,wBAAA;IAAA,2BAAA;ErBgvFV;EqBvvFM;IAOI,gCAAA;IAAA,mCAAA;ErBovFV;EqB3vFM;IAOI,+BAAA;IAAA,kCAAA;ErBwvFV;EqB/vFM;IAOI,8BAAA;IAAA,iCAAA;ErB4vFV;EqBnwFM;IAOI,+BAAA;IAAA,kCAAA;ErBgwFV;EqBvwFM;IAOI,8BAAA;IAAA,iCAAA;ErBowFV;EqB3wFM;IAOI,2BAAA;IAAA,8BAAA;ErBwwFV;EqB/wFM;IAOI,wBAAA;ErB2wFV;EqBlxFM;IAOI,gCAAA;ErB8wFV;EqBrxFM;IAOI,+BAAA;ErBixFV;EqBxxFM;IAOI,8BAAA;ErBoxFV;EqB3xFM;IAOI,+BAAA;ErBuxFV;EqB9xFM;IAOI,8BAAA;ErB0xFV;EqBjyFM;IAOI,2BAAA;ErB6xFV;EqBpyFM;IAOI,0BAAA;ErBgyFV;EqBvyFM;IAOI,kCAAA;ErBmyFV;EqB1yFM;IAOI,iCAAA;ErBsyFV;EqB7yFM;IAOI,gCAAA;ErByyFV;EqBhzFM;IAOI,iCAAA;ErB4yFV;EqBnzFM;IAOI,gCAAA;ErB+yFV;EqBtzFM;IAOI,6BAAA;ErBkzFV;EqBzzFM;IAOI,2BAAA;ErBqzFV;EqB5zFM;IAOI,mCAAA;ErBwzFV;EqB/zFM;IAOI,kCAAA;ErB2zFV;EqBl0FM;IAOI,iCAAA;ErB8zFV;EqBr0FM;IAOI,kCAAA;ErBi0FV;EqBx0FM;IAOI,iCAAA;ErBo0FV;EqB30FM;IAOI,8BAAA;ErBu0FV;EqB90FM;IAOI,yBAAA;ErB00FV;EqBj1FM;IAOI,iCAAA;ErB60FV;EqBp1FM;IAOI,gCAAA;ErBg1FV;EqBv1FM;IAOI,+BAAA;ErBm1FV;EqB11FM;IAOI,gCAAA;ErBs1FV;EqB71FM;IAOI,+BAAA;ErBy1FV;EqBh2FM;IAOI,4BAAA;ErB41FV;EqBn2FM;IAOI,qBAAA;ErB+1FV;EqBt2FM;IAOI,6BAAA;ErBk2FV;EqBz2FM;IAOI,4BAAA;ErBq2FV;EqB52FM;IAOI,2BAAA;ErBw2FV;EqB/2FM;IAOI,4BAAA;ErB22FV;EqBl3FM;IAOI,2BAAA;ErB82FV;EqBr3FM;IAOI,2BAAA;IAAA,0BAAA;ErBk3FV;EqBz3FM;IAOI,mCAAA;IAAA,kCAAA;ErBs3FV;EqB73FM;IAOI,kCAAA;IAAA,iCAAA;ErB03FV;EqBj4FM;IAOI,iCAAA;IAAA,gCAAA;ErB83FV;EqBr4FM;IAOI,kCAAA;IAAA,iCAAA;ErBk4FV;EqBz4FM;IAOI,iCAAA;IAAA,gCAAA;ErBs4FV;EqB74FM;IAOI,yBAAA;IAAA,4BAAA;ErB04FV;EqBj5FM;IAOI,iCAAA;IAAA,oCAAA;ErB84FV;EqBr5FM;IAOI,gCAAA;IAAA,mCAAA;ErBk5FV;EqBz5FM;IAOI,+BAAA;IAAA,kCAAA;ErBs5FV;EqB75FM;IAOI,gCAAA;IAAA,mCAAA;ErB05FV;EqBj6FM;IAOI,+BAAA;IAAA,kCAAA;ErB85FV;EqBr6FM;IAOI,yBAAA;ErBi6FV;EqBx6FM;IAOI,iCAAA;ErBo6FV;EqB36FM;IAOI,gCAAA;ErBu6FV;EqB96FM;IAOI,+BAAA;ErB06FV;EqBj7FM;IAOI,gCAAA;ErB66FV;EqBp7FM;IAOI,+BAAA;ErBg7FV;EqBv7FM;IAOI,2BAAA;ErBm7FV;EqB17FM;IAOI,mCAAA;ErBs7FV;EqB77FM;IAOI,kCAAA;ErBy7FV;EqBh8FM;IAOI,iCAAA;ErB47FV;EqBn8FM;IAOI,kCAAA;ErB+7FV;EqBt8FM;IAOI,iCAAA;ErBk8FV;EqBz8FM;IAOI,4BAAA;ErBq8FV;EqB58FM;IAOI,oCAAA;ErBw8FV;EqB/8FM;IAOI,mCAAA;ErB28FV;EqBl9FM;IAOI,kCAAA;ErB88FV;EqBr9FM;IAOI,mCAAA;ErBi9FV;EqBx9FM;IAOI,kCAAA;ErBo9FV;EqB39FM;IAOI,0BAAA;ErBu9FV;EqB99FM;IAOI,kCAAA;ErB09FV;EqBj+FM;IAOI,iCAAA;ErB69FV;EqBp+FM;IAOI,gCAAA;ErBg+FV;EqBv+FM;IAOI,iCAAA;ErBm+FV;EqB1+FM;IAOI,gCAAA;ErBs+FV;EqB7+FM;IAOI,iBAAA;ErBy+FV;EqBh/FM;IAOI,yBAAA;ErB4+FV;EqBn/FM;IAOI,wBAAA;ErB++FV;EqBt/FM;IAOI,uBAAA;ErBk/FV;EqBz/FM;IAOI,wBAAA;ErBq/FV;EqB5/FM;IAOI,uBAAA;ErBw/FV;EqB//FM;IAOI,qBAAA;ErB2/FV;EqBlgGM;IAOI,6BAAA;ErB8/FV;EqBrgGM;IAOI,4BAAA;ErBigGV;EqBxgGM;IAOI,2BAAA;ErBogGV;EqB3gGM;IAOI,4BAAA;ErBugGV;EqB9gGM;IAOI,2BAAA;ErB0gGV;EqBjhGM;IAOI,6BAAA;IAAA,wBAAA;ErB6gGV;EqBphGM;IAOI,qCAAA;IAAA,gCAAA;ErBghGV;EqBvhGM;IAOI,oCAAA;IAAA,+BAAA;ErBmhGV;EqB1hGM;IAOI,mCAAA;IAAA,8BAAA;ErBshGV;EqB7hGM;IAOI,oCAAA;IAAA,+BAAA;ErByhGV;EqBhiGM;IAOI,mCAAA;IAAA,8BAAA;ErB4hGV;EqBniGM;IAOI,2BAAA;ErB+hGV;EqBtiGM;IAOI,4BAAA;ErBkiGV;EqBziGM;IAOI,6BAAA;ErBqiGV;AACF;AahjGI;EQGI;IAOI,sBAAA;ErB0iGV;EqBjjGM;IAOI,uBAAA;ErB6iGV;EqBpjGM;IAOI,sBAAA;ErBgjGV;EqBvjGM;IAOI,iCAAA;IAAA,8BAAA;ErBmjGV;EqB1jGM;IAOI,+BAAA;IAAA,4BAAA;ErBsjGV;EqB7jGM;IAOI,8BAAA;IAAA,2BAAA;ErByjGV;EqBhkGM;IAOI,oCAAA;IAAA,iCAAA;ErB4jGV;EqBnkGM;IAOI,8BAAA;IAAA,2BAAA;ErB+jGV;EqBtkGM;IAOI,0BAAA;ErBkkGV;EqBzkGM;IAOI,gCAAA;ErBqkGV;EqB5kGM;IAOI,yBAAA;ErBwkGV;EqB/kGM;IAOI,wBAAA;ErB2kGV;EqBllGM;IAOI,+BAAA;ErB8kGV;EqBrlGM;IAOI,yBAAA;ErBilGV;EqBxlGM;IAOI,6BAAA;ErBolGV;EqB3lGM;IAOI,8BAAA;ErBulGV;EqB9lGM;IAOI,wBAAA;ErB0lGV;EqBjmGM;IAOI,+BAAA;ErB6lGV;EqBpmGM;IAOI,wBAAA;ErBgmGV;EqBvmGM;IAOI,yBAAA;ErBmmGV;EqB1mGM;IAOI,8BAAA;ErBsmGV;EqB7mGM;IAOI,iCAAA;ErBymGV;EqBhnGM;IAOI,sCAAA;ErB4mGV;EqBnnGM;IAOI,yCAAA;ErB+mGV;EqBtnGM;IAOI,uBAAA;ErBknGV;EqBznGM;IAOI,uBAAA;ErBqnGV;EqB5nGM;IAOI,yBAAA;ErBwnGV;EqB/nGM;IAOI,yBAAA;ErB2nGV;EqBloGM;IAOI,0BAAA;ErB8nGV;EqBroGM;IAOI,4BAAA;ErBioGV;EqBxoGM;IAOI,kCAAA;ErBooGV;EqB3oGM;IAOI,sCAAA;ErBuoGV;EqB9oGM;IAOI,oCAAA;ErB0oGV;EqBjpGM;IAOI,kCAAA;ErB6oGV;EqBppGM;IAOI,yCAAA;ErBgpGV;EqBvpGM;IAOI,wCAAA;ErBmpGV;EqB1pGM;IAOI,wCAAA;ErBspGV;EqB7pGM;IAOI,kCAAA;ErBypGV;EqBhqGM;IAOI,gCAAA;ErB4pGV;EqBnqGM;IAOI,8BAAA;ErB+pGV;EqBtqGM;IAOI,gCAAA;ErBkqGV;EqBzqGM;IAOI,+BAAA;ErBqqGV;EqB5qGM;IAOI,oCAAA;ErBwqGV;EqB/qGM;IAOI,kCAAA;ErB2qGV;EqBlrGM;IAOI,gCAAA;ErB8qGV;EqBrrGM;IAOI,uCAAA;ErBirGV;EqBxrGM;IAOI,sCAAA;ErBorGV;EqB3rGM;IAOI,iCAAA;ErBurGV;EqB9rGM;IAOI,2BAAA;ErB0rGV;EqBjsGM;IAOI,iCAAA;ErB6rGV;EqBpsGM;IAOI,+BAAA;ErBgsGV;EqBvsGM;IAOI,6BAAA;ErBmsGV;EqB1sGM;IAOI,+BAAA;ErBssGV;EqB7sGM;IAOI,8BAAA;ErBysGV;EqBhtGM;IAOI,oBAAA;ErB4sGV;EqBntGM;IAOI,mBAAA;ErB+sGV;EqBttGM;IAOI,mBAAA;ErBktGV;EqBztGM;IAOI,mBAAA;ErBqtGV;EqB5tGM;IAOI,mBAAA;ErBwtGV;EqB/tGM;IAOI,mBAAA;ErB2tGV;EqBluGM;IAOI,mBAAA;ErB8tGV;EqBruGM;IAOI,mBAAA;ErBiuGV;EqBxuGM;IAOI,oBAAA;ErBouGV;EqB3uGM;IAOI,4BAAA;ErBuuGV;EqB9uGM;IAOI,2BAAA;ErB0uGV;EqBjvGM;IAOI,0BAAA;ErB6uGV;EqBpvGM;IAOI,2BAAA;ErBgvGV;EqBvvGM;IAOI,0BAAA;ErBmvGV;EqB1vGM;IAOI,uBAAA;ErBsvGV;EqB7vGM;IAOI,0BAAA;IAAA,yBAAA;ErB0vGV;EqBjwGM;IAOI,kCAAA;IAAA,iCAAA;ErB8vGV;EqBrwGM;IAOI,iCAAA;IAAA,gCAAA;ErBkwGV;EqBzwGM;IAOI,gCAAA;IAAA,+BAAA;ErBswGV;EqB7wGM;IAOI,iCAAA;IAAA,gCAAA;ErB0wGV;EqBjxGM;IAOI,gCAAA;IAAA,+BAAA;ErB8wGV;EqBrxGM;IAOI,6BAAA;IAAA,4BAAA;ErBkxGV;EqBzxGM;IAOI,wBAAA;IAAA,2BAAA;ErBsxGV;EqB7xGM;IAOI,gCAAA;IAAA,mCAAA;ErB0xGV;EqBjyGM;IAOI,+BAAA;IAAA,kCAAA;ErB8xGV;EqBryGM;IAOI,8BAAA;IAAA,iCAAA;ErBkyGV;EqBzyGM;IAOI,+BAAA;IAAA,kCAAA;ErBsyGV;EqB7yGM;IAOI,8BAAA;IAAA,iCAAA;ErB0yGV;EqBjzGM;IAOI,2BAAA;IAAA,8BAAA;ErB8yGV;EqBrzGM;IAOI,wBAAA;ErBizGV;EqBxzGM;IAOI,gCAAA;ErBozGV;EqB3zGM;IAOI,+BAAA;ErBuzGV;EqB9zGM;IAOI,8BAAA;ErB0zGV;EqBj0GM;IAOI,+BAAA;ErB6zGV;EqBp0GM;IAOI,8BAAA;ErBg0GV;EqBv0GM;IAOI,2BAAA;ErBm0GV;EqB10GM;IAOI,0BAAA;ErBs0GV;EqB70GM;IAOI,kCAAA;ErBy0GV;EqBh1GM;IAOI,iCAAA;ErB40GV;EqBn1GM;IAOI,gCAAA;ErB+0GV;EqBt1GM;IAOI,iCAAA;ErBk1GV;EqBz1GM;IAOI,gCAAA;ErBq1GV;EqB51GM;IAOI,6BAAA;ErBw1GV;EqB/1GM;IAOI,2BAAA;ErB21GV;EqBl2GM;IAOI,mCAAA;ErB81GV;EqBr2GM;IAOI,kCAAA;ErBi2GV;EqBx2GM;IAOI,iCAAA;ErBo2GV;EqB32GM;IAOI,kCAAA;ErBu2GV;EqB92GM;IAOI,iCAAA;ErB02GV;EqBj3GM;IAOI,8BAAA;ErB62GV;EqBp3GM;IAOI,yBAAA;ErBg3GV;EqBv3GM;IAOI,iCAAA;ErBm3GV;EqB13GM;IAOI,gCAAA;ErBs3GV;EqB73GM;IAOI,+BAAA;ErBy3GV;EqBh4GM;IAOI,gCAAA;ErB43GV;EqBn4GM;IAOI,+BAAA;ErB+3GV;EqBt4GM;IAOI,4BAAA;ErBk4GV;EqBz4GM;IAOI,qBAAA;ErBq4GV;EqB54GM;IAOI,6BAAA;ErBw4GV;EqB/4GM;IAOI,4BAAA;ErB24GV;EqBl5GM;IAOI,2BAAA;ErB84GV;EqBr5GM;IAOI,4BAAA;ErBi5GV;EqBx5GM;IAOI,2BAAA;ErBo5GV;EqB35GM;IAOI,2BAAA;IAAA,0BAAA;ErBw5GV;EqB/5GM;IAOI,mCAAA;IAAA,kCAAA;ErB45GV;EqBn6GM;IAOI,kCAAA;IAAA,iCAAA;ErBg6GV;EqBv6GM;IAOI,iCAAA;IAAA,gCAAA;ErBo6GV;EqB36GM;IAOI,kCAAA;IAAA,iCAAA;ErBw6GV;EqB/6GM;IAOI,iCAAA;IAAA,gCAAA;ErB46GV;EqBn7GM;IAOI,yBAAA;IAAA,4BAAA;ErBg7GV;EqBv7GM;IAOI,iCAAA;IAAA,oCAAA;ErBo7GV;EqB37GM;IAOI,gCAAA;IAAA,mCAAA;ErBw7GV;EqB/7GM;IAOI,+BAAA;IAAA,kCAAA;ErB47GV;EqBn8GM;IAOI,gCAAA;IAAA,mCAAA;ErBg8GV;EqBv8GM;IAOI,+BAAA;IAAA,kCAAA;ErBo8GV;EqB38GM;IAOI,yBAAA;ErBu8GV;EqB98GM;IAOI,iCAAA;ErB08GV;EqBj9GM;IAOI,gCAAA;ErB68GV;EqBp9GM;IAOI,+BAAA;ErBg9GV;EqBv9GM;IAOI,gCAAA;ErBm9GV;EqB19GM;IAOI,+BAAA;ErBs9GV;EqB79GM;IAOI,2BAAA;ErBy9GV;EqBh+GM;IAOI,mCAAA;ErB49GV;EqBn+GM;IAOI,kCAAA;ErB+9GV;EqBt+GM;IAOI,iCAAA;ErBk+GV;EqBz+GM;IAOI,kCAAA;ErBq+GV;EqB5+GM;IAOI,iCAAA;ErBw+GV;EqB/+GM;IAOI,4BAAA;ErB2+GV;EqBl/GM;IAOI,oCAAA;ErB8+GV;EqBr/GM;IAOI,mCAAA;ErBi/GV;EqBx/GM;IAOI,kCAAA;ErBo/GV;EqB3/GM;IAOI,mCAAA;ErBu/GV;EqB9/GM;IAOI,kCAAA;ErB0/GV;EqBjgHM;IAOI,0BAAA;ErB6/GV;EqBpgHM;IAOI,kCAAA;ErBggHV;EqBvgHM;IAOI,iCAAA;ErBmgHV;EqB1gHM;IAOI,gCAAA;ErBsgHV;EqB7gHM;IAOI,iCAAA;ErBygHV;EqBhhHM;IAOI,gCAAA;ErB4gHV;EqBnhHM;IAOI,iBAAA;ErB+gHV;EqBthHM;IAOI,yBAAA;ErBkhHV;EqBzhHM;IAOI,wBAAA;ErBqhHV;EqB5hHM;IAOI,uBAAA;ErBwhHV;EqB/hHM;IAOI,wBAAA;ErB2hHV;EqBliHM;IAOI,uBAAA;ErB8hHV;EqBriHM;IAOI,qBAAA;ErBiiHV;EqBxiHM;IAOI,6BAAA;ErBoiHV;EqB3iHM;IAOI,4BAAA;ErBuiHV;EqB9iHM;IAOI,2BAAA;ErB0iHV;EqBjjHM;IAOI,4BAAA;ErB6iHV;EqBpjHM;IAOI,2BAAA;ErBgjHV;EqBvjHM;IAOI,6BAAA;IAAA,wBAAA;ErBmjHV;EqB1jHM;IAOI,qCAAA;IAAA,gCAAA;ErBsjHV;EqB7jHM;IAOI,oCAAA;IAAA,+BAAA;ErByjHV;EqBhkHM;IAOI,mCAAA;IAAA,8BAAA;ErB4jHV;EqBnkHM;IAOI,oCAAA;IAAA,+BAAA;ErB+jHV;EqBtkHM;IAOI,mCAAA;IAAA,8BAAA;ErBkkHV;EqBzkHM;IAOI,2BAAA;ErBqkHV;EqB5kHM;IAOI,4BAAA;ErBwkHV;EqB/kHM;IAOI,6BAAA;ErB2kHV;AACF;AatlHI;EQGI;IAOI,sBAAA;ErBglHV;EqBvlHM;IAOI,uBAAA;ErBmlHV;EqB1lHM;IAOI,sBAAA;ErBslHV;EqB7lHM;IAOI,iCAAA;IAAA,8BAAA;ErBylHV;EqBhmHM;IAOI,+BAAA;IAAA,4BAAA;ErB4lHV;EqBnmHM;IAOI,8BAAA;IAAA,2BAAA;ErB+lHV;EqBtmHM;IAOI,oCAAA;IAAA,iCAAA;ErBkmHV;EqBzmHM;IAOI,8BAAA;IAAA,2BAAA;ErBqmHV;EqB5mHM;IAOI,0BAAA;ErBwmHV;EqB/mHM;IAOI,gCAAA;ErB2mHV;EqBlnHM;IAOI,yBAAA;ErB8mHV;EqBrnHM;IAOI,wBAAA;ErBinHV;EqBxnHM;IAOI,+BAAA;ErBonHV;EqB3nHM;IAOI,yBAAA;ErBunHV;EqB9nHM;IAOI,6BAAA;ErB0nHV;EqBjoHM;IAOI,8BAAA;ErB6nHV;EqBpoHM;IAOI,wBAAA;ErBgoHV;EqBvoHM;IAOI,+BAAA;ErBmoHV;EqB1oHM;IAOI,wBAAA;ErBsoHV;EqB7oHM;IAOI,yBAAA;ErByoHV;EqBhpHM;IAOI,8BAAA;ErB4oHV;EqBnpHM;IAOI,iCAAA;ErB+oHV;EqBtpHM;IAOI,sCAAA;ErBkpHV;EqBzpHM;IAOI,yCAAA;ErBqpHV;EqB5pHM;IAOI,uBAAA;ErBwpHV;EqB/pHM;IAOI,uBAAA;ErB2pHV;EqBlqHM;IAOI,yBAAA;ErB8pHV;EqBrqHM;IAOI,yBAAA;ErBiqHV;EqBxqHM;IAOI,0BAAA;ErBoqHV;EqB3qHM;IAOI,4BAAA;ErBuqHV;EqB9qHM;IAOI,kCAAA;ErB0qHV;EqBjrHM;IAOI,sCAAA;ErB6qHV;EqBprHM;IAOI,oCAAA;ErBgrHV;EqBvrHM;IAOI,kCAAA;ErBmrHV;EqB1rHM;IAOI,yCAAA;ErBsrHV;EqB7rHM;IAOI,wCAAA;ErByrHV;EqBhsHM;IAOI,wCAAA;ErB4rHV;EqBnsHM;IAOI,kCAAA;ErB+rHV;EqBtsHM;IAOI,gCAAA;ErBksHV;EqBzsHM;IAOI,8BAAA;ErBqsHV;EqB5sHM;IAOI,gCAAA;ErBwsHV;EqB/sHM;IAOI,+BAAA;ErB2sHV;EqBltHM;IAOI,oCAAA;ErB8sHV;EqBrtHM;IAOI,kCAAA;ErBitHV;EqBxtHM;IAOI,gCAAA;ErBotHV;EqB3tHM;IAOI,uCAAA;ErButHV;EqB9tHM;IAOI,sCAAA;ErB0tHV;EqBjuHM;IAOI,iCAAA;ErB6tHV;EqBpuHM;IAOI,2BAAA;ErBguHV;EqBvuHM;IAOI,iCAAA;ErBmuHV;EqB1uHM;IAOI,+BAAA;ErBsuHV;EqB7uHM;IAOI,6BAAA;ErByuHV;EqBhvHM;IAOI,+BAAA;ErB4uHV;EqBnvHM;IAOI,8BAAA;ErB+uHV;EqBtvHM;IAOI,oBAAA;ErBkvHV;EqBzvHM;IAOI,mBAAA;ErBqvHV;EqB5vHM;IAOI,mBAAA;ErBwvHV;EqB/vHM;IAOI,mBAAA;ErB2vHV;EqBlwHM;IAOI,mBAAA;ErB8vHV;EqBrwHM;IAOI,mBAAA;ErBiwHV;EqBxwHM;IAOI,mBAAA;ErBowHV;EqB3wHM;IAOI,mBAAA;ErBuwHV;EqB9wHM;IAOI,oBAAA;ErB0wHV;EqBjxHM;IAOI,4BAAA;ErB6wHV;EqBpxHM;IAOI,2BAAA;ErBgxHV;EqBvxHM;IAOI,0BAAA;ErBmxHV;EqB1xHM;IAOI,2BAAA;ErBsxHV;EqB7xHM;IAOI,0BAAA;ErByxHV;EqBhyHM;IAOI,uBAAA;ErB4xHV;EqBnyHM;IAOI,0BAAA;IAAA,yBAAA;ErBgyHV;EqBvyHM;IAOI,kCAAA;IAAA,iCAAA;ErBoyHV;EqB3yHM;IAOI,iCAAA;IAAA,gCAAA;ErBwyHV;EqB/yHM;IAOI,gCAAA;IAAA,+BAAA;ErB4yHV;EqBnzHM;IAOI,iCAAA;IAAA,gCAAA;ErBgzHV;EqBvzHM;IAOI,gCAAA;IAAA,+BAAA;ErBozHV;EqB3zHM;IAOI,6BAAA;IAAA,4BAAA;ErBwzHV;EqB/zHM;IAOI,wBAAA;IAAA,2BAAA;ErB4zHV;EqBn0HM;IAOI,gCAAA;IAAA,mCAAA;ErBg0HV;EqBv0HM;IAOI,+BAAA;IAAA,kCAAA;ErBo0HV;EqB30HM;IAOI,8BAAA;IAAA,iCAAA;ErBw0HV;EqB/0HM;IAOI,+BAAA;IAAA,kCAAA;ErB40HV;EqBn1HM;IAOI,8BAAA;IAAA,iCAAA;ErBg1HV;EqBv1HM;IAOI,2BAAA;IAAA,8BAAA;ErBo1HV;EqB31HM;IAOI,wBAAA;ErBu1HV;EqB91HM;IAOI,gCAAA;ErB01HV;EqBj2HM;IAOI,+BAAA;ErB61HV;EqBp2HM;IAOI,8BAAA;ErBg2HV;EqBv2HM;IAOI,+BAAA;ErBm2HV;EqB12HM;IAOI,8BAAA;ErBs2HV;EqB72HM;IAOI,2BAAA;ErBy2HV;EqBh3HM;IAOI,0BAAA;ErB42HV;EqBn3HM;IAOI,kCAAA;ErB+2HV;EqBt3HM;IAOI,iCAAA;ErBk3HV;EqBz3HM;IAOI,gCAAA;ErBq3HV;EqB53HM;IAOI,iCAAA;ErBw3HV;EqB/3HM;IAOI,gCAAA;ErB23HV;EqBl4HM;IAOI,6BAAA;ErB83HV;EqBr4HM;IAOI,2BAAA;ErBi4HV;EqBx4HM;IAOI,mCAAA;ErBo4HV;EqB34HM;IAOI,kCAAA;ErBu4HV;EqB94HM;IAOI,iCAAA;ErB04HV;EqBj5HM;IAOI,kCAAA;ErB64HV;EqBp5HM;IAOI,iCAAA;ErBg5HV;EqBv5HM;IAOI,8BAAA;ErBm5HV;EqB15HM;IAOI,yBAAA;ErBs5HV;EqB75HM;IAOI,iCAAA;ErBy5HV;EqBh6HM;IAOI,gCAAA;ErB45HV;EqBn6HM;IAOI,+BAAA;ErB+5HV;EqBt6HM;IAOI,gCAAA;ErBk6HV;EqBz6HM;IAOI,+BAAA;ErBq6HV;EqB56HM;IAOI,4BAAA;ErBw6HV;EqB/6HM;IAOI,qBAAA;ErB26HV;EqBl7HM;IAOI,6BAAA;ErB86HV;EqBr7HM;IAOI,4BAAA;ErBi7HV;EqBx7HM;IAOI,2BAAA;ErBo7HV;EqB37HM;IAOI,4BAAA;ErBu7HV;EqB97HM;IAOI,2BAAA;ErB07HV;EqBj8HM;IAOI,2BAAA;IAAA,0BAAA;ErB87HV;EqBr8HM;IAOI,mCAAA;IAAA,kCAAA;ErBk8HV;EqBz8HM;IAOI,kCAAA;IAAA,iCAAA;ErBs8HV;EqB78HM;IAOI,iCAAA;IAAA,gCAAA;ErB08HV;EqBj9HM;IAOI,kCAAA;IAAA,iCAAA;ErB88HV;EqBr9HM;IAOI,iCAAA;IAAA,gCAAA;ErBk9HV;EqBz9HM;IAOI,yBAAA;IAAA,4BAAA;ErBs9HV;EqB79HM;IAOI,iCAAA;IAAA,oCAAA;ErB09HV;EqBj+HM;IAOI,gCAAA;IAAA,mCAAA;ErB89HV;EqBr+HM;IAOI,+BAAA;IAAA,kCAAA;ErBk+HV;EqBz+HM;IAOI,gCAAA;IAAA,mCAAA;ErBs+HV;EqB7+HM;IAOI,+BAAA;IAAA,kCAAA;ErB0+HV;EqBj/HM;IAOI,yBAAA;ErB6+HV;EqBp/HM;IAOI,iCAAA;ErBg/HV;EqBv/HM;IAOI,gCAAA;ErBm/HV;EqB1/HM;IAOI,+BAAA;ErBs/HV;EqB7/HM;IAOI,gCAAA;ErBy/HV;EqBhgIM;IAOI,+BAAA;ErB4/HV;EqBngIM;IAOI,2BAAA;ErB+/HV;EqBtgIM;IAOI,mCAAA;ErBkgIV;EqBzgIM;IAOI,kCAAA;ErBqgIV;EqB5gIM;IAOI,iCAAA;ErBwgIV;EqB/gIM;IAOI,kCAAA;ErB2gIV;EqBlhIM;IAOI,iCAAA;ErB8gIV;EqBrhIM;IAOI,4BAAA;ErBihIV;EqBxhIM;IAOI,oCAAA;ErBohIV;EqB3hIM;IAOI,mCAAA;ErBuhIV;EqB9hIM;IAOI,kCAAA;ErB0hIV;EqBjiIM;IAOI,mCAAA;ErB6hIV;EqBpiIM;IAOI,kCAAA;ErBgiIV;EqBviIM;IAOI,0BAAA;ErBmiIV;EqB1iIM;IAOI,kCAAA;ErBsiIV;EqB7iIM;IAOI,iCAAA;ErByiIV;EqBhjIM;IAOI,gCAAA;ErB4iIV;EqBnjIM;IAOI,iCAAA;ErB+iIV;EqBtjIM;IAOI,gCAAA;ErBkjIV;EqBzjIM;IAOI,iBAAA;ErBqjIV;EqB5jIM;IAOI,yBAAA;ErBwjIV;EqB/jIM;IAOI,wBAAA;ErB2jIV;EqBlkIM;IAOI,uBAAA;ErB8jIV;EqBrkIM;IAOI,wBAAA;ErBikIV;EqBxkIM;IAOI,uBAAA;ErBokIV;EqB3kIM;IAOI,qBAAA;ErBukIV;EqB9kIM;IAOI,6BAAA;ErB0kIV;EqBjlIM;IAOI,4BAAA;ErB6kIV;EqBplIM;IAOI,2BAAA;ErBglIV;EqBvlIM;IAOI,4BAAA;ErBmlIV;EqB1lIM;IAOI,2BAAA;ErBslIV;EqB7lIM;IAOI,6BAAA;IAAA,wBAAA;ErBylIV;EqBhmIM;IAOI,qCAAA;IAAA,gCAAA;ErB4lIV;EqBnmIM;IAOI,oCAAA;IAAA,+BAAA;ErB+lIV;EqBtmIM;IAOI,mCAAA;IAAA,8BAAA;ErBkmIV;EqBzmIM;IAOI,oCAAA;IAAA,+BAAA;ErBqmIV;EqB5mIM;IAOI,mCAAA;IAAA,8BAAA;ErBwmIV;EqB/mIM;IAOI,2BAAA;ErB2mIV;EqBlnIM;IAOI,4BAAA;ErB8mIV;EqBrnIM;IAOI,6BAAA;ErBinIV;AACF;Aa5nII;EQGI;IAOI,sBAAA;ErBsnIV;EqB7nIM;IAOI,uBAAA;ErBynIV;EqBhoIM;IAOI,sBAAA;ErB4nIV;EqBnoIM;IAOI,iCAAA;IAAA,8BAAA;ErB+nIV;EqBtoIM;IAOI,+BAAA;IAAA,4BAAA;ErBkoIV;EqBzoIM;IAOI,8BAAA;IAAA,2BAAA;ErBqoIV;EqB5oIM;IAOI,oCAAA;IAAA,iCAAA;ErBwoIV;EqB/oIM;IAOI,8BAAA;IAAA,2BAAA;ErB2oIV;EqBlpIM;IAOI,0BAAA;ErB8oIV;EqBrpIM;IAOI,gCAAA;ErBipIV;EqBxpIM;IAOI,yBAAA;ErBopIV;EqB3pIM;IAOI,wBAAA;ErBupIV;EqB9pIM;IAOI,+BAAA;ErB0pIV;EqBjqIM;IAOI,yBAAA;ErB6pIV;EqBpqIM;IAOI,6BAAA;ErBgqIV;EqBvqIM;IAOI,8BAAA;ErBmqIV;EqB1qIM;IAOI,wBAAA;ErBsqIV;EqB7qIM;IAOI,+BAAA;ErByqIV;EqBhrIM;IAOI,wBAAA;ErB4qIV;EqBnrIM;IAOI,yBAAA;ErB+qIV;EqBtrIM;IAOI,8BAAA;ErBkrIV;EqBzrIM;IAOI,iCAAA;ErBqrIV;EqB5rIM;IAOI,sCAAA;ErBwrIV;EqB/rIM;IAOI,yCAAA;ErB2rIV;EqBlsIM;IAOI,uBAAA;ErB8rIV;EqBrsIM;IAOI,uBAAA;ErBisIV;EqBxsIM;IAOI,yBAAA;ErBosIV;EqB3sIM;IAOI,yBAAA;ErBusIV;EqB9sIM;IAOI,0BAAA;ErB0sIV;EqBjtIM;IAOI,4BAAA;ErB6sIV;EqBptIM;IAOI,kCAAA;ErBgtIV;EqBvtIM;IAOI,sCAAA;ErBmtIV;EqB1tIM;IAOI,oCAAA;ErBstIV;EqB7tIM;IAOI,kCAAA;ErBytIV;EqBhuIM;IAOI,yCAAA;ErB4tIV;EqBnuIM;IAOI,wCAAA;ErB+tIV;EqBtuIM;IAOI,wCAAA;ErBkuIV;EqBzuIM;IAOI,kCAAA;ErBquIV;EqB5uIM;IAOI,gCAAA;ErBwuIV;EqB/uIM;IAOI,8BAAA;ErB2uIV;EqBlvIM;IAOI,gCAAA;ErB8uIV;EqBrvIM;IAOI,+BAAA;ErBivIV;EqBxvIM;IAOI,oCAAA;ErBovIV;EqB3vIM;IAOI,kCAAA;ErBuvIV;EqB9vIM;IAOI,gCAAA;ErB0vIV;EqBjwIM;IAOI,uCAAA;ErB6vIV;EqBpwIM;IAOI,sCAAA;ErBgwIV;EqBvwIM;IAOI,iCAAA;ErBmwIV;EqB1wIM;IAOI,2BAAA;ErBswIV;EqB7wIM;IAOI,iCAAA;ErBywIV;EqBhxIM;IAOI,+BAAA;ErB4wIV;EqBnxIM;IAOI,6BAAA;ErB+wIV;EqBtxIM;IAOI,+BAAA;ErBkxIV;EqBzxIM;IAOI,8BAAA;ErBqxIV;EqB5xIM;IAOI,oBAAA;ErBwxIV;EqB/xIM;IAOI,mBAAA;ErB2xIV;EqBlyIM;IAOI,mBAAA;ErB8xIV;EqBryIM;IAOI,mBAAA;ErBiyIV;EqBxyIM;IAOI,mBAAA;ErBoyIV;EqB3yIM;IAOI,mBAAA;ErBuyIV;EqB9yIM;IAOI,mBAAA;ErB0yIV;EqBjzIM;IAOI,mBAAA;ErB6yIV;EqBpzIM;IAOI,oBAAA;ErBgzIV;EqBvzIM;IAOI,4BAAA;ErBmzIV;EqB1zIM;IAOI,2BAAA;ErBszIV;EqB7zIM;IAOI,0BAAA;ErByzIV;EqBh0IM;IAOI,2BAAA;ErB4zIV;EqBn0IM;IAOI,0BAAA;ErB+zIV;EqBt0IM;IAOI,uBAAA;ErBk0IV;EqBz0IM;IAOI,0BAAA;IAAA,yBAAA;ErBs0IV;EqB70IM;IAOI,kCAAA;IAAA,iCAAA;ErB00IV;EqBj1IM;IAOI,iCAAA;IAAA,gCAAA;ErB80IV;EqBr1IM;IAOI,gCAAA;IAAA,+BAAA;ErBk1IV;EqBz1IM;IAOI,iCAAA;IAAA,gCAAA;ErBs1IV;EqB71IM;IAOI,gCAAA;IAAA,+BAAA;ErB01IV;EqBj2IM;IAOI,6BAAA;IAAA,4BAAA;ErB81IV;EqBr2IM;IAOI,wBAAA;IAAA,2BAAA;ErBk2IV;EqBz2IM;IAOI,gCAAA;IAAA,mCAAA;ErBs2IV;EqB72IM;IAOI,+BAAA;IAAA,kCAAA;ErB02IV;EqBj3IM;IAOI,8BAAA;IAAA,iCAAA;ErB82IV;EqBr3IM;IAOI,+BAAA;IAAA,kCAAA;ErBk3IV;EqBz3IM;IAOI,8BAAA;IAAA,iCAAA;ErBs3IV;EqB73IM;IAOI,2BAAA;IAAA,8BAAA;ErB03IV;EqBj4IM;IAOI,wBAAA;ErB63IV;EqBp4IM;IAOI,gCAAA;ErBg4IV;EqBv4IM;IAOI,+BAAA;ErBm4IV;EqB14IM;IAOI,8BAAA;ErBs4IV;EqB74IM;IAOI,+BAAA;ErBy4IV;EqBh5IM;IAOI,8BAAA;ErB44IV;EqBn5IM;IAOI,2BAAA;ErB+4IV;EqBt5IM;IAOI,0BAAA;ErBk5IV;EqBz5IM;IAOI,kCAAA;ErBq5IV;EqB55IM;IAOI,iCAAA;ErBw5IV;EqB/5IM;IAOI,gCAAA;ErB25IV;EqBl6IM;IAOI,iCAAA;ErB85IV;EqBr6IM;IAOI,gCAAA;ErBi6IV;EqBx6IM;IAOI,6BAAA;ErBo6IV;EqB36IM;IAOI,2BAAA;ErBu6IV;EqB96IM;IAOI,mCAAA;ErB06IV;EqBj7IM;IAOI,kCAAA;ErB66IV;EqBp7IM;IAOI,iCAAA;ErBg7IV;EqBv7IM;IAOI,kCAAA;ErBm7IV;EqB17IM;IAOI,iCAAA;ErBs7IV;EqB77IM;IAOI,8BAAA;ErBy7IV;EqBh8IM;IAOI,yBAAA;ErB47IV;EqBn8IM;IAOI,iCAAA;ErB+7IV;EqBt8IM;IAOI,gCAAA;ErBk8IV;EqBz8IM;IAOI,+BAAA;ErBq8IV;EqB58IM;IAOI,gCAAA;ErBw8IV;EqB/8IM;IAOI,+BAAA;ErB28IV;EqBl9IM;IAOI,4BAAA;ErB88IV;EqBr9IM;IAOI,qBAAA;ErBi9IV;EqBx9IM;IAOI,6BAAA;ErBo9IV;EqB39IM;IAOI,4BAAA;ErBu9IV;EqB99IM;IAOI,2BAAA;ErB09IV;EqBj+IM;IAOI,4BAAA;ErB69IV;EqBp+IM;IAOI,2BAAA;ErBg+IV;EqBv+IM;IAOI,2BAAA;IAAA,0BAAA;ErBo+IV;EqB3+IM;IAOI,mCAAA;IAAA,kCAAA;ErBw+IV;EqB/+IM;IAOI,kCAAA;IAAA,iCAAA;ErB4+IV;EqBn/IM;IAOI,iCAAA;IAAA,gCAAA;ErBg/IV;EqBv/IM;IAOI,kCAAA;IAAA,iCAAA;ErBo/IV;EqB3/IM;IAOI,iCAAA;IAAA,gCAAA;ErBw/IV;EqB//IM;IAOI,yBAAA;IAAA,4BAAA;ErB4/IV;EqBngJM;IAOI,iCAAA;IAAA,oCAAA;ErBggJV;EqBvgJM;IAOI,gCAAA;IAAA,mCAAA;ErBogJV;EqB3gJM;IAOI,+BAAA;IAAA,kCAAA;ErBwgJV;EqB/gJM;IAOI,gCAAA;IAAA,mCAAA;ErB4gJV;EqBnhJM;IAOI,+BAAA;IAAA,kCAAA;ErBghJV;EqBvhJM;IAOI,yBAAA;ErBmhJV;EqB1hJM;IAOI,iCAAA;ErBshJV;EqB7hJM;IAOI,gCAAA;ErByhJV;EqBhiJM;IAOI,+BAAA;ErB4hJV;EqBniJM;IAOI,gCAAA;ErB+hJV;EqBtiJM;IAOI,+BAAA;ErBkiJV;EqBziJM;IAOI,2BAAA;ErBqiJV;EqB5iJM;IAOI,mCAAA;ErBwiJV;EqB/iJM;IAOI,kCAAA;ErB2iJV;EqBljJM;IAOI,iCAAA;ErB8iJV;EqBrjJM;IAOI,kCAAA;ErBijJV;EqBxjJM;IAOI,iCAAA;ErBojJV;EqB3jJM;IAOI,4BAAA;ErBujJV;EqB9jJM;IAOI,oCAAA;ErB0jJV;EqBjkJM;IAOI,mCAAA;ErB6jJV;EqBpkJM;IAOI,kCAAA;ErBgkJV;EqBvkJM;IAOI,mCAAA;ErBmkJV;EqB1kJM;IAOI,kCAAA;ErBskJV;EqB7kJM;IAOI,0BAAA;ErBykJV;EqBhlJM;IAOI,kCAAA;ErB4kJV;EqBnlJM;IAOI,iCAAA;ErB+kJV;EqBtlJM;IAOI,gCAAA;ErBklJV;EqBzlJM;IAOI,iCAAA;ErBqlJV;EqB5lJM;IAOI,gCAAA;ErBwlJV;EqB/lJM;IAOI,iBAAA;ErB2lJV;EqBlmJM;IAOI,yBAAA;ErB8lJV;EqBrmJM;IAOI,wBAAA;ErBimJV;EqBxmJM;IAOI,uBAAA;ErBomJV;EqB3mJM;IAOI,wBAAA;ErBumJV;EqB9mJM;IAOI,uBAAA;ErB0mJV;EqBjnJM;IAOI,qBAAA;ErB6mJV;EqBpnJM;IAOI,6BAAA;ErBgnJV;EqBvnJM;IAOI,4BAAA;ErBmnJV;EqB1nJM;IAOI,2BAAA;ErBsnJV;EqB7nJM;IAOI,4BAAA;ErBynJV;EqBhoJM;IAOI,2BAAA;ErB4nJV;EqBnoJM;IAOI,6BAAA;IAAA,wBAAA;ErB+nJV;EqBtoJM;IAOI,qCAAA;IAAA,gCAAA;ErBkoJV;EqBzoJM;IAOI,oCAAA;IAAA,+BAAA;ErBqoJV;EqB5oJM;IAOI,mCAAA;IAAA,8BAAA;ErBwoJV;EqB/oJM;IAOI,oCAAA;IAAA,+BAAA;ErB2oJV;EqBlpJM;IAOI,mCAAA;IAAA,8BAAA;ErB8oJV;EqBrpJM;IAOI,2BAAA;ErBipJV;EqBxpJM;IAOI,4BAAA;ErBopJV;EqB3pJM;IAOI,6BAAA;ErBupJV;AACF;AalqJI;EQGI;IAOI,sBAAA;ErB4pJV;EqBnqJM;IAOI,uBAAA;ErB+pJV;EqBtqJM;IAOI,sBAAA;ErBkqJV;EqBzqJM;IAOI,iCAAA;IAAA,8BAAA;ErBqqJV;EqB5qJM;IAOI,+BAAA;IAAA,4BAAA;ErBwqJV;EqB/qJM;IAOI,8BAAA;IAAA,2BAAA;ErB2qJV;EqBlrJM;IAOI,oCAAA;IAAA,iCAAA;ErB8qJV;EqBrrJM;IAOI,8BAAA;IAAA,2BAAA;ErBirJV;EqBxrJM;IAOI,0BAAA;ErBorJV;EqB3rJM;IAOI,gCAAA;ErBurJV;EqB9rJM;IAOI,yBAAA;ErB0rJV;EqBjsJM;IAOI,wBAAA;ErB6rJV;EqBpsJM;IAOI,+BAAA;ErBgsJV;EqBvsJM;IAOI,yBAAA;ErBmsJV;EqB1sJM;IAOI,6BAAA;ErBssJV;EqB7sJM;IAOI,8BAAA;ErBysJV;EqBhtJM;IAOI,wBAAA;ErB4sJV;EqBntJM;IAOI,+BAAA;ErB+sJV;EqBttJM;IAOI,wBAAA;ErBktJV;EqBztJM;IAOI,yBAAA;ErBqtJV;EqB5tJM;IAOI,8BAAA;ErBwtJV;EqB/tJM;IAOI,iCAAA;ErB2tJV;EqBluJM;IAOI,sCAAA;ErB8tJV;EqBruJM;IAOI,yCAAA;ErBiuJV;EqBxuJM;IAOI,uBAAA;ErBouJV;EqB3uJM;IAOI,uBAAA;ErBuuJV;EqB9uJM;IAOI,yBAAA;ErB0uJV;EqBjvJM;IAOI,yBAAA;ErB6uJV;EqBpvJM;IAOI,0BAAA;ErBgvJV;EqBvvJM;IAOI,4BAAA;ErBmvJV;EqB1vJM;IAOI,kCAAA;ErBsvJV;EqB7vJM;IAOI,sCAAA;ErByvJV;EqBhwJM;IAOI,oCAAA;ErB4vJV;EqBnwJM;IAOI,kCAAA;ErB+vJV;EqBtwJM;IAOI,yCAAA;ErBkwJV;EqBzwJM;IAOI,wCAAA;ErBqwJV;EqB5wJM;IAOI,wCAAA;ErBwwJV;EqB/wJM;IAOI,kCAAA;ErB2wJV;EqBlxJM;IAOI,gCAAA;ErB8wJV;EqBrxJM;IAOI,8BAAA;ErBixJV;EqBxxJM;IAOI,gCAAA;ErBoxJV;EqB3xJM;IAOI,+BAAA;ErBuxJV;EqB9xJM;IAOI,oCAAA;ErB0xJV;EqBjyJM;IAOI,kCAAA;ErB6xJV;EqBpyJM;IAOI,gCAAA;ErBgyJV;EqBvyJM;IAOI,uCAAA;ErBmyJV;EqB1yJM;IAOI,sCAAA;ErBsyJV;EqB7yJM;IAOI,iCAAA;ErByyJV;EqBhzJM;IAOI,2BAAA;ErB4yJV;EqBnzJM;IAOI,iCAAA;ErB+yJV;EqBtzJM;IAOI,+BAAA;ErBkzJV;EqBzzJM;IAOI,6BAAA;ErBqzJV;EqB5zJM;IAOI,+BAAA;ErBwzJV;EqB/zJM;IAOI,8BAAA;ErB2zJV;EqBl0JM;IAOI,oBAAA;ErB8zJV;EqBr0JM;IAOI,mBAAA;ErBi0JV;EqBx0JM;IAOI,mBAAA;ErBo0JV;EqB30JM;IAOI,mBAAA;ErBu0JV;EqB90JM;IAOI,mBAAA;ErB00JV;EqBj1JM;IAOI,mBAAA;ErB60JV;EqBp1JM;IAOI,mBAAA;ErBg1JV;EqBv1JM;IAOI,mBAAA;ErBm1JV;EqB11JM;IAOI,oBAAA;ErBs1JV;EqB71JM;IAOI,4BAAA;ErBy1JV;EqBh2JM;IAOI,2BAAA;ErB41JV;EqBn2JM;IAOI,0BAAA;ErB+1JV;EqBt2JM;IAOI,2BAAA;ErBk2JV;EqBz2JM;IAOI,0BAAA;ErBq2JV;EqB52JM;IAOI,uBAAA;ErBw2JV;EqB/2JM;IAOI,0BAAA;IAAA,yBAAA;ErB42JV;EqBn3JM;IAOI,kCAAA;IAAA,iCAAA;ErBg3JV;EqBv3JM;IAOI,iCAAA;IAAA,gCAAA;ErBo3JV;EqB33JM;IAOI,gCAAA;IAAA,+BAAA;ErBw3JV;EqB/3JM;IAOI,iCAAA;IAAA,gCAAA;ErB43JV;EqBn4JM;IAOI,gCAAA;IAAA,+BAAA;ErBg4JV;EqBv4JM;IAOI,6BAAA;IAAA,4BAAA;ErBo4JV;EqB34JM;IAOI,wBAAA;IAAA,2BAAA;ErBw4JV;EqB/4JM;IAOI,gCAAA;IAAA,mCAAA;ErB44JV;EqBn5JM;IAOI,+BAAA;IAAA,kCAAA;ErBg5JV;EqBv5JM;IAOI,8BAAA;IAAA,iCAAA;ErBo5JV;EqB35JM;IAOI,+BAAA;IAAA,kCAAA;ErBw5JV;EqB/5JM;IAOI,8BAAA;IAAA,iCAAA;ErB45JV;EqBn6JM;IAOI,2BAAA;IAAA,8BAAA;ErBg6JV;EqBv6JM;IAOI,wBAAA;ErBm6JV;EqB16JM;IAOI,gCAAA;ErBs6JV;EqB76JM;IAOI,+BAAA;ErBy6JV;EqBh7JM;IAOI,8BAAA;ErB46JV;EqBn7JM;IAOI,+BAAA;ErB+6JV;EqBt7JM;IAOI,8BAAA;ErBk7JV;EqBz7JM;IAOI,2BAAA;ErBq7JV;EqB57JM;IAOI,0BAAA;ErBw7JV;EqB/7JM;IAOI,kCAAA;ErB27JV;EqBl8JM;IAOI,iCAAA;ErB87JV;EqBr8JM;IAOI,gCAAA;ErBi8JV;EqBx8JM;IAOI,iCAAA;ErBo8JV;EqB38JM;IAOI,gCAAA;ErBu8JV;EqB98JM;IAOI,6BAAA;ErB08JV;EqBj9JM;IAOI,2BAAA;ErB68JV;EqBp9JM;IAOI,mCAAA;ErBg9JV;EqBv9JM;IAOI,kCAAA;ErBm9JV;EqB19JM;IAOI,iCAAA;ErBs9JV;EqB79JM;IAOI,kCAAA;ErBy9JV;EqBh+JM;IAOI,iCAAA;ErB49JV;EqBn+JM;IAOI,8BAAA;ErB+9JV;EqBt+JM;IAOI,yBAAA;ErBk+JV;EqBz+JM;IAOI,iCAAA;ErBq+JV;EqB5+JM;IAOI,gCAAA;ErBw+JV;EqB/+JM;IAOI,+BAAA;ErB2+JV;EqBl/JM;IAOI,gCAAA;ErB8+JV;EqBr/JM;IAOI,+BAAA;ErBi/JV;EqBx/JM;IAOI,4BAAA;ErBo/JV;EqB3/JM;IAOI,qBAAA;ErBu/JV;EqB9/JM;IAOI,6BAAA;ErB0/JV;EqBjgKM;IAOI,4BAAA;ErB6/JV;EqBpgKM;IAOI,2BAAA;ErBggKV;EqBvgKM;IAOI,4BAAA;ErBmgKV;EqB1gKM;IAOI,2BAAA;ErBsgKV;EqB7gKM;IAOI,2BAAA;IAAA,0BAAA;ErB0gKV;EqBjhKM;IAOI,mCAAA;IAAA,kCAAA;ErB8gKV;EqBrhKM;IAOI,kCAAA;IAAA,iCAAA;ErBkhKV;EqBzhKM;IAOI,iCAAA;IAAA,gCAAA;ErBshKV;EqB7hKM;IAOI,kCAAA;IAAA,iCAAA;ErB0hKV;EqBjiKM;IAOI,iCAAA;IAAA,gCAAA;ErB8hKV;EqBriKM;IAOI,yBAAA;IAAA,4BAAA;ErBkiKV;EqBziKM;IAOI,iCAAA;IAAA,oCAAA;ErBsiKV;EqB7iKM;IAOI,gCAAA;IAAA,mCAAA;ErB0iKV;EqBjjKM;IAOI,+BAAA;IAAA,kCAAA;ErB8iKV;EqBrjKM;IAOI,gCAAA;IAAA,mCAAA;ErBkjKV;EqBzjKM;IAOI,+BAAA;IAAA,kCAAA;ErBsjKV;EqB7jKM;IAOI,yBAAA;ErByjKV;EqBhkKM;IAOI,iCAAA;ErB4jKV;EqBnkKM;IAOI,gCAAA;ErB+jKV;EqBtkKM;IAOI,+BAAA;ErBkkKV;EqBzkKM;IAOI,gCAAA;ErBqkKV;EqB5kKM;IAOI,+BAAA;ErBwkKV;EqB/kKM;IAOI,2BAAA;ErB2kKV;EqBllKM;IAOI,mCAAA;ErB8kKV;EqBrlKM;IAOI,kCAAA;ErBilKV;EqBxlKM;IAOI,iCAAA;ErBolKV;EqB3lKM;IAOI,kCAAA;ErBulKV;EqB9lKM;IAOI,iCAAA;ErB0lKV;EqBjmKM;IAOI,4BAAA;ErB6lKV;EqBpmKM;IAOI,oCAAA;ErBgmKV;EqBvmKM;IAOI,mCAAA;ErBmmKV;EqB1mKM;IAOI,kCAAA;ErBsmKV;EqB7mKM;IAOI,mCAAA;ErBymKV;EqBhnKM;IAOI,kCAAA;ErB4mKV;EqBnnKM;IAOI,0BAAA;ErB+mKV;EqBtnKM;IAOI,kCAAA;ErBknKV;EqBznKM;IAOI,iCAAA;ErBqnKV;EqB5nKM;IAOI,gCAAA;ErBwnKV;EqB/nKM;IAOI,iCAAA;ErB2nKV;EqBloKM;IAOI,gCAAA;ErB8nKV;EqBroKM;IAOI,iBAAA;ErBioKV;EqBxoKM;IAOI,yBAAA;ErBooKV;EqB3oKM;IAOI,wBAAA;ErBuoKV;EqB9oKM;IAOI,uBAAA;ErB0oKV;EqBjpKM;IAOI,wBAAA;ErB6oKV;EqBppKM;IAOI,uBAAA;ErBgpKV;EqBvpKM;IAOI,qBAAA;ErBmpKV;EqB1pKM;IAOI,6BAAA;ErBspKV;EqB7pKM;IAOI,4BAAA;ErBypKV;EqBhqKM;IAOI,2BAAA;ErB4pKV;EqBnqKM;IAOI,4BAAA;ErB+pKV;EqBtqKM;IAOI,2BAAA;ErBkqKV;EqBzqKM;IAOI,6BAAA;IAAA,wBAAA;ErBqqKV;EqB5qKM;IAOI,qCAAA;IAAA,gCAAA;ErBwqKV;EqB/qKM;IAOI,oCAAA;IAAA,+BAAA;ErB2qKV;EqBlrKM;IAOI,mCAAA;IAAA,8BAAA;ErB8qKV;EqBrrKM;IAOI,oCAAA;IAAA,+BAAA;ErBirKV;EqBxrKM;IAOI,mCAAA;IAAA,8BAAA;ErBorKV;EqB3rKM;IAOI,2BAAA;ErBurKV;EqB9rKM;IAOI,4BAAA;ErB0rKV;EqBjsKM;IAOI,6BAAA;ErB6rKV;AACF;AsBjuKA;ED4BQ;IAOI,0BAAA;ErBksKV;EqBzsKM;IAOI,gCAAA;ErBqsKV;EqB5sKM;IAOI,yBAAA;ErBwsKV;EqB/sKM;IAOI,wBAAA;ErB2sKV;EqBltKM;IAOI,+BAAA;ErB8sKV;EqBrtKM;IAOI,yBAAA;ErBitKV;EqBxtKM;IAOI,6BAAA;ErBotKV;EqB3tKM;IAOI,8BAAA;ErButKV;EqB9tKM;IAOI,wBAAA;ErB0tKV;EqBjuKM;IAOI,+BAAA;ErB6tKV;EqBpuKM;IAOI,wBAAA;ErBguKV;AACF", "file": "boosted-utilities.css", "sourcesContent": ["@mixin bsBanner($file) {\n  /*!\n   * Boosted #{$file} v5.3.7 (https://boosted.orange.com/)\n   * Copyright 2014-2025 The Boosted Authors\n   * Copyright 2014-2025 Orange SA\n   * Licensed under MIT (https://github.com/Orange-OpenSource/Orange-Boosted-Bootstrap/blob/main/LICENSE)\n   * This a fork of Bootstrap: Initial license below\n   * Bootstrap #{$file} v5.3.7 (https://getbootstrap.com/)\n   * Copyright 2011-2025 The Bootstrap Authors\n   * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n   */\n}\n", "// Boosted mod\n:root,\n[data-bs-theme] {\n  color: var(--#{$prefix}body-color);\n  background-color: var(--#{$prefix}body-bg);\n}\n\n// Note that some of the following variables in `:root, [data-bs-theme=\"light\"]` could be extracted into `:root` only selector since they are not modified by other color modes!\n// End mod\n\n:root,\n[data-bs-theme=\"light\"] {\n  color-scheme: light; // Boosted mod\n\n  // Note: Custom variable values only support SassScript inside `#{}`.\n\n  // Colors\n  //\n  // Generate palettes for full colors, grays, and theme colors.\n\n  @each $color, $value in $colors {\n    --#{$prefix}#{$color}: #{$value};\n  }\n\n  @each $color, $value in $grays {\n    --#{$prefix}gray-#{$color}: #{$value};\n  }\n\n  @each $color, $value in $theme-colors {\n    --#{$prefix}#{$color}: #{$value};\n  }\n\n  @each $color, $value in $theme-colors-rgb {\n    --#{$prefix}#{$color}-rgb: #{$value};\n  }\n\n  @each $color, $value in $theme-colors-text {\n    --#{$prefix}#{$color}-text-emphasis: #{$value};\n  }\n\n  @each $color, $value in $theme-colors-bg-subtle {\n    --#{$prefix}#{$color}-bg-subtle: #{$value};\n  }\n\n  @each $color, $value in $theme-colors-border-subtle {\n    --#{$prefix}#{$color}-border-subtle: #{$value};\n  }\n\n  --#{$prefix}white-rgb: #{to-rgb($white)};\n  --#{$prefix}black-rgb: #{to-rgb($black)};\n\n  // Boosted mod\n  @each $icon, $svg in $svg-as-custom-props {\n    --#{$prefix}#{$icon}-icon: #{escape-svg($svg)};\n  }\n  // End mod\n\n  // Fonts\n\n  // Note: Use `inspect` for lists so that quoted items keep the quotes.\n  // See https://github.com/sass/sass/issues/2383#issuecomment-336349172\n  --#{$prefix}font-sans-serif: #{inspect($font-family-sans-serif)};\n  --#{$prefix}font-monospace: #{inspect($font-family-monospace)};\n  --#{$prefix}gradient: #{$gradient};\n\n  // Root and body\n  // scss-docs-start root-body-variables\n  @if $font-size-root != null {\n    --#{$prefix}root-font-size: #{$font-size-root};\n  }\n  --#{$prefix}body-font-family: #{inspect($font-family-base)};\n  @include rfs($font-size-base, --#{$prefix}body-font-size);\n  --#{$prefix}body-font-weight: #{$font-weight-base};\n  --#{$prefix}body-line-height: #{$line-height-base};\n  @if $body-text-align != null {\n    --#{$prefix}body-text-align: #{$body-text-align};\n  }\n\n  --#{$prefix}body-color: #{$body-color};\n  --#{$prefix}body-color-rgb: #{to-rgb($body-color)};\n  --#{$prefix}body-bg: #{$body-bg};\n  --#{$prefix}body-bg-rgb: #{to-rgb($body-bg)};\n\n  --#{$prefix}emphasis-color: #{$body-emphasis-color};\n  --#{$prefix}emphasis-color-rgb: #{to-rgb($body-emphasis-color)};\n\n  --#{$prefix}secondary-color: #{$body-secondary-color};\n  --#{$prefix}secondary-color-rgb: #{to-rgb($body-secondary-color)};\n  --#{$prefix}secondary-bg: #{$body-secondary-bg};\n  --#{$prefix}secondary-bg-rgb: #{to-rgb($body-secondary-bg)};\n\n  --#{$prefix}tertiary-color: #{$body-tertiary-color};\n  --#{$prefix}tertiary-color-rgb: #{to-rgb($body-tertiary-color)};\n  --#{$prefix}tertiary-bg: #{$body-tertiary-bg};\n  --#{$prefix}tertiary-bg-rgb: #{to-rgb($body-tertiary-bg)};\n  // scss-docs-end root-body-variables\n\n  --#{$prefix}heading-color: #{$headings-color};\n\n  --#{$prefix}link-color: #{$link-color};\n  --#{$prefix}link-color-rgb: #{to-rgb($link-color)};\n  --#{$prefix}link-decoration: #{$link-decoration};\n\n  --#{$prefix}link-hover-color: #{$link-hover-color};\n  --#{$prefix}link-hover-color-rgb: #{to-rgb($link-hover-color)};\n\n  @if $link-hover-decoration != null {\n    --#{$prefix}link-hover-decoration: #{$link-hover-decoration};\n  }\n\n  --#{$prefix}code-color: #{$code-color};\n  --#{$prefix}highlight-color: #{$mark-color};\n  --#{$prefix}highlight-bg: #{$mark-bg};\n  --#{$prefix}disabled-color: #{$disabled-color}; // Boosted mod\n  --#{$prefix}tertiary-active-bg: #{$tertiary-active-bg}; // Boosted mod\n\n  // scss-docs-start root-border-var\n  --#{$prefix}border-width: #{$border-width};\n  --#{$prefix}border-style: #{$border-style};\n  --#{$prefix}border-color: #{$border-color};\n  --#{$prefix}border-color-subtle: #{$border-color-subtle}; // Boosted mod\n  --#{$prefix}border-color-translucent: #{$border-color-translucent};\n\n  --#{$prefix}border-radius: #{$border-radius};\n  --#{$prefix}border-radius-sm: #{$border-radius-sm};\n  --#{$prefix}border-radius-lg: #{$border-radius-lg};\n  --#{$prefix}border-radius-xl: #{$border-radius-xl};\n  --#{$prefix}border-radius-xxl: #{$border-radius-xxl};\n  --#{$prefix}border-radius-2xl: var(--#{$prefix}border-radius-xxl); // Deprecated in v5.3.0 for consistency\n  --#{$prefix}border-radius-pill: #{$border-radius-pill};\n  // scss-docs-end root-border-var\n\n  --#{$prefix}box-shadow: #{$box-shadow};\n  --#{$prefix}box-shadow-sm: #{$box-shadow-sm};\n  --#{$prefix}box-shadow-lg: #{$box-shadow-lg};\n  --#{$prefix}box-shadow-inset: #{$box-shadow-inset};\n\n  --#{$prefix}focus-visible-inner-color: #{$focus-visible-inner-color}; // Boosted mod\n  --#{$prefix}focus-visible-outer-color: #{$focus-visible-outer-color}; // Boosted mod\n\n  // Focus styles\n  // scss-docs-start root-focus-variables\n  --#{$prefix}focus-ring-width: #{$focus-ring-width};\n  --#{$prefix}focus-ring-opacity: #{$focus-ring-opacity};\n  --#{$prefix}focus-ring-color: #{$focus-ring-color};\n  // scss-docs-end root-focus-variables\n\n  // scss-docs-start root-form-validation-variables\n  --#{$prefix}form-valid-color: #{$form-valid-color};\n  --#{$prefix}form-valid-border-color: #{$form-valid-border-color};\n  --#{$prefix}form-invalid-color: #{$form-invalid-color};\n  --#{$prefix}form-invalid-border-color: #{$form-invalid-border-color};\n  // scss-docs-end root-form-validation-variables\n\n  --#{$prefix}form-check-filter: #{$form-check-filter}; // Boosted mod\n  --#{$prefix}form-check-input-disabled-color: #{$form-check-input-disabled-color}; // Boosted mod\n  --#{$prefix}form-color-disabled-filter: #{$form-color-disabled-filter}; // Boosted mod\n  --#{$prefix}form-select-indicator: #{$form-select-indicator}; // Boosted mod\n  --#{$prefix}form-select-disabled-indicator: #{$form-select-disabled-indicator}; // Boosted mod\n  --#{$prefix}form-switch-square-bg: #{$form-switch-square-bg}; // Boosted mod\n  --#{$prefix}form-switch-unchecked-invalid-border-color: #{$form-switch-unchecked-invalid-border-color}; // Boosted mod\n\n  // Boosted mod\n  // Table-specific styles\n  --#{$prefix}table-active-bg-factor: #{$table-active-bg-factor};\n  --#{$prefix}table-hover-bg-factor: #{$table-hover-bg-factor};\n  --#{$prefix}table-striped-bg-factor: #{$table-striped-bg-factor};\n\n  // Breadcrumb-specific styles\n  --#{$prefix}breadcrumb-divider-filter: #{$breadcrumb-divider-filter};\n  // End mod\n}\n\n@if $enable-dark-mode {\n  @include color-mode(dark, true) {\n    color-scheme: dark;\n\n    // scss-docs-start root-dark-mode-vars\n    --#{$prefix}body-color: #{$body-color-dark};\n    --#{$prefix}body-color-rgb: #{to-rgb($body-color-dark)};\n    --#{$prefix}body-bg: #{$body-bg-dark};\n    --#{$prefix}body-bg-rgb: #{to-rgb($body-bg-dark)};\n\n    --#{$prefix}emphasis-color: #{$body-emphasis-color-dark};\n    --#{$prefix}emphasis-color-rgb: #{to-rgb($body-emphasis-color-dark)};\n\n    --#{$prefix}secondary-color: #{$body-secondary-color-dark};\n    --#{$prefix}secondary-color-rgb: #{to-rgb($body-secondary-color-dark)};\n    --#{$prefix}secondary-bg: #{$body-secondary-bg-dark};\n    --#{$prefix}secondary-bg-rgb: #{to-rgb($body-secondary-bg-dark)};\n\n    --#{$prefix}tertiary-color: #{$body-tertiary-color-dark};\n    --#{$prefix}tertiary-color-rgb: #{to-rgb($body-tertiary-color-dark)};\n    --#{$prefix}tertiary-bg: #{$body-tertiary-bg-dark};\n    --#{$prefix}tertiary-bg-rgb: #{to-rgb($body-tertiary-bg-dark)};\n\n    // Boosted mod\n    @each $color, $value in $theme-colors-dark {\n      --#{$prefix}#{$color}: #{$value};\n    }\n\n    @each $color, $value in $theme-colors-rgb-dark {\n      --#{$prefix}#{$color}-rgb: #{$value};\n    }\n    // End mod\n\n    @each $color, $value in $theme-colors-text-dark {\n      --#{$prefix}#{$color}-text-emphasis: #{$value};\n    }\n\n    @each $color, $value in $theme-colors-bg-subtle-dark {\n      --#{$prefix}#{$color}-bg-subtle: #{$value};\n    }\n\n    @each $color, $value in $theme-colors-border-subtle-dark {\n      --#{$prefix}#{$color}-border-subtle: #{$value};\n    }\n\n    // Boosted mod\n    @each $icon, $svg in $svg-as-custom-props-dark {\n      --#{$prefix}#{$icon}-icon: #{escape-svg($svg)};\n    }\n    // End mod\n\n    --#{$prefix}heading-color: #{$headings-color-dark};\n\n    --#{$prefix}link-color: #{$link-color-dark};\n    --#{$prefix}link-hover-color: #{$link-hover-color-dark};\n    --#{$prefix}link-color-rgb: #{to-rgb($link-color-dark)};\n    --#{$prefix}link-hover-color-rgb: #{to-rgb($link-hover-color-dark)};\n\n    --#{$prefix}code-color: #{$code-color-dark};\n    --#{$prefix}highlight-color: #{$mark-color-dark};\n    --#{$prefix}highlight-bg: #{$mark-bg-dark};\n    --#{$prefix}disabled-color: #{$disabled-color-dark}; // Boosted mod\n    --#{$prefix}tertiary-active-bg: #{$tertiary-active-bg-dark}; // Boosted mod\n\n    --#{$prefix}border-color: #{$border-color-dark};\n    --#{$prefix}border-color-subtle: #{$border-color-subtle-dark}; // Boosted mod\n    --#{$prefix}border-color-translucent: #{$border-color-translucent-dark};\n\n    --#{$prefix}focus-visible-inner-color: #{$focus-visible-inner-color-dark}; // Boosted mod\n    --#{$prefix}focus-visible-outer-color: #{$focus-visible-outer-color-dark}; // Boosted mod\n\n    --#{$prefix}focus-ring-color: #{$focus-ring-color-dark}; // Boosted mod\n\n    --#{$prefix}form-valid-color: #{$form-valid-color-dark};\n    --#{$prefix}form-valid-border-color: #{$form-valid-border-color-dark};\n    --#{$prefix}form-invalid-color: #{$form-invalid-color-dark};\n    --#{$prefix}form-invalid-border-color: #{$form-invalid-border-color-dark};\n    --#{$prefix}form-check-filter: #{$form-check-filter-dark}; // Boosted mod\n    --#{$prefix}form-check-input-disabled-color: #{$form-check-input-disabled-color-dark}; // Boosted mod\n    --#{$prefix}form-select-indicator: #{$form-select-indicator-dark}; // Boosted mod\n    --#{$prefix}form-select-disabled-indicator: #{$form-select-disabled-indicator-dark}; // Boosted mod\n    --#{$prefix}form-color-disabled-filter: #{$form-color-disabled-filter-dark};\n    --#{$prefix}form-switch-square-bg: #{$form-switch-square-bg-dark}; // Boosted mod\n    --#{$prefix}form-switch-unchecked-invalid-border-color: #{$form-switch-unchecked-invalid-border-color-dark}; // Boosted mod\n\n    // Boosted mod\n    // Table-specific styles\n    --#{$prefix}table-active-bg-factor: #{$table-active-bg-factor-dark};\n    --#{$prefix}table-hover-bg-factor: #{$table-hover-bg-factor-dark};\n    --#{$prefix}table-striped-bg-factor: #{$table-striped-bg-factor-dark};\n\n    // Breadcrumb-specific styles\n    --#{$prefix}breadcrumb-divider-filter: #{$breadcrumb-divider-filter-dark};\n    // End mod\n    // scss-docs-end root-dark-mode-vars\n  }\n}\n", "/*!\n * Boosted Utilities v5.3.7 (https://boosted.orange.com/)\n * Copyright 2014-2025 The Boosted Authors\n * Copyright 2014-2025 Orange SA\n * Licensed under MIT (https://github.com/Orange-OpenSource/Orange-Boosted-Bootstrap/blob/main/LICENSE)\n * This a fork of Bootstrap: Initial license below\n * Bootstrap Utilities v5.3.7 (https://getbootstrap.com/)\n * Copyright 2011-2025 The Bootstrap Authors\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n */\n:root,\n[data-bs-theme] {\n  color: var(--bs-body-color);\n  background-color: var(--bs-body-bg);\n}\n\n:root,\n[data-bs-theme=light] {\n  color-scheme: light;\n  --bs-blue: #4170d8;\n  --bs-indigo: #a885d8;\n  --bs-purple: #a885d8;\n  --bs-pink: #ffb4e6;\n  --bs-red: #cd3c14;\n  --bs-orange: #f16e00;\n  --bs-yellow: #fc0;\n  --bs-green: #228722;\n  --bs-teal: #50be87;\n  --bs-cyan: #4bb4e6;\n  --bs-black: #000;\n  --bs-white: #fff;\n  --bs-gray: #999;\n  --bs-gray-dark: #595959;\n  --bs-gray-100: #fafafa;\n  --bs-gray-200: #f6f6f6;\n  --bs-gray-300: #eee;\n  --bs-gray-400: #ddd;\n  --bs-gray-500: #ccc;\n  --bs-gray-600: #999;\n  --bs-gray-700: #666;\n  --bs-gray-800: #595959;\n  --bs-gray-900: #333;\n  --bs-gray-950: #141414;\n  --bs-primary: #f16e00;\n  --bs-secondary: #000;\n  --bs-success: #228722;\n  --bs-info: #4170d8;\n  --bs-warning: #fc0;\n  --bs-danger: #cd3c14;\n  --bs-light: #ccc;\n  --bs-dark: #000;\n  --bs-primary-rgb: 241, 110, 0;\n  --bs-secondary-rgb: 0, 0, 0;\n  --bs-success-rgb: 34, 135, 34;\n  --bs-info-rgb: 65, 112, 216;\n  --bs-warning-rgb: 255, 204, 0;\n  --bs-danger-rgb: 205, 60, 20;\n  --bs-light-rgb: 204, 204, 204;\n  --bs-dark-rgb: 0, 0, 0;\n  --bs-primary-text-emphasis: #f16e00;\n  --bs-secondary-text-emphasis: #000;\n  --bs-success-text-emphasis: #228722;\n  --bs-info-text-emphasis: #4170d8;\n  --bs-warning-text-emphasis: #fc0;\n  --bs-danger-text-emphasis: #cd3c14;\n  --bs-light-text-emphasis: #ccc;\n  --bs-dark-text-emphasis: #000;\n  --bs-primary-bg-subtle: #f16e00;\n  --bs-secondary-bg-subtle: #000;\n  --bs-success-bg-subtle: #228722;\n  --bs-info-bg-subtle: #4170d8;\n  --bs-warning-bg-subtle: #fc0;\n  --bs-danger-bg-subtle: #cd3c14;\n  --bs-light-bg-subtle: #ccc;\n  --bs-dark-bg-subtle: #000;\n  --bs-primary-border-subtle: #f16e00;\n  --bs-secondary-border-subtle: #000;\n  --bs-success-border-subtle: #228722;\n  --bs-info-border-subtle: #4170d8;\n  --bs-warning-border-subtle: #fc0;\n  --bs-danger-border-subtle: #cd3c14;\n  --bs-light-border-subtle: #ccc;\n  --bs-dark-border-subtle: #000;\n  --bs-white-rgb: 255, 255, 255;\n  --bs-black-rgb: 0, 0, 0;\n  --bs-chevron-icon: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 9 14'%3e%3cpath d='M9 2 7 0 0 7l7 7 2-2-5-5 5-5z'/%3e%3c/svg%3e\");\n  --bs-close-icon: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='116 116 767 767' fill='%23000'%3e%3cpath d='M817.493 676.165a49.977 49.977 0 0 1 0 70.664l-70.664 70.664a49.977 49.977 0 0 1-70.664 0L499.5 640.828 322.835 817.493a49.977 49.977 0 0 1-70.664 0l-70.664-70.664a49.977 49.977 0 0 1 0-70.664L358.172 499.5 181.507 322.835a49.977 49.977 0 0 1 0-70.664l70.664-70.664a49.977 49.977 0 0 1 70.664 0L499.5 358.172l176.665-176.665a49.977 49.977 0 0 1 70.664 0l70.664 70.664a49.977 49.977 0 0 1 0 70.664L640.828 499.5Z'/%3e%3c/svg%3e\");\n  --bs-check-icon: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 15 12'%3e%3cpath fill='%23000' d='M13 0 5 8 2 5 0 7l5 5L15 2z'/%3e%3c/svg%3e\");\n  --bs-success-icon: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 125 125'%3e%3cpath fill='%23228722' d='M62.5 0a62.5 62.5 0 1 0 0 125 62.5 62.5 0 0 0 0-125zm28 29.4c3.3 0 6 2.6 6 5.9a5.9 5.9 0 0 1-1.3 3.7L57.7 86a5.8 5.8 0 0 1-9.1 0L29.8 62.5c-.8-1-1.2-2.3-1.2-3.7a5.9 5.9 0 0 1 1.7-4.1l2.3-2.4a5.8 5.8 0 0 1 4.2-1.7 5.8 5.8 0 0 1 3.8 1.4L52 64.7 86.6 31a5.8 5.8 0 0 1 4-1.6z'/%3e%3c/svg%3e\");\n  --bs-error-icon: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 140 125'%3e%3cpath fill='%23cd3c14' d='M70.3 0c-5.8 0-10.8 3.1-13.5 7.8L2.3 101.3l-.2.2A15.6 15.6 0 0 0 15.6 125H125a15.6 15.6 0 0 0 13.5-23.5L83.8 7.8A15.6 15.6 0 0 0 70.3 0zm19.2 50a6.4 6.4 0 0 1 4.4 1.9 6.4 6.4 0 0 1 0 9L79.4 75.6l15 15a6.4 6.4 0 0 1 0 9.2 6.4 6.4 0 0 1-4.5 1.9 6.4 6.4 0 0 1-4.6-2l-15-15-15 15a6.4 6.4 0 0 1-4.6 2 6.4 6.4 0 0 1-4.6-2 6.4 6.4 0 0 1 0-9l15-15L46.8 61a6.4 6.4 0 1 1 9-9.1l14.6 14.5L84.8 52a6.4 6.4 0 0 1 4.7-1.9z'/%3e%3c/svg%3e\");\n  --bs-font-sans-serif: HelvNeueOrange/*rtl:insert:Arabic*/, \"Helvetica Neue\", Helvetica, \"Noto Sans\", \"Liberation Sans\", Arial, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n  --bs-font-monospace: SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace;\n  --bs-gradient: linear-gradient(180deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));\n  --bs-body-font-family: var(--bs-font-sans-serif);\n  --bs-body-font-size: 1rem;\n  --bs-body-font-weight: 400;\n  --bs-body-line-height: 1.125;\n  --bs-body-color: #000;\n  --bs-body-color-rgb: 0, 0, 0;\n  --bs-body-bg: #fff;\n  --bs-body-bg-rgb: 255, 255, 255;\n  --bs-emphasis-color: #000;\n  --bs-emphasis-color-rgb: 0, 0, 0;\n  --bs-secondary-color: #666;\n  --bs-secondary-color-rgb: 102, 102, 102;\n  --bs-secondary-bg: #eee;\n  --bs-secondary-bg-rgb: 238, 238, 238;\n  --bs-tertiary-color: #ccc;\n  --bs-tertiary-color-rgb: 204, 204, 204;\n  --bs-tertiary-bg: #fafafa;\n  --bs-tertiary-bg-rgb: 250, 250, 250;\n  --bs-heading-color: inherit;\n  --bs-link-color: #000;\n  --bs-link-color-rgb: 0, 0, 0;\n  --bs-link-decoration: underline;\n  --bs-link-hover-color: #f16e00;\n  --bs-link-hover-color-rgb: 241, 110, 0;\n  --bs-code-color: #666;\n  --bs-highlight-color: #fff;\n  --bs-highlight-bg: #000;\n  --bs-disabled-color: var(--bs-tertiary-color);\n  --bs-tertiary-active-bg: #ddd;\n  --bs-border-width: 0.125rem;\n  --bs-border-style: solid;\n  --bs-border-color: #000;\n  --bs-border-color-subtle: #ccc;\n  --bs-border-color-translucent: rgba(0, 0, 0, 0.175);\n  --bs-border-radius: 0.375rem;\n  --bs-border-radius-sm: 0.25rem;\n  --bs-border-radius-lg: 0.5rem;\n  --bs-border-radius-xl: 1rem;\n  --bs-border-radius-xxl: 2rem;\n  --bs-border-radius-2xl: var(--bs-border-radius-xxl);\n  --bs-border-radius-pill: 50rem;\n  --bs-box-shadow: ;\n  --bs-box-shadow-sm: ;\n  --bs-box-shadow-lg: ;\n  --bs-box-shadow-inset: ;\n  --bs-focus-visible-inner-color: #fff;\n  --bs-focus-visible-outer-color: #000;\n  --bs-focus-ring-width: 0.25rem;\n  --bs-focus-ring-opacity: 0.25;\n  --bs-focus-ring-color: rgba(241, 110, 0, 0.25);\n  --bs-form-valid-color: var(--bs-success-text-emphasis);\n  --bs-form-valid-border-color: var(--bs-success);\n  --bs-form-invalid-color: var(--bs-danger-text-emphasis);\n  --bs-form-invalid-border-color: var(--bs-danger);\n  --bs-form-check-filter: invert(1);\n  --bs-form-check-input-disabled-color: #333;\n  --bs-form-color-disabled-filter: brightness(0) invert(1) brightness(0.8);\n  --bs-form-select-indicator: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 14 7'%3e%3cpath d='M7 7 0 0h14L7 7z'/%3e%3c/svg%3e\");\n  --bs-form-select-disabled-indicator: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 14 7'%3e%3cpath fill='%23666' d='M7 7 0 0h14L7 7z'/%3e%3c/svg%3e\");\n  --bs-form-switch-square-bg: #000;\n  --bs-form-switch-unchecked-invalid-border-color: #31c3eb;\n  --bs-table-active-bg-factor: 0.135;\n  --bs-table-hover-bg-factor: 0.065;\n  --bs-table-striped-bg-factor: 0.035;\n  --bs-breadcrumb-divider-filter: none;\n}\n\n[data-bs-theme=dark] {\n  color-scheme: dark;\n  --bs-body-color: #fff;\n  --bs-body-color-rgb: 255, 255, 255;\n  --bs-body-bg: #141414;\n  --bs-body-bg-rgb: 20, 20, 20;\n  --bs-emphasis-color: #fff;\n  --bs-emphasis-color-rgb: 255, 255, 255;\n  --bs-secondary-color: #999;\n  --bs-secondary-color-rgb: 153, 153, 153;\n  --bs-secondary-bg: #333;\n  --bs-secondary-bg-rgb: 51, 51, 51;\n  --bs-tertiary-color: #666;\n  --bs-tertiary-color-rgb: 102, 102, 102;\n  --bs-tertiary-bg: #000;\n  --bs-tertiary-bg-rgb: 0, 0, 0;\n  --bs-primary: #ff7900;\n  --bs-secondary: #fff;\n  --bs-success: #6c6;\n  --bs-info: #69f;\n  --bs-warning: #fc0;\n  --bs-danger: #ff4d4d;\n  --bs-light: #ccc;\n  --bs-dark: #000;\n  --bs-primary-rgb: 255, 121, 0;\n  --bs-secondary-rgb: 255, 255, 255;\n  --bs-success-rgb: 102, 204, 102;\n  --bs-info-rgb: 102, 153, 255;\n  --bs-warning-rgb: 255, 204, 0;\n  --bs-danger-rgb: 255, 77, 77;\n  --bs-light-rgb: 204, 204, 204;\n  --bs-dark-rgb: 0, 0, 0;\n  --bs-primary-text-emphasis: #ff7900;\n  --bs-secondary-text-emphasis: #fff;\n  --bs-success-text-emphasis: #6c6;\n  --bs-info-text-emphasis: #69f;\n  --bs-warning-text-emphasis: #fc0;\n  --bs-danger-text-emphasis: #ff4d4d;\n  --bs-light-text-emphasis: #ccc;\n  --bs-dark-text-emphasis: #000;\n  --bs-primary-bg-subtle: #ff7900;\n  --bs-secondary-bg-subtle: #fff;\n  --bs-success-bg-subtle: #6c6;\n  --bs-info-bg-subtle: #69f;\n  --bs-warning-bg-subtle: #fc0;\n  --bs-danger-bg-subtle: #ff4d4d;\n  --bs-light-bg-subtle: #ccc;\n  --bs-dark-bg-subtle: #000;\n  --bs-primary-border-subtle: #ff7900;\n  --bs-secondary-border-subtle: #fff;\n  --bs-success-border-subtle: #6c6;\n  --bs-info-border-subtle: #69f;\n  --bs-warning-border-subtle: #fc0;\n  --bs-danger-border-subtle: #ff4d4d;\n  --bs-light-border-subtle: #ccc;\n  --bs-dark-border-subtle: #000;\n  --bs-success-icon: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 125 125'%3e%3cpath fill='%236c6' d='M62.5 0a62.5 62.5 0 1 0 0 125 62.5 62.5 0 0 0 0-125zm28 29.4c3.3 0 6 2.6 6 5.9a5.9 5.9 0 0 1-1.3 3.7L57.7 86a5.8 5.8 0 0 1-9.1 0L29.8 62.5c-.8-1-1.2-2.3-1.2-3.7a5.9 5.9 0 0 1 1.7-4.1l2.3-2.4a5.8 5.8 0 0 1 4.2-1.7 5.8 5.8 0 0 1 3.8 1.4L52 64.7 86.6 31a5.8 5.8 0 0 1 4-1.6z'/%3e%3c/svg%3e\");\n  --bs-error-icon: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 140 125'%3e%3cpath fill='%23ff4d4d' d='M70.3 0c-5.8 0-10.8 3.1-13.5 7.8L2.3 101.3l-.2.2A15.6 15.6 0 0 0 15.6 125H125a15.6 15.6 0 0 0 13.5-23.5L83.8 7.8A15.6 15.6 0 0 0 70.3 0zm19.2 50a6.4 6.4 0 0 1 4.4 1.9 6.4 6.4 0 0 1 0 9L79.4 75.6l15 15a6.4 6.4 0 0 1 0 9.2 6.4 6.4 0 0 1-4.5 1.9 6.4 6.4 0 0 1-4.6-2l-15-15-15 15a6.4 6.4 0 0 1-4.6 2 6.4 6.4 0 0 1-4.6-2 6.4 6.4 0 0 1 0-9l15-15L46.8 61a6.4 6.4 0 1 1 9-9.1l14.6 14.5L84.8 52a6.4 6.4 0 0 1 4.7-1.9z'/%3e%3c/svg%3e\");\n  --bs-heading-color: inherit;\n  --bs-link-color: #fff;\n  --bs-link-hover-color: #ff7900;\n  --bs-link-color-rgb: 255, 255, 255;\n  --bs-link-hover-color-rgb: 255, 121, 0;\n  --bs-code-color: #999;\n  --bs-highlight-color: #000;\n  --bs-highlight-bg: #fff;\n  --bs-disabled-color: var(--bs-tertiary-color);\n  --bs-tertiary-active-bg: #666;\n  --bs-border-color: #fff;\n  --bs-border-color-subtle: #666;\n  --bs-border-color-translucent: rgba(255, 255, 255, 0.15);\n  --bs-focus-visible-inner-color: #000;\n  --bs-focus-visible-outer-color: #fff;\n  --bs-focus-ring-color: rgba(255, 121, 0, 0.25);\n  --bs-form-valid-color: var(--bs-success-text-emphasis);\n  --bs-form-valid-border-color: var(--bs-success);\n  --bs-form-invalid-color: var(--bs-danger-text-emphasis);\n  --bs-form-invalid-border-color: var(--bs-danger);\n  --bs-form-check-filter: none;\n  --bs-form-check-input-disabled-color: #666;\n  --bs-form-select-indicator: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 14 7'%3e%3cpath fill='%23fff' d='M7 7 0 0h14L7 7z'/%3e%3c/svg%3e\");\n  --bs-form-select-disabled-indicator: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 14 7'%3e%3cpath fill='%23999' d='M7 7 0 0h14L7 7z'/%3e%3c/svg%3e\");\n  --bs-form-color-disabled-filter: brightness(0) invert(1) brightness(0.4);\n  --bs-form-switch-square-bg: #141414;\n  --bs-form-switch-unchecked-invalid-border-color: var(--bs-danger);\n  --bs-table-active-bg-factor: 0.35;\n  --bs-table-hover-bg-factor: 0.135;\n  --bs-table-striped-bg-factor: 1;\n  --bs-breadcrumb-divider-filter: invert(1);\n}\n\n.clearfix::after {\n  display: block;\n  clear: both;\n  content: \"\";\n}\n\n.text-bg-primary {\n  color: #000 !important;\n  background-color: RGBA(var(--bs-primary-rgb), var(--bs-bg-opacity, 1)) !important;\n}\n\n.text-bg-secondary {\n  color: var(--bs-highlight-color) !important;\n  background-color: RGBA(var(--bs-secondary-rgb), var(--bs-bg-opacity, 1)) !important;\n}\n\n.text-bg-success {\n  color: var(--bs-highlight-color) !important;\n  background-color: RGBA(var(--bs-success-rgb), var(--bs-bg-opacity, 1)) !important;\n}\n\n.text-bg-info {\n  color: var(--bs-highlight-color) !important;\n  background-color: RGBA(var(--bs-info-rgb), var(--bs-bg-opacity, 1)) !important;\n}\n\n.text-bg-warning {\n  color: #000 !important;\n  background-color: RGBA(var(--bs-warning-rgb), var(--bs-bg-opacity, 1)) !important;\n}\n\n.text-bg-danger {\n  color: var(--bs-highlight-color) !important;\n  background-color: RGBA(var(--bs-danger-rgb), var(--bs-bg-opacity, 1)) !important;\n}\n\n.text-bg-light {\n  color: #000 !important;\n  background-color: RGBA(var(--bs-light-rgb), var(--bs-bg-opacity, 1)) !important;\n}\n\n.text-bg-dark {\n  color: #fff !important;\n  background-color: RGBA(var(--bs-dark-rgb), var(--bs-bg-opacity, 1)) !important;\n}\n\n.link-primary {\n  color: RGBA(var(--bs-primary-rgb), var(--bs-link-opacity, 1)) !important;\n  text-decoration-color: RGBA(var(--bs-primary-rgb), var(--bs-link-underline-opacity, 1)) !important;\n}\n.link-primary:hover {\n  color: RGBA(244, 139, 51, var(--bs-link-opacity, 1)) !important;\n  text-decoration-color: RGBA(244, 139, 51, var(--bs-link-underline-opacity, 1)) !important;\n}\n\n.link-secondary {\n  color: RGBA(var(--bs-secondary-rgb), var(--bs-link-opacity, 1)) !important;\n  text-decoration-color: RGBA(var(--bs-secondary-rgb), var(--bs-link-underline-opacity, 1)) !important;\n}\n.link-secondary:hover {\n  color: RGBA(0, 0, 0, var(--bs-link-opacity, 1)) !important;\n  text-decoration-color: RGBA(0, 0, 0, var(--bs-link-underline-opacity, 1)) !important;\n}\n\n.link-success {\n  color: RGBA(var(--bs-success-rgb), var(--bs-link-opacity, 1)) !important;\n  text-decoration-color: RGBA(var(--bs-success-rgb), var(--bs-link-underline-opacity, 1)) !important;\n}\n.link-success:hover {\n  color: RGBA(27, 108, 27, var(--bs-link-opacity, 1)) !important;\n  text-decoration-color: RGBA(27, 108, 27, var(--bs-link-underline-opacity, 1)) !important;\n}\n\n.link-info {\n  color: RGBA(var(--bs-info-rgb), var(--bs-link-opacity, 1)) !important;\n  text-decoration-color: RGBA(var(--bs-info-rgb), var(--bs-link-underline-opacity, 1)) !important;\n}\n.link-info:hover {\n  color: RGBA(52, 90, 173, var(--bs-link-opacity, 1)) !important;\n  text-decoration-color: RGBA(52, 90, 173, var(--bs-link-underline-opacity, 1)) !important;\n}\n\n.link-warning {\n  color: RGBA(var(--bs-warning-rgb), var(--bs-link-opacity, 1)) !important;\n  text-decoration-color: RGBA(var(--bs-warning-rgb), var(--bs-link-underline-opacity, 1)) !important;\n}\n.link-warning:hover {\n  color: RGBA(255, 214, 51, var(--bs-link-opacity, 1)) !important;\n  text-decoration-color: RGBA(255, 214, 51, var(--bs-link-underline-opacity, 1)) !important;\n}\n\n.link-danger {\n  color: RGBA(var(--bs-danger-rgb), var(--bs-link-opacity, 1)) !important;\n  text-decoration-color: RGBA(var(--bs-danger-rgb), var(--bs-link-underline-opacity, 1)) !important;\n}\n.link-danger:hover {\n  color: RGBA(164, 48, 16, var(--bs-link-opacity, 1)) !important;\n  text-decoration-color: RGBA(164, 48, 16, var(--bs-link-underline-opacity, 1)) !important;\n}\n\n.link-light {\n  color: RGBA(var(--bs-light-rgb), var(--bs-link-opacity, 1)) !important;\n  text-decoration-color: RGBA(var(--bs-light-rgb), var(--bs-link-underline-opacity, 1)) !important;\n}\n.link-light:hover {\n  color: RGBA(214, 214, 214, var(--bs-link-opacity, 1)) !important;\n  text-decoration-color: RGBA(214, 214, 214, var(--bs-link-underline-opacity, 1)) !important;\n}\n\n.link-dark {\n  color: RGBA(var(--bs-dark-rgb), var(--bs-link-opacity, 1)) !important;\n  text-decoration-color: RGBA(var(--bs-dark-rgb), var(--bs-link-underline-opacity, 1)) !important;\n}\n.link-dark:hover {\n  color: RGBA(0, 0, 0, var(--bs-link-opacity, 1)) !important;\n  text-decoration-color: RGBA(0, 0, 0, var(--bs-link-underline-opacity, 1)) !important;\n}\n\n[data-bs-theme=dark] .link-primary:hover {\n  color: RGBA(255, 148, 51, var(--bs-link-opacity, 1)) !important;\n  text-decoration-color: RGBA(255, 148, 51, var(--bs-link-underline-opacity, 1)) !important;\n}\n[data-bs-theme=dark] .link-secondary:hover {\n  color: RGBA(255, 255, 255, var(--bs-link-opacity, 1)) !important;\n  text-decoration-color: RGBA(255, 255, 255, var(--bs-link-underline-opacity, 1)) !important;\n}\n[data-bs-theme=dark] .link-success:hover {\n  color: RGBA(133, 214, 133, var(--bs-link-opacity, 1)) !important;\n  text-decoration-color: RGBA(133, 214, 133, var(--bs-link-underline-opacity, 1)) !important;\n}\n[data-bs-theme=dark] .link-info:hover {\n  color: RGBA(133, 173, 255, var(--bs-link-opacity, 1)) !important;\n  text-decoration-color: RGBA(133, 173, 255, var(--bs-link-underline-opacity, 1)) !important;\n}\n[data-bs-theme=dark] .link-warning:hover {\n  color: RGBA(255, 214, 51, var(--bs-link-opacity, 1)) !important;\n  text-decoration-color: RGBA(255, 214, 51, var(--bs-link-underline-opacity, 1)) !important;\n}\n[data-bs-theme=dark] .link-danger:hover {\n  color: RGBA(255, 113, 113, var(--bs-link-opacity, 1)) !important;\n  text-decoration-color: RGBA(255, 113, 113, var(--bs-link-underline-opacity, 1)) !important;\n}\n[data-bs-theme=dark] .link-light:hover {\n  color: RGBA(214, 214, 214, var(--bs-link-opacity, 1)) !important;\n  text-decoration-color: RGBA(214, 214, 214, var(--bs-link-underline-opacity, 1)) !important;\n}\n[data-bs-theme=dark] .link-dark:hover {\n  color: RGBA(0, 0, 0, var(--bs-link-opacity, 1)) !important;\n  text-decoration-color: RGBA(0, 0, 0, var(--bs-link-underline-opacity, 1)) !important;\n}\n\n.link-body-emphasis {\n  color: RGBA(var(--bs-emphasis-color-rgb), var(--bs-link-opacity, 1)) !important;\n  text-decoration-color: RGBA(var(--bs-emphasis-color-rgb), var(--bs-link-underline-opacity, 1)) !important;\n}\n.link-body-emphasis:hover, .link-body-emphasis:focus {\n  color: RGBA(var(--bs-emphasis-color-rgb), var(--bs-link-opacity, 0.75)) !important;\n  text-decoration-color: RGBA(var(--bs-emphasis-color-rgb), var(--bs-link-underline-opacity, 0.75)) !important;\n}\n\n.focus-ring:focus {\n  outline: 0;\n  box-shadow: var(--bs-focus-ring-x, 0) var(--bs-focus-ring-y, 0) var(--bs-focus-ring-blur, 0) var(--bs-focus-ring-width) var(--bs-focus-ring-color);\n}\n\n.link-chevron {\n  font-weight: 700;\n  text-decoration: none;\n  background-color: transparent;\n}\n.link-chevron::after {\n  display: inline-block;\n  width: calc(0.5rem - 1px);\n  height: 0.625rem;\n  margin-left: 0.3125rem;\n  vertical-align: middle;\n  content: \"\";\n  background-color: currentcolor;\n  mask: var(--bs-chevron-icon) no-repeat;\n  transform: rotate(0.5turn) translateY(1px);\n}\n.link-chevron:hover {\n  text-decoration: underline;\n}\n\n.icon-link {\n  display: inline-flex;\n  gap: 0.3125rem;\n  align-items: center;\n  text-decoration-color: rgba(var(--bs-link-color-rgb), var(--bs-link-opacity, 0.5));\n  text-underline-offset: 0.25em;\n  backface-visibility: hidden;\n}\n.icon-link > .bi {\n  flex-shrink: 0;\n  width: 1em;\n  height: 1em;\n  fill: currentcolor;\n  transition: 0.2s ease-in-out transform;\n}\n@media (prefers-reduced-motion: reduce) {\n  .icon-link > .bi {\n    transition: none;\n  }\n}\n\n.icon-link-hover:hover > .bi, .icon-link-hover:focus-visible > .bi {\n  transform: var(--bs-icon-link-transform, translate3d(0.25em, 0, 0));\n}\n\n.ratio {\n  position: relative;\n  width: 100%;\n}\n.ratio::before {\n  display: block;\n  padding-top: var(--bs-aspect-ratio);\n  content: \"\";\n}\n.ratio > * {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n}\n\n.ratio-1x1 {\n  --bs-aspect-ratio: 100%;\n}\n\n.ratio-4x3 {\n  --bs-aspect-ratio: 75%;\n}\n\n.ratio-16x9 {\n  --bs-aspect-ratio: 56.25%;\n}\n\n.ratio-21x9 {\n  --bs-aspect-ratio: 42.8571428571%;\n}\n\n.ratio-9x16 {\n  --bs-aspect-ratio: 177.7777777778%;\n}\n\n.fixed-top {\n  position: fixed;\n  top: 0;\n  right: 0;\n  left: 0;\n  z-index: 1030;\n}\n\n.fixed-bottom {\n  position: fixed;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: 1030;\n}\n\n.sticky-top {\n  position: sticky;\n  top: 0;\n  z-index: 1020;\n}\n\n.sticky-bottom {\n  position: sticky;\n  bottom: 0;\n  z-index: 1020;\n}\n\n@media (min-width: 480px) {\n  .sticky-sm-top {\n    position: sticky;\n    top: 0;\n    z-index: 1020;\n  }\n  .sticky-sm-bottom {\n    position: sticky;\n    bottom: 0;\n    z-index: 1020;\n  }\n}\n@media (min-width: 768px) {\n  .sticky-md-top {\n    position: sticky;\n    top: 0;\n    z-index: 1020;\n  }\n  .sticky-md-bottom {\n    position: sticky;\n    bottom: 0;\n    z-index: 1020;\n  }\n}\n@media (min-width: 1024px) {\n  .sticky-lg-top {\n    position: sticky;\n    top: 0;\n    z-index: 1020;\n  }\n  .sticky-lg-bottom {\n    position: sticky;\n    bottom: 0;\n    z-index: 1020;\n  }\n}\n@media (min-width: 1280px) {\n  .sticky-xl-top {\n    position: sticky;\n    top: 0;\n    z-index: 1020;\n  }\n  .sticky-xl-bottom {\n    position: sticky;\n    bottom: 0;\n    z-index: 1020;\n  }\n}\n@media (min-width: 1440px) {\n  .sticky-xxl-top {\n    position: sticky;\n    top: 0;\n    z-index: 1020;\n  }\n  .sticky-xxl-bottom {\n    position: sticky;\n    bottom: 0;\n    z-index: 1020;\n  }\n}\n.hstack {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  align-self: stretch;\n}\n\n.vstack {\n  display: flex;\n  flex: 1 1 auto;\n  flex-direction: column;\n  align-self: stretch;\n}\n\n.visually-hidden,\n.visually-hidden-focusable:not(:focus):not(:focus-within) {\n  width: 1px !important;\n  height: 1px !important;\n  padding: 0 !important;\n  margin: -1px !important;\n  overflow: hidden !important;\n  clip: rect(0, 0, 0, 0) !important;\n  white-space: nowrap !important;\n  border: 0 !important;\n}\n.visually-hidden:not(caption),\n.visually-hidden-focusable:not(:focus):not(:focus-within):not(caption) {\n  position: absolute !important;\n}\n.visually-hidden *,\n.visually-hidden-focusable:not(:focus):not(:focus-within) * {\n  overflow: hidden !important;\n}\n\n.stretched-link::after {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: 1;\n  content: \"\";\n}\n\n.text-truncate {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.vr {\n  display: inline-block;\n  align-self: stretch;\n  width: 2px;\n  min-height: 1em;\n  background-color: currentcolor;\n}\n\n.align-baseline {\n  vertical-align: baseline !important;\n}\n\n.align-top {\n  vertical-align: top !important;\n}\n\n.align-middle {\n  vertical-align: middle !important;\n}\n\n.align-bottom {\n  vertical-align: bottom !important;\n}\n\n.align-text-bottom {\n  vertical-align: text-bottom !important;\n}\n\n.align-text-top {\n  vertical-align: text-top !important;\n}\n\n.float-start {\n  float: left !important;\n}\n\n.float-end {\n  float: right !important;\n}\n\n.float-none {\n  float: none !important;\n}\n\n.object-fit-contain {\n  object-fit: contain !important;\n}\n\n.object-fit-cover {\n  object-fit: cover !important;\n}\n\n.object-fit-fill {\n  object-fit: fill !important;\n}\n\n.object-fit-scale {\n  object-fit: scale-down !important;\n}\n\n.object-fit-none {\n  object-fit: none !important;\n}\n\n.opacity-0 {\n  opacity: 0 !important;\n}\n\n.opacity-25 {\n  opacity: 0.25 !important;\n}\n\n.opacity-50 {\n  opacity: 0.5 !important;\n}\n\n.opacity-75 {\n  opacity: 0.75 !important;\n}\n\n.opacity-100 {\n  opacity: 1 !important;\n}\n\n.overflow-auto {\n  overflow: auto !important;\n}\n\n.overflow-hidden {\n  overflow: hidden !important;\n}\n\n.overflow-visible {\n  overflow: visible !important;\n}\n\n.overflow-scroll {\n  overflow: scroll !important;\n}\n\n.overflow-x-auto {\n  overflow-x: auto !important;\n}\n\n.overflow-x-hidden {\n  overflow-x: hidden !important;\n}\n\n.overflow-x-visible {\n  overflow-x: visible !important;\n}\n\n.overflow-x-scroll {\n  overflow-x: scroll !important;\n}\n\n.overflow-y-auto {\n  overflow-y: auto !important;\n}\n\n.overflow-y-hidden {\n  overflow-y: hidden !important;\n}\n\n.overflow-y-visible {\n  overflow-y: visible !important;\n}\n\n.overflow-y-scroll {\n  overflow-y: scroll !important;\n}\n\n.d-inline {\n  display: inline !important;\n}\n\n.d-inline-block {\n  display: inline-block !important;\n}\n\n.d-block {\n  display: block !important;\n}\n\n.d-grid {\n  display: grid !important;\n}\n\n.d-inline-grid {\n  display: inline-grid !important;\n}\n\n.d-table {\n  display: table !important;\n}\n\n.d-table-row {\n  display: table-row !important;\n}\n\n.d-table-cell {\n  display: table-cell !important;\n}\n\n.d-flex {\n  display: flex !important;\n}\n\n.d-inline-flex {\n  display: inline-flex !important;\n}\n\n.d-none {\n  display: none !important;\n}\n\n.shadow {\n  box-shadow: var(--bs-box-shadow) !important;\n}\n\n.shadow-sm {\n  box-shadow: var(--bs-box-shadow-sm) !important;\n}\n\n.shadow-lg {\n  box-shadow: var(--bs-box-shadow-lg) !important;\n}\n\n.shadow-none {\n  box-shadow: none !important;\n}\n\n.focus-ring-primary {\n  --bs-focus-ring-color: rgba(var(--bs-primary-rgb), var(--bs-focus-ring-opacity));\n}\n\n.focus-ring-secondary {\n  --bs-focus-ring-color: rgba(var(--bs-secondary-rgb), var(--bs-focus-ring-opacity));\n}\n\n.focus-ring-success {\n  --bs-focus-ring-color: rgba(var(--bs-success-rgb), var(--bs-focus-ring-opacity));\n}\n\n.focus-ring-info {\n  --bs-focus-ring-color: rgba(var(--bs-info-rgb), var(--bs-focus-ring-opacity));\n}\n\n.focus-ring-warning {\n  --bs-focus-ring-color: rgba(var(--bs-warning-rgb), var(--bs-focus-ring-opacity));\n}\n\n.focus-ring-danger {\n  --bs-focus-ring-color: rgba(var(--bs-danger-rgb), var(--bs-focus-ring-opacity));\n}\n\n.focus-ring-light {\n  --bs-focus-ring-color: rgba(var(--bs-light-rgb), var(--bs-focus-ring-opacity));\n}\n\n.focus-ring-dark {\n  --bs-focus-ring-color: rgba(var(--bs-dark-rgb), var(--bs-focus-ring-opacity));\n}\n\n.position-static {\n  position: static !important;\n}\n\n.position-relative {\n  position: relative !important;\n}\n\n.position-absolute {\n  position: absolute !important;\n}\n\n.position-fixed {\n  position: fixed !important;\n}\n\n.position-sticky {\n  position: sticky !important;\n}\n\n.top-0 {\n  top: 0 !important;\n}\n\n.top-50 {\n  top: 50% !important;\n}\n\n.top-100 {\n  top: 100% !important;\n}\n\n.bottom-0 {\n  bottom: 0 !important;\n}\n\n.bottom-50 {\n  bottom: 50% !important;\n}\n\n.bottom-100 {\n  bottom: 100% !important;\n}\n\n.start-0 {\n  left: 0 !important;\n}\n\n.start-50 {\n  left: 50% !important;\n}\n\n.start-100 {\n  left: 100% !important;\n}\n\n.end-0 {\n  right: 0 !important;\n}\n\n.end-50 {\n  right: 50% !important;\n}\n\n.end-100 {\n  right: 100% !important;\n}\n\n.translate-middle {\n  transform: translate(-50%, -50%) !important;\n}\n\n.translate-middle-x {\n  transform: translateX(-50%) !important;\n}\n\n.translate-middle-y {\n  transform: translateY(-50%) !important;\n}\n\n.border {\n  border: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;\n}\n\n.border-0 {\n  border: 0 !important;\n}\n\n.border-top {\n  border-top: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;\n}\n\n.border-top-0 {\n  border-top: 0 !important;\n}\n\n.border-end {\n  border-right: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;\n}\n\n.border-end-0 {\n  border-right: 0 !important;\n}\n\n.border-bottom {\n  border-bottom: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;\n}\n\n.border-bottom-0 {\n  border-bottom: 0 !important;\n}\n\n.border-start {\n  border-left: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;\n}\n\n.border-start-0 {\n  border-left: 0 !important;\n}\n\n.border-primary {\n  --bs-border-opacity: 1;\n  border-color: rgba(var(--bs-primary-rgb), var(--bs-border-opacity)) !important;\n}\n\n.border-secondary {\n  --bs-border-opacity: 1;\n  border-color: rgba(var(--bs-secondary-rgb), var(--bs-border-opacity)) !important;\n}\n\n.border-success {\n  --bs-border-opacity: 1;\n  border-color: rgba(var(--bs-success-rgb), var(--bs-border-opacity)) !important;\n}\n\n.border-info {\n  --bs-border-opacity: 1;\n  border-color: rgba(var(--bs-info-rgb), var(--bs-border-opacity)) !important;\n}\n\n.border-warning {\n  --bs-border-opacity: 1;\n  border-color: rgba(var(--bs-warning-rgb), var(--bs-border-opacity)) !important;\n}\n\n.border-danger {\n  --bs-border-opacity: 1;\n  border-color: rgba(var(--bs-danger-rgb), var(--bs-border-opacity)) !important;\n}\n\n.border-light {\n  --bs-border-opacity: 1;\n  border-color: rgba(204, 204, 204, var(--bs-border-opacity)) !important;\n}\n\n.border-dark {\n  --bs-border-opacity: 1;\n  border-color: rgba(102, 102, 102, var(--bs-border-opacity)) !important;\n}\n\n.border-black {\n  --bs-border-opacity: 1;\n  border-color: rgba(var(--bs-black-rgb), var(--bs-border-opacity)) !important;\n}\n\n.border-white {\n  --bs-border-opacity: 1;\n  border-color: rgba(var(--bs-white-rgb), var(--bs-border-opacity)) !important;\n}\n\n.border-primary-subtle {\n  border-color: var(--bs-primary-border-subtle) !important;\n}\n\n.border-secondary-subtle {\n  border-color: var(--bs-secondary-border-subtle) !important;\n}\n\n.border-success-subtle {\n  border-color: var(--bs-success-border-subtle) !important;\n}\n\n.border-info-subtle {\n  border-color: var(--bs-info-border-subtle) !important;\n}\n\n.border-warning-subtle {\n  border-color: var(--bs-warning-border-subtle) !important;\n}\n\n.border-danger-subtle {\n  border-color: var(--bs-danger-border-subtle) !important;\n}\n\n.border-light-subtle {\n  border-color: var(--bs-light-border-subtle) !important;\n}\n\n.border-dark-subtle {\n  border-color: var(--bs-dark-border-subtle) !important;\n}\n\n.border-1 {\n  border-width: 0.0625rem !important;\n}\n\n.border-2 {\n  border-width: 0.125rem !important;\n}\n\n.border-3 {\n  border-width: 0.1875rem !important;\n}\n\n.border-4 {\n  border-width: 0.25rem !important;\n}\n\n.border-5 {\n  border-width: 0.3125rem !important;\n}\n\n.border-opacity-10 {\n  --bs-border-opacity: 0.1;\n}\n\n.border-opacity-25 {\n  --bs-border-opacity: 0.25;\n}\n\n.border-opacity-50 {\n  --bs-border-opacity: 0.5;\n}\n\n.border-opacity-75 {\n  --bs-border-opacity: 0.75;\n}\n\n.border-opacity-100 {\n  --bs-border-opacity: 1;\n}\n\n.w-25 {\n  width: 25% !important;\n}\n\n.w-50 {\n  width: 50% !important;\n}\n\n.w-75 {\n  width: 75% !important;\n}\n\n.w-100 {\n  width: 100% !important;\n}\n\n.w-auto {\n  width: auto !important;\n}\n\n.mw-100 {\n  max-width: 100% !important;\n}\n\n.vw-100 {\n  width: 100vw !important;\n}\n\n.min-vw-100 {\n  min-width: 100vw !important;\n}\n\n.h-25 {\n  height: 25% !important;\n}\n\n.h-50 {\n  height: 50% !important;\n}\n\n.h-75 {\n  height: 75% !important;\n}\n\n.h-100 {\n  height: 100% !important;\n}\n\n.h-auto {\n  height: auto !important;\n}\n\n.mh-100 {\n  max-height: 100% !important;\n}\n\n.vh-100 {\n  height: 100vh !important;\n}\n\n.min-vh-100 {\n  min-height: 100vh !important;\n}\n\n.flex-fill {\n  flex: 1 1 auto !important;\n}\n\n.flex-row {\n  flex-direction: row !important;\n}\n\n.flex-column {\n  flex-direction: column !important;\n}\n\n.flex-row-reverse {\n  flex-direction: row-reverse !important;\n}\n\n.flex-column-reverse {\n  flex-direction: column-reverse !important;\n}\n\n.flex-grow-0 {\n  flex-grow: 0 !important;\n}\n\n.flex-grow-1 {\n  flex-grow: 1 !important;\n}\n\n.flex-shrink-0 {\n  flex-shrink: 0 !important;\n}\n\n.flex-shrink-1 {\n  flex-shrink: 1 !important;\n}\n\n.flex-wrap {\n  flex-wrap: wrap !important;\n}\n\n.flex-nowrap {\n  flex-wrap: nowrap !important;\n}\n\n.flex-wrap-reverse {\n  flex-wrap: wrap-reverse !important;\n}\n\n.justify-content-start {\n  justify-content: flex-start !important;\n}\n\n.justify-content-end {\n  justify-content: flex-end !important;\n}\n\n.justify-content-center {\n  justify-content: center !important;\n}\n\n.justify-content-between {\n  justify-content: space-between !important;\n}\n\n.justify-content-around {\n  justify-content: space-around !important;\n}\n\n.justify-content-evenly {\n  justify-content: space-evenly !important;\n}\n\n.align-items-start {\n  align-items: flex-start !important;\n}\n\n.align-items-end {\n  align-items: flex-end !important;\n}\n\n.align-items-center {\n  align-items: center !important;\n}\n\n.align-items-baseline {\n  align-items: baseline !important;\n}\n\n.align-items-stretch {\n  align-items: stretch !important;\n}\n\n.align-content-start {\n  align-content: flex-start !important;\n}\n\n.align-content-end {\n  align-content: flex-end !important;\n}\n\n.align-content-center {\n  align-content: center !important;\n}\n\n.align-content-between {\n  align-content: space-between !important;\n}\n\n.align-content-around {\n  align-content: space-around !important;\n}\n\n.align-content-stretch {\n  align-content: stretch !important;\n}\n\n.align-self-auto {\n  align-self: auto !important;\n}\n\n.align-self-start {\n  align-self: flex-start !important;\n}\n\n.align-self-end {\n  align-self: flex-end !important;\n}\n\n.align-self-center {\n  align-self: center !important;\n}\n\n.align-self-baseline {\n  align-self: baseline !important;\n}\n\n.align-self-stretch {\n  align-self: stretch !important;\n}\n\n.order-first {\n  order: -1 !important;\n}\n\n.order-0 {\n  order: 0 !important;\n}\n\n.order-1 {\n  order: 1 !important;\n}\n\n.order-2 {\n  order: 2 !important;\n}\n\n.order-3 {\n  order: 3 !important;\n}\n\n.order-4 {\n  order: 4 !important;\n}\n\n.order-5 {\n  order: 5 !important;\n}\n\n.order-last {\n  order: 6 !important;\n}\n\n.m-0 {\n  margin: 0 !important;\n}\n\n.m-1 {\n  margin: 0.3125rem !important;\n}\n\n.m-2 {\n  margin: 0.625rem !important;\n}\n\n.m-3 {\n  margin: 1.25rem !important;\n}\n\n.m-4 {\n  margin: 1.875rem !important;\n}\n\n.m-5 {\n  margin: 3.75rem !important;\n}\n\n.m-auto {\n  margin: auto !important;\n}\n\n.mx-0 {\n  margin-right: 0 !important;\n  margin-left: 0 !important;\n}\n\n.mx-1 {\n  margin-right: 0.3125rem !important;\n  margin-left: 0.3125rem !important;\n}\n\n.mx-2 {\n  margin-right: 0.625rem !important;\n  margin-left: 0.625rem !important;\n}\n\n.mx-3 {\n  margin-right: 1.25rem !important;\n  margin-left: 1.25rem !important;\n}\n\n.mx-4 {\n  margin-right: 1.875rem !important;\n  margin-left: 1.875rem !important;\n}\n\n.mx-5 {\n  margin-right: 3.75rem !important;\n  margin-left: 3.75rem !important;\n}\n\n.mx-auto {\n  margin-right: auto !important;\n  margin-left: auto !important;\n}\n\n.my-0 {\n  margin-top: 0 !important;\n  margin-bottom: 0 !important;\n}\n\n.my-1 {\n  margin-top: 0.3125rem !important;\n  margin-bottom: 0.3125rem !important;\n}\n\n.my-2 {\n  margin-top: 0.625rem !important;\n  margin-bottom: 0.625rem !important;\n}\n\n.my-3 {\n  margin-top: 1.25rem !important;\n  margin-bottom: 1.25rem !important;\n}\n\n.my-4 {\n  margin-top: 1.875rem !important;\n  margin-bottom: 1.875rem !important;\n}\n\n.my-5 {\n  margin-top: 3.75rem !important;\n  margin-bottom: 3.75rem !important;\n}\n\n.my-auto {\n  margin-top: auto !important;\n  margin-bottom: auto !important;\n}\n\n.mt-0 {\n  margin-top: 0 !important;\n}\n\n.mt-1 {\n  margin-top: 0.3125rem !important;\n}\n\n.mt-2 {\n  margin-top: 0.625rem !important;\n}\n\n.mt-3 {\n  margin-top: 1.25rem !important;\n}\n\n.mt-4 {\n  margin-top: 1.875rem !important;\n}\n\n.mt-5 {\n  margin-top: 3.75rem !important;\n}\n\n.mt-auto {\n  margin-top: auto !important;\n}\n\n.me-0 {\n  margin-right: 0 !important;\n}\n\n.me-1 {\n  margin-right: 0.3125rem !important;\n}\n\n.me-2 {\n  margin-right: 0.625rem !important;\n}\n\n.me-3 {\n  margin-right: 1.25rem !important;\n}\n\n.me-4 {\n  margin-right: 1.875rem !important;\n}\n\n.me-5 {\n  margin-right: 3.75rem !important;\n}\n\n.me-auto {\n  margin-right: auto !important;\n}\n\n.mb-0 {\n  margin-bottom: 0 !important;\n}\n\n.mb-1 {\n  margin-bottom: 0.3125rem !important;\n}\n\n.mb-2 {\n  margin-bottom: 0.625rem !important;\n}\n\n.mb-3 {\n  margin-bottom: 1.25rem !important;\n}\n\n.mb-4 {\n  margin-bottom: 1.875rem !important;\n}\n\n.mb-5 {\n  margin-bottom: 3.75rem !important;\n}\n\n.mb-auto {\n  margin-bottom: auto !important;\n}\n\n.ms-0 {\n  margin-left: 0 !important;\n}\n\n.ms-1 {\n  margin-left: 0.3125rem !important;\n}\n\n.ms-2 {\n  margin-left: 0.625rem !important;\n}\n\n.ms-3 {\n  margin-left: 1.25rem !important;\n}\n\n.ms-4 {\n  margin-left: 1.875rem !important;\n}\n\n.ms-5 {\n  margin-left: 3.75rem !important;\n}\n\n.ms-auto {\n  margin-left: auto !important;\n}\n\n.p-0 {\n  padding: 0 !important;\n}\n\n.p-1 {\n  padding: 0.3125rem !important;\n}\n\n.p-2 {\n  padding: 0.625rem !important;\n}\n\n.p-3 {\n  padding: 1.25rem !important;\n}\n\n.p-4 {\n  padding: 1.875rem !important;\n}\n\n.p-5 {\n  padding: 3.75rem !important;\n}\n\n.px-0 {\n  padding-right: 0 !important;\n  padding-left: 0 !important;\n}\n\n.px-1 {\n  padding-right: 0.3125rem !important;\n  padding-left: 0.3125rem !important;\n}\n\n.px-2 {\n  padding-right: 0.625rem !important;\n  padding-left: 0.625rem !important;\n}\n\n.px-3 {\n  padding-right: 1.25rem !important;\n  padding-left: 1.25rem !important;\n}\n\n.px-4 {\n  padding-right: 1.875rem !important;\n  padding-left: 1.875rem !important;\n}\n\n.px-5 {\n  padding-right: 3.75rem !important;\n  padding-left: 3.75rem !important;\n}\n\n.py-0 {\n  padding-top: 0 !important;\n  padding-bottom: 0 !important;\n}\n\n.py-1 {\n  padding-top: 0.3125rem !important;\n  padding-bottom: 0.3125rem !important;\n}\n\n.py-2 {\n  padding-top: 0.625rem !important;\n  padding-bottom: 0.625rem !important;\n}\n\n.py-3 {\n  padding-top: 1.25rem !important;\n  padding-bottom: 1.25rem !important;\n}\n\n.py-4 {\n  padding-top: 1.875rem !important;\n  padding-bottom: 1.875rem !important;\n}\n\n.py-5 {\n  padding-top: 3.75rem !important;\n  padding-bottom: 3.75rem !important;\n}\n\n.pt-0 {\n  padding-top: 0 !important;\n}\n\n.pt-1 {\n  padding-top: 0.3125rem !important;\n}\n\n.pt-2 {\n  padding-top: 0.625rem !important;\n}\n\n.pt-3 {\n  padding-top: 1.25rem !important;\n}\n\n.pt-4 {\n  padding-top: 1.875rem !important;\n}\n\n.pt-5 {\n  padding-top: 3.75rem !important;\n}\n\n.pe-0 {\n  padding-right: 0 !important;\n}\n\n.pe-1 {\n  padding-right: 0.3125rem !important;\n}\n\n.pe-2 {\n  padding-right: 0.625rem !important;\n}\n\n.pe-3 {\n  padding-right: 1.25rem !important;\n}\n\n.pe-4 {\n  padding-right: 1.875rem !important;\n}\n\n.pe-5 {\n  padding-right: 3.75rem !important;\n}\n\n.pb-0 {\n  padding-bottom: 0 !important;\n}\n\n.pb-1 {\n  padding-bottom: 0.3125rem !important;\n}\n\n.pb-2 {\n  padding-bottom: 0.625rem !important;\n}\n\n.pb-3 {\n  padding-bottom: 1.25rem !important;\n}\n\n.pb-4 {\n  padding-bottom: 1.875rem !important;\n}\n\n.pb-5 {\n  padding-bottom: 3.75rem !important;\n}\n\n.ps-0 {\n  padding-left: 0 !important;\n}\n\n.ps-1 {\n  padding-left: 0.3125rem !important;\n}\n\n.ps-2 {\n  padding-left: 0.625rem !important;\n}\n\n.ps-3 {\n  padding-left: 1.25rem !important;\n}\n\n.ps-4 {\n  padding-left: 1.875rem !important;\n}\n\n.ps-5 {\n  padding-left: 3.75rem !important;\n}\n\n.gap-0 {\n  gap: 0 !important;\n}\n\n.gap-1 {\n  gap: 0.3125rem !important;\n}\n\n.gap-2 {\n  gap: 0.625rem !important;\n}\n\n.gap-3 {\n  gap: 1.25rem !important;\n}\n\n.gap-4 {\n  gap: 1.875rem !important;\n}\n\n.gap-5 {\n  gap: 3.75rem !important;\n}\n\n.row-gap-0 {\n  row-gap: 0 !important;\n}\n\n.row-gap-1 {\n  row-gap: 0.3125rem !important;\n}\n\n.row-gap-2 {\n  row-gap: 0.625rem !important;\n}\n\n.row-gap-3 {\n  row-gap: 1.25rem !important;\n}\n\n.row-gap-4 {\n  row-gap: 1.875rem !important;\n}\n\n.row-gap-5 {\n  row-gap: 3.75rem !important;\n}\n\n.column-gap-0 {\n  column-gap: 0 !important;\n}\n\n.column-gap-1 {\n  column-gap: 0.3125rem !important;\n}\n\n.column-gap-2 {\n  column-gap: 0.625rem !important;\n}\n\n.column-gap-3 {\n  column-gap: 1.25rem !important;\n}\n\n.column-gap-4 {\n  column-gap: 1.875rem !important;\n}\n\n.column-gap-5 {\n  column-gap: 3.75rem !important;\n}\n\n.font-monospace {\n  font-family: var(--bs-font-monospace) !important;\n}\n\n.fs-1 {\n  font-size: 2.125rem !important;\n}\n\n.fs-2 {\n  font-size: 1.875rem !important;\n}\n\n.fs-3 {\n  font-size: 1.5rem !important;\n}\n\n.fs-4 {\n  font-size: 1.25rem !important;\n}\n\n.fs-5 {\n  font-size: 1.125rem !important;\n}\n\n.fs-6 {\n  font-size: 1rem !important;\n}\n\n.fw-normal {\n  font-weight: 400 !important;\n}\n\n.fw-medium {\n  font-weight: 500 !important;\n}\n\n.fw-semibold {\n  font-weight: 600 !important;\n}\n\n.fw-bold {\n  font-weight: 700 !important;\n}\n\n.lh-1 {\n  line-height: 1 !important;\n}\n\n.lh-sm {\n  line-height: 1.25 !important;\n}\n\n.lh-base {\n  line-height: 1.5 !important;\n}\n\n.lh-lg {\n  line-height: 2 !important;\n}\n\n.ll-sm {\n  max-width: 40ch !important;\n}\n\n.ll-md {\n  max-width: 80ch !important;\n}\n\n.text-start {\n  text-align: left !important;\n}\n\n.text-end {\n  text-align: right !important;\n}\n\n.text-center {\n  text-align: center !important;\n}\n\n.text-decoration-none {\n  text-decoration: none !important;\n}\n\n.text-decoration-underline {\n  text-decoration: underline !important;\n}\n\n.text-decoration-line-through {\n  text-decoration: line-through !important;\n}\n\n.text-lowercase {\n  text-transform: lowercase !important;\n}\n\n.text-uppercase {\n  text-transform: uppercase !important;\n}\n\n.text-capitalize {\n  text-transform: capitalize !important;\n}\n\n.text-wrap {\n  white-space: normal !important;\n}\n\n.text-nowrap {\n  white-space: nowrap !important;\n}\n\n/* rtl:begin:remove */\n.text-break {\n  word-wrap: break-word !important;\n  word-break: break-word !important;\n}\n\n/* rtl:end:remove */\n.text-primary {\n  --bs-text-opacity: 1;\n  color: rgba(var(--bs-primary-rgb), var(--bs-text-opacity)) !important;\n}\n\n.text-secondary {\n  --bs-text-opacity: 1;\n  color: rgba(var(--bs-secondary-rgb), var(--bs-text-opacity)) !important;\n}\n\n.text-success {\n  --bs-text-opacity: 1;\n  color: rgba(var(--bs-success-rgb), var(--bs-text-opacity)) !important;\n}\n\n.text-info {\n  --bs-text-opacity: 1;\n  color: rgba(var(--bs-info-rgb), var(--bs-text-opacity)) !important;\n}\n\n.text-warning {\n  --bs-text-opacity: 1;\n  color: rgba(var(--bs-warning-rgb), var(--bs-text-opacity)) !important;\n}\n\n.text-danger {\n  --bs-text-opacity: 1;\n  color: rgba(var(--bs-danger-rgb), var(--bs-text-opacity)) !important;\n}\n\n.text-light {\n  --bs-text-opacity: 1;\n  color: rgba(var(--bs-light-rgb), var(--bs-text-opacity)) !important;\n}\n\n.text-dark {\n  --bs-text-opacity: 1;\n  color: rgba(var(--bs-dark-rgb), var(--bs-text-opacity)) !important;\n}\n\n.text-black {\n  --bs-text-opacity: 1;\n  color: rgba(var(--bs-black-rgb), var(--bs-text-opacity)) !important;\n}\n\n.text-white {\n  --bs-text-opacity: 1;\n  color: rgba(var(--bs-white-rgb), var(--bs-text-opacity)) !important;\n}\n\n.text-body {\n  --bs-text-opacity: 1;\n  color: rgba(var(--bs-body-color-rgb), var(--bs-text-opacity)) !important;\n}\n\n.text-muted {\n  --bs-text-opacity: 1;\n  color: var(--bs-secondary-color) !important;\n}\n\n.text-black-50 {\n  --bs-text-opacity: 1;\n  color: rgba(0, 0, 0, 0.5) !important;\n}\n\n.text-white-50 {\n  --bs-text-opacity: 1;\n  color: rgba(255, 255, 255, 0.5) !important;\n}\n\n.text-body-secondary {\n  --bs-text-opacity: 1;\n  color: var(--bs-secondary-color) !important;\n}\n\n.text-body-tertiary {\n  --bs-text-opacity: 1;\n  color: var(--bs-tertiary-color) !important;\n}\n\n.text-body-emphasis {\n  --bs-text-opacity: 1;\n  color: var(--bs-emphasis-color) !important;\n}\n\n.text-reset {\n  --bs-text-opacity: 1;\n  color: inherit !important;\n}\n\n.text-opacity-25 {\n  --bs-text-opacity: 0.25;\n}\n\n.text-opacity-50 {\n  --bs-text-opacity: 0.5;\n}\n\n.text-opacity-75 {\n  --bs-text-opacity: 0.75;\n}\n\n.text-opacity-100 {\n  --bs-text-opacity: 1;\n}\n\n.text-primary-emphasis {\n  color: var(--bs-primary-text-emphasis) !important;\n}\n\n.text-secondary-emphasis {\n  color: var(--bs-secondary-text-emphasis) !important;\n}\n\n.text-success-emphasis {\n  color: var(--bs-success-text-emphasis) !important;\n}\n\n.text-info-emphasis {\n  color: var(--bs-info-text-emphasis) !important;\n}\n\n.text-warning-emphasis {\n  color: var(--bs-warning-text-emphasis) !important;\n}\n\n.text-danger-emphasis {\n  color: var(--bs-danger-text-emphasis) !important;\n}\n\n.text-light-emphasis {\n  color: var(--bs-light-text-emphasis) !important;\n}\n\n.text-dark-emphasis {\n  color: var(--bs-dark-text-emphasis) !important;\n}\n\n.link-opacity-10 {\n  --bs-link-opacity: 0.1;\n}\n\n.link-opacity-10-hover:hover {\n  --bs-link-opacity: 0.1;\n}\n\n.link-opacity-25 {\n  --bs-link-opacity: 0.25;\n}\n\n.link-opacity-25-hover:hover {\n  --bs-link-opacity: 0.25;\n}\n\n.link-opacity-50 {\n  --bs-link-opacity: 0.5;\n}\n\n.link-opacity-50-hover:hover {\n  --bs-link-opacity: 0.5;\n}\n\n.link-opacity-75 {\n  --bs-link-opacity: 0.75;\n}\n\n.link-opacity-75-hover:hover {\n  --bs-link-opacity: 0.75;\n}\n\n.link-opacity-100 {\n  --bs-link-opacity: 1;\n}\n\n.link-opacity-100-hover:hover {\n  --bs-link-opacity: 1;\n}\n\n.link-offset-1 {\n  text-underline-offset: 0.125em !important;\n}\n\n.link-offset-1-hover:hover {\n  text-underline-offset: 0.125em !important;\n}\n\n.link-offset-2 {\n  text-underline-offset: 0.25em !important;\n}\n\n.link-offset-2-hover:hover {\n  text-underline-offset: 0.25em !important;\n}\n\n.link-offset-3 {\n  text-underline-offset: 0.375em !important;\n}\n\n.link-offset-3-hover:hover {\n  text-underline-offset: 0.375em !important;\n}\n\n.link-underline-primary {\n  --bs-link-underline-opacity: 1;\n  text-decoration-color: rgba(var(--bs-primary-rgb), var(--bs-link-underline-opacity)) !important;\n}\n\n.link-underline-secondary {\n  --bs-link-underline-opacity: 1;\n  text-decoration-color: rgba(var(--bs-secondary-rgb), var(--bs-link-underline-opacity)) !important;\n}\n\n.link-underline-success {\n  --bs-link-underline-opacity: 1;\n  text-decoration-color: rgba(var(--bs-success-rgb), var(--bs-link-underline-opacity)) !important;\n}\n\n.link-underline-info {\n  --bs-link-underline-opacity: 1;\n  text-decoration-color: rgba(var(--bs-info-rgb), var(--bs-link-underline-opacity)) !important;\n}\n\n.link-underline-warning {\n  --bs-link-underline-opacity: 1;\n  text-decoration-color: rgba(var(--bs-warning-rgb), var(--bs-link-underline-opacity)) !important;\n}\n\n.link-underline-danger {\n  --bs-link-underline-opacity: 1;\n  text-decoration-color: rgba(var(--bs-danger-rgb), var(--bs-link-underline-opacity)) !important;\n}\n\n.link-underline-light {\n  --bs-link-underline-opacity: 1;\n  text-decoration-color: rgba(var(--bs-light-rgb), var(--bs-link-underline-opacity)) !important;\n}\n\n.link-underline-dark {\n  --bs-link-underline-opacity: 1;\n  text-decoration-color: rgba(var(--bs-dark-rgb), var(--bs-link-underline-opacity)) !important;\n}\n\n.link-underline {\n  --bs-link-underline-opacity: 1;\n  text-decoration-color: rgba(var(--bs-link-color-rgb), var(--bs-link-underline-opacity, 1)) !important;\n}\n\n.link-underline-opacity-0 {\n  --bs-link-underline-opacity: 0;\n}\n\n.link-underline-opacity-0-hover:hover {\n  --bs-link-underline-opacity: 0;\n}\n\n.link-underline-opacity-10 {\n  --bs-link-underline-opacity: 0.1;\n}\n\n.link-underline-opacity-10-hover:hover {\n  --bs-link-underline-opacity: 0.1;\n}\n\n.link-underline-opacity-25 {\n  --bs-link-underline-opacity: 0.25;\n}\n\n.link-underline-opacity-25-hover:hover {\n  --bs-link-underline-opacity: 0.25;\n}\n\n.link-underline-opacity-50 {\n  --bs-link-underline-opacity: 0.5;\n}\n\n.link-underline-opacity-50-hover:hover {\n  --bs-link-underline-opacity: 0.5;\n}\n\n.link-underline-opacity-75 {\n  --bs-link-underline-opacity: 0.75;\n}\n\n.link-underline-opacity-75-hover:hover {\n  --bs-link-underline-opacity: 0.75;\n}\n\n.link-underline-opacity-100 {\n  --bs-link-underline-opacity: 1;\n}\n\n.link-underline-opacity-100-hover:hover {\n  --bs-link-underline-opacity: 1;\n}\n\n.bg-primary {\n  --bs-bg-opacity: 1;\n  background-color: rgba(var(--bs-primary-rgb), var(--bs-bg-opacity)) !important;\n}\n\n.bg-secondary {\n  --bs-bg-opacity: 1;\n  background-color: rgba(var(--bs-secondary-rgb), var(--bs-bg-opacity)) !important;\n}\n\n.bg-success {\n  --bs-bg-opacity: 1;\n  background-color: rgba(var(--bs-success-rgb), var(--bs-bg-opacity)) !important;\n}\n\n.bg-info {\n  --bs-bg-opacity: 1;\n  background-color: rgba(var(--bs-info-rgb), var(--bs-bg-opacity)) !important;\n}\n\n.bg-warning {\n  --bs-bg-opacity: 1;\n  background-color: rgba(var(--bs-warning-rgb), var(--bs-bg-opacity)) !important;\n}\n\n.bg-danger {\n  --bs-bg-opacity: 1;\n  background-color: rgba(var(--bs-danger-rgb), var(--bs-bg-opacity)) !important;\n}\n\n.bg-light {\n  --bs-bg-opacity: 1;\n  background-color: rgba(var(--bs-light-rgb), var(--bs-bg-opacity)) !important;\n}\n\n.bg-dark {\n  --bs-bg-opacity: 1;\n  background-color: rgba(var(--bs-dark-rgb), var(--bs-bg-opacity)) !important;\n}\n\n.bg-black {\n  --bs-bg-opacity: 1;\n  background-color: rgba(var(--bs-black-rgb), var(--bs-bg-opacity)) !important;\n}\n\n.bg-white {\n  --bs-bg-opacity: 1;\n  background-color: rgba(var(--bs-white-rgb), var(--bs-bg-opacity)) !important;\n}\n\n.bg-body {\n  --bs-bg-opacity: 1;\n  background-color: rgba(var(--bs-body-bg-rgb), var(--bs-bg-opacity)) !important;\n}\n\n.bg-transparent {\n  --bs-bg-opacity: 1;\n  background-color: transparent !important;\n}\n\n.bg-body-secondary {\n  --bs-bg-opacity: 1;\n  background-color: rgba(var(--bs-secondary-bg-rgb), var(--bs-bg-opacity)) !important;\n}\n\n.bg-body-tertiary {\n  --bs-bg-opacity: 1;\n  background-color: rgba(var(--bs-tertiary-bg-rgb), var(--bs-bg-opacity)) !important;\n}\n\n.bg-supporting-green {\n  --bs-bg-opacity: 1;\n  background-color: #50be87 !important;\n}\n\n.bg-supporting-blue {\n  --bs-bg-opacity: 1;\n  background-color: #4bb4e6 !important;\n}\n\n.bg-supporting-yellow {\n  --bs-bg-opacity: 1;\n  background-color: #ffd200 !important;\n}\n\n.bg-supporting-pink {\n  --bs-bg-opacity: 1;\n  background-color: #ffb4e6 !important;\n}\n\n.bg-supporting-purple {\n  --bs-bg-opacity: 1;\n  background-color: #a885d8 !important;\n}\n\n.bg-supporting-orange {\n  --bs-bg-opacity: 1;\n  background-color: #ff7900 !important;\n}\n\n.bg-opacity-10 {\n  --bs-bg-opacity: 0.1;\n}\n\n.bg-opacity-25 {\n  --bs-bg-opacity: 0.25;\n}\n\n.bg-opacity-50 {\n  --bs-bg-opacity: 0.5;\n}\n\n.bg-opacity-75 {\n  --bs-bg-opacity: 0.75;\n}\n\n.bg-opacity-100 {\n  --bs-bg-opacity: 1;\n}\n\n.bg-primary-subtle {\n  background-color: var(--bs-primary-bg-subtle) !important;\n}\n\n.bg-secondary-subtle {\n  background-color: var(--bs-secondary-bg-subtle) !important;\n}\n\n.bg-success-subtle {\n  background-color: var(--bs-success-bg-subtle) !important;\n}\n\n.bg-info-subtle {\n  background-color: var(--bs-info-bg-subtle) !important;\n}\n\n.bg-warning-subtle {\n  background-color: var(--bs-warning-bg-subtle) !important;\n}\n\n.bg-danger-subtle {\n  background-color: var(--bs-danger-bg-subtle) !important;\n}\n\n.bg-light-subtle {\n  background-color: var(--bs-light-bg-subtle) !important;\n}\n\n.bg-dark-subtle {\n  background-color: var(--bs-dark-bg-subtle) !important;\n}\n\n.bg-gradient {\n  background-image: var(--bs-gradient) !important;\n}\n\n.user-select-all {\n  user-select: all !important;\n}\n\n.user-select-auto {\n  user-select: auto !important;\n}\n\n.user-select-none {\n  user-select: none !important;\n}\n\n.pe-none {\n  pointer-events: none !important;\n}\n\n.pe-auto {\n  pointer-events: auto !important;\n}\n\n.rounded {\n  border-radius: var(--bs-border-radius) !important;\n}\n\n.rounded-0 {\n  border-radius: 0 !important;\n}\n\n.rounded-1 {\n  border-radius: var(--bs-border-radius-sm) !important;\n}\n\n.rounded-2 {\n  border-radius: var(--bs-border-radius) !important;\n}\n\n.rounded-3 {\n  border-radius: var(--bs-border-radius-lg) !important;\n}\n\n.rounded-4 {\n  border-radius: var(--bs-border-radius-xl) !important;\n}\n\n.rounded-5 {\n  border-radius: var(--bs-border-radius-xxl) !important;\n}\n\n.rounded-circle {\n  border-radius: 50% !important;\n}\n\n.rounded-pill {\n  border-radius: var(--bs-border-radius-pill) !important;\n}\n\n.rounded-top {\n  border-top-left-radius: var(--bs-border-radius) !important;\n  border-top-right-radius: var(--bs-border-radius) !important;\n}\n\n.rounded-top-0 {\n  border-top-left-radius: 0 !important;\n  border-top-right-radius: 0 !important;\n}\n\n.rounded-top-1 {\n  border-top-left-radius: var(--bs-border-radius-sm) !important;\n  border-top-right-radius: var(--bs-border-radius-sm) !important;\n}\n\n.rounded-top-2 {\n  border-top-left-radius: var(--bs-border-radius) !important;\n  border-top-right-radius: var(--bs-border-radius) !important;\n}\n\n.rounded-top-3 {\n  border-top-left-radius: var(--bs-border-radius-lg) !important;\n  border-top-right-radius: var(--bs-border-radius-lg) !important;\n}\n\n.rounded-top-4 {\n  border-top-left-radius: var(--bs-border-radius-xl) !important;\n  border-top-right-radius: var(--bs-border-radius-xl) !important;\n}\n\n.rounded-top-5 {\n  border-top-left-radius: var(--bs-border-radius-xxl) !important;\n  border-top-right-radius: var(--bs-border-radius-xxl) !important;\n}\n\n.rounded-top-circle {\n  border-top-left-radius: 50% !important;\n  border-top-right-radius: 50% !important;\n}\n\n.rounded-top-pill {\n  border-top-left-radius: var(--bs-border-radius-pill) !important;\n  border-top-right-radius: var(--bs-border-radius-pill) !important;\n}\n\n.rounded-end {\n  border-top-right-radius: var(--bs-border-radius) !important;\n  border-bottom-right-radius: var(--bs-border-radius) !important;\n}\n\n.rounded-end-0 {\n  border-top-right-radius: 0 !important;\n  border-bottom-right-radius: 0 !important;\n}\n\n.rounded-end-1 {\n  border-top-right-radius: var(--bs-border-radius-sm) !important;\n  border-bottom-right-radius: var(--bs-border-radius-sm) !important;\n}\n\n.rounded-end-2 {\n  border-top-right-radius: var(--bs-border-radius) !important;\n  border-bottom-right-radius: var(--bs-border-radius) !important;\n}\n\n.rounded-end-3 {\n  border-top-right-radius: var(--bs-border-radius-lg) !important;\n  border-bottom-right-radius: var(--bs-border-radius-lg) !important;\n}\n\n.rounded-end-4 {\n  border-top-right-radius: var(--bs-border-radius-xl) !important;\n  border-bottom-right-radius: var(--bs-border-radius-xl) !important;\n}\n\n.rounded-end-5 {\n  border-top-right-radius: var(--bs-border-radius-xxl) !important;\n  border-bottom-right-radius: var(--bs-border-radius-xxl) !important;\n}\n\n.rounded-end-circle {\n  border-top-right-radius: 50% !important;\n  border-bottom-right-radius: 50% !important;\n}\n\n.rounded-end-pill {\n  border-top-right-radius: var(--bs-border-radius-pill) !important;\n  border-bottom-right-radius: var(--bs-border-radius-pill) !important;\n}\n\n.rounded-bottom {\n  border-bottom-right-radius: var(--bs-border-radius) !important;\n  border-bottom-left-radius: var(--bs-border-radius) !important;\n}\n\n.rounded-bottom-0 {\n  border-bottom-right-radius: 0 !important;\n  border-bottom-left-radius: 0 !important;\n}\n\n.rounded-bottom-1 {\n  border-bottom-right-radius: var(--bs-border-radius-sm) !important;\n  border-bottom-left-radius: var(--bs-border-radius-sm) !important;\n}\n\n.rounded-bottom-2 {\n  border-bottom-right-radius: var(--bs-border-radius) !important;\n  border-bottom-left-radius: var(--bs-border-radius) !important;\n}\n\n.rounded-bottom-3 {\n  border-bottom-right-radius: var(--bs-border-radius-lg) !important;\n  border-bottom-left-radius: var(--bs-border-radius-lg) !important;\n}\n\n.rounded-bottom-4 {\n  border-bottom-right-radius: var(--bs-border-radius-xl) !important;\n  border-bottom-left-radius: var(--bs-border-radius-xl) !important;\n}\n\n.rounded-bottom-5 {\n  border-bottom-right-radius: var(--bs-border-radius-xxl) !important;\n  border-bottom-left-radius: var(--bs-border-radius-xxl) !important;\n}\n\n.rounded-bottom-circle {\n  border-bottom-right-radius: 50% !important;\n  border-bottom-left-radius: 50% !important;\n}\n\n.rounded-bottom-pill {\n  border-bottom-right-radius: var(--bs-border-radius-pill) !important;\n  border-bottom-left-radius: var(--bs-border-radius-pill) !important;\n}\n\n.rounded-start {\n  border-bottom-left-radius: var(--bs-border-radius) !important;\n  border-top-left-radius: var(--bs-border-radius) !important;\n}\n\n.rounded-start-0 {\n  border-bottom-left-radius: 0 !important;\n  border-top-left-radius: 0 !important;\n}\n\n.rounded-start-1 {\n  border-bottom-left-radius: var(--bs-border-radius-sm) !important;\n  border-top-left-radius: var(--bs-border-radius-sm) !important;\n}\n\n.rounded-start-2 {\n  border-bottom-left-radius: var(--bs-border-radius) !important;\n  border-top-left-radius: var(--bs-border-radius) !important;\n}\n\n.rounded-start-3 {\n  border-bottom-left-radius: var(--bs-border-radius-lg) !important;\n  border-top-left-radius: var(--bs-border-radius-lg) !important;\n}\n\n.rounded-start-4 {\n  border-bottom-left-radius: var(--bs-border-radius-xl) !important;\n  border-top-left-radius: var(--bs-border-radius-xl) !important;\n}\n\n.rounded-start-5 {\n  border-bottom-left-radius: var(--bs-border-radius-xxl) !important;\n  border-top-left-radius: var(--bs-border-radius-xxl) !important;\n}\n\n.rounded-start-circle {\n  border-bottom-left-radius: 50% !important;\n  border-top-left-radius: 50% !important;\n}\n\n.rounded-start-pill {\n  border-bottom-left-radius: var(--bs-border-radius-pill) !important;\n  border-top-left-radius: var(--bs-border-radius-pill) !important;\n}\n\n.visible {\n  visibility: visible !important;\n}\n\n.invisible {\n  visibility: hidden !important;\n}\n\n.z-n1 {\n  z-index: -1 !important;\n}\n\n.z-0 {\n  z-index: 0 !important;\n}\n\n.z-1 {\n  z-index: 1 !important;\n}\n\n.z-2 {\n  z-index: 2 !important;\n}\n\n.z-3 {\n  z-index: 3 !important;\n}\n\n@media (min-width: 480px) {\n  .float-sm-start {\n    float: left !important;\n  }\n  .float-sm-end {\n    float: right !important;\n  }\n  .float-sm-none {\n    float: none !important;\n  }\n  .object-fit-sm-contain {\n    object-fit: contain !important;\n  }\n  .object-fit-sm-cover {\n    object-fit: cover !important;\n  }\n  .object-fit-sm-fill {\n    object-fit: fill !important;\n  }\n  .object-fit-sm-scale {\n    object-fit: scale-down !important;\n  }\n  .object-fit-sm-none {\n    object-fit: none !important;\n  }\n  .d-sm-inline {\n    display: inline !important;\n  }\n  .d-sm-inline-block {\n    display: inline-block !important;\n  }\n  .d-sm-block {\n    display: block !important;\n  }\n  .d-sm-grid {\n    display: grid !important;\n  }\n  .d-sm-inline-grid {\n    display: inline-grid !important;\n  }\n  .d-sm-table {\n    display: table !important;\n  }\n  .d-sm-table-row {\n    display: table-row !important;\n  }\n  .d-sm-table-cell {\n    display: table-cell !important;\n  }\n  .d-sm-flex {\n    display: flex !important;\n  }\n  .d-sm-inline-flex {\n    display: inline-flex !important;\n  }\n  .d-sm-none {\n    display: none !important;\n  }\n  .flex-sm-fill {\n    flex: 1 1 auto !important;\n  }\n  .flex-sm-row {\n    flex-direction: row !important;\n  }\n  .flex-sm-column {\n    flex-direction: column !important;\n  }\n  .flex-sm-row-reverse {\n    flex-direction: row-reverse !important;\n  }\n  .flex-sm-column-reverse {\n    flex-direction: column-reverse !important;\n  }\n  .flex-sm-grow-0 {\n    flex-grow: 0 !important;\n  }\n  .flex-sm-grow-1 {\n    flex-grow: 1 !important;\n  }\n  .flex-sm-shrink-0 {\n    flex-shrink: 0 !important;\n  }\n  .flex-sm-shrink-1 {\n    flex-shrink: 1 !important;\n  }\n  .flex-sm-wrap {\n    flex-wrap: wrap !important;\n  }\n  .flex-sm-nowrap {\n    flex-wrap: nowrap !important;\n  }\n  .flex-sm-wrap-reverse {\n    flex-wrap: wrap-reverse !important;\n  }\n  .justify-content-sm-start {\n    justify-content: flex-start !important;\n  }\n  .justify-content-sm-end {\n    justify-content: flex-end !important;\n  }\n  .justify-content-sm-center {\n    justify-content: center !important;\n  }\n  .justify-content-sm-between {\n    justify-content: space-between !important;\n  }\n  .justify-content-sm-around {\n    justify-content: space-around !important;\n  }\n  .justify-content-sm-evenly {\n    justify-content: space-evenly !important;\n  }\n  .align-items-sm-start {\n    align-items: flex-start !important;\n  }\n  .align-items-sm-end {\n    align-items: flex-end !important;\n  }\n  .align-items-sm-center {\n    align-items: center !important;\n  }\n  .align-items-sm-baseline {\n    align-items: baseline !important;\n  }\n  .align-items-sm-stretch {\n    align-items: stretch !important;\n  }\n  .align-content-sm-start {\n    align-content: flex-start !important;\n  }\n  .align-content-sm-end {\n    align-content: flex-end !important;\n  }\n  .align-content-sm-center {\n    align-content: center !important;\n  }\n  .align-content-sm-between {\n    align-content: space-between !important;\n  }\n  .align-content-sm-around {\n    align-content: space-around !important;\n  }\n  .align-content-sm-stretch {\n    align-content: stretch !important;\n  }\n  .align-self-sm-auto {\n    align-self: auto !important;\n  }\n  .align-self-sm-start {\n    align-self: flex-start !important;\n  }\n  .align-self-sm-end {\n    align-self: flex-end !important;\n  }\n  .align-self-sm-center {\n    align-self: center !important;\n  }\n  .align-self-sm-baseline {\n    align-self: baseline !important;\n  }\n  .align-self-sm-stretch {\n    align-self: stretch !important;\n  }\n  .order-sm-first {\n    order: -1 !important;\n  }\n  .order-sm-0 {\n    order: 0 !important;\n  }\n  .order-sm-1 {\n    order: 1 !important;\n  }\n  .order-sm-2 {\n    order: 2 !important;\n  }\n  .order-sm-3 {\n    order: 3 !important;\n  }\n  .order-sm-4 {\n    order: 4 !important;\n  }\n  .order-sm-5 {\n    order: 5 !important;\n  }\n  .order-sm-last {\n    order: 6 !important;\n  }\n  .m-sm-0 {\n    margin: 0 !important;\n  }\n  .m-sm-1 {\n    margin: 0.3125rem !important;\n  }\n  .m-sm-2 {\n    margin: 0.625rem !important;\n  }\n  .m-sm-3 {\n    margin: 1.25rem !important;\n  }\n  .m-sm-4 {\n    margin: 1.875rem !important;\n  }\n  .m-sm-5 {\n    margin: 3.75rem !important;\n  }\n  .m-sm-auto {\n    margin: auto !important;\n  }\n  .mx-sm-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important;\n  }\n  .mx-sm-1 {\n    margin-right: 0.3125rem !important;\n    margin-left: 0.3125rem !important;\n  }\n  .mx-sm-2 {\n    margin-right: 0.625rem !important;\n    margin-left: 0.625rem !important;\n  }\n  .mx-sm-3 {\n    margin-right: 1.25rem !important;\n    margin-left: 1.25rem !important;\n  }\n  .mx-sm-4 {\n    margin-right: 1.875rem !important;\n    margin-left: 1.875rem !important;\n  }\n  .mx-sm-5 {\n    margin-right: 3.75rem !important;\n    margin-left: 3.75rem !important;\n  }\n  .mx-sm-auto {\n    margin-right: auto !important;\n    margin-left: auto !important;\n  }\n  .my-sm-0 {\n    margin-top: 0 !important;\n    margin-bottom: 0 !important;\n  }\n  .my-sm-1 {\n    margin-top: 0.3125rem !important;\n    margin-bottom: 0.3125rem !important;\n  }\n  .my-sm-2 {\n    margin-top: 0.625rem !important;\n    margin-bottom: 0.625rem !important;\n  }\n  .my-sm-3 {\n    margin-top: 1.25rem !important;\n    margin-bottom: 1.25rem !important;\n  }\n  .my-sm-4 {\n    margin-top: 1.875rem !important;\n    margin-bottom: 1.875rem !important;\n  }\n  .my-sm-5 {\n    margin-top: 3.75rem !important;\n    margin-bottom: 3.75rem !important;\n  }\n  .my-sm-auto {\n    margin-top: auto !important;\n    margin-bottom: auto !important;\n  }\n  .mt-sm-0 {\n    margin-top: 0 !important;\n  }\n  .mt-sm-1 {\n    margin-top: 0.3125rem !important;\n  }\n  .mt-sm-2 {\n    margin-top: 0.625rem !important;\n  }\n  .mt-sm-3 {\n    margin-top: 1.25rem !important;\n  }\n  .mt-sm-4 {\n    margin-top: 1.875rem !important;\n  }\n  .mt-sm-5 {\n    margin-top: 3.75rem !important;\n  }\n  .mt-sm-auto {\n    margin-top: auto !important;\n  }\n  .me-sm-0 {\n    margin-right: 0 !important;\n  }\n  .me-sm-1 {\n    margin-right: 0.3125rem !important;\n  }\n  .me-sm-2 {\n    margin-right: 0.625rem !important;\n  }\n  .me-sm-3 {\n    margin-right: 1.25rem !important;\n  }\n  .me-sm-4 {\n    margin-right: 1.875rem !important;\n  }\n  .me-sm-5 {\n    margin-right: 3.75rem !important;\n  }\n  .me-sm-auto {\n    margin-right: auto !important;\n  }\n  .mb-sm-0 {\n    margin-bottom: 0 !important;\n  }\n  .mb-sm-1 {\n    margin-bottom: 0.3125rem !important;\n  }\n  .mb-sm-2 {\n    margin-bottom: 0.625rem !important;\n  }\n  .mb-sm-3 {\n    margin-bottom: 1.25rem !important;\n  }\n  .mb-sm-4 {\n    margin-bottom: 1.875rem !important;\n  }\n  .mb-sm-5 {\n    margin-bottom: 3.75rem !important;\n  }\n  .mb-sm-auto {\n    margin-bottom: auto !important;\n  }\n  .ms-sm-0 {\n    margin-left: 0 !important;\n  }\n  .ms-sm-1 {\n    margin-left: 0.3125rem !important;\n  }\n  .ms-sm-2 {\n    margin-left: 0.625rem !important;\n  }\n  .ms-sm-3 {\n    margin-left: 1.25rem !important;\n  }\n  .ms-sm-4 {\n    margin-left: 1.875rem !important;\n  }\n  .ms-sm-5 {\n    margin-left: 3.75rem !important;\n  }\n  .ms-sm-auto {\n    margin-left: auto !important;\n  }\n  .p-sm-0 {\n    padding: 0 !important;\n  }\n  .p-sm-1 {\n    padding: 0.3125rem !important;\n  }\n  .p-sm-2 {\n    padding: 0.625rem !important;\n  }\n  .p-sm-3 {\n    padding: 1.25rem !important;\n  }\n  .p-sm-4 {\n    padding: 1.875rem !important;\n  }\n  .p-sm-5 {\n    padding: 3.75rem !important;\n  }\n  .px-sm-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important;\n  }\n  .px-sm-1 {\n    padding-right: 0.3125rem !important;\n    padding-left: 0.3125rem !important;\n  }\n  .px-sm-2 {\n    padding-right: 0.625rem !important;\n    padding-left: 0.625rem !important;\n  }\n  .px-sm-3 {\n    padding-right: 1.25rem !important;\n    padding-left: 1.25rem !important;\n  }\n  .px-sm-4 {\n    padding-right: 1.875rem !important;\n    padding-left: 1.875rem !important;\n  }\n  .px-sm-5 {\n    padding-right: 3.75rem !important;\n    padding-left: 3.75rem !important;\n  }\n  .py-sm-0 {\n    padding-top: 0 !important;\n    padding-bottom: 0 !important;\n  }\n  .py-sm-1 {\n    padding-top: 0.3125rem !important;\n    padding-bottom: 0.3125rem !important;\n  }\n  .py-sm-2 {\n    padding-top: 0.625rem !important;\n    padding-bottom: 0.625rem !important;\n  }\n  .py-sm-3 {\n    padding-top: 1.25rem !important;\n    padding-bottom: 1.25rem !important;\n  }\n  .py-sm-4 {\n    padding-top: 1.875rem !important;\n    padding-bottom: 1.875rem !important;\n  }\n  .py-sm-5 {\n    padding-top: 3.75rem !important;\n    padding-bottom: 3.75rem !important;\n  }\n  .pt-sm-0 {\n    padding-top: 0 !important;\n  }\n  .pt-sm-1 {\n    padding-top: 0.3125rem !important;\n  }\n  .pt-sm-2 {\n    padding-top: 0.625rem !important;\n  }\n  .pt-sm-3 {\n    padding-top: 1.25rem !important;\n  }\n  .pt-sm-4 {\n    padding-top: 1.875rem !important;\n  }\n  .pt-sm-5 {\n    padding-top: 3.75rem !important;\n  }\n  .pe-sm-0 {\n    padding-right: 0 !important;\n  }\n  .pe-sm-1 {\n    padding-right: 0.3125rem !important;\n  }\n  .pe-sm-2 {\n    padding-right: 0.625rem !important;\n  }\n  .pe-sm-3 {\n    padding-right: 1.25rem !important;\n  }\n  .pe-sm-4 {\n    padding-right: 1.875rem !important;\n  }\n  .pe-sm-5 {\n    padding-right: 3.75rem !important;\n  }\n  .pb-sm-0 {\n    padding-bottom: 0 !important;\n  }\n  .pb-sm-1 {\n    padding-bottom: 0.3125rem !important;\n  }\n  .pb-sm-2 {\n    padding-bottom: 0.625rem !important;\n  }\n  .pb-sm-3 {\n    padding-bottom: 1.25rem !important;\n  }\n  .pb-sm-4 {\n    padding-bottom: 1.875rem !important;\n  }\n  .pb-sm-5 {\n    padding-bottom: 3.75rem !important;\n  }\n  .ps-sm-0 {\n    padding-left: 0 !important;\n  }\n  .ps-sm-1 {\n    padding-left: 0.3125rem !important;\n  }\n  .ps-sm-2 {\n    padding-left: 0.625rem !important;\n  }\n  .ps-sm-3 {\n    padding-left: 1.25rem !important;\n  }\n  .ps-sm-4 {\n    padding-left: 1.875rem !important;\n  }\n  .ps-sm-5 {\n    padding-left: 3.75rem !important;\n  }\n  .gap-sm-0 {\n    gap: 0 !important;\n  }\n  .gap-sm-1 {\n    gap: 0.3125rem !important;\n  }\n  .gap-sm-2 {\n    gap: 0.625rem !important;\n  }\n  .gap-sm-3 {\n    gap: 1.25rem !important;\n  }\n  .gap-sm-4 {\n    gap: 1.875rem !important;\n  }\n  .gap-sm-5 {\n    gap: 3.75rem !important;\n  }\n  .row-gap-sm-0 {\n    row-gap: 0 !important;\n  }\n  .row-gap-sm-1 {\n    row-gap: 0.3125rem !important;\n  }\n  .row-gap-sm-2 {\n    row-gap: 0.625rem !important;\n  }\n  .row-gap-sm-3 {\n    row-gap: 1.25rem !important;\n  }\n  .row-gap-sm-4 {\n    row-gap: 1.875rem !important;\n  }\n  .row-gap-sm-5 {\n    row-gap: 3.75rem !important;\n  }\n  .column-gap-sm-0 {\n    column-gap: 0 !important;\n  }\n  .column-gap-sm-1 {\n    column-gap: 0.3125rem !important;\n  }\n  .column-gap-sm-2 {\n    column-gap: 0.625rem !important;\n  }\n  .column-gap-sm-3 {\n    column-gap: 1.25rem !important;\n  }\n  .column-gap-sm-4 {\n    column-gap: 1.875rem !important;\n  }\n  .column-gap-sm-5 {\n    column-gap: 3.75rem !important;\n  }\n  .text-sm-start {\n    text-align: left !important;\n  }\n  .text-sm-end {\n    text-align: right !important;\n  }\n  .text-sm-center {\n    text-align: center !important;\n  }\n}\n@media (min-width: 768px) {\n  .float-md-start {\n    float: left !important;\n  }\n  .float-md-end {\n    float: right !important;\n  }\n  .float-md-none {\n    float: none !important;\n  }\n  .object-fit-md-contain {\n    object-fit: contain !important;\n  }\n  .object-fit-md-cover {\n    object-fit: cover !important;\n  }\n  .object-fit-md-fill {\n    object-fit: fill !important;\n  }\n  .object-fit-md-scale {\n    object-fit: scale-down !important;\n  }\n  .object-fit-md-none {\n    object-fit: none !important;\n  }\n  .d-md-inline {\n    display: inline !important;\n  }\n  .d-md-inline-block {\n    display: inline-block !important;\n  }\n  .d-md-block {\n    display: block !important;\n  }\n  .d-md-grid {\n    display: grid !important;\n  }\n  .d-md-inline-grid {\n    display: inline-grid !important;\n  }\n  .d-md-table {\n    display: table !important;\n  }\n  .d-md-table-row {\n    display: table-row !important;\n  }\n  .d-md-table-cell {\n    display: table-cell !important;\n  }\n  .d-md-flex {\n    display: flex !important;\n  }\n  .d-md-inline-flex {\n    display: inline-flex !important;\n  }\n  .d-md-none {\n    display: none !important;\n  }\n  .flex-md-fill {\n    flex: 1 1 auto !important;\n  }\n  .flex-md-row {\n    flex-direction: row !important;\n  }\n  .flex-md-column {\n    flex-direction: column !important;\n  }\n  .flex-md-row-reverse {\n    flex-direction: row-reverse !important;\n  }\n  .flex-md-column-reverse {\n    flex-direction: column-reverse !important;\n  }\n  .flex-md-grow-0 {\n    flex-grow: 0 !important;\n  }\n  .flex-md-grow-1 {\n    flex-grow: 1 !important;\n  }\n  .flex-md-shrink-0 {\n    flex-shrink: 0 !important;\n  }\n  .flex-md-shrink-1 {\n    flex-shrink: 1 !important;\n  }\n  .flex-md-wrap {\n    flex-wrap: wrap !important;\n  }\n  .flex-md-nowrap {\n    flex-wrap: nowrap !important;\n  }\n  .flex-md-wrap-reverse {\n    flex-wrap: wrap-reverse !important;\n  }\n  .justify-content-md-start {\n    justify-content: flex-start !important;\n  }\n  .justify-content-md-end {\n    justify-content: flex-end !important;\n  }\n  .justify-content-md-center {\n    justify-content: center !important;\n  }\n  .justify-content-md-between {\n    justify-content: space-between !important;\n  }\n  .justify-content-md-around {\n    justify-content: space-around !important;\n  }\n  .justify-content-md-evenly {\n    justify-content: space-evenly !important;\n  }\n  .align-items-md-start {\n    align-items: flex-start !important;\n  }\n  .align-items-md-end {\n    align-items: flex-end !important;\n  }\n  .align-items-md-center {\n    align-items: center !important;\n  }\n  .align-items-md-baseline {\n    align-items: baseline !important;\n  }\n  .align-items-md-stretch {\n    align-items: stretch !important;\n  }\n  .align-content-md-start {\n    align-content: flex-start !important;\n  }\n  .align-content-md-end {\n    align-content: flex-end !important;\n  }\n  .align-content-md-center {\n    align-content: center !important;\n  }\n  .align-content-md-between {\n    align-content: space-between !important;\n  }\n  .align-content-md-around {\n    align-content: space-around !important;\n  }\n  .align-content-md-stretch {\n    align-content: stretch !important;\n  }\n  .align-self-md-auto {\n    align-self: auto !important;\n  }\n  .align-self-md-start {\n    align-self: flex-start !important;\n  }\n  .align-self-md-end {\n    align-self: flex-end !important;\n  }\n  .align-self-md-center {\n    align-self: center !important;\n  }\n  .align-self-md-baseline {\n    align-self: baseline !important;\n  }\n  .align-self-md-stretch {\n    align-self: stretch !important;\n  }\n  .order-md-first {\n    order: -1 !important;\n  }\n  .order-md-0 {\n    order: 0 !important;\n  }\n  .order-md-1 {\n    order: 1 !important;\n  }\n  .order-md-2 {\n    order: 2 !important;\n  }\n  .order-md-3 {\n    order: 3 !important;\n  }\n  .order-md-4 {\n    order: 4 !important;\n  }\n  .order-md-5 {\n    order: 5 !important;\n  }\n  .order-md-last {\n    order: 6 !important;\n  }\n  .m-md-0 {\n    margin: 0 !important;\n  }\n  .m-md-1 {\n    margin: 0.3125rem !important;\n  }\n  .m-md-2 {\n    margin: 0.625rem !important;\n  }\n  .m-md-3 {\n    margin: 1.25rem !important;\n  }\n  .m-md-4 {\n    margin: 1.875rem !important;\n  }\n  .m-md-5 {\n    margin: 3.75rem !important;\n  }\n  .m-md-auto {\n    margin: auto !important;\n  }\n  .mx-md-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important;\n  }\n  .mx-md-1 {\n    margin-right: 0.3125rem !important;\n    margin-left: 0.3125rem !important;\n  }\n  .mx-md-2 {\n    margin-right: 0.625rem !important;\n    margin-left: 0.625rem !important;\n  }\n  .mx-md-3 {\n    margin-right: 1.25rem !important;\n    margin-left: 1.25rem !important;\n  }\n  .mx-md-4 {\n    margin-right: 1.875rem !important;\n    margin-left: 1.875rem !important;\n  }\n  .mx-md-5 {\n    margin-right: 3.75rem !important;\n    margin-left: 3.75rem !important;\n  }\n  .mx-md-auto {\n    margin-right: auto !important;\n    margin-left: auto !important;\n  }\n  .my-md-0 {\n    margin-top: 0 !important;\n    margin-bottom: 0 !important;\n  }\n  .my-md-1 {\n    margin-top: 0.3125rem !important;\n    margin-bottom: 0.3125rem !important;\n  }\n  .my-md-2 {\n    margin-top: 0.625rem !important;\n    margin-bottom: 0.625rem !important;\n  }\n  .my-md-3 {\n    margin-top: 1.25rem !important;\n    margin-bottom: 1.25rem !important;\n  }\n  .my-md-4 {\n    margin-top: 1.875rem !important;\n    margin-bottom: 1.875rem !important;\n  }\n  .my-md-5 {\n    margin-top: 3.75rem !important;\n    margin-bottom: 3.75rem !important;\n  }\n  .my-md-auto {\n    margin-top: auto !important;\n    margin-bottom: auto !important;\n  }\n  .mt-md-0 {\n    margin-top: 0 !important;\n  }\n  .mt-md-1 {\n    margin-top: 0.3125rem !important;\n  }\n  .mt-md-2 {\n    margin-top: 0.625rem !important;\n  }\n  .mt-md-3 {\n    margin-top: 1.25rem !important;\n  }\n  .mt-md-4 {\n    margin-top: 1.875rem !important;\n  }\n  .mt-md-5 {\n    margin-top: 3.75rem !important;\n  }\n  .mt-md-auto {\n    margin-top: auto !important;\n  }\n  .me-md-0 {\n    margin-right: 0 !important;\n  }\n  .me-md-1 {\n    margin-right: 0.3125rem !important;\n  }\n  .me-md-2 {\n    margin-right: 0.625rem !important;\n  }\n  .me-md-3 {\n    margin-right: 1.25rem !important;\n  }\n  .me-md-4 {\n    margin-right: 1.875rem !important;\n  }\n  .me-md-5 {\n    margin-right: 3.75rem !important;\n  }\n  .me-md-auto {\n    margin-right: auto !important;\n  }\n  .mb-md-0 {\n    margin-bottom: 0 !important;\n  }\n  .mb-md-1 {\n    margin-bottom: 0.3125rem !important;\n  }\n  .mb-md-2 {\n    margin-bottom: 0.625rem !important;\n  }\n  .mb-md-3 {\n    margin-bottom: 1.25rem !important;\n  }\n  .mb-md-4 {\n    margin-bottom: 1.875rem !important;\n  }\n  .mb-md-5 {\n    margin-bottom: 3.75rem !important;\n  }\n  .mb-md-auto {\n    margin-bottom: auto !important;\n  }\n  .ms-md-0 {\n    margin-left: 0 !important;\n  }\n  .ms-md-1 {\n    margin-left: 0.3125rem !important;\n  }\n  .ms-md-2 {\n    margin-left: 0.625rem !important;\n  }\n  .ms-md-3 {\n    margin-left: 1.25rem !important;\n  }\n  .ms-md-4 {\n    margin-left: 1.875rem !important;\n  }\n  .ms-md-5 {\n    margin-left: 3.75rem !important;\n  }\n  .ms-md-auto {\n    margin-left: auto !important;\n  }\n  .p-md-0 {\n    padding: 0 !important;\n  }\n  .p-md-1 {\n    padding: 0.3125rem !important;\n  }\n  .p-md-2 {\n    padding: 0.625rem !important;\n  }\n  .p-md-3 {\n    padding: 1.25rem !important;\n  }\n  .p-md-4 {\n    padding: 1.875rem !important;\n  }\n  .p-md-5 {\n    padding: 3.75rem !important;\n  }\n  .px-md-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important;\n  }\n  .px-md-1 {\n    padding-right: 0.3125rem !important;\n    padding-left: 0.3125rem !important;\n  }\n  .px-md-2 {\n    padding-right: 0.625rem !important;\n    padding-left: 0.625rem !important;\n  }\n  .px-md-3 {\n    padding-right: 1.25rem !important;\n    padding-left: 1.25rem !important;\n  }\n  .px-md-4 {\n    padding-right: 1.875rem !important;\n    padding-left: 1.875rem !important;\n  }\n  .px-md-5 {\n    padding-right: 3.75rem !important;\n    padding-left: 3.75rem !important;\n  }\n  .py-md-0 {\n    padding-top: 0 !important;\n    padding-bottom: 0 !important;\n  }\n  .py-md-1 {\n    padding-top: 0.3125rem !important;\n    padding-bottom: 0.3125rem !important;\n  }\n  .py-md-2 {\n    padding-top: 0.625rem !important;\n    padding-bottom: 0.625rem !important;\n  }\n  .py-md-3 {\n    padding-top: 1.25rem !important;\n    padding-bottom: 1.25rem !important;\n  }\n  .py-md-4 {\n    padding-top: 1.875rem !important;\n    padding-bottom: 1.875rem !important;\n  }\n  .py-md-5 {\n    padding-top: 3.75rem !important;\n    padding-bottom: 3.75rem !important;\n  }\n  .pt-md-0 {\n    padding-top: 0 !important;\n  }\n  .pt-md-1 {\n    padding-top: 0.3125rem !important;\n  }\n  .pt-md-2 {\n    padding-top: 0.625rem !important;\n  }\n  .pt-md-3 {\n    padding-top: 1.25rem !important;\n  }\n  .pt-md-4 {\n    padding-top: 1.875rem !important;\n  }\n  .pt-md-5 {\n    padding-top: 3.75rem !important;\n  }\n  .pe-md-0 {\n    padding-right: 0 !important;\n  }\n  .pe-md-1 {\n    padding-right: 0.3125rem !important;\n  }\n  .pe-md-2 {\n    padding-right: 0.625rem !important;\n  }\n  .pe-md-3 {\n    padding-right: 1.25rem !important;\n  }\n  .pe-md-4 {\n    padding-right: 1.875rem !important;\n  }\n  .pe-md-5 {\n    padding-right: 3.75rem !important;\n  }\n  .pb-md-0 {\n    padding-bottom: 0 !important;\n  }\n  .pb-md-1 {\n    padding-bottom: 0.3125rem !important;\n  }\n  .pb-md-2 {\n    padding-bottom: 0.625rem !important;\n  }\n  .pb-md-3 {\n    padding-bottom: 1.25rem !important;\n  }\n  .pb-md-4 {\n    padding-bottom: 1.875rem !important;\n  }\n  .pb-md-5 {\n    padding-bottom: 3.75rem !important;\n  }\n  .ps-md-0 {\n    padding-left: 0 !important;\n  }\n  .ps-md-1 {\n    padding-left: 0.3125rem !important;\n  }\n  .ps-md-2 {\n    padding-left: 0.625rem !important;\n  }\n  .ps-md-3 {\n    padding-left: 1.25rem !important;\n  }\n  .ps-md-4 {\n    padding-left: 1.875rem !important;\n  }\n  .ps-md-5 {\n    padding-left: 3.75rem !important;\n  }\n  .gap-md-0 {\n    gap: 0 !important;\n  }\n  .gap-md-1 {\n    gap: 0.3125rem !important;\n  }\n  .gap-md-2 {\n    gap: 0.625rem !important;\n  }\n  .gap-md-3 {\n    gap: 1.25rem !important;\n  }\n  .gap-md-4 {\n    gap: 1.875rem !important;\n  }\n  .gap-md-5 {\n    gap: 3.75rem !important;\n  }\n  .row-gap-md-0 {\n    row-gap: 0 !important;\n  }\n  .row-gap-md-1 {\n    row-gap: 0.3125rem !important;\n  }\n  .row-gap-md-2 {\n    row-gap: 0.625rem !important;\n  }\n  .row-gap-md-3 {\n    row-gap: 1.25rem !important;\n  }\n  .row-gap-md-4 {\n    row-gap: 1.875rem !important;\n  }\n  .row-gap-md-5 {\n    row-gap: 3.75rem !important;\n  }\n  .column-gap-md-0 {\n    column-gap: 0 !important;\n  }\n  .column-gap-md-1 {\n    column-gap: 0.3125rem !important;\n  }\n  .column-gap-md-2 {\n    column-gap: 0.625rem !important;\n  }\n  .column-gap-md-3 {\n    column-gap: 1.25rem !important;\n  }\n  .column-gap-md-4 {\n    column-gap: 1.875rem !important;\n  }\n  .column-gap-md-5 {\n    column-gap: 3.75rem !important;\n  }\n  .text-md-start {\n    text-align: left !important;\n  }\n  .text-md-end {\n    text-align: right !important;\n  }\n  .text-md-center {\n    text-align: center !important;\n  }\n}\n@media (min-width: 1024px) {\n  .float-lg-start {\n    float: left !important;\n  }\n  .float-lg-end {\n    float: right !important;\n  }\n  .float-lg-none {\n    float: none !important;\n  }\n  .object-fit-lg-contain {\n    object-fit: contain !important;\n  }\n  .object-fit-lg-cover {\n    object-fit: cover !important;\n  }\n  .object-fit-lg-fill {\n    object-fit: fill !important;\n  }\n  .object-fit-lg-scale {\n    object-fit: scale-down !important;\n  }\n  .object-fit-lg-none {\n    object-fit: none !important;\n  }\n  .d-lg-inline {\n    display: inline !important;\n  }\n  .d-lg-inline-block {\n    display: inline-block !important;\n  }\n  .d-lg-block {\n    display: block !important;\n  }\n  .d-lg-grid {\n    display: grid !important;\n  }\n  .d-lg-inline-grid {\n    display: inline-grid !important;\n  }\n  .d-lg-table {\n    display: table !important;\n  }\n  .d-lg-table-row {\n    display: table-row !important;\n  }\n  .d-lg-table-cell {\n    display: table-cell !important;\n  }\n  .d-lg-flex {\n    display: flex !important;\n  }\n  .d-lg-inline-flex {\n    display: inline-flex !important;\n  }\n  .d-lg-none {\n    display: none !important;\n  }\n  .flex-lg-fill {\n    flex: 1 1 auto !important;\n  }\n  .flex-lg-row {\n    flex-direction: row !important;\n  }\n  .flex-lg-column {\n    flex-direction: column !important;\n  }\n  .flex-lg-row-reverse {\n    flex-direction: row-reverse !important;\n  }\n  .flex-lg-column-reverse {\n    flex-direction: column-reverse !important;\n  }\n  .flex-lg-grow-0 {\n    flex-grow: 0 !important;\n  }\n  .flex-lg-grow-1 {\n    flex-grow: 1 !important;\n  }\n  .flex-lg-shrink-0 {\n    flex-shrink: 0 !important;\n  }\n  .flex-lg-shrink-1 {\n    flex-shrink: 1 !important;\n  }\n  .flex-lg-wrap {\n    flex-wrap: wrap !important;\n  }\n  .flex-lg-nowrap {\n    flex-wrap: nowrap !important;\n  }\n  .flex-lg-wrap-reverse {\n    flex-wrap: wrap-reverse !important;\n  }\n  .justify-content-lg-start {\n    justify-content: flex-start !important;\n  }\n  .justify-content-lg-end {\n    justify-content: flex-end !important;\n  }\n  .justify-content-lg-center {\n    justify-content: center !important;\n  }\n  .justify-content-lg-between {\n    justify-content: space-between !important;\n  }\n  .justify-content-lg-around {\n    justify-content: space-around !important;\n  }\n  .justify-content-lg-evenly {\n    justify-content: space-evenly !important;\n  }\n  .align-items-lg-start {\n    align-items: flex-start !important;\n  }\n  .align-items-lg-end {\n    align-items: flex-end !important;\n  }\n  .align-items-lg-center {\n    align-items: center !important;\n  }\n  .align-items-lg-baseline {\n    align-items: baseline !important;\n  }\n  .align-items-lg-stretch {\n    align-items: stretch !important;\n  }\n  .align-content-lg-start {\n    align-content: flex-start !important;\n  }\n  .align-content-lg-end {\n    align-content: flex-end !important;\n  }\n  .align-content-lg-center {\n    align-content: center !important;\n  }\n  .align-content-lg-between {\n    align-content: space-between !important;\n  }\n  .align-content-lg-around {\n    align-content: space-around !important;\n  }\n  .align-content-lg-stretch {\n    align-content: stretch !important;\n  }\n  .align-self-lg-auto {\n    align-self: auto !important;\n  }\n  .align-self-lg-start {\n    align-self: flex-start !important;\n  }\n  .align-self-lg-end {\n    align-self: flex-end !important;\n  }\n  .align-self-lg-center {\n    align-self: center !important;\n  }\n  .align-self-lg-baseline {\n    align-self: baseline !important;\n  }\n  .align-self-lg-stretch {\n    align-self: stretch !important;\n  }\n  .order-lg-first {\n    order: -1 !important;\n  }\n  .order-lg-0 {\n    order: 0 !important;\n  }\n  .order-lg-1 {\n    order: 1 !important;\n  }\n  .order-lg-2 {\n    order: 2 !important;\n  }\n  .order-lg-3 {\n    order: 3 !important;\n  }\n  .order-lg-4 {\n    order: 4 !important;\n  }\n  .order-lg-5 {\n    order: 5 !important;\n  }\n  .order-lg-last {\n    order: 6 !important;\n  }\n  .m-lg-0 {\n    margin: 0 !important;\n  }\n  .m-lg-1 {\n    margin: 0.3125rem !important;\n  }\n  .m-lg-2 {\n    margin: 0.625rem !important;\n  }\n  .m-lg-3 {\n    margin: 1.25rem !important;\n  }\n  .m-lg-4 {\n    margin: 1.875rem !important;\n  }\n  .m-lg-5 {\n    margin: 3.75rem !important;\n  }\n  .m-lg-auto {\n    margin: auto !important;\n  }\n  .mx-lg-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important;\n  }\n  .mx-lg-1 {\n    margin-right: 0.3125rem !important;\n    margin-left: 0.3125rem !important;\n  }\n  .mx-lg-2 {\n    margin-right: 0.625rem !important;\n    margin-left: 0.625rem !important;\n  }\n  .mx-lg-3 {\n    margin-right: 1.25rem !important;\n    margin-left: 1.25rem !important;\n  }\n  .mx-lg-4 {\n    margin-right: 1.875rem !important;\n    margin-left: 1.875rem !important;\n  }\n  .mx-lg-5 {\n    margin-right: 3.75rem !important;\n    margin-left: 3.75rem !important;\n  }\n  .mx-lg-auto {\n    margin-right: auto !important;\n    margin-left: auto !important;\n  }\n  .my-lg-0 {\n    margin-top: 0 !important;\n    margin-bottom: 0 !important;\n  }\n  .my-lg-1 {\n    margin-top: 0.3125rem !important;\n    margin-bottom: 0.3125rem !important;\n  }\n  .my-lg-2 {\n    margin-top: 0.625rem !important;\n    margin-bottom: 0.625rem !important;\n  }\n  .my-lg-3 {\n    margin-top: 1.25rem !important;\n    margin-bottom: 1.25rem !important;\n  }\n  .my-lg-4 {\n    margin-top: 1.875rem !important;\n    margin-bottom: 1.875rem !important;\n  }\n  .my-lg-5 {\n    margin-top: 3.75rem !important;\n    margin-bottom: 3.75rem !important;\n  }\n  .my-lg-auto {\n    margin-top: auto !important;\n    margin-bottom: auto !important;\n  }\n  .mt-lg-0 {\n    margin-top: 0 !important;\n  }\n  .mt-lg-1 {\n    margin-top: 0.3125rem !important;\n  }\n  .mt-lg-2 {\n    margin-top: 0.625rem !important;\n  }\n  .mt-lg-3 {\n    margin-top: 1.25rem !important;\n  }\n  .mt-lg-4 {\n    margin-top: 1.875rem !important;\n  }\n  .mt-lg-5 {\n    margin-top: 3.75rem !important;\n  }\n  .mt-lg-auto {\n    margin-top: auto !important;\n  }\n  .me-lg-0 {\n    margin-right: 0 !important;\n  }\n  .me-lg-1 {\n    margin-right: 0.3125rem !important;\n  }\n  .me-lg-2 {\n    margin-right: 0.625rem !important;\n  }\n  .me-lg-3 {\n    margin-right: 1.25rem !important;\n  }\n  .me-lg-4 {\n    margin-right: 1.875rem !important;\n  }\n  .me-lg-5 {\n    margin-right: 3.75rem !important;\n  }\n  .me-lg-auto {\n    margin-right: auto !important;\n  }\n  .mb-lg-0 {\n    margin-bottom: 0 !important;\n  }\n  .mb-lg-1 {\n    margin-bottom: 0.3125rem !important;\n  }\n  .mb-lg-2 {\n    margin-bottom: 0.625rem !important;\n  }\n  .mb-lg-3 {\n    margin-bottom: 1.25rem !important;\n  }\n  .mb-lg-4 {\n    margin-bottom: 1.875rem !important;\n  }\n  .mb-lg-5 {\n    margin-bottom: 3.75rem !important;\n  }\n  .mb-lg-auto {\n    margin-bottom: auto !important;\n  }\n  .ms-lg-0 {\n    margin-left: 0 !important;\n  }\n  .ms-lg-1 {\n    margin-left: 0.3125rem !important;\n  }\n  .ms-lg-2 {\n    margin-left: 0.625rem !important;\n  }\n  .ms-lg-3 {\n    margin-left: 1.25rem !important;\n  }\n  .ms-lg-4 {\n    margin-left: 1.875rem !important;\n  }\n  .ms-lg-5 {\n    margin-left: 3.75rem !important;\n  }\n  .ms-lg-auto {\n    margin-left: auto !important;\n  }\n  .p-lg-0 {\n    padding: 0 !important;\n  }\n  .p-lg-1 {\n    padding: 0.3125rem !important;\n  }\n  .p-lg-2 {\n    padding: 0.625rem !important;\n  }\n  .p-lg-3 {\n    padding: 1.25rem !important;\n  }\n  .p-lg-4 {\n    padding: 1.875rem !important;\n  }\n  .p-lg-5 {\n    padding: 3.75rem !important;\n  }\n  .px-lg-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important;\n  }\n  .px-lg-1 {\n    padding-right: 0.3125rem !important;\n    padding-left: 0.3125rem !important;\n  }\n  .px-lg-2 {\n    padding-right: 0.625rem !important;\n    padding-left: 0.625rem !important;\n  }\n  .px-lg-3 {\n    padding-right: 1.25rem !important;\n    padding-left: 1.25rem !important;\n  }\n  .px-lg-4 {\n    padding-right: 1.875rem !important;\n    padding-left: 1.875rem !important;\n  }\n  .px-lg-5 {\n    padding-right: 3.75rem !important;\n    padding-left: 3.75rem !important;\n  }\n  .py-lg-0 {\n    padding-top: 0 !important;\n    padding-bottom: 0 !important;\n  }\n  .py-lg-1 {\n    padding-top: 0.3125rem !important;\n    padding-bottom: 0.3125rem !important;\n  }\n  .py-lg-2 {\n    padding-top: 0.625rem !important;\n    padding-bottom: 0.625rem !important;\n  }\n  .py-lg-3 {\n    padding-top: 1.25rem !important;\n    padding-bottom: 1.25rem !important;\n  }\n  .py-lg-4 {\n    padding-top: 1.875rem !important;\n    padding-bottom: 1.875rem !important;\n  }\n  .py-lg-5 {\n    padding-top: 3.75rem !important;\n    padding-bottom: 3.75rem !important;\n  }\n  .pt-lg-0 {\n    padding-top: 0 !important;\n  }\n  .pt-lg-1 {\n    padding-top: 0.3125rem !important;\n  }\n  .pt-lg-2 {\n    padding-top: 0.625rem !important;\n  }\n  .pt-lg-3 {\n    padding-top: 1.25rem !important;\n  }\n  .pt-lg-4 {\n    padding-top: 1.875rem !important;\n  }\n  .pt-lg-5 {\n    padding-top: 3.75rem !important;\n  }\n  .pe-lg-0 {\n    padding-right: 0 !important;\n  }\n  .pe-lg-1 {\n    padding-right: 0.3125rem !important;\n  }\n  .pe-lg-2 {\n    padding-right: 0.625rem !important;\n  }\n  .pe-lg-3 {\n    padding-right: 1.25rem !important;\n  }\n  .pe-lg-4 {\n    padding-right: 1.875rem !important;\n  }\n  .pe-lg-5 {\n    padding-right: 3.75rem !important;\n  }\n  .pb-lg-0 {\n    padding-bottom: 0 !important;\n  }\n  .pb-lg-1 {\n    padding-bottom: 0.3125rem !important;\n  }\n  .pb-lg-2 {\n    padding-bottom: 0.625rem !important;\n  }\n  .pb-lg-3 {\n    padding-bottom: 1.25rem !important;\n  }\n  .pb-lg-4 {\n    padding-bottom: 1.875rem !important;\n  }\n  .pb-lg-5 {\n    padding-bottom: 3.75rem !important;\n  }\n  .ps-lg-0 {\n    padding-left: 0 !important;\n  }\n  .ps-lg-1 {\n    padding-left: 0.3125rem !important;\n  }\n  .ps-lg-2 {\n    padding-left: 0.625rem !important;\n  }\n  .ps-lg-3 {\n    padding-left: 1.25rem !important;\n  }\n  .ps-lg-4 {\n    padding-left: 1.875rem !important;\n  }\n  .ps-lg-5 {\n    padding-left: 3.75rem !important;\n  }\n  .gap-lg-0 {\n    gap: 0 !important;\n  }\n  .gap-lg-1 {\n    gap: 0.3125rem !important;\n  }\n  .gap-lg-2 {\n    gap: 0.625rem !important;\n  }\n  .gap-lg-3 {\n    gap: 1.25rem !important;\n  }\n  .gap-lg-4 {\n    gap: 1.875rem !important;\n  }\n  .gap-lg-5 {\n    gap: 3.75rem !important;\n  }\n  .row-gap-lg-0 {\n    row-gap: 0 !important;\n  }\n  .row-gap-lg-1 {\n    row-gap: 0.3125rem !important;\n  }\n  .row-gap-lg-2 {\n    row-gap: 0.625rem !important;\n  }\n  .row-gap-lg-3 {\n    row-gap: 1.25rem !important;\n  }\n  .row-gap-lg-4 {\n    row-gap: 1.875rem !important;\n  }\n  .row-gap-lg-5 {\n    row-gap: 3.75rem !important;\n  }\n  .column-gap-lg-0 {\n    column-gap: 0 !important;\n  }\n  .column-gap-lg-1 {\n    column-gap: 0.3125rem !important;\n  }\n  .column-gap-lg-2 {\n    column-gap: 0.625rem !important;\n  }\n  .column-gap-lg-3 {\n    column-gap: 1.25rem !important;\n  }\n  .column-gap-lg-4 {\n    column-gap: 1.875rem !important;\n  }\n  .column-gap-lg-5 {\n    column-gap: 3.75rem !important;\n  }\n  .text-lg-start {\n    text-align: left !important;\n  }\n  .text-lg-end {\n    text-align: right !important;\n  }\n  .text-lg-center {\n    text-align: center !important;\n  }\n}\n@media (min-width: 1280px) {\n  .float-xl-start {\n    float: left !important;\n  }\n  .float-xl-end {\n    float: right !important;\n  }\n  .float-xl-none {\n    float: none !important;\n  }\n  .object-fit-xl-contain {\n    object-fit: contain !important;\n  }\n  .object-fit-xl-cover {\n    object-fit: cover !important;\n  }\n  .object-fit-xl-fill {\n    object-fit: fill !important;\n  }\n  .object-fit-xl-scale {\n    object-fit: scale-down !important;\n  }\n  .object-fit-xl-none {\n    object-fit: none !important;\n  }\n  .d-xl-inline {\n    display: inline !important;\n  }\n  .d-xl-inline-block {\n    display: inline-block !important;\n  }\n  .d-xl-block {\n    display: block !important;\n  }\n  .d-xl-grid {\n    display: grid !important;\n  }\n  .d-xl-inline-grid {\n    display: inline-grid !important;\n  }\n  .d-xl-table {\n    display: table !important;\n  }\n  .d-xl-table-row {\n    display: table-row !important;\n  }\n  .d-xl-table-cell {\n    display: table-cell !important;\n  }\n  .d-xl-flex {\n    display: flex !important;\n  }\n  .d-xl-inline-flex {\n    display: inline-flex !important;\n  }\n  .d-xl-none {\n    display: none !important;\n  }\n  .flex-xl-fill {\n    flex: 1 1 auto !important;\n  }\n  .flex-xl-row {\n    flex-direction: row !important;\n  }\n  .flex-xl-column {\n    flex-direction: column !important;\n  }\n  .flex-xl-row-reverse {\n    flex-direction: row-reverse !important;\n  }\n  .flex-xl-column-reverse {\n    flex-direction: column-reverse !important;\n  }\n  .flex-xl-grow-0 {\n    flex-grow: 0 !important;\n  }\n  .flex-xl-grow-1 {\n    flex-grow: 1 !important;\n  }\n  .flex-xl-shrink-0 {\n    flex-shrink: 0 !important;\n  }\n  .flex-xl-shrink-1 {\n    flex-shrink: 1 !important;\n  }\n  .flex-xl-wrap {\n    flex-wrap: wrap !important;\n  }\n  .flex-xl-nowrap {\n    flex-wrap: nowrap !important;\n  }\n  .flex-xl-wrap-reverse {\n    flex-wrap: wrap-reverse !important;\n  }\n  .justify-content-xl-start {\n    justify-content: flex-start !important;\n  }\n  .justify-content-xl-end {\n    justify-content: flex-end !important;\n  }\n  .justify-content-xl-center {\n    justify-content: center !important;\n  }\n  .justify-content-xl-between {\n    justify-content: space-between !important;\n  }\n  .justify-content-xl-around {\n    justify-content: space-around !important;\n  }\n  .justify-content-xl-evenly {\n    justify-content: space-evenly !important;\n  }\n  .align-items-xl-start {\n    align-items: flex-start !important;\n  }\n  .align-items-xl-end {\n    align-items: flex-end !important;\n  }\n  .align-items-xl-center {\n    align-items: center !important;\n  }\n  .align-items-xl-baseline {\n    align-items: baseline !important;\n  }\n  .align-items-xl-stretch {\n    align-items: stretch !important;\n  }\n  .align-content-xl-start {\n    align-content: flex-start !important;\n  }\n  .align-content-xl-end {\n    align-content: flex-end !important;\n  }\n  .align-content-xl-center {\n    align-content: center !important;\n  }\n  .align-content-xl-between {\n    align-content: space-between !important;\n  }\n  .align-content-xl-around {\n    align-content: space-around !important;\n  }\n  .align-content-xl-stretch {\n    align-content: stretch !important;\n  }\n  .align-self-xl-auto {\n    align-self: auto !important;\n  }\n  .align-self-xl-start {\n    align-self: flex-start !important;\n  }\n  .align-self-xl-end {\n    align-self: flex-end !important;\n  }\n  .align-self-xl-center {\n    align-self: center !important;\n  }\n  .align-self-xl-baseline {\n    align-self: baseline !important;\n  }\n  .align-self-xl-stretch {\n    align-self: stretch !important;\n  }\n  .order-xl-first {\n    order: -1 !important;\n  }\n  .order-xl-0 {\n    order: 0 !important;\n  }\n  .order-xl-1 {\n    order: 1 !important;\n  }\n  .order-xl-2 {\n    order: 2 !important;\n  }\n  .order-xl-3 {\n    order: 3 !important;\n  }\n  .order-xl-4 {\n    order: 4 !important;\n  }\n  .order-xl-5 {\n    order: 5 !important;\n  }\n  .order-xl-last {\n    order: 6 !important;\n  }\n  .m-xl-0 {\n    margin: 0 !important;\n  }\n  .m-xl-1 {\n    margin: 0.3125rem !important;\n  }\n  .m-xl-2 {\n    margin: 0.625rem !important;\n  }\n  .m-xl-3 {\n    margin: 1.25rem !important;\n  }\n  .m-xl-4 {\n    margin: 1.875rem !important;\n  }\n  .m-xl-5 {\n    margin: 3.75rem !important;\n  }\n  .m-xl-auto {\n    margin: auto !important;\n  }\n  .mx-xl-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important;\n  }\n  .mx-xl-1 {\n    margin-right: 0.3125rem !important;\n    margin-left: 0.3125rem !important;\n  }\n  .mx-xl-2 {\n    margin-right: 0.625rem !important;\n    margin-left: 0.625rem !important;\n  }\n  .mx-xl-3 {\n    margin-right: 1.25rem !important;\n    margin-left: 1.25rem !important;\n  }\n  .mx-xl-4 {\n    margin-right: 1.875rem !important;\n    margin-left: 1.875rem !important;\n  }\n  .mx-xl-5 {\n    margin-right: 3.75rem !important;\n    margin-left: 3.75rem !important;\n  }\n  .mx-xl-auto {\n    margin-right: auto !important;\n    margin-left: auto !important;\n  }\n  .my-xl-0 {\n    margin-top: 0 !important;\n    margin-bottom: 0 !important;\n  }\n  .my-xl-1 {\n    margin-top: 0.3125rem !important;\n    margin-bottom: 0.3125rem !important;\n  }\n  .my-xl-2 {\n    margin-top: 0.625rem !important;\n    margin-bottom: 0.625rem !important;\n  }\n  .my-xl-3 {\n    margin-top: 1.25rem !important;\n    margin-bottom: 1.25rem !important;\n  }\n  .my-xl-4 {\n    margin-top: 1.875rem !important;\n    margin-bottom: 1.875rem !important;\n  }\n  .my-xl-5 {\n    margin-top: 3.75rem !important;\n    margin-bottom: 3.75rem !important;\n  }\n  .my-xl-auto {\n    margin-top: auto !important;\n    margin-bottom: auto !important;\n  }\n  .mt-xl-0 {\n    margin-top: 0 !important;\n  }\n  .mt-xl-1 {\n    margin-top: 0.3125rem !important;\n  }\n  .mt-xl-2 {\n    margin-top: 0.625rem !important;\n  }\n  .mt-xl-3 {\n    margin-top: 1.25rem !important;\n  }\n  .mt-xl-4 {\n    margin-top: 1.875rem !important;\n  }\n  .mt-xl-5 {\n    margin-top: 3.75rem !important;\n  }\n  .mt-xl-auto {\n    margin-top: auto !important;\n  }\n  .me-xl-0 {\n    margin-right: 0 !important;\n  }\n  .me-xl-1 {\n    margin-right: 0.3125rem !important;\n  }\n  .me-xl-2 {\n    margin-right: 0.625rem !important;\n  }\n  .me-xl-3 {\n    margin-right: 1.25rem !important;\n  }\n  .me-xl-4 {\n    margin-right: 1.875rem !important;\n  }\n  .me-xl-5 {\n    margin-right: 3.75rem !important;\n  }\n  .me-xl-auto {\n    margin-right: auto !important;\n  }\n  .mb-xl-0 {\n    margin-bottom: 0 !important;\n  }\n  .mb-xl-1 {\n    margin-bottom: 0.3125rem !important;\n  }\n  .mb-xl-2 {\n    margin-bottom: 0.625rem !important;\n  }\n  .mb-xl-3 {\n    margin-bottom: 1.25rem !important;\n  }\n  .mb-xl-4 {\n    margin-bottom: 1.875rem !important;\n  }\n  .mb-xl-5 {\n    margin-bottom: 3.75rem !important;\n  }\n  .mb-xl-auto {\n    margin-bottom: auto !important;\n  }\n  .ms-xl-0 {\n    margin-left: 0 !important;\n  }\n  .ms-xl-1 {\n    margin-left: 0.3125rem !important;\n  }\n  .ms-xl-2 {\n    margin-left: 0.625rem !important;\n  }\n  .ms-xl-3 {\n    margin-left: 1.25rem !important;\n  }\n  .ms-xl-4 {\n    margin-left: 1.875rem !important;\n  }\n  .ms-xl-5 {\n    margin-left: 3.75rem !important;\n  }\n  .ms-xl-auto {\n    margin-left: auto !important;\n  }\n  .p-xl-0 {\n    padding: 0 !important;\n  }\n  .p-xl-1 {\n    padding: 0.3125rem !important;\n  }\n  .p-xl-2 {\n    padding: 0.625rem !important;\n  }\n  .p-xl-3 {\n    padding: 1.25rem !important;\n  }\n  .p-xl-4 {\n    padding: 1.875rem !important;\n  }\n  .p-xl-5 {\n    padding: 3.75rem !important;\n  }\n  .px-xl-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important;\n  }\n  .px-xl-1 {\n    padding-right: 0.3125rem !important;\n    padding-left: 0.3125rem !important;\n  }\n  .px-xl-2 {\n    padding-right: 0.625rem !important;\n    padding-left: 0.625rem !important;\n  }\n  .px-xl-3 {\n    padding-right: 1.25rem !important;\n    padding-left: 1.25rem !important;\n  }\n  .px-xl-4 {\n    padding-right: 1.875rem !important;\n    padding-left: 1.875rem !important;\n  }\n  .px-xl-5 {\n    padding-right: 3.75rem !important;\n    padding-left: 3.75rem !important;\n  }\n  .py-xl-0 {\n    padding-top: 0 !important;\n    padding-bottom: 0 !important;\n  }\n  .py-xl-1 {\n    padding-top: 0.3125rem !important;\n    padding-bottom: 0.3125rem !important;\n  }\n  .py-xl-2 {\n    padding-top: 0.625rem !important;\n    padding-bottom: 0.625rem !important;\n  }\n  .py-xl-3 {\n    padding-top: 1.25rem !important;\n    padding-bottom: 1.25rem !important;\n  }\n  .py-xl-4 {\n    padding-top: 1.875rem !important;\n    padding-bottom: 1.875rem !important;\n  }\n  .py-xl-5 {\n    padding-top: 3.75rem !important;\n    padding-bottom: 3.75rem !important;\n  }\n  .pt-xl-0 {\n    padding-top: 0 !important;\n  }\n  .pt-xl-1 {\n    padding-top: 0.3125rem !important;\n  }\n  .pt-xl-2 {\n    padding-top: 0.625rem !important;\n  }\n  .pt-xl-3 {\n    padding-top: 1.25rem !important;\n  }\n  .pt-xl-4 {\n    padding-top: 1.875rem !important;\n  }\n  .pt-xl-5 {\n    padding-top: 3.75rem !important;\n  }\n  .pe-xl-0 {\n    padding-right: 0 !important;\n  }\n  .pe-xl-1 {\n    padding-right: 0.3125rem !important;\n  }\n  .pe-xl-2 {\n    padding-right: 0.625rem !important;\n  }\n  .pe-xl-3 {\n    padding-right: 1.25rem !important;\n  }\n  .pe-xl-4 {\n    padding-right: 1.875rem !important;\n  }\n  .pe-xl-5 {\n    padding-right: 3.75rem !important;\n  }\n  .pb-xl-0 {\n    padding-bottom: 0 !important;\n  }\n  .pb-xl-1 {\n    padding-bottom: 0.3125rem !important;\n  }\n  .pb-xl-2 {\n    padding-bottom: 0.625rem !important;\n  }\n  .pb-xl-3 {\n    padding-bottom: 1.25rem !important;\n  }\n  .pb-xl-4 {\n    padding-bottom: 1.875rem !important;\n  }\n  .pb-xl-5 {\n    padding-bottom: 3.75rem !important;\n  }\n  .ps-xl-0 {\n    padding-left: 0 !important;\n  }\n  .ps-xl-1 {\n    padding-left: 0.3125rem !important;\n  }\n  .ps-xl-2 {\n    padding-left: 0.625rem !important;\n  }\n  .ps-xl-3 {\n    padding-left: 1.25rem !important;\n  }\n  .ps-xl-4 {\n    padding-left: 1.875rem !important;\n  }\n  .ps-xl-5 {\n    padding-left: 3.75rem !important;\n  }\n  .gap-xl-0 {\n    gap: 0 !important;\n  }\n  .gap-xl-1 {\n    gap: 0.3125rem !important;\n  }\n  .gap-xl-2 {\n    gap: 0.625rem !important;\n  }\n  .gap-xl-3 {\n    gap: 1.25rem !important;\n  }\n  .gap-xl-4 {\n    gap: 1.875rem !important;\n  }\n  .gap-xl-5 {\n    gap: 3.75rem !important;\n  }\n  .row-gap-xl-0 {\n    row-gap: 0 !important;\n  }\n  .row-gap-xl-1 {\n    row-gap: 0.3125rem !important;\n  }\n  .row-gap-xl-2 {\n    row-gap: 0.625rem !important;\n  }\n  .row-gap-xl-3 {\n    row-gap: 1.25rem !important;\n  }\n  .row-gap-xl-4 {\n    row-gap: 1.875rem !important;\n  }\n  .row-gap-xl-5 {\n    row-gap: 3.75rem !important;\n  }\n  .column-gap-xl-0 {\n    column-gap: 0 !important;\n  }\n  .column-gap-xl-1 {\n    column-gap: 0.3125rem !important;\n  }\n  .column-gap-xl-2 {\n    column-gap: 0.625rem !important;\n  }\n  .column-gap-xl-3 {\n    column-gap: 1.25rem !important;\n  }\n  .column-gap-xl-4 {\n    column-gap: 1.875rem !important;\n  }\n  .column-gap-xl-5 {\n    column-gap: 3.75rem !important;\n  }\n  .text-xl-start {\n    text-align: left !important;\n  }\n  .text-xl-end {\n    text-align: right !important;\n  }\n  .text-xl-center {\n    text-align: center !important;\n  }\n}\n@media (min-width: 1440px) {\n  .float-xxl-start {\n    float: left !important;\n  }\n  .float-xxl-end {\n    float: right !important;\n  }\n  .float-xxl-none {\n    float: none !important;\n  }\n  .object-fit-xxl-contain {\n    object-fit: contain !important;\n  }\n  .object-fit-xxl-cover {\n    object-fit: cover !important;\n  }\n  .object-fit-xxl-fill {\n    object-fit: fill !important;\n  }\n  .object-fit-xxl-scale {\n    object-fit: scale-down !important;\n  }\n  .object-fit-xxl-none {\n    object-fit: none !important;\n  }\n  .d-xxl-inline {\n    display: inline !important;\n  }\n  .d-xxl-inline-block {\n    display: inline-block !important;\n  }\n  .d-xxl-block {\n    display: block !important;\n  }\n  .d-xxl-grid {\n    display: grid !important;\n  }\n  .d-xxl-inline-grid {\n    display: inline-grid !important;\n  }\n  .d-xxl-table {\n    display: table !important;\n  }\n  .d-xxl-table-row {\n    display: table-row !important;\n  }\n  .d-xxl-table-cell {\n    display: table-cell !important;\n  }\n  .d-xxl-flex {\n    display: flex !important;\n  }\n  .d-xxl-inline-flex {\n    display: inline-flex !important;\n  }\n  .d-xxl-none {\n    display: none !important;\n  }\n  .flex-xxl-fill {\n    flex: 1 1 auto !important;\n  }\n  .flex-xxl-row {\n    flex-direction: row !important;\n  }\n  .flex-xxl-column {\n    flex-direction: column !important;\n  }\n  .flex-xxl-row-reverse {\n    flex-direction: row-reverse !important;\n  }\n  .flex-xxl-column-reverse {\n    flex-direction: column-reverse !important;\n  }\n  .flex-xxl-grow-0 {\n    flex-grow: 0 !important;\n  }\n  .flex-xxl-grow-1 {\n    flex-grow: 1 !important;\n  }\n  .flex-xxl-shrink-0 {\n    flex-shrink: 0 !important;\n  }\n  .flex-xxl-shrink-1 {\n    flex-shrink: 1 !important;\n  }\n  .flex-xxl-wrap {\n    flex-wrap: wrap !important;\n  }\n  .flex-xxl-nowrap {\n    flex-wrap: nowrap !important;\n  }\n  .flex-xxl-wrap-reverse {\n    flex-wrap: wrap-reverse !important;\n  }\n  .justify-content-xxl-start {\n    justify-content: flex-start !important;\n  }\n  .justify-content-xxl-end {\n    justify-content: flex-end !important;\n  }\n  .justify-content-xxl-center {\n    justify-content: center !important;\n  }\n  .justify-content-xxl-between {\n    justify-content: space-between !important;\n  }\n  .justify-content-xxl-around {\n    justify-content: space-around !important;\n  }\n  .justify-content-xxl-evenly {\n    justify-content: space-evenly !important;\n  }\n  .align-items-xxl-start {\n    align-items: flex-start !important;\n  }\n  .align-items-xxl-end {\n    align-items: flex-end !important;\n  }\n  .align-items-xxl-center {\n    align-items: center !important;\n  }\n  .align-items-xxl-baseline {\n    align-items: baseline !important;\n  }\n  .align-items-xxl-stretch {\n    align-items: stretch !important;\n  }\n  .align-content-xxl-start {\n    align-content: flex-start !important;\n  }\n  .align-content-xxl-end {\n    align-content: flex-end !important;\n  }\n  .align-content-xxl-center {\n    align-content: center !important;\n  }\n  .align-content-xxl-between {\n    align-content: space-between !important;\n  }\n  .align-content-xxl-around {\n    align-content: space-around !important;\n  }\n  .align-content-xxl-stretch {\n    align-content: stretch !important;\n  }\n  .align-self-xxl-auto {\n    align-self: auto !important;\n  }\n  .align-self-xxl-start {\n    align-self: flex-start !important;\n  }\n  .align-self-xxl-end {\n    align-self: flex-end !important;\n  }\n  .align-self-xxl-center {\n    align-self: center !important;\n  }\n  .align-self-xxl-baseline {\n    align-self: baseline !important;\n  }\n  .align-self-xxl-stretch {\n    align-self: stretch !important;\n  }\n  .order-xxl-first {\n    order: -1 !important;\n  }\n  .order-xxl-0 {\n    order: 0 !important;\n  }\n  .order-xxl-1 {\n    order: 1 !important;\n  }\n  .order-xxl-2 {\n    order: 2 !important;\n  }\n  .order-xxl-3 {\n    order: 3 !important;\n  }\n  .order-xxl-4 {\n    order: 4 !important;\n  }\n  .order-xxl-5 {\n    order: 5 !important;\n  }\n  .order-xxl-last {\n    order: 6 !important;\n  }\n  .m-xxl-0 {\n    margin: 0 !important;\n  }\n  .m-xxl-1 {\n    margin: 0.3125rem !important;\n  }\n  .m-xxl-2 {\n    margin: 0.625rem !important;\n  }\n  .m-xxl-3 {\n    margin: 1.25rem !important;\n  }\n  .m-xxl-4 {\n    margin: 1.875rem !important;\n  }\n  .m-xxl-5 {\n    margin: 3.75rem !important;\n  }\n  .m-xxl-auto {\n    margin: auto !important;\n  }\n  .mx-xxl-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important;\n  }\n  .mx-xxl-1 {\n    margin-right: 0.3125rem !important;\n    margin-left: 0.3125rem !important;\n  }\n  .mx-xxl-2 {\n    margin-right: 0.625rem !important;\n    margin-left: 0.625rem !important;\n  }\n  .mx-xxl-3 {\n    margin-right: 1.25rem !important;\n    margin-left: 1.25rem !important;\n  }\n  .mx-xxl-4 {\n    margin-right: 1.875rem !important;\n    margin-left: 1.875rem !important;\n  }\n  .mx-xxl-5 {\n    margin-right: 3.75rem !important;\n    margin-left: 3.75rem !important;\n  }\n  .mx-xxl-auto {\n    margin-right: auto !important;\n    margin-left: auto !important;\n  }\n  .my-xxl-0 {\n    margin-top: 0 !important;\n    margin-bottom: 0 !important;\n  }\n  .my-xxl-1 {\n    margin-top: 0.3125rem !important;\n    margin-bottom: 0.3125rem !important;\n  }\n  .my-xxl-2 {\n    margin-top: 0.625rem !important;\n    margin-bottom: 0.625rem !important;\n  }\n  .my-xxl-3 {\n    margin-top: 1.25rem !important;\n    margin-bottom: 1.25rem !important;\n  }\n  .my-xxl-4 {\n    margin-top: 1.875rem !important;\n    margin-bottom: 1.875rem !important;\n  }\n  .my-xxl-5 {\n    margin-top: 3.75rem !important;\n    margin-bottom: 3.75rem !important;\n  }\n  .my-xxl-auto {\n    margin-top: auto !important;\n    margin-bottom: auto !important;\n  }\n  .mt-xxl-0 {\n    margin-top: 0 !important;\n  }\n  .mt-xxl-1 {\n    margin-top: 0.3125rem !important;\n  }\n  .mt-xxl-2 {\n    margin-top: 0.625rem !important;\n  }\n  .mt-xxl-3 {\n    margin-top: 1.25rem !important;\n  }\n  .mt-xxl-4 {\n    margin-top: 1.875rem !important;\n  }\n  .mt-xxl-5 {\n    margin-top: 3.75rem !important;\n  }\n  .mt-xxl-auto {\n    margin-top: auto !important;\n  }\n  .me-xxl-0 {\n    margin-right: 0 !important;\n  }\n  .me-xxl-1 {\n    margin-right: 0.3125rem !important;\n  }\n  .me-xxl-2 {\n    margin-right: 0.625rem !important;\n  }\n  .me-xxl-3 {\n    margin-right: 1.25rem !important;\n  }\n  .me-xxl-4 {\n    margin-right: 1.875rem !important;\n  }\n  .me-xxl-5 {\n    margin-right: 3.75rem !important;\n  }\n  .me-xxl-auto {\n    margin-right: auto !important;\n  }\n  .mb-xxl-0 {\n    margin-bottom: 0 !important;\n  }\n  .mb-xxl-1 {\n    margin-bottom: 0.3125rem !important;\n  }\n  .mb-xxl-2 {\n    margin-bottom: 0.625rem !important;\n  }\n  .mb-xxl-3 {\n    margin-bottom: 1.25rem !important;\n  }\n  .mb-xxl-4 {\n    margin-bottom: 1.875rem !important;\n  }\n  .mb-xxl-5 {\n    margin-bottom: 3.75rem !important;\n  }\n  .mb-xxl-auto {\n    margin-bottom: auto !important;\n  }\n  .ms-xxl-0 {\n    margin-left: 0 !important;\n  }\n  .ms-xxl-1 {\n    margin-left: 0.3125rem !important;\n  }\n  .ms-xxl-2 {\n    margin-left: 0.625rem !important;\n  }\n  .ms-xxl-3 {\n    margin-left: 1.25rem !important;\n  }\n  .ms-xxl-4 {\n    margin-left: 1.875rem !important;\n  }\n  .ms-xxl-5 {\n    margin-left: 3.75rem !important;\n  }\n  .ms-xxl-auto {\n    margin-left: auto !important;\n  }\n  .p-xxl-0 {\n    padding: 0 !important;\n  }\n  .p-xxl-1 {\n    padding: 0.3125rem !important;\n  }\n  .p-xxl-2 {\n    padding: 0.625rem !important;\n  }\n  .p-xxl-3 {\n    padding: 1.25rem !important;\n  }\n  .p-xxl-4 {\n    padding: 1.875rem !important;\n  }\n  .p-xxl-5 {\n    padding: 3.75rem !important;\n  }\n  .px-xxl-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important;\n  }\n  .px-xxl-1 {\n    padding-right: 0.3125rem !important;\n    padding-left: 0.3125rem !important;\n  }\n  .px-xxl-2 {\n    padding-right: 0.625rem !important;\n    padding-left: 0.625rem !important;\n  }\n  .px-xxl-3 {\n    padding-right: 1.25rem !important;\n    padding-left: 1.25rem !important;\n  }\n  .px-xxl-4 {\n    padding-right: 1.875rem !important;\n    padding-left: 1.875rem !important;\n  }\n  .px-xxl-5 {\n    padding-right: 3.75rem !important;\n    padding-left: 3.75rem !important;\n  }\n  .py-xxl-0 {\n    padding-top: 0 !important;\n    padding-bottom: 0 !important;\n  }\n  .py-xxl-1 {\n    padding-top: 0.3125rem !important;\n    padding-bottom: 0.3125rem !important;\n  }\n  .py-xxl-2 {\n    padding-top: 0.625rem !important;\n    padding-bottom: 0.625rem !important;\n  }\n  .py-xxl-3 {\n    padding-top: 1.25rem !important;\n    padding-bottom: 1.25rem !important;\n  }\n  .py-xxl-4 {\n    padding-top: 1.875rem !important;\n    padding-bottom: 1.875rem !important;\n  }\n  .py-xxl-5 {\n    padding-top: 3.75rem !important;\n    padding-bottom: 3.75rem !important;\n  }\n  .pt-xxl-0 {\n    padding-top: 0 !important;\n  }\n  .pt-xxl-1 {\n    padding-top: 0.3125rem !important;\n  }\n  .pt-xxl-2 {\n    padding-top: 0.625rem !important;\n  }\n  .pt-xxl-3 {\n    padding-top: 1.25rem !important;\n  }\n  .pt-xxl-4 {\n    padding-top: 1.875rem !important;\n  }\n  .pt-xxl-5 {\n    padding-top: 3.75rem !important;\n  }\n  .pe-xxl-0 {\n    padding-right: 0 !important;\n  }\n  .pe-xxl-1 {\n    padding-right: 0.3125rem !important;\n  }\n  .pe-xxl-2 {\n    padding-right: 0.625rem !important;\n  }\n  .pe-xxl-3 {\n    padding-right: 1.25rem !important;\n  }\n  .pe-xxl-4 {\n    padding-right: 1.875rem !important;\n  }\n  .pe-xxl-5 {\n    padding-right: 3.75rem !important;\n  }\n  .pb-xxl-0 {\n    padding-bottom: 0 !important;\n  }\n  .pb-xxl-1 {\n    padding-bottom: 0.3125rem !important;\n  }\n  .pb-xxl-2 {\n    padding-bottom: 0.625rem !important;\n  }\n  .pb-xxl-3 {\n    padding-bottom: 1.25rem !important;\n  }\n  .pb-xxl-4 {\n    padding-bottom: 1.875rem !important;\n  }\n  .pb-xxl-5 {\n    padding-bottom: 3.75rem !important;\n  }\n  .ps-xxl-0 {\n    padding-left: 0 !important;\n  }\n  .ps-xxl-1 {\n    padding-left: 0.3125rem !important;\n  }\n  .ps-xxl-2 {\n    padding-left: 0.625rem !important;\n  }\n  .ps-xxl-3 {\n    padding-left: 1.25rem !important;\n  }\n  .ps-xxl-4 {\n    padding-left: 1.875rem !important;\n  }\n  .ps-xxl-5 {\n    padding-left: 3.75rem !important;\n  }\n  .gap-xxl-0 {\n    gap: 0 !important;\n  }\n  .gap-xxl-1 {\n    gap: 0.3125rem !important;\n  }\n  .gap-xxl-2 {\n    gap: 0.625rem !important;\n  }\n  .gap-xxl-3 {\n    gap: 1.25rem !important;\n  }\n  .gap-xxl-4 {\n    gap: 1.875rem !important;\n  }\n  .gap-xxl-5 {\n    gap: 3.75rem !important;\n  }\n  .row-gap-xxl-0 {\n    row-gap: 0 !important;\n  }\n  .row-gap-xxl-1 {\n    row-gap: 0.3125rem !important;\n  }\n  .row-gap-xxl-2 {\n    row-gap: 0.625rem !important;\n  }\n  .row-gap-xxl-3 {\n    row-gap: 1.25rem !important;\n  }\n  .row-gap-xxl-4 {\n    row-gap: 1.875rem !important;\n  }\n  .row-gap-xxl-5 {\n    row-gap: 3.75rem !important;\n  }\n  .column-gap-xxl-0 {\n    column-gap: 0 !important;\n  }\n  .column-gap-xxl-1 {\n    column-gap: 0.3125rem !important;\n  }\n  .column-gap-xxl-2 {\n    column-gap: 0.625rem !important;\n  }\n  .column-gap-xxl-3 {\n    column-gap: 1.25rem !important;\n  }\n  .column-gap-xxl-4 {\n    column-gap: 1.875rem !important;\n  }\n  .column-gap-xxl-5 {\n    column-gap: 3.75rem !important;\n  }\n  .text-xxl-start {\n    text-align: left !important;\n  }\n  .text-xxl-end {\n    text-align: right !important;\n  }\n  .text-xxl-center {\n    text-align: center !important;\n  }\n}\n@media print {\n  .d-print-inline {\n    display: inline !important;\n  }\n  .d-print-inline-block {\n    display: inline-block !important;\n  }\n  .d-print-block {\n    display: block !important;\n  }\n  .d-print-grid {\n    display: grid !important;\n  }\n  .d-print-inline-grid {\n    display: inline-grid !important;\n  }\n  .d-print-table {\n    display: table !important;\n  }\n  .d-print-table-row {\n    display: table-row !important;\n  }\n  .d-print-table-cell {\n    display: table-cell !important;\n  }\n  .d-print-flex {\n    display: flex !important;\n  }\n  .d-print-inline-flex {\n    display: inline-flex !important;\n  }\n  .d-print-none {\n    display: none !important;\n  }\n}\n\n/*# sourceMappingURL=boosted-utilities.css.map */\n", "// stylelint-disable scss/dimension-no-non-numeric-values\n\n// SCSS RFS mixin\n//\n// Automated responsive values for font sizes, paddings, margins and much more\n//\n// Licensed under MIT (https://github.com/twbs/rfs/blob/main/LICENSE)\n\n// Configuration\n\n// Base value\n$rfs-base-value: 1.25rem !default;\n$rfs-unit: rem !default;\n\n@if $rfs-unit != rem and $rfs-unit != px {\n  @error \"`#{$rfs-unit}` is not a valid unit for $rfs-unit. Use `px` or `rem`.\";\n}\n\n// Breakpoint at where values start decreasing if screen width is smaller\n$rfs-breakpoint: 1200px !default;\n$rfs-breakpoint-unit: px !default;\n\n@if $rfs-breakpoint-unit != px and $rfs-breakpoint-unit != em and $rfs-breakpoint-unit != rem {\n  @error \"`#{$rfs-breakpoint-unit}` is not a valid unit for $rfs-breakpoint-unit. Use `px`, `em` or `rem`.\";\n}\n\n// Resize values based on screen height and width\n$rfs-two-dimensional: false !default;\n\n// Factor of decrease\n$rfs-factor: 10 !default;\n\n@if type-of($rfs-factor) != number or $rfs-factor <= 1 {\n  @error \"`#{$rfs-factor}` is not a valid  $rfs-factor, it must be greater than 1.\";\n}\n\n// Mode. Possibilities: \"min-media-query\", \"max-media-query\"\n$rfs-mode: min-media-query !default;\n\n// Generate enable or disable classes. Possibilities: false, \"enable\" or \"disable\"\n$rfs-class: false !default;\n\n// 1 rem = $rfs-rem-value px\n$rfs-rem-value: 16 !default;\n\n// Safari iframe resize bug: https://github.com/twbs/rfs/issues/14\n$rfs-safari-iframe-resize-bug-fix: false !default;\n\n// Disable RFS by setting $enable-rfs to false\n$enable-rfs: true !default;\n\n// Cache $rfs-base-value unit\n$rfs-base-value-unit: unit($rfs-base-value);\n\n@function divide($dividend, $divisor, $precision: 10) {\n  $sign: if($dividend > 0 and $divisor > 0 or $dividend < 0 and $divisor < 0, 1, -1);\n  $dividend: abs($dividend);\n  $divisor: abs($divisor);\n  @if $dividend == 0 {\n    @return 0;\n  }\n  @if $divisor == 0 {\n    @error \"Cannot divide by 0\";\n  }\n  $remainder: $dividend;\n  $result: 0;\n  $factor: 10;\n  @while ($remainder > 0 and $precision >= 0) {\n    $quotient: 0;\n    @while ($remainder >= $divisor) {\n      $remainder: $remainder - $divisor;\n      $quotient: $quotient + 1;\n    }\n    $result: $result * 10 + $quotient;\n    $factor: $factor * .1;\n    $remainder: $remainder * 10;\n    $precision: $precision - 1;\n    @if ($precision < 0 and $remainder >= $divisor * 5) {\n      $result: $result + 1;\n    }\n  }\n  $result: $result * $factor * $sign;\n  $dividend-unit: unit($dividend);\n  $divisor-unit: unit($divisor);\n  $unit-map: (\n    \"px\": 1px,\n    \"rem\": 1rem,\n    \"em\": 1em,\n    \"%\": 1%\n  );\n  @if ($dividend-unit != $divisor-unit and map-has-key($unit-map, $dividend-unit)) {\n    $result: $result * map-get($unit-map, $dividend-unit);\n  }\n  @return $result;\n}\n\n// Remove px-unit from $rfs-base-value for calculations\n@if $rfs-base-value-unit == px {\n  $rfs-base-value: divide($rfs-base-value, $rfs-base-value * 0 + 1);\n}\n@else if $rfs-base-value-unit == rem {\n  $rfs-base-value: divide($rfs-base-value, divide($rfs-base-value * 0 + 1, $rfs-rem-value));\n}\n\n// Cache $rfs-breakpoint unit to prevent multiple calls\n$rfs-breakpoint-unit-cache: unit($rfs-breakpoint);\n\n// Remove unit from $rfs-breakpoint for calculations\n@if $rfs-breakpoint-unit-cache == px {\n  $rfs-breakpoint: divide($rfs-breakpoint, $rfs-breakpoint * 0 + 1);\n}\n@else if $rfs-breakpoint-unit-cache == rem or $rfs-breakpoint-unit-cache == \"em\" {\n  $rfs-breakpoint: divide($rfs-breakpoint, divide($rfs-breakpoint * 0 + 1, $rfs-rem-value));\n}\n\n// Calculate the media query value\n$rfs-mq-value: if($rfs-breakpoint-unit == px, #{$rfs-breakpoint}px, #{divide($rfs-breakpoint, $rfs-rem-value)}#{$rfs-breakpoint-unit});\n$rfs-mq-property-width: if($rfs-mode == max-media-query, max-width, min-width);\n$rfs-mq-property-height: if($rfs-mode == max-media-query, max-height, min-height);\n\n// Internal mixin used to determine which media query needs to be used\n@mixin _rfs-media-query {\n  @if $rfs-two-dimensional {\n    @if $rfs-mode == max-media-query {\n      @media (#{$rfs-mq-property-width}: #{$rfs-mq-value}), (#{$rfs-mq-property-height}: #{$rfs-mq-value}) {\n        @content;\n      }\n    }\n    @else {\n      @media (#{$rfs-mq-property-width}: #{$rfs-mq-value}) and (#{$rfs-mq-property-height}: #{$rfs-mq-value}) {\n        @content;\n      }\n    }\n  }\n  @else {\n    @media (#{$rfs-mq-property-width}: #{$rfs-mq-value}) {\n      @content;\n    }\n  }\n}\n\n// Internal mixin that adds disable classes to the selector if needed.\n@mixin _rfs-rule {\n  @if $rfs-class == disable and $rfs-mode == max-media-query {\n    // Adding an extra class increases specificity, which prevents the media query to override the property\n    &,\n    .disable-rfs &,\n    &.disable-rfs {\n      @content;\n    }\n  }\n  @else if $rfs-class == enable and $rfs-mode == min-media-query {\n    .enable-rfs &,\n    &.enable-rfs {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Internal mixin that adds enable classes to the selector if needed.\n@mixin _rfs-media-query-rule {\n\n  @if $rfs-class == enable {\n    @if $rfs-mode == min-media-query {\n      @content;\n    }\n\n    @include _rfs-media-query () {\n      .enable-rfs &,\n      &.enable-rfs {\n        @content;\n      }\n    }\n  }\n  @else {\n    @if $rfs-class == disable and $rfs-mode == min-media-query {\n      .disable-rfs &,\n      &.disable-rfs {\n        @content;\n      }\n    }\n    @include _rfs-media-query () {\n      @content;\n    }\n  }\n}\n\n// Helper function to get the formatted non-responsive value\n@function rfs-value($values) {\n  // Convert to list\n  $values: if(type-of($values) != list, ($values,), $values);\n\n  $val: \"\";\n\n  // Loop over each value and calculate value\n  @each $value in $values {\n    @if $value == 0 {\n      $val: $val + \" 0\";\n    }\n    @else {\n      // Cache $value unit\n      $unit: if(type-of($value) == \"number\", unit($value), false);\n\n      @if $unit == px {\n        // Convert to rem if needed\n        $val: $val + \" \" + if($rfs-unit == rem, #{divide($value, $value * 0 + $rfs-rem-value)}rem, $value);\n      }\n      @else if $unit == rem {\n        // Convert to px if needed\n        $val: $val + \" \" + if($rfs-unit == px, #{divide($value, $value * 0 + 1) * $rfs-rem-value}px, $value);\n      } @else {\n        // If $value isn't a number (like inherit) or $value has a unit (not px or rem, like 1.5em) or $ is 0, just print the value\n        $val: $val + \" \" + $value;\n      }\n    }\n  }\n\n  // Remove first space\n  @return unquote(str-slice($val, 2));\n}\n\n// Helper function to get the responsive value calculated by RFS\n@function rfs-fluid-value($values) {\n  // Convert to list\n  $values: if(type-of($values) != list, ($values,), $values);\n\n  $val: \"\";\n\n  // Loop over each value and calculate value\n  @each $value in $values {\n    @if $value == 0 {\n      $val: $val + \" 0\";\n    } @else {\n      // Cache $value unit\n      $unit: if(type-of($value) == \"number\", unit($value), false);\n\n      // If $value isn't a number (like inherit) or $value has a unit (not px or rem, like 1.5em) or $ is 0, just print the value\n      @if not $unit or $unit != px and $unit != rem {\n        $val: $val + \" \" + $value;\n      } @else {\n        // Remove unit from $value for calculations\n        $value: divide($value, $value * 0 + if($unit == px, 1, divide(1, $rfs-rem-value)));\n\n        // Only add the media query if the value is greater than the minimum value\n        @if abs($value) <= $rfs-base-value or not $enable-rfs {\n          $val: $val + \" \" + if($rfs-unit == rem, #{divide($value, $rfs-rem-value)}rem, #{$value}px);\n        }\n        @else {\n          // Calculate the minimum value\n          $value-min: $rfs-base-value + divide(abs($value) - $rfs-base-value, $rfs-factor);\n\n          // Calculate difference between $value and the minimum value\n          $value-diff: abs($value) - $value-min;\n\n          // Base value formatting\n          $min-width: if($rfs-unit == rem, #{divide($value-min, $rfs-rem-value)}rem, #{$value-min}px);\n\n          // Use negative value if needed\n          $min-width: if($value < 0, -$min-width, $min-width);\n\n          // Use `vmin` if two-dimensional is enabled\n          $variable-unit: if($rfs-two-dimensional, vmin, vw);\n\n          // Calculate the variable width between 0 and $rfs-breakpoint\n          $variable-width: #{divide($value-diff * 100, $rfs-breakpoint)}#{$variable-unit};\n\n          // Return the calculated value\n          $val: $val + \" calc(\" + $min-width + if($value < 0, \" - \", \" + \") + $variable-width + \")\";\n        }\n      }\n    }\n  }\n\n  // Remove first space\n  @return unquote(str-slice($val, 2));\n}\n\n// RFS mixin\n@mixin rfs($values, $property: font-size) {\n  @if $values != null {\n    $val: rfs-value($values);\n    $fluid-val: rfs-fluid-value($values);\n\n    // Do not print the media query if responsive & non-responsive values are the same\n    @if $val == $fluid-val {\n      #{$property}: $val;\n    }\n    @else {\n      @include _rfs-rule () {\n        #{$property}: if($rfs-mode == max-media-query, $val, $fluid-val);\n\n        // Include safari iframe resize fix if needed\n        min-width: if($rfs-safari-iframe-resize-bug-fix, (0 * 1vw), null);\n      }\n\n      @include _rfs-media-query-rule () {\n        #{$property}: if($rfs-mode == max-media-query, $fluid-val, $val);\n      }\n    }\n  }\n}\n\n// Shorthand helper mixins\n@mixin font-size($value) {\n  @include rfs($value);\n}\n\n@mixin padding($value) {\n  @include rfs($value, padding);\n}\n\n@mixin padding-top($value) {\n  @include rfs($value, padding-top);\n}\n\n@mixin padding-right($value) {\n  @include rfs($value, padding-right);\n}\n\n@mixin padding-bottom($value) {\n  @include rfs($value, padding-bottom);\n}\n\n@mixin padding-left($value) {\n  @include rfs($value, padding-left);\n}\n\n@mixin margin($value) {\n  @include rfs($value, margin);\n}\n\n@mixin margin-top($value) {\n  @include rfs($value, margin-top);\n}\n\n@mixin margin-right($value) {\n  @include rfs($value, margin-right);\n}\n\n@mixin margin-bottom($value) {\n  @include rfs($value, margin-bottom);\n}\n\n@mixin margin-left($value) {\n  @include rfs($value, margin-left);\n}\n", "// scss-docs-start color-mode-mixin\n@mixin color-mode($mode: light, $root: false) {\n  @if $color-mode-type == \"media-query\" {\n    @if $root == true {\n      @media (prefers-color-scheme: $mode) {\n        :root {\n          @content;\n        }\n      }\n    } @else {\n      @media (prefers-color-scheme: $mode) {\n        @content;\n      }\n    }\n  } @else {\n    [data-bs-theme=\"#{$mode}\"] {\n      @content;\n    }\n  }\n}\n// scss-docs-end color-mode-mixin\n", "// scss-docs-start clearfix\n@mixin clearfix() {\n  &::after {\n    display: block;\n    clear: both;\n    content: \"\";\n  }\n}\n// scss-docs-end clearfix\n", "// All-caps `RGBA()` function used because of this Sass bug: https://github.com/sass/node-sass/issues/2251\n@each $color, $value in $theme-colors {\n  .text-bg-#{$color} {\n    // Boosted mod: Force colors to have a good rendering\n    $text-bg-color: var(--#{$prefix}highlight-color);\n    @if index((\"primary\", \"warning\", \"light\"), #{$color}) {\n      $text-bg-color: $black;\n    } @else if (#{$color} == \"dark\") {\n      $text-bg-color: $white;\n    }\n    // End mod\n    color: $text-bg-color if($enable-important-utilities, !important, null); // Boosted mod: instead of `color-contrast($value)`\n    background-color: RGBA(var(--#{$prefix}#{$color}-rgb), var(--#{$prefix}bg-opacity, 1)) if($enable-important-utilities, !important, null);\n  }\n}\n", "// All-caps `RGBA()` function used because of this Sass bug: https://github.com/sass/node-sass/issues/2251\n@each $color, $value in $theme-colors {\n  .link-#{$color} {\n    color: RGBA(var(--#{$prefix}#{$color}-rgb), var(--#{$prefix}link-opacity, 1)) if($enable-important-utilities, !important, null);\n    text-decoration-color: RGBA(var(--#{$prefix}#{$color}-rgb), var(--#{$prefix}link-underline-opacity, 1)) if($enable-important-utilities, !important, null);\n\n    @if $link-shade-percentage != 0 {\n      // Boosted mod: no &:focus\n      &:hover {\n        $hover-color: if(color-contrast($value) == $color-contrast-light, shade-color($value, $link-shade-percentage), tint-color($value, $link-shade-percentage));\n        color: RGBA(#{to-rgb($hover-color)}, var(--#{$prefix}link-opacity, 1)) if($enable-important-utilities, !important, null);\n        text-decoration-color: RGBA(to-rgb($hover-color), var(--#{$prefix}link-underline-opacity, 1)) if($enable-important-utilities, !important, null);\n      }\n    }\n  }\n}\n\n// Boosted mod\n@if $enable-dark-mode {\n  @include color-mode(dark) {\n    @each $color, $value in $theme-colors-dark {\n      .link-#{$color} {\n        @if $link-shade-percentage != 0 {\n          &:hover {\n            $hover-color: if(color-contrast($value) == $color-contrast-light, shade-color($value, $link-shade-percentage), tint-color($value, $link-shade-percentage));\n            color: RGBA(#{to-rgb($hover-color)}, var(--#{$prefix}link-opacity, 1)) if($enable-important-utilities, !important, null);\n            text-decoration-color: RGBA(to-rgb($hover-color), var(--#{$prefix}link-underline-opacity, 1)) if($enable-important-utilities, !important, null);\n          }\n        }\n      }\n    }\n  }\n}\n// End mod\n\n// One-off special link helper as a bridge until v6\n.link-body-emphasis {\n  color: RGBA(var(--#{$prefix}emphasis-color-rgb), var(--#{$prefix}link-opacity, 1)) if($enable-important-utilities, !important, null);\n  text-decoration-color: RGBA(var(--#{$prefix}emphasis-color-rgb), var(--#{$prefix}link-underline-opacity, 1)) if($enable-important-utilities, !important, null);\n\n  @if $link-shade-percentage != 0 {\n    &:hover,\n    &:focus {\n      color: RGBA(var(--#{$prefix}emphasis-color-rgb), var(--#{$prefix}link-opacity, .75)) if($enable-important-utilities, !important, null);\n      text-decoration-color: RGBA(var(--#{$prefix}emphasis-color-rgb), var(--#{$prefix}link-underline-opacity, .75)) if($enable-important-utilities, !important, null);\n    }\n  }\n}\n", ".focus-ring:focus {\n  outline: 0;\n  // By default, there is no `--bs-focus-ring-x`, `--bs-focus-ring-y`, or `--bs-focus-ring-blur`, but we provide CSS variables with fallbacks to initial `0` values\n  box-shadow: var(--#{$prefix}focus-ring-x, 0) var(--#{$prefix}focus-ring-y, 0) var(--#{$prefix}focus-ring-blur, 0) var(--#{$prefix}focus-ring-width) var(--#{$prefix}focus-ring-color);\n}\n", "// Boosted mod\n.link-chevron {\n  font-weight: $font-weight-bold;\n  text-decoration: if($link-decoration == none, null, none);\n  background-color: transparent;\n\n  &::after {\n    display: inline-block;\n    width: $linked-chevron-icon-width;\n    height: $linked-chevron-icon-height;\n    margin-left: $linked-chevron-margin-left;\n    vertical-align: middle;\n    content: \"\";\n    background-color: currentcolor;\n    mask: var(--#{$prefix}chevron-icon) no-repeat;\n    transform: $linked-chevron-transform;\n  }\n\n  &:hover {\n    text-decoration: $link-decoration;\n  }\n}\n// End mod\n", "@import \"color-palette\";\n\n// Variables\n//\n// Variables should follow the `$component-state-property-size` formula for\n// consistent naming. Ex: $nav-link-disabled-color and $modal-content-box-shadow-xs.\n\n// Color system\n\n// scss-docs-start gray-color-variables\n$white:    $ods-white-100 !default;\n$gray-100: #fafafa !default;\n$gray-200: #f6f6f6 !default;\n$gray-300: $ods-gray-200 !default;\n$gray-400: $ods-gray-300 !default;\n$gray-500: $ods-gray-400 !default;\n$gray-600: $ods-gray-500 !default;\n$gray-700: $ods-gray-600 !default;\n$gray-800: $ods-gray-700 !default;\n$gray-900: $ods-gray-800 !default;\n$gray-950: $ods-gray-900 !default;\n$black:    $ods-black-900 !default;\n// scss-docs-end gray-color-variables\n\n// fusv-disable\n// scss-docs-start gray-colors-map\n$grays: (\n  \"100\": $gray-100,\n  \"200\": $gray-200,\n  \"300\": $gray-300,\n  \"400\": $gray-400,\n  \"500\": $gray-500,\n  \"600\": $gray-600,\n  \"700\": $gray-700,\n  \"800\": $gray-800,\n  \"900\": $gray-900,\n  \"950\": $gray-950,\n) !default;\n// scss-docs-end gray-colors-map\n// fusv-enable\n\n// Boosted mod\n// scss-docs-start brand-colors\n//// Functional colors\n$functional-green:  $ods-forest-200 !default;\n$functional-blue:   $ods-water-200 !default;\n$functional-yellow: $ods-sun-100 !default;\n$functional-red:    $ods-fire-200 !default;\n//// Supporting colors\n$supporting-blue:   $ods-blue-300 !default;\n$supporting-yellow: $ods-yellow-300 !default;\n$supporting-green:  $ods-green-300 !default;\n$supporting-purple: $ods-purple-300 !default;\n$supporting-pink:   $ods-pink-300 !default;\n$supporting-orange: $ods-orange-100 !default;\n// scss-docs-end brand-colors\n// End mod\n\n\n// scss-docs-start color-variables\n$blue:    $functional-blue !default;\n$indigo:  $supporting-purple !default;\n$purple:  $supporting-purple !default;\n$pink:    $supporting-pink !default;\n$red:     $functional-red !default;\n$orange:  $ods-orange-200 !default;\n$yellow:  $functional-yellow !default;\n$green:   $functional-green !default;\n$teal:    $supporting-green !default;\n$cyan:    $supporting-blue !default;\n// scss-docs-end color-variables\n\n// scss-docs-start colors-map\n$colors: (\n  \"blue\":       $blue,\n  \"indigo\":     $indigo,\n  \"purple\":     $purple,\n  \"pink\":       $pink,\n  \"red\":        $red,\n  \"orange\":     $orange,\n  \"yellow\":     $yellow,\n  \"green\":      $green,\n  \"teal\":       $teal,\n  \"cyan\":       $cyan,\n  \"black\":      $black,\n  \"white\":      $white,\n  \"gray\":       $gray-600,\n  \"gray-dark\":  $gray-800\n) !default;\n// scss-docs-end colors-map\n\n// The contrast ratio to reach against white, to determine if color changes from \"light\" to \"dark\". Acceptable values for WCAG 2.2 are 3, 4.5 and 7.\n// See https://www.w3.org/TR/WCAG/#contrast-minimum\n$min-contrast-ratio:   4.5 !default;\n\n// Customize the light and dark text colors for use in our YIQ color contrast function.\n$color-contrast-dark:  $black !default;\n$color-contrast-light: $white !default;\n\n// fusv-disable\n$blue-100: tint-color($blue, 80%) !default;\n$blue-200: tint-color($blue, 60%) !default;\n$blue-300: tint-color($blue, 40%) !default;\n$blue-400: tint-color($blue, 20%) !default;\n$blue-500: $blue !default;\n$blue-600: shade-color($blue, 20%) !default;\n$blue-700: shade-color($blue, 40%) !default;\n$blue-800: shade-color($blue, 60%) !default;\n$blue-900: shade-color($blue, 80%) !default;\n\n$indigo-100: $ods-purple-100 !default;\n$indigo-200: $ods-purple-200 !default;\n$indigo-300: $ods-purple-300 !default;\n$indigo-400: $ods-purple-400 !default;\n$indigo-500: $ods-purple-500 !default;\n$indigo-600: $ods-purple-600 !default;\n$indigo-700: shade-color($ods-purple-600, 20%) !default;\n$indigo-800: shade-color($ods-purple-600, 40%) !default;\n$indigo-900: shade-color($ods-purple-600, 60%) !default;\n\n$purple-100: $ods-purple-100 !default;\n$purple-200: $ods-purple-200 !default;\n$purple-300: $ods-purple-300 !default;\n$purple-400: $ods-purple-400 !default;\n$purple-500: $ods-purple-500 !default;\n$purple-600: $ods-purple-600 !default;\n$purple-700: shade-color($ods-purple-600, 20%) !default;\n$purple-800: shade-color($ods-purple-600, 40%) !default;\n$purple-900: shade-color($ods-purple-600, 60%) !default;\n\n$pink-100: $ods-pink-100 !default;\n$pink-200: $ods-pink-200 !default;\n$pink-300: $ods-pink-300 !default;\n$pink-400: $ods-pink-400 !default;\n$pink-500: $ods-pink-500 !default;\n$pink-600: $ods-pink-600 !default;\n$pink-700: shade-color($ods-pink-600, 20%) !default;\n$pink-800: shade-color($ods-pink-600, 40%) !default;\n$pink-900: shade-color($ods-pink-600, 60%) !default;\n\n$red-100: tint-color($red, 80%) !default;\n$red-200: tint-color($red, 60%) !default;\n$red-300: tint-color($red, 40%) !default;\n$red-400: tint-color($red, 20%) !default;\n$red-500: $red !default;\n$red-600: shade-color($red, 20%) !default;\n$red-700: shade-color($red, 40%) !default;\n$red-800: shade-color($red, 60%) !default;\n$red-900: shade-color($red, 80%) !default;\n\n$orange-100: tint-color($orange, 80%) !default;\n$orange-200: tint-color($orange, 60%) !default;\n$orange-300: tint-color($orange, 40%) !default;\n$orange-400: tint-color($orange, 20%) !default;\n$orange-500: $orange !default;\n$orange-600: shade-color($orange, 20%) !default;\n$orange-700: shade-color($orange, 40%) !default;\n$orange-800: shade-color($orange, 60%) !default;\n$orange-900: shade-color($orange, 80%) !default;\n\n$yellow-100: $ods-yellow-100 !default;\n$yellow-200: $ods-yellow-200 !default;\n$yellow-300: $ods-yellow-300 !default;\n$yellow-400: $ods-yellow-400 !default;\n$yellow-500: $ods-yellow-500 !default;\n$yellow-600: $ods-yellow-600 !default;\n$yellow-700: shade-color($ods-yellow-600, 20%) !default;\n$yellow-800: shade-color($ods-yellow-600, 40%) !default;\n$yellow-900: shade-color($ods-yellow-600, 60%) !default;\n\n$green-100: tint-color($green, 80%) !default;\n$green-200: tint-color($green, 60%) !default;\n$green-300: tint-color($green, 40%) !default;\n$green-400: tint-color($green, 20%) !default;\n$green-500: $green !default;\n$green-600: shade-color($green, 20%) !default;\n$green-700: shade-color($green, 40%) !default;\n$green-800: shade-color($green, 60%) !default;\n$green-900: shade-color($green, 80%) !default;\n\n$teal-100: $ods-green-100 !default;\n$teal-200: $ods-green-200 !default;\n$teal-300: $ods-green-300 !default;\n$teal-400: $ods-green-400 !default;\n$teal-500: $ods-green-500 !default;\n$teal-600: $ods-green-600 !default;\n$teal-700: shade-color($ods-green-600, 20%) !default;\n$teal-800: shade-color($ods-green-600, 40%) !default;\n$teal-900: shade-color($ods-green-600, 60%) !default;\n\n$cyan-100: $ods-blue-100 !default;\n$cyan-200: $ods-blue-200 !default;\n$cyan-300: $ods-blue-300 !default;\n$cyan-400: $ods-blue-400 !default;\n$cyan-500: $ods-blue-500 !default;\n$cyan-600: $ods-blue-600 !default;\n$cyan-700: shade-color($ods-blue-600, 20%) !default;\n$cyan-800: shade-color($ods-blue-600, 40%) !default;\n$cyan-900: shade-color($ods-blue-600, 60%) !default;\n\n$blues: (\n  \"blue-100\": $blue-100,\n  \"blue-200\": $blue-200,\n  \"blue-300\": $blue-300,\n  \"blue-400\": $blue-400,\n  \"blue-500\": $blue-500,\n  \"blue-600\": $blue-600,\n  \"blue-700\": $blue-700,\n  \"blue-800\": $blue-800,\n  \"blue-900\": $blue-900\n) !default;\n\n$indigos: (\n  \"indigo-100\": $indigo-100,\n  \"indigo-200\": $indigo-200,\n  \"indigo-300\": $indigo-300,\n  \"indigo-400\": $indigo-400,\n  \"indigo-500\": $indigo-500,\n  \"indigo-600\": $indigo-600,\n  \"indigo-700\": $indigo-700,\n  \"indigo-800\": $indigo-800,\n  \"indigo-900\": $indigo-900\n) !default;\n\n$purples: (\n  \"purple-100\": $purple-100,\n  \"purple-200\": $purple-200,\n  \"purple-300\": $purple-300,\n  \"purple-400\": $purple-400,\n  \"purple-500\": $purple-500,\n  \"purple-600\": $purple-600,\n  \"purple-700\": $purple-700,\n  \"purple-800\": $purple-800,\n  \"purple-900\": $purple-900\n) !default;\n\n$pinks: (\n  \"pink-100\": $pink-100,\n  \"pink-200\": $pink-200,\n  \"pink-300\": $pink-300,\n  \"pink-400\": $pink-400,\n  \"pink-500\": $pink-500,\n  \"pink-600\": $pink-600,\n  \"pink-700\": $pink-700,\n  \"pink-800\": $pink-800,\n  \"pink-900\": $pink-900\n) !default;\n\n$reds: (\n  \"red-100\": $red-100,\n  \"red-200\": $red-200,\n  \"red-300\": $red-300,\n  \"red-400\": $red-400,\n  \"red-500\": $red-500,\n  \"red-600\": $red-600,\n  \"red-700\": $red-700,\n  \"red-800\": $red-800,\n  \"red-900\": $red-900\n) !default;\n\n$oranges: (\n  \"orange-100\": $orange-100,\n  \"orange-200\": $orange-200,\n  \"orange-300\": $orange-300,\n  \"orange-400\": $orange-400,\n  \"orange-500\": $orange-500,\n  \"orange-600\": $orange-600,\n  \"orange-700\": $orange-700,\n  \"orange-800\": $orange-800,\n  \"orange-900\": $orange-900\n) !default;\n\n$yellows: (\n  \"yellow-100\": $yellow-100,\n  \"yellow-200\": $yellow-200,\n  \"yellow-300\": $yellow-300,\n  \"yellow-400\": $yellow-400,\n  \"yellow-500\": $yellow-500,\n  \"yellow-600\": $yellow-600,\n  \"yellow-700\": $yellow-700,\n  \"yellow-800\": $yellow-800,\n  \"yellow-900\": $yellow-900\n) !default;\n\n$greens: (\n  \"green-100\": $green-100,\n  \"green-200\": $green-200,\n  \"green-300\": $green-300,\n  \"green-400\": $green-400,\n  \"green-500\": $green-500,\n  \"green-600\": $green-600,\n  \"green-700\": $green-700,\n  \"green-800\": $green-800,\n  \"green-900\": $green-900\n) !default;\n\n$teals: (\n  \"teal-100\": $teal-100,\n  \"teal-200\": $teal-200,\n  \"teal-300\": $teal-300,\n  \"teal-400\": $teal-400,\n  \"teal-500\": $teal-500,\n  \"teal-600\": $teal-600,\n  \"teal-700\": $teal-700,\n  \"teal-800\": $teal-800,\n  \"teal-900\": $teal-900\n) !default;\n\n$cyans: (\n  \"cyan-100\": $cyan-100,\n  \"cyan-200\": $cyan-200,\n  \"cyan-300\": $cyan-300,\n  \"cyan-400\": $cyan-400,\n  \"cyan-500\": $cyan-500,\n  \"cyan-600\": $cyan-600,\n  \"cyan-700\": $cyan-700,\n  \"cyan-800\": $cyan-800,\n  \"cyan-900\": $cyan-900\n) !default;\n// fusv-enable\n\n// scss-docs-start theme-color-variables\n$primary:       $orange !default;\n$secondary:     $black !default;\n$success:       $green !default;\n$info:          $blue !default;\n$warning:       $yellow !default;\n$danger:        $red !default;\n$light:         $gray-500 !default;\n$dark:          $black !default;\n// scss-docs-end theme-color-variables\n\n// scss-docs-start theme-colors-map\n$theme-colors: (\n  \"primary\":    $primary,\n  \"secondary\":  $secondary,\n  \"success\":    $success,\n  \"info\":       $info,\n  \"warning\":    $warning,\n  \"danger\":     $danger,\n  \"light\":      $light,\n  \"dark\":       $dark\n) !default;\n// scss-docs-end theme-colors-map\n\n// scss-docs-start theme-text-variables\n$primary-text-emphasis:   $primary !default; // Boosted mod: instead of `shade-color($primary, 60%)`\n$secondary-text-emphasis: $secondary !default; // Boosted mod: instead of `shade-color($secondary, 60%)`\n$success-text-emphasis:   $success !default; // Boosted mod: instead of `shade-color($success, 60%)`\n$info-text-emphasis:      $info !default; // Boosted mod: instead of `shade-color($info, 60%)`\n$warning-text-emphasis:   $warning !default; // Boosted mod: instead of `shade-color($warning, 60%)`\n$danger-text-emphasis:    $danger !default; // Boosted mod: instead of `shade-color($danger, 60%)`\n$light-text-emphasis:     $light !default; // Boosted mod: instead of `$gray-700`\n$dark-text-emphasis:      $dark !default; // Boosted mod: instead of `$gray-700`\n// scss-docs-end theme-text-variables\n\n// scss-docs-start theme-bg-subtle-variables\n$primary-bg-subtle:       $primary !default; // Boosted mod: instead of `tint-color($primary, 80%)`\n$secondary-bg-subtle:     $secondary !default; // Boosted mod: instead of `tint-color($secondary, 80%)`\n$success-bg-subtle:       $success !default; // Boosted mod: instead of `tint-color($success, 80%)`\n$info-bg-subtle:          $info !default; // Boosted mod: instead of `tint-color($info, 80%)`\n$warning-bg-subtle:       $warning !default; // Boosted mod: instead of `tint-color($warning, 80%)`\n$danger-bg-subtle:        $danger !default; // Boosted mod: instead of `tint-color($danger, 80%)`\n$light-bg-subtle:         $light !default; // Boosted mod: instead of `mix($gray-100, $white)`\n$dark-bg-subtle:          $dark !default; // Boosted mod: instead of `$gray-400`\n// scss-docs-end theme-bg-subtle-variables\n\n// scss-docs-start theme-border-subtle-variables\n$primary-border-subtle:   $primary !default; // Boosted mod: instead of `tint-color($primary, 60%)`\n$secondary-border-subtle: $secondary !default; // Boosted mod: instead of `tint-color($secondary, 60%)`\n$success-border-subtle:   $success !default; // Boosted mod: instead of `tint-color($success, 60%)`\n$info-border-subtle:      $info !default; // Boosted mod: instead of `tint-color($info, 60%)`\n$warning-border-subtle:   $warning !default; // Boosted mod: instead of `tint-color($warning, 60%)`\n$danger-border-subtle:    $danger !default; // Boosted mod: instead of `tint-color($danger, 60%)`\n$light-border-subtle:     $light !default; // Boosted mod: instead of `$gray-200`\n$dark-border-subtle:      $dark !default; // Boosted mod: instead of `$gray-500`\n// scss-docs-end theme-border-subtle-variables\n\n// Characters which are escaped by the escape-svg function\n$escaped-characters: (\n  (\"<\", \"%3c\"),\n  (\">\", \"%3e\"),\n  (\"#\", \"%23\"),\n  (\"(\", \"%28\"),\n  (\")\", \"%29\"),\n) !default;\n\n// Boosted mod\n//// SVG as Data-URi\n$chevron-icon:          url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 9 14'><path d='M9 2 7 0 0 7l7 7 2-2-5-5 5-5z'/></svg>\") !default;\n$cross-icon:            url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30' fill='#{$black}'><path d='m15 17.121-8.132 8.132-2.121-2.12L12.879 15 4.747 6.868l2.12-2.121L15 12.879l8.132-8.132 2.12 2.121L17.122 15l8.132 8.132-2.121 2.12L15 17.123z'/></svg>\") !default;\n$cross-icon-stroke:     url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='116 116 767 767' fill='#{$black}'><path d='M817.493 676.165a49.977 49.977 0 0 1 0 70.664l-70.664 70.664a49.977 49.977 0 0 1-70.664 0L499.5 640.828 322.835 817.493a49.977 49.977 0 0 1-70.664 0l-70.664-70.664a49.977 49.977 0 0 1 0-70.664L358.172 499.5 181.507 322.835a49.977 49.977 0 0 1 0-70.664l70.664-70.664a49.977 49.977 0 0 1 70.664 0L499.5 358.172l176.665-176.665a49.977 49.977 0 0 1 70.664 0l70.664 70.664a49.977 49.977 0 0 1 0 70.664L640.828 499.5Z'/></svg>\") !default;\n$check-icon:            url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 15 12'><path fill='#{$black}' d='M13 0 5 8 2 5 0 7l5 5L15 2z'/></svg>\") !default;\n$burger-icon:           url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30' fill='#{$black}'><path d='M28 21v2H2v-2h26Zm0-7v2H2v-2h26Zm0-7v2H2V7h26Z'/></svg>\") !default;\n$burger-icon-small:     url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 25 25' fill='#{$black}'><path d='M2 19h21v-2H2v2Zm0-6h21v-2H2v2Zm0-6h21V5H2v2Z'/></svg>\") !default;\n$success-icon:          url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 125 125'><path fill='#{$success}' d='M62.5 0a62.5 62.5 0 1 0 0 125 62.5 62.5 0 0 0 0-125zm28 29.4c3.3 0 6 2.6 6 5.9a5.9 5.9 0 0 1-1.3 3.7L57.7 86a5.8 5.8 0 0 1-9.1 0L29.8 62.5c-.8-1-1.2-2.3-1.2-3.7a5.9 5.9 0 0 1 1.7-4.1l2.3-2.4a5.8 5.8 0 0 1 4.2-1.7 5.8 5.8 0 0 1 3.8 1.4L52 64.7 86.6 31a5.8 5.8 0 0 1 4-1.6z'/></svg>\") !default;\n$info-icon:             url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 125 125'><path fill='#{$info}' d='M62.5 0a62.5 62.5 0 1 0 0 125 62.5 62.5 0 0 0 0-125zm0 14.7a11 11 0 1 1 0 22 11 11 0 0 1 0-22zM47.8 44.1h25.7v46.2c0 4.7 1.3 6.5 1.8 7.2.8 1 2.3 1.5 4.8 1.6h.8v3.8H47.8v-3.7h.8c2.3-.1 4-.8 5-2 .4-.4 1-2 1-7V57c0-4.8-.6-6.6-1.2-7.3-.8-1-2.4-1.5-4.9-1.6h-.7V44z'/></svg>\") !default;\n$warning-icon:          url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='75 125 850 750'><path fill='#{$warning}' fill-rule='evenodd' d='M828.111 875H170.889A93.71 93.71 0 0 1 89.8 734.017l-.008-.005.772-1.321.036-.062 327.8-561.117h.008a93.94 93.94 0 0 1 162.182 0h.008l328.612 562.5-.009.005A93.709 93.709 0 0 1 828.111 875ZM500.5 775a47.5 47.5 0 1 1 47.507-47.5A47.5 47.5 0 0 1 500.5 775Zm47.368-424.038-15.7 258.121c-.009 17.482-14.185 24.05-31.671 24.05s-31.662-6.568-31.671-24.05l-15.74-258.716c-.057-.949-.094-1.9-.094-2.867a47.507 47.507 0 0 1 95.014 0 47.782 47.782 0 0 1-.138 3.462Z'/></svg>\") !default;\n$warning-icon-filled:   url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='75 125 850 750'><path fill='#{$warning}' d='M828.111 875H170.889A93.71 93.71 0 0 1 89.8 734.017l-.008-.005.772-1.321.036-.062 327.8-561.117h.008a93.94 93.94 0 0 1 162.182 0h.008l328.612 562.5-.009.005A93.709 93.709 0 0 1 828.111 875Z'/><path fill='#{$black}' d='M500.5 775a47.5 47.5 0 1 1 47.507-47.5A47.5 47.5 0 0 1 500.5 775Zm47.368-424.038-15.7 258.121c-.009 17.482-14.185 24.05-31.671 24.05s-31.662-6.568-31.671-24.05l-15.74-258.716c-.057-.949-.094-1.9-.094-2.867a47.507 47.507 0 0 1 95.014 0 47.782 47.782 0 0 1-.138 3.462Z'/></svg>\") !default;\n$danger-icon:           url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 140 125'><path fill='#{$danger}' d='M70.3 0c-5.8 0-10.8 3.1-13.5 7.8L2.3 101.3l-.2.2A15.6 15.6 0 0 0 15.6 125H125a15.6 15.6 0 0 0 13.5-23.5L83.8 7.8A15.6 15.6 0 0 0 70.3 0zm19.2 50a6.4 6.4 0 0 1 4.4 1.9 6.4 6.4 0 0 1 0 9L79.4 75.6l15 15a6.4 6.4 0 0 1 0 9.2 6.4 6.4 0 0 1-4.5 1.9 6.4 6.4 0 0 1-4.6-2l-15-15-15 15a6.4 6.4 0 0 1-4.6 2 6.4 6.4 0 0 1-4.6-2 6.4 6.4 0 0 1 0-9l15-15L46.8 61a6.4 6.4 0 1 1 9-9.1l14.6 14.5L84.8 52a6.4 6.4 0 0 1 4.7-1.9z'/></svg>\") !default;\n$add-icon:              url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 14 14'><path fill='currentColor' d='M14 6H8V0H6v6H0v2h6v6h2V8h6V6z'/></svg>\") !default;\n$remove-icon:           url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 14 2'><path fill='currentColor' d='M0 0h14v2H0z'/></svg>\") !default;\n$add-icon-sm:           url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 10 10'><path d='M10 4H6V0H4v4H0v2h4v4h2V6h4V4z'/></svg>\") !default;\n$remove-icon-sm:        url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 10 2'><path d='M0 0h10v2H0z'/></svg>\") !default;\n$play-icon:             url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'><path fill='currentColor' d='M12.138 16.8 3 21.6V2.4l9.138 4.8L21 12z' fill-rule='evenodd'/></svg>\") !default;\n$pause-icon:            url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'><path fill='currentColor' d='M10.2 21H3V3h7.2v18ZM21 21h-7.2V3H21v18Z' fill-rule='evenodd'/></svg>\") !default;\n$helper-icon:           url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1000 1000'><path fill='#{$info}' d='M500 75C265.277 75 75 265.279 75 500s190.277 425 425 425 425-190.279 425-425S734.721 75 500 75Zm30.8 680.633a54.149 54.149 0 0 1-37.069 14.267 56.1 56.1 0 0 1-37.95-14.085q-16.233-14.079-16.226-39.384 0-22.458 15.679-37.781t38.5-15.324q22.464 0 37.789 15.324t15.324 37.781q-.003 24.951-16.047 39.202Zm133.12-330.046a162.251 162.251 0 0 1-29.23 39.38q-16.92 16.574-60.772 55.785A248.236 248.236 0 0 0 554.5 540.18a79.146 79.146 0 0 0-10.868 15.32 75.1 75.1 0 0 0-5.529 13.9q-1.953 6.954-5.879 24.42-6.762 37.068-42.413 37.069-18.541 0-31.192-12.119t-12.647-36q0-29.945 9.262-51.863a131.346 131.346 0 0 1 24.6-38.491q15.319-16.577 41.35-39.4 22.789-19.946 32.962-30.113a101.987 101.987 0 0 0 17.105-22.632 54.714 54.714 0 0 0 6.955-27.086q0-28.517-21.213-48.119t-54.7-19.6q-39.213 0-57.743 19.783t-31.359 58.272Q401.059 423.8 367.2 423.8q-19.964 0-33.683-14.079T319.8 379.248q0-33.852 21.739-68.606t63.447-57.562q41.7-22.814 97.3-22.813 51.66 0 91.244 19.069 39.549 19.074 61.119 51.856t21.571 71.286q.004 30.297-12.297 53.109Z'/></svg>\") !default;\n\n//// SVG used several times\n$svg-as-custom-props: (\n  \"chevron\": $chevron-icon,\n  \"close\":   $cross-icon-stroke,\n  \"check\":   $check-icon,\n  \"success\": $success-icon,\n  \"error\":   $danger-icon\n) !default;\n\n//// Filters\n// see https://codepen.io/sosuke/pen/Pjoqqp\n$invert-filter:         invert(1) !default;\n// fusv-disable\n$orange-filter:         invert(46%) sepia(60%) saturate(2878%) hue-rotate(6deg) brightness(98%) contrast(104%) !default; // Deprecated in v5.3.3\n// fusv-enable\n// End mod\n\n// Options\n//\n// Quickly modify global styling by enabling or disabling optional features.\n\n$enable-caret:                true !default;\n$enable-rounded:              false !default;\n$enable-shadows:              false !default;\n$enable-gradients:            false !default;\n$enable-transitions:          true !default;\n$enable-reduced-motion:       true !default;\n$enable-smooth-scroll:        true !default;\n$enable-grid-classes:         true !default;\n$enable-container-classes:    true !default;\n$enable-cssgrid:              false !default;\n$enable-button-pointers:      true !default;\n$enable-rfs:                  false !default;\n$enable-validation-icons:     true !default;\n$enable-negative-margins:     false !default;\n$enable-deprecation-messages: false !default;\n$enable-important-utilities:  true !default;\n$enable-fixed-header:         true !default; // Boosted mod: used to apply scroll-padding-top\n\n$enable-dark-mode:            true !default;\n$color-mode-type:             data !default; // `data` or `media-query`\n\n// Prefix for :root CSS variables\n\n$variable-prefix:             bs- !default; // Deprecated in v5.2.0 for the shorter `$prefix`\n$prefix:                      $variable-prefix !default;\n// fusv-disable\n$boosted-variable-prefix:     o- !default; // Deprecated in v5.2.0 for the shorter `$prefix`\n$boosted-prefix:              $boosted-variable-prefix !default; // Deprecated in v5.3.0 for the shorter `$prefix`\n// fusv-enable\n\n// Gradient\n//\n// The gradient which is added to components if `$enable-gradients` is `true`\n// This gradient is also added to elements with `.bg-gradient`\n// scss-docs-start variable-gradient\n$gradient: linear-gradient(180deg, rgba($white, .15), rgba($white, 0)) !default;\n// scss-docs-end variable-gradient\n\n// Spacing\n//\n// Control the default styling of most Boosted elements by modifying these\n// variables. Mostly focused on spacing.\n// You can add more entries to the $spacers map, should you need more variation.\n\n// scss-docs-start spacer-variables-maps\n$spacer: 1.25rem !default; // Boosted mod\n$spacers: (\n  0: 0,\n  1: $spacer * .25,\n  2: $spacer * .5,\n  3: $spacer,\n  4: $spacer * 1.5,\n  5: $spacer * 3,\n) !default;\n// scss-docs-end spacer-variables-maps\n\n$target-size: 2.75rem !default; // Boosted mod: minimum target size (44×44px)\n\n\n// Position\n//\n// Define the edge positioning anchors of the position utilities.\n\n// scss-docs-start position-map\n$position-values: (\n  0: 0,\n  50: 50%,\n  100: 100%\n) !default;\n// scss-docs-end position-map\n\n// Body\n//\n// Settings for the `<body>` element.\n\n$body-text-align:           null !default;\n$body-color:                $black !default; // Boosted mod: instead of `$gray-900`\n$body-bg:                   $white !default;\n\n$body-secondary-color:      $gray-700 !default; // Boosted mod: instead of `rgba($body-color, .75)`\n$body-secondary-bg:         $gray-300 !default; // Boosted mod: instead of `$gray-200`\n\n$body-tertiary-color:       $gray-500 !default; // Boosted mod: instead of `rgba($body-color, .5)`\n$body-tertiary-bg:          $gray-100 !default;\n\n$body-emphasis-color:       $black !default;\n\n// Links\n//\n// Style anchor elements.\n\n$link-color:                              $black !default; // Boosted mod\n$link-decoration:                         underline !default;\n$link-shade-percentage:                   20% !default;\n$link-hover-color:                        $primary !default; // Boosted mod\n$link-hover-decoration:                   null !default;\n\n$stretched-link-pseudo-element:           after !default;\n$stretched-link-z-index:                  1 !default;\n\n// Boosted mod\n$linked-chevron-icon-width:   subtract(.5rem, 1px) !default;\n$linked-chevron-icon-height:  $spacer * .5 !default;\n$linked-chevron-transform:    rotate(.5turn) translateY(1px) !default;\n$linked-chevron-margin-left:  $spacer * .25 !default;\n// End mod\n\n// Icon links\n// scss-docs-start icon-link-variables\n$icon-link-gap:               .3125rem !default; // Boosted mod: instead of `.375rem`\n$icon-link-underline-offset:  .25em !default;\n$icon-link-icon-size:         1em !default;\n$icon-link-icon-transition:   .2s ease-in-out transform !default;\n$icon-link-icon-transform:    translate3d(.25em, 0, 0) !default;\n// scss-docs-end icon-link-variables\n\n\n// Paragraphs\n//\n// Style p element.\n\n$paragraph-margin-bottom:   1rem !default;\n\n\n// Grid breakpoints\n//\n// Define the minimum dimensions at which your layout will change,\n// adapting to different screen sizes, for use in media queries.\n\n// scss-docs-start grid-breakpoints\n$grid-breakpoints: (\n  xs: 0,\n  sm: 480px,\n  md: 768px,\n  lg: 1024px,\n  xl: 1280px,\n  xxl: 1440px\n) !default;\n// scss-docs-end grid-breakpoints\n\n@include _assert-ascending($grid-breakpoints, \"$grid-breakpoints\");\n@include _assert-starts-at-zero($grid-breakpoints, \"$grid-breakpoints\");\n\n\n// Grid containers\n//\n// Define the maximum width of `.container` for different screen sizes.\n\n// scss-docs-start container-max-widths\n$container-max-widths: (\n  xs: 312px,\n  sm: 468px,\n  md: 744px,\n  lg: 960px,\n  xl: 1200px,\n  xxl: 1320px\n) !default;\n\n// Boosted mod\n$container-fluid-margin: (\n  xs: 4px,\n  sm: 6px,\n  md: 12px,\n  lg: 32px,\n  xl: 40px,\n  xxl: 60px\n) !default;\n// End mod\n// scss-docs-end container-max-widths\n\n@include _assert-ascending($container-max-widths, \"$container-max-widths\");\n\n\n// Grid columns\n//\n// Set the number of columns and specify the width of the gutters.\n\n$grid-columns:                12 !default;\n$grid-gutter-width:           $spacer !default;\n$grid-gutter-breakpoint:      \"md\" !default; // Boosted mod: gutter depends on breakpoint\n$grid-row-columns:            6 !default;\n\n// Container padding\n\n$container-padding-x: $grid-gutter-width !default;\n\n\n// Components\n//\n// Define common padding and border radius sizes and more.\n\n// scss-docs-start border-variables\n$border-width:                .125rem !default;\n$border-widths: (\n  1: $border-width * .5,\n  2: $border-width,\n  3: $border-width * 1.5,\n  4: $border-width * 2,\n  5: $border-width * 2.5\n) !default;\n$border-style:                solid !default;\n$border-color:                $black !default; // Boosted mod: instead of `$gray-300`\n$border-color-subtle:         $gray-500 !default; // Boosted mod\n$border-color-translucent:    rgba($black, .175) !default;\n// scss-docs-end border-variables\n\n// scss-docs-start border-radius-variables\n$border-radius:               .375rem !default;\n$border-radius-sm:            .25rem !default;\n$border-radius-lg:            .5rem !default;\n$border-radius-xl:            1rem !default;\n$border-radius-xxl:           2rem !default;\n$border-radius-pill:          50rem !default;\n// scss-docs-end border-radius-variables\n// fusv-disable\n$border-radius-2xl:           $border-radius-xxl !default; // Deprecated in v5.3.0\n// fusv-enable\n\n// fusv-disable\n$outline-width:               var(--#{$prefix}border-width) !default; // Deprecated in v5.2.3\n$outline-offset:              $outline-width !default; // Deprecated in v5.2.3\n// fusv-enable\n\n// scss-docs-start focus-visible-variables\n$focus-visible-zindex:                5 !default; // Boosted mod\n\n$focus-visible-inner-width:           2px !default; // Boosted mod\n$focus-visible-inner-color:           $white !default; // Boosted mod\n\n$focus-visible-outer-width:           3px !default;  // Boosted mod\n$focus-visible-outer-offset:          $focus-visible-inner-width !default; // Boosted mod\n$focus-visible-outer-color:           $black !default; // Boosted mod\n// scss-docs-end focus-visible-variables\n\n// scss-docs-start box-shadow-variables\n$box-shadow:        null !default; // Boosted mod: instead of `0 .5rem 1rem rgba($black, .15)`\n$box-shadow-sm:     null !default; // Boosted mod: instead of `0 .125rem .25rem rgba($black, .075)`\n$box-shadow-lg:     null !default; // Boosted mod: instead of `0 1rem 3rem rgba($black, .175)`\n$box-shadow-inset:  null !default; // Boosted mod: instead of `inset 0 1px 2px rgba($black, .075)`\n// scss-docs-end box-shadow-variables\n\n$component-active-color:      $black !default;\n$component-active-bg:         $supporting-orange !default;\n$disabled-color:              var(--#{$prefix}tertiary-color) !default; // Boosted mod\n$tertiary-active-bg:          $gray-400 !default; // Boosted mod\n\n// scss-docs-start focus-ring-variables\n$focus-ring-width:      .25rem !default;\n$focus-ring-opacity:    .25 !default;\n$focus-ring-color:      rgba($primary, $focus-ring-opacity) !default;\n// Boosted mod: no `$focus-ring-blur`\n$focus-ring-box-shadow: null !default; // Boosted mod: instead of `0 0 $focus-ring-blur $focus-ring-width $focus-ring-color`\n// scss-docs-end focus-ring-variables\n\n// scss-docs-start caret-variables\n$caret-width:                 add($spacer * .25, var(--#{$prefix}border-width)) !default;\n$caret-vertical-align:        center !default;\n$caret-spacing:               $spacer * .5 !default;\n// scss-docs-end caret-variables\n\n$transition-duration: .2s !default; // Boosted mod\n$transition-timing:   ease-in-out !default; // Boosted mod\n$transition-base:     all $transition-duration $transition-timing !default;\n$transition-fade:     opacity $transition-timing linear !default;\n// scss-docs-start collapse-transition\n$transition-collapse:         height .35s ease !default;\n$transition-collapse-width:   width .35s ease !default;\n// scss-docs-end collapse-transition\n$transition-focus:    null !default; // Boosted mod\n\n// stylelint-disable function-disallowed-list\n// scss-docs-start aspect-ratios\n$aspect-ratios: (\n  \"1x1\": 100%,\n  \"4x3\": calc(3 / 4 * 100%),\n  \"16x9\": calc(9 / 16 * 100%),\n  \"21x9\": calc(9 / 21 * 100%),\n  \"9x16\": calc(16 / 9 * 100%) // Boosted mod: additional ratio for portrait videos\n) !default;\n// scss-docs-end aspect-ratios\n// stylelint-enable function-disallowed-list\n\n// Typography\n//\n// Font, line-height, and color for body text, headings, and more.\n\n// scss-docs-start font-variables\n// stylelint-disable value-keyword-case\n$font-family-sans-serif:      HelvNeueOrange#{\"/*rtl:insert:Arabic*/\"}, \"Helvetica Neue\", Helvetica, \"Noto Sans\", \"Liberation Sans\", Arial, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\" !default;\n$font-family-monospace:       SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace !default;\n// stylelint-enable value-keyword-case\n$font-family-base:            var(--#{$prefix}font-sans-serif) !default;\n$font-family-code:            var(--#{$prefix}font-monospace) !default;\n\n\n// Boosted mod\n//// Type scale & vertical rhythm completely revamped to match Orange Web Guidelines\n\n// $font-size-root affects the value of `rem`, which is used for as well font sizes, paddings and margins\n// $font-size-base affects the font size of the body text\n$font-size-root:              null !default;\n$font-size-base:              1rem !default; // Assumes the browser default, typically `16px`\n$font-size-sm:                $font-size-base * .875 !default;  // 14px\n$font-size-lg:                $font-size-base * 1.125 !default; // 18px\n$font-size-xlg:               $font-size-base * 1.25 !default;  // 20px\n\n$font-weight-lighter:         null !default;\n$font-weight-light:           null !default;\n$font-weight-normal:          400 !default;\n$font-weight-medium:          500 !default;\n$font-weight-semibold:        600 !default;\n$font-weight-bold:            700 !default;\n$font-weight-bolder:          null !default;\n\n$font-weight-base:            $font-weight-normal !default;\n\n// stylelint-disable function-disallowed-list\n$line-height-base:            calc(18 / 16) !default;\n$line-height-sm:              calc(16 / 14) !default;\n$line-height-lg:              calc(30 / 16) !default;\n// stylelint-enable function-disallowed-list\n\n$line-length-sm:              40ch !default;\n$line-length-md:              80ch !default;\n\n$letter-spacing-base:         $spacer * -.005 !default; // -0.1px\n\n$h1-font-size:                $font-size-base * 2.125 !default;   // 34px\n$h2-font-size:                $font-size-base * 1.875 !default;   // 30px\n$h3-font-size:                $font-size-base * 1.5 !default;     // 24px\n$h4-font-size:                $font-size-xlg !default;            // 20px\n$h5-font-size:                $font-size-lg !default;             // 18px\n$h6-font-size:                $font-size-base !default;           // 16px\n// scss-docs-end font-variables\n\n// scss-docs-start font-sizes\n$font-sizes: (\n  1: $h1-font-size,\n  2: $h2-font-size,\n  3: $h3-font-size,\n  4: $h4-font-size,\n  5: $h5-font-size,\n  6: $h6-font-size\n) !default;\n// scss-docs-end font-sizes\n\n// scss-docs-start letter-spacing\n$h1-spacing:                  $letter-spacing-base * 10 !default; // -1px\n$h2-spacing:                  $letter-spacing-base * 8 !default;  // -0.8px\n$mid-spacing:                 $letter-spacing-base * 6 !default;  // -0.6px\n$h3-spacing:                  $letter-spacing-base * 5 !default;  // -0.5px\n$h4-spacing:                  $letter-spacing-base * 4 !default;  // -0.4px\n$h5-spacing:                  $letter-spacing-base * 2 !default;  // -0.2px\n$h6-spacing:                  $letter-spacing-base !default;\n// scss-docs-end letter-spacing\n\n// stylelint-disable function-disallowed-list\n// scss-docs-start line-height\n$h1-line-height:              1 !default;\n$h2-line-height:              calc(32 / 30) !default;\n$h3-line-height:              calc(26 / 24) !default;\n$h4-line-height:              calc(22 / 20) !default;\n$h5-line-height:              calc(20 / 18) !default;\n$h6-line-height:              $line-height-base !default;\n// scss-docs-end line-height\n// stylelint-enable function-disallowed-list\n\n// scss-docs-start headings-variables\n$headings-margin-bottom:      $spacer !default; // Boosted mod\n$headings-font-family:        null !default;\n$headings-font-style:         null !default;\n$headings-font-weight:        700 !default;\n$headings-line-height:        $h6-line-height !default;\n$headings-color:              inherit !default;\n// scss-docs-end headings-variables\n\n// scss-docs-start display-headings\n$display1-size:               $font-size-xlg * 3 !default;        // 60px\n$display2-size:               $font-size-xlg * 2.5 !default;      // 50px\n$display3-size:               $font-size-xlg * 2 !default;        // 40px\n$display4-size:               $h1-font-size !default;             // 34px\n$display1-spacing:            $letter-spacing-base * 20 !default; // -2px\n$display2-spacing:            $letter-spacing-base * 16 !default; // -1.6px\n$display3-spacing:            $h1-spacing !default;               // -1px\n$display4-spacing:            $h1-spacing !default;               // -1px\n$display-line-height:         $h1-line-height !default;\n// scss-docs-end display-headings\n\n// scss-docs-start type-variables\n$lead-font-size:              $font-size-xlg !default;\n$lead-font-weight:            400 !default;\n$lead-line-height:            1.5 !default;\n$lead-letter-spacing:         $letter-spacing-base * 4 !default;\n\n$small-font-size:             .875rem !default;\n\n$sub-sup-font-size:           .75em !default;\n\n// fusv-disable\n$text-muted:                  var(--#{$prefix}secondary-color) !default; // Deprecated in 5.3.0\n// fusv-enable\n\n$initialism-font-size:        $small-font-size !default;\n\n$blockquote-margin-y:         $spacer !default;\n$blockquote-font-size:        $font-size-xlg !default;\n$blockquote-footer-color:     var(--#{$prefix}secondary-color) !default; // Boosted mod: instead of `$gray-600`\n$blockquote-footer-font-size: $small-font-size !default;\n$blockquote-line-height:      1.5 !default; // Boosted mod\n$blockquote-letter-spacing:   $letter-spacing-base * .25 !default; // Boosted mod\n\n$hr-margin-y:                 $spacer !default;\n$hr-color:                    inherit !default;\n\n// fusv-disable\n$hr-bg-color:                 null !default; // Deprecated in v5.2.0\n$hr-height:                   null !default; // Deprecated in v5.2.0\n// fusv-enable\n\n$hr-border-color:             null !default; // Allows for inherited colors\n$hr-border-width:             var(--#{$prefix}border-width) !default;\n$hr-opacity:                  null !default;\n\n// scss-docs-start vr-variables\n$vr-border-width:             2px !default; // Boosted mod: instead of `var(--#{$prefix}border-width)`\n// scss-docs-end vr-variables\n\n$legend-margin-bottom:        $spacer * .25 !default;\n$legend-font-size:            $font-size-xlg !default;\n$legend-font-weight:          $font-weight-bold !default;\n\n$dt-font-weight:              $font-weight-bold !default;\n\n$list-inline-padding:         $spacer * .25 !default;\n\n$mark-padding:                0 .1875em !default; // Boosted mod\n$mark-color:                  $white !default; // Boosted mod: instead of `$body-color`\n$mark-bg:                     $black !default; // Boosted mod: instead of `$yellow-100`\n// scss-docs-end type-variables\n// End mod\n\n// Tables\n//\n// Customizes the `.table` component with basic values, each used across all table variations.\n\n// scss-docs-start table-variables\n$table-cell-padding-y:                  .875rem !default; // Boosted mod\n$table-cell-padding-x:                  $spacer * .5 !default; // Boosted mod\n$table-cell-padding-y-sm:               .5625rem !default; // Boosted mod\n$table-cell-padding-x-sm:               $table-cell-padding-x !default; // Boosted mod\n\n$table-cell-icon-margin-top:            -.75rem !default; // Boosted mod\n$table-cell-icon-margin-bottom:         -.625rem !default; // Boosted mod\n$table-cell-vertical-align:             top !default;\n$table-line-height:                     1.25 !default; // Boosted mod\n\n$table-color:                           var(--#{$prefix}emphasis-color) !default;\n$table-bg:                              var(--#{$prefix}body-bg) !default;\n$table-accent-bg:                       transparent !default;\n\n$table-th-font-weight:                  null !default;\n\n$table-striped-color:                   $table-color !default;\n$table-striped-bg-factor:               .035 !default; // Boosted mod: equivalent to `$gray-200`\n$table-striped-bg:                      rgba(var(--#{$prefix}black-rgb), var(--#{$prefix}table-striped-bg-factor)) !default; // Boosted mod: instead of `rgba(var(--#{$prefix}emphasis-color-rgb), $table-striped-bg-factor)`\n$table-variant-striped-bg-factor:       .08 !default; // Boosted mod\n\n$table-active-color:                    $table-color !default;\n$table-active-bg-factor:                .135 !default; // Boosted mod\n$table-active-bg:                       rgba(var(--#{$prefix}emphasis-color-rgb), var(--#{$prefix}table-active-bg-factor)) !default; // Boosted mod: instead of `rgba(var(--#{$prefix}emphasis-color-rgb), $table-active-bg-factor)`\n$table-variant-active-bg-factor:        .4 !default; // Boosted mod\n\n$table-hover-color:                     $table-color !default;\n$table-hover-bg-factor:                 .065 !default; // Boosted mod\n$table-hover-bg:                        rgba(var(--#{$prefix}emphasis-color-rgb), var(--#{$prefix}table-hover-bg-factor)) !default; // Boosted mod: instead of `rgba(var(--#{$prefix}emphasis-color-rgb), $table-hover-bg-factor)`\n$table-variant-hover-bg-factor:         .2 !default; // Boosted mod\n\n$table-border-factor:                   .4 !default; // Boosted mod\n// stylelint-disable-next-line function-disallowed-list\n$table-border-width:                    calc(var(--#{$prefix}border-width) * .5) !default;  // Boosted mod\n$table-border-color:                    var(--#{$prefix}border-color-subtle) !default; // Boosted mod: instead of `var(--#{$prefix}border-color)`\n\n$table-striped-order:                   odd !default;\n$table-striped-columns-order:           even !default;\n\n$table-group-separator-color:           currentcolor !default;\n\n$table-caption-color:                   var(--#{$prefix}caption-color, var(--#{$prefix}emphasis-color)) !default; // Boosted mod: instead of `var(--#{$prefix}secondary-color)`\n$table-caption-padding-y:               .75rem !default; // Boosted mod\n\n$table-bg-scale:                        -60% !default;\n// scss-docs-end table-variables\n\n// scss-docs-start table-loop\n$table-variants: (\n  \"primary\":    shift-color($primary, $table-bg-scale),\n  \"secondary\":  shift-color($secondary, $table-bg-scale),\n  \"success\":    shift-color($success, $table-bg-scale),\n  \"info\":       shift-color($info, $table-bg-scale),\n  \"warning\":    shift-color($warning, $table-bg-scale),\n  \"danger\":     shift-color($danger, $table-bg-scale),\n  \"light\":      $light,\n  \"dark\":       $dark,\n) !default;\n// scss-docs-end table-loop\n\n// Buttons + Forms\n//\n// Shared variables that are reassigned to `$input-` and `$btn-` specific variables.\n\n// scss-docs-start input-btn-variables\n$input-btn-padding-y:         .5rem !default;\n$input-btn-padding-x:         1.125rem !default;\n$input-btn-font-family:       inherit !default;\n$input-btn-font-size:         $font-size-base !default;\n$input-btn-line-height:       1.25 !default;\n\n$input-btn-focus-width:       $focus-visible-outer-offset !default; // Boosted mod: instead of `$focus-ring-width`\n// Boosted mod: no `$input-btn-focus-color-opacity`\n// Boosted mod: no `$input-btn-focus-color`\n// Boosted mod: no `$input-btn-focus-blur`\n$input-btn-focus-box-shadow:  $focus-ring-box-shadow !default;\n\n$input-btn-padding-y-sm:      $spacer * .25 !default;\n$input-btn-padding-x-sm:      $spacer * .5 !default;\n$input-btn-font-size-sm:      $font-size-sm !default;\n\n$input-btn-padding-y-lg:      .8125rem !default;\n$input-btn-padding-x-lg:      $spacer !default;\n$input-btn-font-size-lg:      $font-size-lg !default;\n\n$input-btn-border-width:      var(--#{$prefix}border-width) !default;\n// scss-docs-end input-btn-variables\n\n// Buttons\n//\n// For each of Boosted's buttons, define text, background, and border color.\n\n// scss-docs-start btn-variables\n$btn-color:                   var(--#{$prefix}body-color) !default;\n$btn-hover-color:             $btn-color !default; // Boosted mod\n$btn-padding-y:               $input-btn-padding-y !default;\n$btn-padding-x:               $input-btn-padding-x !default;\n$btn-font-family:             $input-btn-font-family !default;\n$btn-font-size:               $input-btn-font-size !default;\n$btn-line-height:             $input-btn-line-height !default;\n$btn-letter-spacing:          $letter-spacing-base !default; // Boosted mod\n$btn-white-space:             null !default; // Set to `nowrap` to prevent text wrapping\n\n$btn-padding-y-sm:            $input-btn-padding-y-sm !default;\n$btn-padding-x-sm:            $input-btn-padding-x-sm !default;\n$btn-font-size-sm:            $input-btn-font-size-sm !default;\n$btn-line-height-sm:          $line-height-sm !default; // Boosted mod\n$btn-letter-spacing-sm:       $letter-spacing-base !default; // Boosted mod\n\n$btn-padding-y-lg:            $input-btn-padding-y-lg !default;\n$btn-padding-x-lg:            $input-btn-padding-x-lg !default;\n$btn-font-size-lg:            $input-btn-font-size-lg !default;\n$btn-line-height-lg:          $h5-line-height !default; // Boosted mod\n$btn-letter-spacing-lg:       $letter-spacing-base * 2 !default; // Boosted mod\n\n$btn-border-width:            $input-btn-border-width !default;\n\n$btn-default-hover-bg:        var(--#{$prefix}highlight-bg) !default; // Boosted mod\n$btn-default-hover-border:    var(--#{$prefix}border-color) !default; // Boosted mod\n$btn-default-hover-color:     var(--#{$prefix}highlight-color) !default; // Boosted mod\n$btn-default-active-bg:       $supporting-orange !default; // Boosted mod\n$btn-default-active-border:   $supporting-orange !default; // Boosted mod\n$btn-default-active-color:    $black !default; // Boosted mod\n$btn-default-disabled-bg:     var(--#{$prefix}disabled-color) !default; // Boosted mod\n$btn-default-disabled-border: var(--#{$prefix}disabled-color) !default; // Boosted mod\n$btn-default-disabled-color:  var(--#{$prefix}highlight-color) !default; // Boosted mod\n\n$btn-outline-default-hover-bg:        var(--#{$prefix}btn-color) !default; // Boosted mod\n$btn-outline-default-hover-border:    var(--#{$prefix}btn-border-color) !default; // Boosted mod\n$btn-outline-default-hover-color:     $white !default; // Boosted mod\n$btn-outline-default-active-bg:       $supporting-orange !default; // Boosted mod\n$btn-outline-default-active-border:   $supporting-orange !default; // Boosted mod\n$btn-outline-default-active-color:    $black !default; // Boosted mod\n$btn-outline-default-disabled-bg:     transparent !default; // Boosted mod\n$btn-outline-default-disabled-border: var(--#{$prefix}disabled-color) !default; // Boosted mod\n$btn-outline-default-disabled-color:  var(--#{$prefix}disabled-color) !default; // Boosted mod\n\n$btn-font-weight:             $font-weight-bold !default;\n$btn-box-shadow:              null !default;\n$btn-focus-width:             $input-btn-focus-width !default;\n$btn-focus-box-shadow:        0 0 0 $btn-focus-width $white !default;\n$btn-disabled-opacity:        1 !default;\n$btn-active-box-shadow:       null !default;\n\n$btn-link-color:              var(--#{$prefix}link-color) !default;\n$btn-link-hover-color:        var(--#{$prefix}link-hover-color) !default;\n$btn-link-disabled-color:     var(--#{$prefix}disabled-color) !default; // Boosted mod: instead of `$gray-600`\n// Boosted mod: no `$btn-link-focus-shadow-rgb`\n\n// Allows for customizing button radius independently from global border radius\n$btn-border-radius:           var(--#{$prefix}border-radius) !default;\n$btn-border-radius-sm:        var(--#{$prefix}border-radius-sm) !default;\n$btn-border-radius-lg:        var(--#{$prefix}border-radius-lg) !default;\n\n$btn-transition:              $transition-focus !default; // Boosted mod\n// scss-docs-end btn-variables\n\n// Boosted mod: icon button\n$btn-icon-padding-x:          subtract($spacer * .5, var(--#{$prefix}border-width)) !default;\n$btn-icon-padding-x-sm:       $spacer * .25 !default;\n$btn-icon-padding-x-lg:       add($spacer * .5, calc(var(--#{$prefix}border-width) * 1.5)) !default; // stylelint-disable-line function-disallowed-list\n// Boosted mod: social button\n// scss-docs-start social-buttons\n$btn-social-networks: (\n  \"facebook\": (\n    \"color\": #3b5998,\n    \"icon\": \"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'><path d='M19 6h5V0h-5c-4 0-7 3-7 7v3H8v6h4v16h6V16h5l1-6h-6V7l1-1z'></path></svg>\"\n  ),\n  \"twitter\": (\n    \"color\": #1da1f2,\n    \"icon\": \"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'><path d='M32 7a13 13 0 01-3.8 1.1 6.6 6.6 0 003-3.6c-1.4.7-2.8 1.3-4.3 1.6a6.6 6.6 0 00-11.1 6A18.6 18.6 0 012.2 5a6.6 6.6 0 002 8.9c-1 0-2-.4-3-.9v.1c0 3.2 2.4 5.9 5.4 6.5a6.6 6.6 0 01-3 0 6.6 6.6 0 006.1 4.6A13.2 13.2 0 010 27.1a18.6 18.6 0 0028.7-16.6C30 9.5 31.1 8.4 32 7z'/></svg>\"\n  ),\n  \"instagram\": (\n    \"color\": #e1306c,\n    \"icon\": \"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'><path d='M16 2.9h6.5c1.5.1 2.4.4 3 .6a5 5 0 011.8 1.2c.5.6.9 1.1 1.2 1.9.2.5.4 1.4.5 3a112.7 112.7 0 01-.5 15.8 5 5 0 01-1.2 1.9c-.6.5-1.1.9-1.9 1.2-.5.2-1.4.4-3 .5a112.7 112.7 0 01-15.8-.5 5 5 0 01-1.9-1.2 5 5 0 01-1.2-1.9c-.2-.5-.4-1.4-.5-3a112.7 112.7 0 01.5-15.8 5 5 0 011.2-1.9c.6-.5 1.1-.9 1.9-1.2C7 3.3 8 3 9.6 3l6.4-.1zM16 0H9.4C7.7.3 6.5.5 5.5.9s-2 1-2.8 1.9c-1 .9-1.5 1.8-1.9 2.8-.4 1-.6 2.2-.7 3.9a117.6 117.6 0 00.7 17c.5 1.1 1 2 1.9 3 .9.8 1.8 1.4 2.8 1.8 1 .4 2.2.6 3.9.7a117.2 117.2 0 0017-.7c1.1-.4 2-1 2.9-1.9s1.4-1.8 1.8-2.8c.4-1 .7-2.2.8-3.9a117.2 117.2 0 00-.8-17A7.8 7.8 0 0026.4.8c-1-.5-2.1-.7-3.8-.8L16 0z'/><path d='M16 7.8a8.2 8.2 0 100 16.4 8.2 8.2 0 000-16.4zm0 13.5a5.3 5.3 0 110-10.6 5.3 5.3 0 010 10.6zM26.5 7.5a2 2 0 11-3.9 0 2 2 0 013.9 0z'/></svg>\"\n  ),\n  \"youtube\": (\n    \"color\": #f00,\n    \"icon\": \"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'><path d='M31.7 9.6s-.3-2.2-1.3-3.2c-1.2-1.3-2.6-1.3-3.2-1.3-4.5-.4-11.2-.4-11.2-.4s-6.7 0-11.2.4c-.6 0-2 0-3.2 1.3C.6 7.4.3 9.6.3 9.6S0 12.2 0 14.8v2.4c0 2.6.3 5.2.3 5.2s.3 2.2 1.3 3.2c1.2 1.2 2.8 1.2 3.5 1.3 2.6.3 11 .4 11 .4s6.6 0 11.1-.4c.6 0 2 0 3.2-1.3 1-1 1.3-3.2 1.3-3.2s.3-2.6.3-5.2v-2.4c0-2.6-.3-5.2-.3-5.2zm-19 10.5v-9l8.6 4.6-8.6 4.4z'/></svg>\"\n  ),\n  \"linkedin\": (\n    \"color\": #0077b5,\n    \"icon\": \"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'><path d='M12 12h5.5v2.8h.1a6.1 6.1 0 015.5-2.8c5.8 0 6.9 3.6 6.9 8.4V30h-5.8v-8.5c0-2 0-4.7-3-4.7s-3.4 2.2-3.4 4.5V30H12V12zM2 12h6v18H2V12zm6-5a3 3 0 11-6 0 3 3 0 016 0z'/></svg>\",\n  ),\n  \"whatsapp\": (\n    \"color\": #25d366,\n    \"icon\": \"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'><path d='M27.3 4.7a15.9 15.9 0 00-25 19.1L.1 32l8.4-2.2A15.9 15.9 0 0027.3 4.7zM16 29c-2.4 0-4.7-.6-6.7-1.8l-.5-.3-5 1.3 1.3-4.8-.3-.5A13.2 13.2 0 1116.1 29zm7.2-9.8l-2.7-1.3c-.3-.1-.6-.2-1 .2l-1.2 1.5c-.2.3-.4.3-.8.1s-1.7-.6-3.2-2c-1.2-1-2-2.3-2.2-2.7s0-.6.2-.8l.6-.7.4-.6v-.7l-1.3-3c-.3-.7-.6-.6-.9-.7h-.7c-.2 0-.7.1-1.1.5C9 9.4 8 10.4 8 12.3s1.4 3.9 1.6 4.1c.2.3 2.8 4.3 6.8 6l2.3.9c.9.3 1.8.2 2.4.1.8-.1 2.4-1 2.7-1.9s.4-1.7.3-1.9l-.8-.4z'/></svg>\"\n  ),\n  \"mail\": (\n    \"color\": $supporting-orange,\n    \"icon\": \"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'><path d='M3.2 14.3c0 9.5 0 9 .2 9.5.3.8 1 1.4 1.7 1.7l12.2.1h11.5v-8.8c0-9.3 0-8.9-.2-9.3-.2-.7-.7-1.2-1.3-1.6l-.8-.3H3.2v8.7zm22.9-2.4a246.2 246.2 0 01-4.9 4.7l-.8.7-.5.6-.7.6c-.6.6-1 .9-1.3 1a4 4 0 01-1.8.5 4 4 0 01-2.4-.6 13 13 0 01-1.9-1.7l-2.4-2.4-.6-.6-1.4-1.3L6.1 12l-.5-.5V8.9l.6.5L7.9 11l1.4 1.4 1.3 1.2 1.3 1.3a195 195 0 012.6 2.4c.4.3 1 .5 1.6.4.5 0 1-.1 1.4-.4L19 16l1-1 1-1a214.7 214.7 0 012.2-2l1-1 2-2 .2-.2v2.8l-.3.3z'/></svg>\",\n    \"size\": 1.5rem\n  ),\n  \"snapchat\": (\n    \"color\": #fffc00,\n    \"icon\": \"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 26 28'><path d='M13 2c3 0 5 2 7 4v6h2l1 1-3 2v1l4 4h1l1 1-4 1-1 2h-2-1c-1 0-2 2-5 2s-4-2-5-2H5l-1-2-4-1 1-1h1l4-4v-1l-3-2 1-1h2V9 6c2-3 4-4 7-4z'/></svg>\"\n  ),\n  \"pinterest\": (\n    \"color\": red,\n    \"icon\": \"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'><path d='M16 2a14 14 0 00-5 27v-4l2-7-1-2c0-2 1-3 3-3l1 2-1 4c0 2 1 3 2 3 3 0 5-3 5-7 0-3-3-5-6-5-4 0-6 3-6 6l1 3a302 302 0 01-1 2c-2-1-3-3-3-5 0-5 3-9 9-9 5 0 9 4 9 8 0 5-3 9-7 9l-4-2v4l-2 3a14 14 0 0018-13c0-8-6-14-14-14z'/></svg>\",\n    \"size\": 1.375rem\n  ),\n  \"tiktok\": (\n    \"color\": #ff2c55,\n    \"icon\": \"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'><path d='M7.024 30.054C4.584 28.212 3 25.235 3 21.876c0-5.59 4.39-10.123 9.805-10.123.45 0 .899.031 1.345.094v5.6a4.363 4.363 0 0 0-1.361-.218c-2.477 0-4.485 2.074-4.485 4.631 0 1.809 1.003 3.374 2.467 4.137l.31.146a4.348 4.348 0 0 0 1.708.348c2.471 0 4.476-2.065 4.484-4.615V0h5.335v.704c.02.211.046.42.082.63l.08.404a7.668 7.668 0 0 0 3.306 4.769A7.22 7.22 0 0 0 30 7.665V8.83l-.199-.047-.182-.047.381.094v4.312a12.4 12.4 0 0 1-7.392-2.443v11.177c0 5.591-4.39 10.124-9.804 10.124-2.02 0-3.898-.63-5.458-1.712l-.322-.234Z'/></svg>\"\n  ),\n  \"x\": (\n    \"color\": #1da1f2,\n    \"icon\": \"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 19'><path d='m15.751 0-5.053 5.776L6.328 0H0l7.561 9.888-7.166 8.19h3.068l5.531-6.32 4.834 6.32H20l-7.883-10.42L18.817 0h-3.066ZM3.581 1.74h1.824l10.97 14.502h-1.7L3.58 1.74Z'/></svg>\"\n  )\n) !default;\n// scss-docs-end social-buttons\n// End mod\n\n// Forms\n\n// scss-docs-start form-text-variables\n$form-text-margin-top:                  .4375rem !default; // Boosted mod\n$form-text-font-size:                   $small-font-size !default;\n$form-text-font-style:                  null !default;\n$form-text-font-weight:                 $font-weight-bold !default; // Boosted mod: instead of `null`\n$form-text-line-height:                 $line-height-sm !default; // Boosted mod\n$form-text-color:                       var(--#{$prefix}secondary-color) !default;\n// scss-docs-end form-text-variables\n\n// scss-docs-start form-label-variables\n$form-label-margin-bottom:              .5rem !default; // Boosted mod\n$form-label-font-size:                  null !default;\n$form-label-font-style:                 null !default;\n$form-label-font-weight:                $font-weight-bold !default;\n$form-label-color:                      null !default;\n$form-label-disabled-color:             var(--#{$prefix}disabled-color) !default; // Boosted mod\n$form-label-required-margin-left:       .1875rem !default; // Boosted mod\n$form-label-required-color:             var(--#{$prefix}primary) !default; // Boosted mod\n// scss-docs-end form-label-variables\n\n// scss-docs-start form-helper-variables\n$form-helper-size:                      1.25rem !default; // Boosted mod\n$form-helper-color:                     var(--#{$prefix}info) !default; // Boosted mod\n$form-helper-bg:                        var(--#{$prefix}highlight-color) !default; // Boosted mod\n$form-helper-icon:                      escape-svg($helper-icon) !default; // Boosted mod\n$form-helper-label-margin-bottom:       $form-label-margin-bottom - divide(($form-helper-size - $font-size-base), 2) !default; // Boosted mod\n// scss-docs-end form-helper-variables\n\n// scss-docs-start form-input-variables\n$input-padding-y:                       $input-btn-padding-y !default;\n$input-padding-x:                       $spacer * .5 !default;\n$input-font-family:                     $input-btn-font-family !default;\n$input-font-size:                       $input-btn-font-size !default;\n$input-font-weight:                     $font-weight-bold !default;\n$input-line-height:                     $input-btn-line-height !default;\n\n$input-padding-y-sm:                    divide($input-padding-y, 2) !default; // Boosted mod\n$input-padding-x-sm:                    $input-btn-padding-x-sm !default;\n$input-font-size-sm:                    $input-btn-font-size-sm !default;\n\n$input-padding-y-lg:                    $input-btn-padding-y-lg !default;\n$input-padding-x-lg:                    $input-btn-padding-x-lg !default;\n$input-font-size-lg:                    $input-btn-font-size-lg !default;\n\n$input-bg:                              var(--#{$prefix}body-bg) !default;\n$input-disabled-color:                  var(--#{$prefix}secondary-color) !default; // Boosted mod\n$input-disabled-bg:                     var(--#{$prefix}secondary-bg) !default;\n$input-disabled-border-color:           null !default;\n\n$input-color:                           var(--#{$prefix}body-color) !default;\n$input-border-color:                    var(--#{$prefix}border-color-subtle) !default; // Boosted mod: instead of var(--#{$prefix}border-color)\n$input-border-width:                    $input-btn-border-width !default;\n$input-box-shadow:                      none !default; // Boosted mod\n\n$input-border-radius:                   var(--#{$prefix}border-radius) !default;\n$input-border-radius-sm:                var(--#{$prefix}border-radius-sm) !default;\n$input-border-radius-lg:                var(--#{$prefix}border-radius-lg) !default;\n\n$input-focus-bg:                        $input-bg !default;\n$input-focus-border-color:              currentcolor !default;\n$input-focus-color:                     $input-color !default;\n$input-focus-width:                     $input-btn-focus-width !default;\n$input-focus-box-shadow:                none !default; // Boosted mod\n\n$input-placeholder-color:               var(--#{$prefix}secondary-color) !default;\n$input-plaintext-color:                 null !default; // Boosted mod: instead of `var(--#{$prefix}body-color)`\n\n// Boosted mod: no $input-height-border\n\n$input-height-inner:                    add($input-line-height * 1em, $input-padding-y * 2) !default;\n$input-height-inner-half:               $spacer !default; // Boosted mod\n$input-height-inner-quarter:            map-get($spacers, 2) !default; // Boosted mod\n\n$input-height:                          2.5rem !default;\n$input-height-sm:                       1.875rem !default;\n$input-height-lg:                       3.125rem !default;\n$input-line-height-lg:                  $h5-line-height !default; // Boosted mod\n\n$input-transition:                      border-color $transition-duration $transition-timing, $transition-focus !default;\n\n$form-color-width:                      2.5rem !default; // Boosted mod: instead of `3rem`\n$form-color-border-color:               var(--#{$prefix}emphasis-color) !default; // Boosted mod\n$form-color-hover-bg-color:             var(--#{$prefix}emphasis-color) !default; // Boosted mod\n$form-color-disabled-bg-color:          $input-bg !default; // Boosted mod\n$form-color-disabled-border-color:      var(--#{$prefix}disabled-color) !default; // Boosted mod\n$form-color-disabled-background-swatch: var(--#{$prefix}form-color-disabled-filter) !default; // Boosted mod\n$form-color-disabled-filter:            brightness(0) invert(1) brightness(.8) !default; // Boosted mod\n// scss-docs-end form-input-variables\n\n// scss-docs-start form-check-variables\n$form-check-input-width:                  1em !default;\n$form-check-min-height:                   $font-size-base * $input-btn-line-height !default;\n$form-check-padding-start:                $form-check-input-width + .5em !default;\n$form-check-margin-bottom:                .125rem !default;\n$form-check-label-padding-top:            .4375rem !default; // Boosted mod\n$form-check-label-color:                  null !default;\n$form-check-label-cursor:                 null !default;\n$form-check-transition:                   null !default;\n$form-check-filter:                       $invert-filter !default; // Boosted mod\n\n$form-check-input-active-filter:          null !default;\n$form-check-input-active-bg-color:        $component-active-bg !default; // Boosted mod\n\n$form-check-input-bg:                     $input-bg !default;\n$form-check-input-border:                 var(--#{$prefix}border-width) solid $input-border-color !default; // Boosted mod: instead of `var(--#{$prefix}border-width) solid var(--#{$prefix}border-color)`\n$form-check-input-border-radius:          0 !default;\n$form-check-radio-border-radius:          50% !default;\n$form-check-input-focus-border:           null !default;\n$form-check-input-focus-box-shadow:       $focus-ring-box-shadow !default;\n\n$form-check-input-checked-color:          $component-active-color !default;\n$form-check-input-checked-bg-color:       $component-active-bg !default;\n$form-check-input-checked-border-color:   $form-check-input-checked-bg-color !default;\n$form-check-input-checked-bg-image:       var(--#{$prefix}check-icon) !default;\n$form-check-input-disabled-color:         $gray-900 !default; // Boosted mod\n$form-check-input-disabled-filter:        var(--#{$prefix}form-check-filter) !default; // Boosted mod\n$form-check-radio-checked-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='2' fill='#{$form-check-input-checked-color}'/></svg>\") !default;\n\n$form-check-input-indeterminate-color:          $form-check-input-checked-color !default;\n$form-check-input-indeterminate-bg-color:       $form-check-input-checked-bg-color !default;\n$form-check-input-indeterminate-border-color:   $form-check-input-indeterminate-bg-color !default;\n$form-check-input-indeterminate-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 10 3'><path fill='#{$form-check-input-indeterminate-color}' d='M0 0h10v3H0z'/></svg>\") !default;\n\n$form-check-input-disabled-opacity:        null !default;\n$form-check-label-disabled-opacity:        null !default;\n$form-check-btn-check-disabled-opacity:    null !default;\n\n$form-check-inline-margin-end:    1rem !default;\n\n// Boosted mod: Star rating\n$form-star-size:                        1.5625rem !default;\n$form-star-size-sm:                     1.25rem !default;\n$form-star-margin-between:              -.125rem !default;\n\n$form-star-rating-checked-color:        var(--#{$prefix}primary) !default;\n$form-star-rating-unchecked-color:      var(--#{$prefix}secondary-color) !default;\n$form-star-rating-hover-color:          var(--#{$prefix}highlight-bg) !default;\n$form-star-rating-disabled-color:       var(--#{$prefix}disabled-color) !default;\n\n$form-star-rating-checked-icon:         escape-svg(url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 25 25'><path fill='#{$black}' stroke='#{$black}' d='m12.5 4.523 2.016 6.227 6.542-.005-5.296 3.843 2.027 6.224L12.5 16.96l-5.289 3.852 2.027-6.224-5.296-3.843 6.542.005L12.5 4.523Z'/></svg>\")) !default;\n$form-star-rating-unchecked-icon:       escape-svg(url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 25 25'><path fill='transparent' stroke='#{$black}' d='m12.5 4.523 2.016 6.227 6.542-.005-5.296 3.843 2.027 6.224L12.5 16.96l-5.289 3.852 2.027-6.224-5.296-3.843 6.542.005L12.5 4.523Z'/></svg>\")) !default;\n$form-star-rating-sm-checked-icon:      escape-svg(url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='#{$black}' stroke='#{$black}' d='M10 3.943 11.54 8.7l4.998-.004-4.046 2.936 1.548 4.755L10 13.444l-4.04 2.943 1.548-4.755-4.046-2.936L8.46 8.7 10 3.943Z'/></svg>\")) !default;\n$form-star-rating-sm-unchecked-icon:    escape-svg(url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='transparent' stroke='#{$black}' d='M10 3.943 11.54 8.7l4.998-.004-4.046 2.936 1.548 4.755L10 13.444l-4.04 2.943 1.548-4.755-4.046-2.936L8.46 8.7 10 3.943Z'/></svg>\")) !default;\n//fusv-disable\n$form-star-focus-color:                 $black !default; // Deprecated in v5.2.3\n$form-star-focus-outline:               var(--#{$prefix}border-width) solid $form-star-focus-color !default; // Deprecated in v5.2.3\n$form-star-focus-color-dark:            $white !default; // Deprecated in v5.2.3\n$form-star-focus-outline-dark:          var(--#{$prefix}border-width) solid $form-star-focus-color-dark !default; // Deprecated in v5.2.3\n$form-star-focus-box-shadow:            $input-btn-focus-box-shadow !default; // Deprecated in v5.2.3\n//fusv-enable\n\n// End mod\n// scss-docs-end form-check-variables\n\n// scss-docs-start form-switch-variables\n// Boosted mod: no $form-switch-color\n$form-switch-width:               $spacer * 3 !default; // Boosted mod\n$form-switch-padding-start:       $form-switch-width + .625rem !default; // Boosted mod\n$form-switch-bg-image:            var(--#{$prefix}close-icon) !default;  // Boosted mod\n$form-switch-bg-position:         right .5rem top 50% !default;  // Boosted mod\n$form-switch-bg-size:             .75rem !default;  // Boosted mod\n$form-switch-bg-square-size:      add(1rem, $spacer * .5) !default;  // Boosted mod\n$form-switch-border-radius:       null !default; // Boosted mod\n$form-switch-transition:          background-position .15s ease-in-out, $transition-focus !default; // Boosted mod\n\n$form-switch-square-bg:             $black !default; // Boosted mod\n$form-switch-bg:                    $white !default; // Boosted mod\n$form-switch-border-color:          $white !default; // Boosted mod\n$form-switch-filter:                var(--#{$prefix}form-check-filter) !default; // Boosted mod\n$form-switch-focus-visible-inner:   $black !default; // Boosted mod\n$form-switch-focus-visible-outer:   $white !default; // Boosted mod\n\n// Boosted mod: no $form-switch-focus-color\n// Boosted mod: no $form-switch-focus-bg-image\n\n// Boosted mod: no $form-switch-checked-color\n$form-switch-checked-bg-image:    $form-check-input-checked-bg-image !default; // Boosted mod\n$form-switch-checked-bg-size:     add(map-get($spacers, 2), map-get($spacers, 1)) !default; // Boosted mod\n// stylelint-disable-next-line function-disallowed-list\n$form-switch-checked-bg-position: calc(var(--#{$prefix}border-width) * 3) 50% !default; // Boosted mod\n\n$form-switch-checked-square-bg:                 var(--#{$prefix}body-bg) !default; // Boosted mod\n$form-switch-checked-bg:                        $supporting-orange !default; // Boosted mod\n$form-switch-checked-border-color:              $supporting-orange !default; // Boosted mod\n$form-switch-checked-filter:                    none !default; // Boosted mod\n$form-switch-checked-focus-inner:               var(--#{$prefix}focus-visible-inner-color) !default; // Boosted mod\n$form-switch-checked-focus-outer:               var(--#{$prefix}focus-visible-outer-color) !default; // Boosted mod\n$form-switch-unchecked-invalid-border-color:    #31c3eb !default; // Boosted mod: will be rendered red when mixed with the filter\n// scss-docs-end form-switch-variables\n\n// scss-docs-start input-group-variables\n$input-group-addon-padding-y:           $input-padding-y !default;\n$input-group-addon-padding-x:           $input-padding-x !default;\n$input-group-addon-font-weight:         $input-font-weight !default;\n$input-group-addon-color:               $input-color !default; // Boosted mod: instead of `null`\n$input-group-addon-bg:                  null !default; // Boosted mod: instead of `var(--#{$prefix}tertiary-bg)`\n$input-group-addon-border-color:        null !default;\n// scss-docs-end input-group-variables\n\n// scss-docs-start form-select-variables\n$form-select-padding-y:             $input-padding-y !default;\n$form-select-padding-x:             $input-padding-x !default;\n$form-select-font-family:           $input-font-family !default;\n$form-select-font-size:             $input-font-size !default;\n$form-select-indicator-padding:     $form-select-padding-x * 3 !default; // Extra padding for background-image\n$form-select-font-weight:           $input-font-weight !default;\n$form-select-line-height:           $input-line-height !default;\n$form-select-color:                 $input-color !default;\n$form-select-bg:                    $input-bg !default;\n$form-select-disabled-color:        $input-disabled-color !default; // Boosted mod: instead of `null`\n$form-select-disabled-bg:           $input-disabled-bg !default;\n$form-select-disabled-border-color: $input-disabled-border-color !default;\n$form-select-bg-position:           right $form-select-padding-x top add(50%, 1px) !default;\n$form-select-bg-size:               .875rem 1rem !default; // In pixels because image dimensions\n$form-select-indicator:             escape-svg(url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 14 7'><path d='M7 7 0 0h14L7 7z'/></svg>\")) !default; // Boosted mod: instead of Bootstrap svg\n$form-select-disabled-indicator:    escape-svg(url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 14 7'><path fill='#{$gray-700}' d='M7 7 0 0h14L7 7z'/></svg>\")) !default; // Boosted mod\n\n$form-select-feedback-icon-padding-end: $form-select-padding-x * 2.5 + $form-select-indicator-padding !default;\n$form-select-feedback-icon-position:    center right $form-select-indicator-padding !default;\n$form-select-feedback-icon-size:        $input-height-inner-half $input-height-inner-half !default;\n\n$form-select-border-width:        $input-border-width !default;\n$form-select-border-color:        $input-border-color !default;\n$form-select-border-radius:       $input-border-radius !default;\n$form-select-box-shadow:          none !default; // Boosted mod\n\n$form-select-focus-border-color:  $input-color !default; // Boosted mod: handle a Firefox-specific visible focus rendering where we remove the border from the select box (see `.form-select` rule)\n// Boosted mod: no $form-select-focus-width\n$form-select-focus-box-shadow:    none !default; // Boosted mod\n\n$form-select-padding-y-sm:        add($input-padding-y-sm, 1px) !default; // Boosted mod\n$form-select-padding-x-sm:        $input-padding-x-sm !default;\n$form-select-font-size-sm:        $input-font-size-sm !default;\n$form-select-border-radius-sm:    $input-border-radius-sm !default;\n\n$form-select-padding-y-lg:        $spacer * .5 !default; // Boosted mod\n$form-select-padding-x-lg:        $input-padding-x-lg !default;\n$form-select-font-size-lg:        $input-font-size-lg !default;\n$form-select-border-radius-lg:    $input-border-radius-lg !default;\n\n$form-select-transition:          $input-transition !default;\n// scss-docs-end form-select-variables\n\n// scss-docs-start form-range-variables\n$form-range-track-width:          100% !default;\n$form-range-track-height:         .375rem !default; // Boosted mod: instead of `.5rem`\n$form-range-track-cursor:         pointer !default;\n$form-range-track-bg:             var(--#{$prefix}secondary-bg) !default;\n$form-range-track-filled-bg:      var(--#{$prefix}primary) !default; // Boosted mod\n$form-range-track-border-radius:  null !default; // Boosted mod: instead of `1rem`\n$form-range-track-box-shadow:     var(--#{$prefix}box-shadow-inset) !default;\n\n$form-range-thumb-width:                   1rem !default;\n$form-range-thumb-height:                  $form-range-thumb-width !default;\n$form-range-thumb-bg:                      var(--#{$prefix}body-bg) !default; // Boosted mod: instead of `$component-active-bg`\n$form-range-thumb-border:                  var(--#{$prefix}border-width) solid var(--#{$prefix}border-color) !default; // Boosted mod: instead of `0`\n$form-range-thumb-border-radius:           50% !default;\n$form-range-thumb-box-shadow:              0 .1rem .25rem rgba($black, .1) !default;\n$form-range-thumb-focus-box-shadow:        null !default; // Boosted mod\n$form-range-thumb-focus-box-shadow-width:  $input-focus-width !default; // For focus box shadow issue in Edge\n$form-range-thumb-hover-bg:                var(--#{$prefix}highlight-bg) !default; // Boosted mod\n$form-range-thumb-active-bg:               var(--#{$prefix}primary) !default; // Boosted mod: instead of `tint-color($component-active-bg, 70%)`\n$form-range-thumb-active-border:           var(--#{$prefix}primary) !default; // Boosted mod\n$form-range-thumb-disabled-bg:             var(--#{$prefix}disabled-color) !default; // Boosted mod: instead of `var(--#{$prefix}secondary-color)`\n$form-range-thumb-transition:              background-color $transition-duration $transition-timing, border-color $transition-duration $transition-timing !default; // Boosted mod: no box shadow\n// scss-docs-end form-range-variables\n\n// scss-docs-start form-file-variables\n$form-file-button-color:          $input-color !default;\n$form-file-button-bg:             $input-bg !default; // Boosted mod: instead of `var(--#{$prefix}tertiary-bg)`\n$form-file-button-hover-bg:       var(--#{$prefix}secondary-bg) !default;\n// scss-docs-end form-file-variables\n\n// Boosted mod: no floating labels\n\n// Form validation\n\n// scss-docs-start form-feedback-variables\n$form-feedback-margin-top:          $form-text-margin-top !default;\n$form-feedback-font-size:           $small-font-size !default;\n$form-feedback-font-style:          null !default;\n// fusv-disable\n$form-feedback-valid-color:         $success !default; // Boosted mod: deprecated in v5.3.0\n$form-feedback-invalid-color:       $danger !default; // Boosted mod: deprecated in v5.3.0\n// fusv-enable\n\n$form-feedback-icon-valid:          var(--#{$prefix}success-icon) !default;\n$form-feedback-icon-invalid:        var(--#{$prefix}error-icon) !default;\n$form-feedback-icon-size:           add($spacer * .25, $spacer * .5) !default; // Boosted mod\n$form-feedback-line-height:         $line-height-sm !default; // Boosted mod\n$form-feedback-color:               null !default; // Boosted mod\n// scss-docs-end form-feedback-variables\n\n// scss-docs-start form-validation-colors\n$form-valid-color:                  var(--#{$prefix}success-text-emphasis) !default; // Boosted mod: instead of `$form-feedback-valid-color`\n$form-valid-border-color:           var(--#{$prefix}success) !default; // Boosted mod: instead of `$form-feedback-valid-color`\n$form-invalid-color:                var(--#{$prefix}danger-text-emphasis) !default; // Boosted mod: instead of `$form-feedback-invalid-color`\n$form-invalid-border-color:         var(--#{$prefix}danger) !default; // Boosted mod: instead of `$form-feedback-invalid-color`\n// scss-docs-end form-validation-colors\n\n// scss-docs-start form-validation-states\n$form-validation-states: (\n  \"valid\": (\n    \"color\": var(--#{$prefix}form-valid-color),\n    \"icon\": $form-feedback-icon-valid,\n    // Boosted mod: no `tooltip-color`\n    // Boosted mod: no `tooltip-bg-color`\n    // Boosted mod: no `focus-box-shadow`\n    \"border-color\": var(--#{$prefix}form-valid-border-color),\n  ),\n  \"invalid\": (\n    \"color\": var(--#{$prefix}form-invalid-color),\n    \"icon\": $form-feedback-icon-invalid,\n    // Boosted mod: no `tooltip-color`\n    // Boosted mod: no `tooltip-bg-color`\n    // Boosted mod: no `focus-box-shadow`\n    \"border-color\": var(--#{$prefix}form-invalid-border-color),\n  )\n) !default;\n// scss-docs-end form-validation-states\n\n// Z-index master list\n//\n// Warning: Avoid customizing these values. They're used for a bird's eye view\n// of components dependent on the z-axis and are designed to all work together.\n\n// scss-docs-start zindex-stack\n$zindex-dropdown:                   1000 !default;\n$zindex-sticky:                     1020 !default;\n$zindex-fixed:                      1030 !default;\n$zindex-back-to-top:                1035 !default; // Boosted mod\n$zindex-offcanvas-backdrop:         1040 !default;\n$zindex-offcanvas:                  1045 !default;\n$zindex-modal-backdrop:             1050 !default;\n$zindex-modal:                      1055 !default;\n$zindex-popover:                    1070 !default;\n$zindex-tooltip:                    1080 !default;\n$zindex-toast:                      1090 !default;\n// scss-docs-end zindex-stack\n\n// scss-docs-start zindex-levels-map\n$zindex-levels: (\n  n1: -1,\n  0: 0,\n  1: 1,\n  2: 2,\n  3: 3\n) !default;\n// scss-docs-end zindex-levels-map\n\n\n// Navs\n\n// scss-docs-start nav-variables\n$nav-link-padding-y:                $spacer * .5 !default;\n$nav-link-padding-x:                $spacer !default;\n$nav-link-font-size:                null !default;\n$nav-link-font-weight:              $font-weight-bold !default;\n$nav-link-color:                    inherit !default; // Boosted mod: instead of `var(--#{$prefix}link-color)`\n$nav-link-hover-color:              var(--#{$prefix}link-hover-color) !default;\n$nav-link-transition:               null !default; // Boosted mod\n$nav-link-disabled-color:           var(--#{$prefix}disabled-color) !default; // Boosted mod: instead of `var(--#{$prefix}secondary-color)`\n// Boosted mod: no `$nav-link-focus-box-shadow`\n\n$nav-tabs-border-color:             var(--#{$prefix}border-color) !default;\n$nav-tabs-border-width:             var(--#{$prefix}border-width) !default;\n$nav-tabs-border-radius:            var(--#{$prefix}border-radius) !default;\n$nav-tabs-link-padding-x:           1.8125rem !default; // Boosted mod\n$nav-tabs-link-hover-color:         var(--#{$prefix}highlight-color) !default; // Boosted mod\n$nav-tabs-link-hover-bg:            var(--#{$prefix}highlight-bg) !default; // Boosted mod\n$nav-tabs-link-hover-border-color:  var(--#{$prefix}border-color) !default; // Boosted mod: instead of `var(--#{$prefix}secondary-bg) var(--#{$prefix}secondary-bg) $nav-tabs-border-color`\n$nav-tabs-link-active-color:        var(--#{$prefix}emphasis-color) !default;\n$nav-tabs-link-active-bg:           var(--#{$prefix}body-bg) !default;\n$nav-tabs-link-active-border-color: $nav-tabs-link-active-color !default; // Boosted mod: instead of `var(--#{$prefix}border-color) var(--#{$prefix}border-color) $nav-tabs-link-active-bg`\n\n$nav-pills-padding-x:               1.8125rem !default; // Boosted mod\n$nav-pills-border-radius:           var(--#{$prefix}border-radius) !default;\n$nav-pills-link-active-color:       $component-active-color !default;\n$nav-pills-link-active-bg:          $component-active-bg !default;\n\n$nav-underline-gap:                       0 !default; // Boosted mod: instead of 1rem\n$nav-underline-gap-lg:                    $spacer * .5 !default; // Boosted mod\n// stylelint-disable-next-line function-disallowed-list\n$nav-underline-border-width:              calc(var(--#{$prefix}border-width) * .5) !default; // Boosted mod: instead of `.125rem`\n$nav-underline-border-color:              var(--#{$prefix}border-color-subtle) !default; // Boosted mod\n$nav-underline-border-radius:             var(--#{$prefix}border-radius) !default; // Boosted mod\n$nav-underline-link-active-color:         var(--#{$prefix}emphasis-color) !default;\n$nav-underline-link-padding-x:            1.8125rem !default; // Boosted mod\n$nav-underline-link-hover-color:          var(--#{$prefix}link-hover-color) !default; // Boosted mod\n// stylelint-disable-next-line function-disallowed-list\n$nav-underline-link-border-width:         0 0 calc(var(--#{$prefix}nav-underline-border-width) * 4) !default; // Boosted mod\n$nav-underline-link-active-bg:            transparent !default; // Boosted mod\n$nav-underline-link-active-border-color:  var(--#{$prefix}primary) !default; // Boosted mod\n// scss-docs-end nav-variables\n\n\n// Navbar\n\n// scss-docs-start navbar-variables\n$navbar-padding-y:                    .375rem !default; // Boosted mod\n$navbar-padding-x:                    null !default;\n$navbar-font-weight:                  $font-weight-bold !default; // Boosted mod\n\n$navbar-nav-link-padding-y:           1rem !default; // Boosted mod\n$navbar-nav-link-padding-x-xs:        $spacer * .25 !default; // Boosted mod\n$navbar-nav-link-padding-x:           $spacer * .5 !default; // Boosted mod\n\n$navbar-brand-font-size:              2.1875rem !default; // Boosted mod\n// Boosted mod: no nav-link-height calculation\n$navbar-brand-padding-y:              0 !default; // Boosted mod\n$navbar-brand-margin-end:             $spacer * 1.5 !default; // Boosted mod\n\n$navbar-toggler-icon-close-bg:        $cross-icon !default; // Boosted mod\n$navbar-toggler-padding-y:            $spacer * .6 !default; // Boosted mod: same as $navbar-nav-icon-padding-y-xs\n$navbar-toggler-padding-x:            $spacer * .75 !default; // Boosted mod: same as $navbar-nav-icon-padding-x-xs\n$navbar-toggler-font-size-xs:         1.04166666rem !default; // Boosted mod\n$navbar-toggler-font-size:            1.25rem !default; // Boosted mod\n$navbar-toggler-border-radius:        $btn-border-radius !default;\n$navbar-toggler-focus-width:          null !default; // Boosted mod\n$navbar-toggler-transition:           $transition-focus !default; // Boosted mod\n\n$navbar-light-color:                  var(--#{$prefix}emphasis-color) !default; // Boosted mod: instead of `rgba(var(--#{$prefix}emphasis-color-rgb), .65)`\n$navbar-light-bg:                     var(--#{$prefix}highlight-color) !default; // Boosted mod\n$navbar-light-hover-color:            var(--#{$prefix}link-hover-color) !default; // Boosted mod: instead of `rgba(var(--#{$prefix}emphasis-color-rgb), .8)`\n$navbar-light-active-color:           var(--#{$prefix}primary) !default; // Boosted mod: instead of `rgba(var(--#{$prefix}emphasis-color-rgb), 1)`\n$navbar-light-disabled-color:         var(--#{$prefix}disabled-color) !default; // Boosted mod: instead of `rgba(var(--#{$prefix}emphasis-color-rgb), .3)`\n$navbar-light-icon-color:             var(--#{$prefix}emphasis-color) !default; // Boosted mod: instead of `rgba($body-color, .75)`\n$navbar-light-icon-hover-color:       var(--#{$prefix}link-hover-color) !default; // Boosted mod\n$navbar-light-toggler-icon-bg:        $burger-icon !default; // Boosted mod: instead of inline SVG\n$navbar-light-toggler-icon-bg-small:  $burger-icon-small !default; // Boosted mod: slightly different burger icon for small breakpoints\n$navbar-light-toggler-border-color:   null !default; // Boosted mod: instead of `rgba(var(--#{$prefix}emphasis-color-rgb), .15)`\n$navbar-light-brand-color:            $navbar-light-color !default; // Boosted mod: instead of `$navbar-light-active-color`\n$navbar-light-brand-hover-color:      $navbar-light-active-color !default;\n// scss-docs-end navbar-variables\n\n// Boosted mod: Orange navbar\n// scss-docs-start orange-navbar-variables\n$navbar-transition-duration:                $transition-duration !default;\n$navbar-transition-timing-function:         $transition-timing !default;\n$navbar-transition:                         padding-top $navbar-transition-duration $navbar-transition-timing-function, padding-bottom $navbar-transition-duration $navbar-transition-timing-function, $transition-focus !default;\n$navbar-brand-transition:                   margin $navbar-transition-duration $navbar-transition-timing-function, $transition-focus !default;\n$navbar-brand-logo-transition:              width $navbar-transition-duration $navbar-transition-timing-function, height $navbar-transition-duration $navbar-transition-timing-function !default;\n$navbar-active-transition:                  bottom $navbar-transition-duration $navbar-transition-timing-function !default;\n\n$navbar-border-width:                       calc(var(--#{$prefix}border-width) * .5) !default; // stylelint-disable-line function-disallowed-list\n$navbar-border-color:                       var(--#{$prefix}border-color-subtle) !default;\n\n$navbar-brand-margin-y-xs:                  $spacer * .5 !default;\n$navbar-brand-logo-size-xs:                 $spacer * 1.5 !default;\n$navbar-brand-font-size-xs:                 1.3125rem !default;\n$navbar-brand-letter-spacing-xs:            $letter-spacing-base * 5 !default;\n$navbar-brand-font-size-two-lined-xs:       1.0625rem !default;\n$navbar-brand-letter-spacing-two-lined-xs:  $letter-spacing-base * 4 !default;\n\n$navbar-brand-margin-y:                     $spacer * .95 !default;\n$navbar-brand-logo-size:                    $spacer * 2.5 !default;\n$navbar-brand-letter-spacing:               $letter-spacing-base * 10 !default;\n$navbar-brand-font-size-two-lined:          1.8125rem !default;\n$navbar-brand-letter-spacing-two-lined:     $letter-spacing-base * 8 !default;\n\n$navbar-icon-size-xs:                       $spacer * 1.25 !default;\n$navbar-icon-size:                          $spacer * 1.5 !default;\n\n$navbar-nav-icon-padding-y-xs:              $spacer * .6 !default;\n$navbar-nav-icon-padding-x-xs:              $spacer * .75 !default;\n$navbar-nav-icon-padding-y:                 $navbar-brand-margin-y !default;\n$navbar-nav-icon-padding-x:                 $spacer !default;\n\n$navbar-supra-link-padding-y:               $spacer * .6 !default;\n$navbar-supra-link-padding-x:               .46875rem !default;\n$navbar-supra-icon-padding-y:               $spacer * .25 !default;\n$navbar-supra-icon-padding-x:               $navbar-nav-icon-padding-x-xs !default;\n$navbar-supra-icon-size:                    $navbar-icon-size-xs !default;\n\n$navbar-minimized-brand-margin-y:           $spacer * .75 !default;\n$navbar-minimized-nav-icon-padding-y:       $navbar-minimized-brand-margin-y !default;\n$navbar-minimized-toggler-padding-y:        $navbar-minimized-brand-margin-y !default;\n\n$navbar-badge-padding-y:                    .125rem !default;\n$navbar-badge-padding-x:                    .375rem !default;\n$navbar-badge-margin-top:                   .375rem !default;\n// scss-docs-end orange-navbar-variables\n// End mod\n\n// Deprecated in v5.3.3: all `$navbar-dark-*`\n$navbar-dark-border-color:          $gray-700 !default; // Boosted mod\n$navbar-dark-color:                 $white !default; // Boosted mod: instead of `rgba($white, .55)`\n$navbar-dark-hover-color:           $supporting-orange !default; // Boosted mod: instead of `rgba($white, .75)`\n$navbar-dark-active-color:          $supporting-orange !default; // Boosted mod: instead of `$white`\n$navbar-dark-disabled-color:        $gray-700 !default; // Boosted mod: instead of `rgba($white, .25)`\n// Boosted mod: no $navbar-dark-icon-color\n// Boosted mod: no $navbar-dark-toggler-icon-bg since dark toggler are handled with filter\n$navbar-dark-toggler-border-color:  transparent !default; // Boosted mod: instead of `rgba($white, .1)`\n$navbar-dark-brand-color:           inherit !default; // Boosted mod: instead of `$navbar-dark-active-color`\n$navbar-dark-brand-hover-color:     $navbar-dark-active-color !default;\n\n\n// Dropdowns\n//\n// Dropdown menu container and contents.\n\n// scss-docs-start dropdown-variables\n$dropdown-min-width:                10rem !default;\n$dropdown-padding-x:                $spacer * .5 !default; // Boosted mod: instead of `0`\n$dropdown-padding-y:                0 !default; // Boosted mod: instead of `.5rem`\n$dropdown-spacer:                   0 !default; // Boosted mod: instead of `.125rem`\n$dropdown-font-size:                $font-size-base !default;\n$dropdown-line-height:              $line-height-base !default; // Boosted mod\n$dropdown-color:                    var(--#{$prefix}body-color) !default;\n$dropdown-bg:                       var(--#{$prefix}body-bg) !default;\n$dropdown-border-color:             var(--#{$prefix}border-color-subtle) !default; // Boosted mod: instead of `var(--#{$prefix}border-color-translucent)`\n$dropdown-border-radius:            var(--#{$prefix}border-radius) !default;\n$dropdown-border-width:             var(--#{$prefix}border-width) !default;\n$dropdown-inner-border-radius:      0 !default; // Boosted mod: instead of `calc(#{$dropdown-border-radius} - #{$dropdown-border-width})`\n$dropdown-divider-bg:               $dropdown-border-color !default;\n$dropdown-divider-margin-y:         $spacer * .25 !default; // Boosted mod: instead of `$spacer * .5`\n$dropdown-box-shadow:               var(--#{$prefix}box-shadow) !default;\n\n$dropdown-link-color:               null !default; // Boosted mod: instead of `var(--#{$prefix}body-color)`\n$dropdown-link-hover-color:         $dropdown-link-color !default;\n$dropdown-link-hover-bg:            var(--#{$prefix}secondary-bg) !default; // Boosted mod: instead of `var(--#{$prefix}tertiary-bg)`\n\n$dropdown-link-active-color:        $dropdown-link-color !default; // Boosted mod: instead of `$component-active-color`\n$dropdown-link-active-bg:           var(--#{$prefix}tertiary-active-bg) !default; // Boosted mod: instead of `$component-active-bg`\n\n$dropdown-link-disabled-color:      var(--#{$prefix}disabled-color) !default; // Boosted mod: instead of `var(--#{$prefix}tertiary-color)`\n\n$dropdown-item-padding-y:           $spacer * .5 !default; // Boosted mod: instead of `$spacer * .25`\n$dropdown-item-padding-x:           $spacer * .5 !default; // Boosted mod: instead of `$spacer`\n\n$dropdown-header-color:             null !default; // Boosted mod: instead of `$gray-600`\n$dropdown-header-padding-x:         $dropdown-item-padding-x !default;\n$dropdown-header-padding-y:         $spacer !default; // Boosted mod: instead of `$dropdown-padding-y`\n// fusv-disable\n$dropdown-header-padding:           $dropdown-header-padding-y $dropdown-header-padding-x !default; // Deprecated in v5.2.0\n// fusv-enable\n// scss-docs-end dropdown-variables\n\n// Deprecated in v5.3.3: all `$dropdown-dark-*`\n$dropdown-dark-color:               $white !default; // Boosted mod\n$dropdown-dark-bg:                  $black !default; // Boosted mod\n$dropdown-dark-border-color:        $gray-700 !default; // Boosted mod\n$dropdown-dark-divider-bg:          $dropdown-dark-border-color !default; // Boosted mod\n$dropdown-dark-box-shadow:          null !default;\n$dropdown-dark-link-color:          $dropdown-dark-color !default;\n$dropdown-dark-link-hover-color:    $white !default;\n$dropdown-dark-link-hover-bg:       $gray-700 !default; // Boosted mod\n$dropdown-dark-link-active-color:   $black !default; // Boosted mod\n$dropdown-dark-link-active-bg:      $white !default; // Boosted mod\n$dropdown-dark-link-disabled-color: $gray-700 !default; // Boosted mod\n$dropdown-dark-header-color:        $white !default; // Boosted mod\n\n// Pagination\n\n// scss-docs-start pagination-variables\n$pagination-padding-y:              null !default; // Boosted mod: instead of `.375rem`\n$pagination-padding-x:              null !default; // Boosted mod: instead of `.75rem`\n// Boosted mod: no $pagination-padding-y-sm\n// Boosted mod: no $pagination-padding-x-sm\n// Boosted mod: no $pagination-padding-y-lg\n// Boosted mod: no $pagination-padding-x-lg\n\n$pagination-font-size:              $font-size-base !default;\n\n$pagination-color:                  inherit !default; // Boosted mod: instead of `var(--#{$prefix}link-color)`\n$pagination-bg:                     transparent !default; // Boosted mod: instead of `var(--#{$prefix}body-bg)`\n$pagination-border-radius:          var(--#{$prefix}border-radius) !default;\n$pagination-border-width:           var(--#{$prefix}border-width) !default;\n$pagination-margin-y:               $spacer !default; // Boosted mod\n$pagination-margin-start:           0 !default; // Boosted mod: instead of `calc(-1 * $pagination-border-width)`\n$pagination-margin-x-first-last:    $spacer * .5 !default; // Boosted mod\n$pagination-border-color:           transparent !default; // Boosted mod: instead of `var(--#{$prefix}border-color)`\n\n// Deprecated in v5.3.3\n// fusv-disable\n$pagination-focus-color:            null !default; // Boosted mod\n$pagination-focus-bg:               null !default; // Boosted mod: instead of `var(--#{$prefix}secondary-bg)`\n$pagination-focus-box-shadow:       0 0 0 $focus-visible-inner-width var(--#{$prefix}focus-visible-inner-color) !default; // Boosted mod: no `$focus-ring-box-shadow`\n$pagination-focus-outline:          null !default; // Boosted mod\n// fusv-enable\n\n$pagination-hover-color:            var(--#{$prefix}body-color) !default; // Boosted mod: instead of `var(--#{$prefix}link-hover-color)`\n$pagination-hover-bg:               var(--#{$prefix}secondary-bg) !default; // Boosted mod: instead of `var(--#{$prefix}tertiary-bg)`\n$pagination-hover-border-color:     $pagination-hover-bg !default; // Boosted mod: instead of `var(--#{$prefix}border-color)`\n\n$pagination-active-color:           var(--#{$prefix}highlight-color) !default; // Boosted mod: instead of `$component-active-color`\n$pagination-active-bg:              var(--#{$prefix}highlight-bg) !default; // Boosted mod: instead of `$component-active-bg`\n$pagination-active-border-color:    $pagination-active-bg !default; // Boosted mod: instead of `$component-active-bg`\n\n$pagination-disabled-color:         var(--#{$prefix}disabled-color) !default; // Boosted mod: instead of `var(--#{$prefix}secondary-color)`\n$pagination-disabled-bg:            transparent !default; // Boosted mod: instead of `var(--#{$prefix}secondary-bg)`\n$pagination-disabled-border-color:  transparent !default; // Boosted mod: instead of `var(--#{$prefix}border-color)`\n\n$pagination-transition:             $transition-focus !default; // Boosted mod: no color, bg-color, border-color, box-shadow\n\n// Boosted mod: no $pagination-border-radius-sm\n// Boosted mod: no $pagination-border-radius-lg\n\n// Boosted mod\n$pagination-padding-end:            1.125rem !default;\n$pagination-icon:                   var(--#{$prefix}chevron-icon) !default;\n$pagination-icon-size:              subtract($spacer * 2, calc(var(--#{$prefix}border-width) * 2)) !default; // stylelint-disable-line function-disallowed-list\n$pagination-icon-width:             add(.5rem, 1px) !default;\n$pagination-icon-height:            subtract(1rem, 1px) !default;\n\n$pagination-active-item-bg:         $supporting-orange !default;\n$pagination-active-item-color:      $black !default;\n$pagination-active-item-border:     $pagination-active-item-bg !default;\n// End mod\n// scss-docs-end pagination-variables\n\n\n// Placeholders\n\n// scss-docs-start placeholders\n$placeholder-opacity-max:           .5 !default;\n$placeholder-opacity-min:           .2 !default;\n// scss-docs-end placeholders\n\n// Cards\n\n// scss-docs-start card-variables\n$card-spacer-top:                   $spacer * .75 !default; // Boosted mod\n$card-spacer-bottom:                $spacer !default; // Boosted mod\n// fusv-disable\n$card-spacer-y:                     $spacer !default; // Deprecated in v5.2.3\n// fusv-enable\n$card-spacer-x:                     $spacer !default;\n$card-title-spacer-y:               $spacer * .5 !default;\n$card-title-color:                  null !default;\n$card-subtitle-color:               null !default;\n$card-border-width:                 var(--#{$prefix}border-width) !default;\n$card-border-color:                 var(--#{$prefix}border-color-subtle) !default; // Boosted mod: instead of `var(--#{$prefix}border-color-translucent)`\n$card-border-radius:                var(--#{$prefix}border-radius) !default;\n$card-box-shadow:                   null !default;\n$card-inner-border-radius:          subtract($card-border-radius, $card-border-width) !default;\n$card-cap-padding-y:                $card-spacer-bottom * .5 !default; // Boosted mod\n$card-cap-padding-x:                $card-spacer-x !default;\n$card-cap-bg:                       var(--#{$prefix}highlight-bg) !default; // Boosted mod: instead of `rgba(var(--#{$prefix}body-color-rgb), .03)`\n$card-cap-color:                    var(--#{$prefix}highlight-color) !default; // Boosted mod: instead of `null`\n$card-cap-font-weight:              $font-weight-bold !default; // Boosted mod\n$card-height:                       null !default;\n$card-color:                        null !default;\n$card-bg:                           var(--#{$prefix}body-bg) !default;\n$card-img-overlay-padding:          $spacer !default;\n$card-group-margin:                 $grid-gutter-width * .5 !default;\n$card-footer-color:                 var(--#{$prefix}secondary-color) !default; // Boosted mod\n// scss-docs-end card-variables\n\n// Accordion\n\n// scss-docs-start accordion-variables\n$accordion-padding-y:                     $spacer * .5 !default; // Boosted mod\n$accordion-padding-x:                     0 !default; // Boosted mod\n$accordion-color:                         null !default; // Boosted mod: instead of `var(--#{$prefix}body-color)`\n$accordion-bg:                            transparent !default; // Boosted mod: instead of `var(--#{$prefix}body-bg)`\n// stylelint-disable-next-line function-disallowed-list\n$accordion-border-width:                  calc(var(--#{$prefix}border-width) * .5) !default; // Boosted mod\n$accordion-border-color:                  var(--#{$prefix}border-color-subtle) !default; // Boosted mod: instead of `var(--#{$prefix}border-color)`\n$accordion-border-radius:                 var(--#{$prefix}border-radius) !default;\n$accordion-inner-border-radius:           subtract($accordion-border-radius, #{$accordion-border-width}) !default;\n\n$accordion-body-padding-top:              $spacer !default; // Boosted mod\n$accordion-body-padding-end:              0 !default; // Boosted mod\n$accordion-body-padding-bottom:           $spacer * 1.5 !default; // Boosted mod\n$accordion-body-padding-start:            0 !default; // Boosted mod\n// fusv-disable\n$accordion-body-padding-y:                $spacer !default; // Deprecated in Boosted 5.2.3 to divide it in -padding<top | end | bottom |start>\n$accordion-body-padding-x:                $spacer !default; // Deprecated in Boosted 5.2.3 to divide it in -padding<top | end | bottom |start>\n// fusv-enable\n\n$accordion-button-padding-y:              $accordion-padding-y !default;\n$accordion-button-padding-x:              $accordion-padding-x !default;\n$accordion-button-color:                  null !default; // Boosted mod: instead of `var(--#{$prefix}body-color)`\n$accordion-button-bg:                     var(--#{$prefix}accordion-bg) !default;\n$accordion-transition:                    $btn-transition, border-radius .15s ease !default;\n$accordion-button-hover-bg:               var(--#{$prefix}secondary-bg) !default; // Boosted mod\n$accordion-button-active-bg:              null !default; // Boosted mod: instead of `var(--#{$prefix}primary-bg-subtle)`\n$accordion-button-active-color:           $accordion-button-color !default; // Boosted mod: instead of `var(--#{$prefix}primary-text-emphasis)`\n\n// Boosted mod: no $accordion-button-focus-border-color\n// Boosted mod: no $accordion-button-focus-box-shadow\n\n// Boosted mod: no $accordion-icon-width\n// Boosted mod: no $accordion-icon-color\n// Boosted mod: no $accordion-icon-active-color\n// Boosted mod: no $accordion-icon-transition\n$accordion-icon-transform:                scaleY(-1) !default;\n\n// Boosted mod: no $accordion-button-icon\n// Boosted mod: no $accordion-button-active-icon\n\n// Boosted mod: accordion sizes\n$accordion-button-font-size:              $h3-font-size !default;\n$accordion-button-line-height:            null !default;\n$accordion-button-font-weight:            $font-weight-bold !default;\n$accordion-button-letter-spacing:         $h3-spacing !default;\n$accordion-button-font-size-sm:           $h5-font-size !default;\n$accordion-button-line-height-sm:         $h5-line-height !default;\n$accordion-button-letter-spacing-sm:      $h5-spacing !default;\n$accordion-button-font-size-lg:           $h2-font-size !default;\n$accordion-button-line-height-lg:         calc(40 / 30) !default; // stylelint-disable-line function-disallowed-list\n$accordion-button-letter-spacing-lg:      $h2-spacing !default;\n// End mod\n// scss-docs-end accordion-variables\n\n// Tooltips\n\n// scss-docs-start tooltip-variables\n$tooltip-font-size:                 $font-size-sm !default;\n$tooltip-font-weight:               $font-weight-bold !default; // Boosted mod\n$tooltip-line-height:               $line-height-sm !default; // Boosted mod\n$tooltip-max-width:                 $spacer * 10 !default;\n$tooltip-color:                     null !default; // Boosted mod: instead of `var(--#{$prefix}body-bg)`\n$tooltip-bg:                        var(--#{$prefix}body-bg) !default; // Boosted mod: instead of `var(--#{$prefix}emphasis-color)`\n// stylelint-disable-next-line function-disallowed-list\n$tooltip-border-width:              calc(var(--#{$prefix}border-width) * .5) !default; // Boosted mod\n$tooltip-border-color:              var(--#{$prefix}emphasis-color) !default; // Boosted mod\n$tooltip-border-radius:             var(--#{$prefix}border-radius) !default;\n$tooltip-opacity:                   1 !default;\n$tooltip-padding-y:                 $spacer * .5 !default;\n$tooltip-padding-x:                 $spacer * .5 !default;\n$tooltip-margin:                    null !default; // TODO: remove this in v6\n\n$tooltip-arrow-width:               $spacer * .5 !default;\n$tooltip-arrow-height:              $tooltip-arrow-width * .5 !default;\n// fusv-disable\n$tooltip-arrow-color:               null !default; // Deprecated in Boosted 5.2.0 for CSS variables\n// fusv-enable\n// scss-docs-end tooltip-variables\n\n// Boosted mod: no form tooltips\n\n\n// Popovers\n\n// scss-docs-start popover-variables\n$popover-font-size:                 $font-size-base !default; // Boosted mod: instead of `$font-size-sm`\n$popover-line-height:               1.5 !default; // Boosted mod\n$popover-font-weight:               $font-weight-bold !default; // Boosted mod\n$popover-bg:                        var(--#{$prefix}body-bg) !default;\n$popover-max-width:                 $spacer * 19 !default; // Boosted mod: instead of `276px`\n$popover-padding-y:                 $spacer !default; // Boosted mod\n$popover-border-width:              var(--#{$prefix}border-width) !default;\n$popover-border-color:              var(--#{$prefix}border-color-subtle) !default; // Boosted mod: instead of `var(--#{$prefix}border-color-translucent)`\n$popover-border-radius:             var(--#{$prefix}border-radius-lg) !default;\n$popover-inner-border-radius:       calc(#{$popover-border-radius} - #{$popover-border-width}) !default; // stylelint-disable-line function-disallowed-list\n$popover-box-shadow:                var(--#{$prefix}box-shadow) !default;\n\n$popover-header-font-size:          $font-size-lg !default; // Boosted mod: instead of `$font-size-base`\n$popover-header-line-height:        1.11 !default; // Boosted mod\n$popover-header-bg:                 $popover-bg !default; // Boosted mod: instead of `var(--#{$prefix}secondary-bg)`\n$popover-header-color:              var(--#{$prefix}heading-color) !default; // Boosted mod: instead of `$headings-color`\n$popover-header-padding-top:        $popover-padding-y !default; // Boosted mod\n$popover-header-padding-bottom:     map-get($spacers, 2) !default; // Boosted mod\n$popover-header-padding-y:          initial !default; // Boosted mod: instead of `.5rem`\n$popover-header-padding-x:          $spacer * .9 !default; // Boosted mod: instead of `$spacer`\n\n$popover-body-color:                null !default; // Boosted mod: instead of `var(--#{$prefix}body-color)`\n$popover-body-padding-top:          0 !default; // Boosted mod\n$popover-body-padding-bottom:       $popover-padding-y !default; // Boosted mod\n$popover-body-padding-y:            initial !default; // Boosted mod: instead of `$spacer`\n$popover-body-padding-x:            $popover-header-padding-x !default; // Boosted mod: instead of `$spacer`\n\n$popover-arrow-width:               $spacer !default; // Boosted mod: instead of `1rem`\n$popover-arrow-height:              $popover-arrow-width * .5 !default; // Boosted mod: instead of `.5rem`\n// scss-docs-end popover-variables\n\n// fusv-disable\n// Deprecated in Bootstrap 5.2.0 for CSS variables\n$popover-arrow-color:               $popover-bg !default;\n$popover-arrow-outer-color:         $popover-border-color !default; // Boosted mod: instead of `var(--#{$prefix}border-color-translucent)`\n// fusv-enable\n\n// Toasts\n\n// scss-docs-start toast-variables\n$toast-max-width:                   21.875rem !default;\n$toast-padding-x:                   $spacer * .5 !default;\n$toast-padding-y:                   $spacer * .25 !default;\n$toast-font-size:                   .875rem !default;\n$toast-color:                       var(--#{$prefix}emphasis-color) !default; // Boosted mod: instead of `null` due to some `bg-dark` issue\n$toast-background-color:            rgba(var(--#{$prefix}body-bg-rgb), .85) !default;\n$toast-border-width:                var(--#{$prefix}border-width) !default;\n$toast-border-color:                var(--#{$prefix}border-color-subtle) !default; // Boosted mod: instead of `var(--#{$prefix}border-color-translucent)`\n$toast-border-radius:               var(--#{$prefix}border-radius) !default;\n$toast-box-shadow:                  var(--#{$prefix}box-shadow) !default;\n$toast-spacing:                     $container-padding-x !default;\n\n$toast-header-color:                null !default; // Boosted mod: instead of `var(--#{$prefix}secondary-color)`\n$toast-header-background-color:     rgba(var(--#{$prefix}body-bg-rgb), .85) !default;\n$toast-header-border-color:         rgba($black, .05) !default; // Boosted mod: instead of `$toast-border-color`\n// scss-docs-end toast-variables\n\n// Badges\n\n// scss-docs-start badge-variables\n$badge-font-size:                   .75em !default;\n$badge-font-weight:                 $font-weight-bold !default;\n$badge-color:                       $white !default;\n$badge-padding-y:                   .35em !default;\n$badge-padding-x:                   .65em !default;\n$badge-border-radius:               var(--#{$prefix}border-radius) !default;\n// scss-docs-end badge-variables\n\n// Modals\n\n// scss-docs-start modal-variables\n$modal-inner-padding:               $spacer * .5 $spacer !default;\n\n$modal-footer-margin-between:       $spacer * .5 !default;\n$modal-footer-padding:              $spacer * .5 subtract($spacer, $modal-footer-margin-between * .5) 0 !default; // Boosted mod\n\n$modal-dialog-margin:               $spacer * .5 !default;\n$modal-dialog-margin-y-sm-up:       $spacer * 1.5 !default;\n\n$modal-title-line-height:           $line-height-base !default;\n\n$modal-content-padding-y:           $spacer !default; // Boosted mod\n$modal-content-padding-x:           0 !default; // Boosted mod\n$modal-content-padding:             $modal-content-padding-y $modal-content-padding-x !default; // Boosted mod\n$modal-content-color:               var(--#{$prefix}body-color) !default;\n$modal-content-bg:                  var(--#{$prefix}body-bg) !default;\n$modal-content-border-color:        var(--#{$prefix}border-color-subtle) !default; // Boosted mod: instead of `var(--#{$prefix}border-color-translucent)`\n$modal-content-border-width:        var(--#{$prefix}border-width) !default;\n$modal-content-border-radius:       var(--#{$prefix}border-radius-lg) !default;\n$modal-content-inner-border-radius: var(--#{$prefix}border-radius) !default; // Boosted mod: instead of `subtract($modal-content-border-radius, $modal-content-border-width)`\n$modal-content-box-shadow-xs:       var(--#{$prefix}box-shadow-sm) !default;\n$modal-content-box-shadow-sm-up:    var(--#{$prefix}box-shadow) !default;\n\n$modal-backdrop-bg:                 $black !default;\n$modal-backdrop-opacity:            .5 !default;\n\n$modal-header-border-color:         null !default; // Boosted mod\n$modal-header-border-width:         $modal-content-border-width !default;\n$modal-header-padding-y:            0 !default;\n$modal-header-padding-x:            $spacer !default;\n$modal-header-padding:              $modal-header-padding-y $modal-header-padding-x !default; // Keep this for backwards compatibility\n\n$modal-footer-bg:                   null !default;\n$modal-footer-border-color:         null !default; // Boosted mod\n$modal-footer-border-width:         $modal-header-border-width !default;\n$modal-footer-margin-top:           $spacer * .5 !default; // Boosted mod\n$modal-footer-margin-top-sm:        $spacer * .75 !default; // Boosted mod\n\n// Boosted mod\n//// Scrollable modal\n$modal-scrollable-inner-padding:     $spacer !default;\n$modal-scrollable-inner-margin:      $spacer 0 0 !default;\n$modal-scrollable-footer-margin-top: $spacer * .5 !default;\n\n//// Modal with top image\n$modal-img-margin:                  -$modal-content-padding-y 0 $modal-content-padding-y !default; // Boosted mod\n$modal-img-btn-close-offset:        $modal-content-padding-y !default;\n// End mod\n\n$modal-sm:                          300px !default;\n$modal-md:                          460px !default;\n$modal-lg:                          700px !default;\n$modal-xl:                          940px !default;\n\n$modal-fade-transform:              translate(0, -50px) !default;\n$modal-show-transform:              none !default;\n$modal-transition:                  transform .3s ease-out !default;\n$modal-scale-transform:             scale(1.02) !default;\n// scss-docs-end modal-variables\n\n// Alerts\n//\n// Define alert colors, border radius, and padding.\n\n// scss-docs-start alert-variables\n$alert-padding-y:                   1rem !default;\n$alert-padding-x:                   $spacer !default;\n$alert-margin-bottom:               $spacer !default;\n$alert-color:                       var(--#{$prefix}body-color) !default; // Boosted mod\n$alert-border-radius:               var(--#{$prefix}border-radius) !default;\n$alert-link-font-weight:            null !default; // Boosted mod\n$alert-heading-font-weight:         $font-weight-bold !default; // Boosted mod\n$alert-border-width:                var(--#{$prefix}border-width) !default;\n\n// Boosted mod\n$alert-padding-sm:                  $spacer * .5 !default;\n$alert-icons: (\n  \"success\": var(--#{$prefix}success-icon),\n  \"info\":    escape-svg($info-icon),\n  // Create a list for this warning icon to indicate that the mask needs to be replaced by a background image\n  // Be aware that the background of the icon won't change anymore\n  // Note: `true` parameter is only used to create a list, it could be empty (e.g. `(escape-svg($warning-icon),)`)\n  \"warning\": (escape-svg($warning-icon-filled), true),\n  \"danger\":  var(--#{$prefix}error-icon)\n) !default;\n$alert-logo-size:                   add($spacer * .5, 1rem) !default;\n$alert-logo-size-sm:                add(1rem, 1px) !default;\n$alert-icon-size:                   3rem !default;\n$alert-icon-size-sm:                $alert-icon-size * .5 !default;\n$alert-icon-margin-y:               $spacer * .1 !default;\n$alert-btn-close-offset:            .5rem !default;\n$alert-btn-close-offset-sm:         $spacer * .25 !default;\n// End mod\n\n$alert-dismissible-padding-r:       $alert-padding-y * 3 !default; // 3x covers width of x plus default padding on either side\n// scss-docs-end alert-variables\n\n// Progress bars\n\n// scss-docs-start progress-variables\n$progress-height:                   $spacer !default;\n$progress-font-size:                $font-size-base !default;\n$progress-bg:                       var(--#{$prefix}tertiary-active-bg) !default; // Boosted mod: instead of `var(--#{$prefix}secondary-bg)`\n$progress-border-radius:            var(--#{$prefix}border-radius) !default;\n$progress-box-shadow:               var(--#{$prefix}box-shadow-inset) !default;\n$progress-bar-color:                var(--#{$prefix}highlight-color) !default; // Boosted mod: instead of `$white`\n$progress-bar-font-weight:          $font-weight-bold !default; // Boosted mod\n$progress-bar-text-indent:          map-get($spacers, 2) !default; // Boosted mod\n$progress-bar-bg:                   var(--#{$prefix}primary) !default; // Boosted mod: instead of `$primary`\n$progress-bar-animation-timing:     1s linear infinite !default;\n$progress-bar-transition:           width .6s ease !default;\n\n// Boosted mod\n$progress-height-sm:                $spacer * .5 !default;\n$progress-height-xs:                $spacer * .25 !default;\n// End mod\n// scss-docs-end progress-variables\n\n// List group\n\n// scss-docs-start list-group-variables\n$list-group-font-weight:              $font-weight-bold !default; // Boosted mod\n$list-group-color:                    null !default; // Boosted mod: instead of `var(--#{$prefix}body-color)`\n$list-group-bg:                       transparent !default; // Boosted mod: instead of `var(--#{$prefix}body-bg)`\n$list-group-border-color:             var(--#{$prefix}border-color-subtle) !default; // Boosted mod: instead of `var(--#{$prefix}border-color)`\n$list-group-border-width:             var(--#{$prefix}border-width) !default;\n$list-group-border-radius:            var(--#{$prefix}border-radius) !default;\n\n$list-group-divider-size:             map-get($border-widths, 1) !default;\n\n$list-group-item-padding-y:           10px !default; // Boosted mod: instead of $spacer * .5\n$list-group-item-padding-x:           13px !default; // Boosted mod: instead of $spacer\n$list-group-item-icon-size:           $spacer * .85 !default; // Boosted mod\n$list-group-item-icon-margin-x:       subtract(var(--#{$prefix}list-group-item-padding-x), 5px) !default; // Boosted mod\n\n$list-group-numbered-item-margin-end: 14px !default; // Boosted mod\n\n$list-group-hover-bg:                 var(--#{$prefix}secondary-bg) !default; // Boosted mod: instead of `var(--#{$prefix}tertiary-bg)`\n$list-group-active-color:             var(--#{$prefix}emphasis-color) !default; // Boosted mod: instead of `$component-active-color`\n$list-group-active-bg:                no-repeat linear-gradient(to right, var(--#{$prefix}primary) 4px, var(--#{$prefix}tertiary-active-bg) 4px) !default; // Boosted mod: instead of `$component-active-bg`\n$list-group-active-border-color:      $list-group-border-color !default; // Boosted mod: instead of `$list-group-active-bg`\n\n$list-group-disabled-color:           var(--#{$prefix}disabled-color) !default; // Boosted mod: instead of `var(--#{$prefix}secondary-color)`\n$list-group-disabled-bg:              $list-group-bg !default;\n\n$list-group-action-color:             var(--#{$prefix}emphasis-color) !default; // Boosted mod: instead of `var(--#{$prefix}secondary-color)`\n$list-group-action-hover-color:       var(--#{$prefix}emphasis-color) !default;\n\n$list-group-action-active-color:      $list-group-active-color !default; // Boosted mod: instead of `var(--#{$prefix}body-color)`\n$list-group-action-active-bg:         $list-group-active-bg !default; // Boosted mod: instead of `var(--#{$prefix}secondary-bg)`\n\n// Boosted mod\n$list-group-icons: (\n  \"success\": var(--#{$prefix}success-icon),\n  \"info\":    escape-svg($info-icon),\n  \"warning\": escape-svg($warning-icon),\n  \"danger\":  var(--#{$prefix}error-icon)\n) !default;\n// End mod\n// scss-docs-end list-group-variables\n\n\n// Image thumbnails\n\n// scss-docs-start thumbnail-variables\n$thumbnail-padding:                 0 !default; // Boosted mod\n$thumbnail-bg:                      var(--#{$prefix}body-bg) !default;\n$thumbnail-border-width:            var(--#{$prefix}border-width) !default;\n$thumbnail-border-color:            var(--#{$prefix}border-color-subtle) !default; // Boosted mod: instead of `var(--#{$prefix}border-color-translucent)`\n$thumbnail-border-radius:           var(--#{$prefix}border-radius) !default;\n$thumbnail-box-shadow:              var(--#{$prefix}box-shadow-sm) !default;\n// scss-docs-end thumbnail-variables\n\n// Figures\n\n// scss-docs-start figure-variables\n$figure-caption-font-size:          $small-font-size !default;\n$figure-caption-color:              var(--#{$prefix}secondary-color) !default;\n// scss-docs-end figure-variables\n\n// Boosted mod\n// Title bars\n\n// scss-docs-start title-bars-variables\n$title-bar-bg:                      var(--#{$prefix}body-bg) !default;\n$title-bar-color:                   var(--#{$prefix}body-color) !default;\n$title-bar-image-ratio:             1.8em !default;\n$title-bar-padding-y:               .3333333em !default;\n$title-bar-font-size:               $h2-font-size !default;\n$title-bar-line-height:             $display-line-height !default;\n$title-bar-letter-spacing:          $h2-spacing !default;\n$title-bar-border-width:            calc(var(--#{$prefix}border-width) * .5) !default; // stylelint-disable-line function-disallowed-list\n$title-bar-border-color:            var(--#{$prefix}border-color-subtle) !default;\n\n$title-bar-font-size-md:            $display2-size !default;\n$title-bar-letter-spacing-md:       $display2-spacing !default;\n\n$title-bar-font-size-xl:            $display1-size !default;\n$title-bar-letter-spacing-xl:       $display1-spacing !default;\n\n// fusv-disable\n$title-bar-border-color-dark:       $gray-700 !default; // Deprecated in v5.3.3\n// fusv-enable\n// scss-docs-end title-bars-variables\n// End mod\n\n\n// Breadcrumbs\n\n// scss-docs-start breadcrumb-variables\n$breadcrumb-font-size:              $font-size-sm !default;\n$breadcrumb-font-weight:            $font-weight-bold !default; // Boosted mod\n\n$breadcrumb-padding-y:              .5rem !default;\n$breadcrumb-padding-x:              0 !default;\n$breadcrumb-item-padding-x:         $spacer * .5 !default;\n$breadcrumb-margin-bottom:          1rem !default;\n$breadcrumb-color:                  var(--#{$prefix}emphasis-color) !default; // Boosted mod\n$breadcrumb-bg:                     null !default;\n$breadcrumb-divider-color:          $black !default; // Boosted mod: instead of `var(--#{$prefix}secondary-color)`\n$breadcrumb-active-color:           null !default; // Boosted mod: instead of `var(--#{$prefix}secondary-color)`\n$breadcrumb-divider:                url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 9 14' width='7' height='10'><path d='m-.4 12 2 2 7-7-7-7-2 2 5 5z'/></svg>\") !default;\n$breadcrumb-divider-filter:         none !default; // Boosted mod\n$breadcrumb-divider-flipped:        $breadcrumb-divider !default;\n$breadcrumb-border-radius:          null !default;\n// scss-docs-end breadcrumb-variables\n\n\n// Carousel\n\n// scss-docs-start carousel-variables\n$carousel-control-color:             $black !default;\n$carousel-control-width:             $spacer * 3 !default;\n$carousel-control-opacity:           null !default;\n$carousel-control-hover-opacity:     null !default;\n$carousel-control-transition:        $transition-focus !default;\n// Boosted mod: no $carousel-control-icon-filter\n\n$carousel-indicator-width:           .5rem !default;\n$carousel-indicator-height:          .5rem !default;\n$carousel-indicator-hit-area-height: $spacer * 1.5 !default;\n$carousel-indicator-spacer:          $spacer * .5 !default;\n$carousel-indicator-opacity:         null !default;\n$carousel-indicator-active-bg:       $white !default;\n$carousel-indicator-active-opacity:  null !default;\n$carousel-indicator-transition:      null !default;\n// Boosted mod\n$carousel-indicator-hover-scale:        1.5 !default;\n$carousel-indicator-active-scale:       calc(2 / 3) !default; // stylelint-disable-line function-disallowed-list\n$carousel-indicator-active-radius:      0 100% 100% 0 / 50% !default;\n$carousel-indicator-animation-duration: 5000ms !default;\n$carousel-indicator-animation-interval: var(--#{$prefix}carousel-interval, #{$carousel-indicator-animation-duration}) !default;\n$carousel-indicators-padding-y:         $spacer * .5 !default;\n$carousel-indicators-margin-bottom:     $spacer !default;\n// End mod\n\n$carousel-caption-width:             70% !default;\n$carousel-caption-color:             null !default; // Boosted mod: instead of `var(--#{$prefix}body-color)`\n$carousel-caption-bg:                var(--#{$prefix}body-bg) !default; // Boosted mod\n$carousel-caption-padding-y:         $spacer !default;\n$carousel-caption-padding-x:         $spacer !default; // Boosted mod\n$carousel-caption-spacer:            $spacer * 3 !default;\n\n$carousel-control-icon-width:        2.5rem !default;\n// Boosted mod\n$carousel-control-icon-size:         1rem 1.5rem !default;\n$carousel-control-icon-bg:           var(--#{$prefix}chevron-icon) !default;\n$carousel-control-icon-color:        $black !default; // Boosted mod\n$carousel-control-icon-active-bg:    $component-active-bg !default;\n\n$carousel-control-pause-indicators-spacing: 10px !default;\n$carousel-control-pause-icon:               $pause-icon !default;\n$carousel-control-play-icon:                $play-icon !default;\n$carousel-control-pause-button-size:        .75rem !default;\n$carousel-control-pause-icon-size:          .75rem .75rem !default;\n// End mod\n\n$carousel-transition-duration:       .6s !default;\n$carousel-transition:                transform $carousel-transition-duration $transition-timing !default; // Define transform transition first if using multiple transitions (e.g., `transform 2s ease, opacity .5s ease-out`)\n// scss-docs-end carousel-variables\n\n// Boosted mod: no dark carousel variables\n\n\n// Spinners\n\n// scss-docs-start spinner-variables\n$spinner-color:           null !default; // Boosted mod\n$spinner-width:           $spacer * 2 !default;\n$spinner-height:          $spinner-width !default;\n$spinner-vertical-align:  -.125em !default;\n// stylelint-disable-next-line function-disallowed-list\n$spinner-border-width:    calc(var(--#{$prefix}border-width) * 3) !default; // Boosted mod\n$spinner-animation-speed: .75s !default;\n\n$spinner-width-sm:        $spacer !default;\n$spinner-height-sm:       $spinner-width-sm !default;\n// stylelint-disable-next-line function-disallowed-list\n$spinner-border-width-sm: calc(var(--#{$prefix}border-width) * 2) !default; // Boosted mod\n\n$spinner-width-lg:        $spacer * 4 !default; // Boosted mod\n$spinner-height-lg:       $spinner-width-lg !default; // Boosted mod\n// stylelint-disable-next-line function-disallowed-list\n$spinner-border-width-lg: calc(var(--#{$prefix}border-width) * 4) !default; // Boosted mod\n// scss-docs-end spinner-variables\n\n\n// Close\n\n// scss-docs-start close-variables\n$btn-close-width:               $spacer !default; // Boosted mod\n$btn-close-height:              $btn-close-width !default;\n$btn-close-padding:             var(--#{$prefix}icon-spacing, #{$btn-icon-padding-x}) !default; // Boosted mod\n$btn-close-border-width:        var(--#{$prefix}border-width) !default; // Boosted mod\n$btn-close-border-color:        transparent !default; // Boosted mod\n$btn-close-color:               var(--#{$prefix}emphasis-color) !default;\n$btn-close-bg:                  var(--#{$prefix}close-icon) !default; // Boosted mod\n// Boosted mod\n// fusv-disable\n$btn-close-focus-shadow:        $btn-focus-box-shadow !default; // Deprecated in v5.3.0\n// fusv-enable\n// End mod\n\n// Boosted mod: no opacity/filter\n\n// Boosted mod\n$btn-close-hover-color:         $btn-close-color !default;\n$btn-close-active-color:        var(--#{$prefix}primary) !default;\n$btn-close-active-border-color: var(--#{$prefix}border-color-subtle) !default;\n$btn-close-disabled-color:      var(--#{$prefix}disabled-color) !default;\n\n$btn-close-icon-size:           1rem auto !default;\n$btn-close-padding-sm:          subtract($btn-icon-padding-x, $spacer * .25) !default;\n// End mod\n// scss-docs-end close-variables\n\n// Deprecated in v5.3.3: all `$btn-close-white-*`\n$btn-close-white-color:               $white !default; // Boosted mod\n$btn-close-white-bg:                  transparent !default; // Boosted mod\n$btn-close-white-border-color:        transparent !default; // Boosted mod\n$btn-close-white-hover-color:         $btn-close-white-color !default; // Boosted mod\n$btn-close-white-active-color:        $supporting-orange !default; // Boosted mod\n$btn-close-white-active-border-color: $gray-700 !default; // Boosted mod\n$btn-close-white-disabled-color:      $gray-700 !default; // Boosted mod\n\n// Offcanvas\n\n// scss-docs-start offcanvas-variables\n$offcanvas-padding-y:               $modal-inner-padding !default;\n$offcanvas-padding-x:               $modal-inner-padding !default;\n$offcanvas-horizontal-width:        400px !default;\n$offcanvas-vertical-height:         30vh !default;\n$offcanvas-transition-duration:     .3s !default;\n$offcanvas-border-color:            $modal-content-border-color !default;\n$offcanvas-border-width:            $modal-content-border-width !default;\n$offcanvas-title-line-height:       $modal-title-line-height !default;\n$offcanvas-bg-color:                $modal-content-bg !default; // Boosted mod: instead of `var(--#{$prefix}body-bg)`\n$offcanvas-color:                   $modal-content-color !default; // Boosted mod: instead of `var(--#{$prefix}body-color)`\n$offcanvas-box-shadow:              none !default; // Boosted mod\n$offcanvas-backdrop-bg:             $modal-backdrop-bg !default;\n$offcanvas-backdrop-opacity:        $modal-backdrop-opacity !default;\n// scss-docs-end offcanvas-variables\n\n// Code\n// Boosted mod\n$code-font-size:                    .875em !default;\n$code-color:                        $gray-700 !default;\n\n$kbd-padding-y:                     $spacer * .05 !default;\n$kbd-padding-x:                     $spacer * .05 !default;\n$kbd-font-size:                     $code-font-size !default;\n$kbd-color:                         var(--#{$prefix}kbd-color, $black) !default;\n$kbd-bg:                            var(--#{$prefix}kbd-bg, $gray-300) !default;\n$nested-kbd-font-weight:            null !default; // Deprecated in v5.2.0, removing in v6\n\n$pre-color:                         var(--#{$prefix}code-color) !default;\n$pre-line-height:                   1.25 !default;\n// End mod\n\n//\n// Boosted mod\n//\n\n//// Scroll margin\n$scroll-offset-top:              $spacer * 6 !default; // Matching .navbar computed height\n\n//// Back to top\n// scss-docs-start back-to-top\n$back-to-top-display-threshold:  100vh !default;\n$back-to-top-target-id:          \"top\" !default;\n$back-to-top-target-offset-top:  $scroll-offset-top !default;\n$back-to-top-offset:             $spacer * 1.5 !default;\n$back-to-top-offset-right:       $back-to-top-offset !default;\n$back-to-top-offset-bottom:      $back-to-top-offset !default;\n$back-to-top-link-offset-top:    subtract(100vh, $back-to-top-offset * 4) !default;\n$back-to-top-link-offset-top-xl: subtract(100vh, $spacer * 5) !default;\n$back-to-top-title-offset-right: add(100%, var(--#{$prefix}border-width)) !default;\n$back-to-top-title-padding:      subtract($btn-padding-y, 1px) $btn-padding-x add($btn-padding-y, 1px) !default;\n$back-to-top-title-color:        var(--#{$prefix}body-color) !default;\n$back-to-top-title-bg-color:     var(--#{$prefix}body-bg) !default;\n$back-to-top-bg:                 var(--#{$prefix}highlight-color) !default;\n$back-to-top-icon:               var(--#{$prefix}chevron-icon) !default;\n$back-to-top-icon-width:         add(.5rem, 1px) !default;\n$back-to-top-icon-height:        subtract(1rem, 1px) !default;\n// scss-docs-end back-to-top\n\n//// Stepped process\n// scss-docs-start stepped-process\n$stepped-process-font-size:   $small-font-size !default;\n$stepped-process-font-weight: $font-weight-bold !default;\n$stepped-process-max-items:   5 !default;\n$stepped-process-counter:     step !default; // Used as a counter name\n$stepped-process-bg:          var(--#{$prefix}body-bg) !default;\n\n$step-item-padding:           7px !default;\n// fusv-disable\n$step-item-padding-end:       $step-item-padding * 2 !default; // Deprecated in v5.2.0\n// fusv-enable\n$step-item-margin-end:        var(--#{$prefix}border-width) !default;\n$step-item-bg:                var(--#{$prefix}secondary) !default;\n$step-item-active-bg:         $supporting-orange !default;\n$step-item-next-bg:           var(--#{$prefix}border-color-subtle) !default;\n$step-item-shadow-size:       calc(var(--#{$prefix}border-width) * 1.5) !default; // stylelint-disable-line function-disallowed-list\n$step-item-drop-shadow:       drop-shadow($step-item-shadow-size 0 0 var(--#{$prefix}stepped-process-bg)) #{\"/* rtl:\"} drop-shadow(calc(-1 * #{$step-item-shadow-size}) 0 0 var(--#{$prefix}stepped-process-bg)) #{\"*/\"} !default; // stylelint-disable-line function-disallowed-list\n\n$step-item-arrow-width:       .8125rem !default;\n$step-item-arrow-shape:       polygon(0% 0%, 1px 0%, subtract(100%, var(--#{$prefix}border-width)) 50%, 1px 100%, 0% 100%) #{\"/* rtl:\"} polygon(100% 0%, subtract(100%, 1px) 0%, var(--#{$prefix}border-width) 50%, subtract(100%, 1px) 100%, 100% 100%) #{\"*/\"} !default; // Used in clip-path\n\n$step-link-width:             1.25ch !default; // Matches width of a single number\n$step-link-color:             var(--#{$prefix}highlight-color) !default;\n$step-link-active-color:      $black !default;\n$step-link-active-outline:    $black !default;\n$step-link-next-color:        var(--#{$prefix}link-color) !default;\n$step-link-line-height:       $line-height-sm !default;\n$step-link-marker:            counter(var(--bs-stepped-process-counter)) inspect(\"\\A0\") !default;\n$step-link-marker-lg:         counter(var(--bs-stepped-process-counter)) inspect(\".\\A0\") !default;\n$step-link-text-decoration:   $link-decoration !default;\n// scss-docs-end stepped-process\n\n\n//// Sticker\n// scss-docs-start sticker\n$sticker-color:                         $black !default;\n$sticker-background-color:              $supporting-orange !default;\n$sticker-font-weight:                   $font-weight-bold !default;\n\n$sticker-size-sm:                       $spacer * 7 !default;\n$sticker-size-md:                       $spacer * 9 !default;\n$sticker-size-lg:                       $spacer * 14 !default;\n\n// Considering @use \"sass:math\", math.sqrt(2) / 2 is approximated to 0.7071067812\n$sticker-content-max-width-sm:          $sticker-size-sm * .7071067812 !default;\n$sticker-content-max-width-md:          $sticker-size-md * .7071067812 !default;\n$sticker-content-max-width-lg:          $sticker-size-lg * .7071067812 !default;\n// scss-docs-end sticker\n\n//// Quantity selector\n// scss-docs-start quantity-selector\n$quantity-selector-width:                 7.5rem !default;\n$quantity-selector-sm-width:              5.625rem !default;\n\n$quantity-selector-btn-padding-x:         add($btn-icon-padding-x, 2px) !default;\n$quantity-selector-btn-padding-x-sm:      add($btn-icon-padding-x-sm, 2px) !default;\n$quantity-selector-btn-bg:                var(--#{$prefix}body-bg) !default;\n$quantity-selector-btn-border:            var(--#{$prefix}border-width) solid var(--#{$prefix}border-color-subtle) !default;\n\n$quantity-selector-disabled-color:        var(--#{$prefix}disabled-color) !default;\n$quantity-selector-disabled-bg:           var(--#{$prefix}body-bg) !default;\n\n$quantity-selector-icon-width:            .875rem !default;\n$quantity-selector-icon-sm-width:         .625rem !default;\n\n$quantity-selector-icon-add:              $add-icon !default;\n$quantity-selector-icon-add-sm:           $add-icon-sm !default;\n$quantity-selector-icon-add-height:       .875rem !default;\n$quantity-selector-icon-sm-add-height:    .625rem !default;\n\n$quantity-selector-icon-remove:           $remove-icon !default;\n$quantity-selector-icon-remove-sm:        $remove-icon-sm !default;\n$quantity-selector-icon-remove-height:    .125rem !default;\n$quantity-selector-icon-sm-remove-height: .125rem !default;\n\n$quantity-selector-input-max-width:       2.5rem !default;\n$quantity-selector-input-sm-max-width:    1.875rem !default;\n// scss-docs-end quantity-selector\n\n//// Footer\n// scss-docs-start footer\n$footer-color:                            var(--#{$prefix}body-color) !default;\n$footer-font-size-sm:                     $font-size-sm !default;\n$footer-line-height-sm:                   $line-height-sm !default;\n$footer-font-size-md:                     $font-size-base !default;\n$footer-line-height-md:                   $line-height-base !default;\n$footer-title-font-weight:                $font-weight-bold !default;\n$footer-letter-spacing:                   $letter-spacing-base !default;\n$footer-accordion-line-height:            $spacer * 1.45 !default;\n$footer-accordion-active-color:           var(--#{$prefix}primary) !default;\n$footer-accordion-btn-hover-bg:           null !default;\n// fusv-disable\n$footer-nav-link-font-weight:             $font-weight-bold !default; // Deprecated in v5.3.3\n// fusv-enable\n\n$footer-title-content-padding-top:        $spacer * 1.25 !default;\n$footer-title-content-padding-bottom:     $spacer * 1.45 !default;\n$footer-title-content-padding-bottom-md:  $spacer * 1.95 !default;\n$footer-title-margin-bottom:              $spacer * .85 !default;\n\n$footer-social-padding-top:               $spacer * .85 !default;\n$footer-social-padding-top-md:            $spacer * 1.5 !default;\n$footer-social-padding-bottom:            $spacer * 1.45 !default;\n$footer-social-title-margin-bottom-md:    $spacer * .1 !default;\n\n$footer-title-margin-bottom-md:           $spacer * 1.05 !default;\n$footer-nav-padding-top:                  $spacer * 1.55 !default;\n$footer-nav-list-padding-top:             $spacer * .85 !default;\n$footer-nav-list-padding-top-md:          $spacer * .05 !default;\n$footer-nav-list-padding-bottom:          $spacer * 1.3 !default;\n$footer-nav-list-padding-bottom-md:       $spacer * 1.75 !default;\n$footer-nav-list-padding-bottom-lg:       $spacer * 2 !default;\n\n$footer-service-padding-y:                $spacer !default;\n$footer-service-padding-y-md:             $spacer * 1.2 !default;\n$footer-service-link-padding-top:         $spacer * .1 !default;\n\n$footer-terms-padding-top:                $spacer * .85 !default;\n$footer-terms-padding-bottom:             $spacer * 1.35 !default;\n$footer-terms-padding-y-md:               $spacer * 1.1 !default;\n\n$footer-gap:                              $spacer * .75 !default;\n$footer-gap-xl:                           $spacer * 1.7 !default;\n// scss-docs-end footer\n\n\n// Tags\n\n// scss-docs-start tag-variables\n$tag-color:                         var(--#{$prefix}body-color) !default;\n$tag-bg:                            var(--#{$prefix}body-bg) !default;\n\n$tag-gap:                           map-get($spacers, 1) !default;\n$tag-font-shift:                    $spacer * .1 !default;\n$tag-font-weight:                   $font-weight-bold !default;\n$tag-border-width:                  var(--#{$prefix}border-width) !default;\n$tag-border-radius:                 var(--#{$prefix}border-radius-pill) !default;\n\n$tag-padding-x:                     $spacer * .65 !default;\n$tag-padding-y:                     $spacer * .45 !default;\n$tag-icon-size:                     $spacer * 1.2 !default;\n$tag-icon-margin-start:             -$spacer * .35 !default;\n$tag-close-margin-end:              -$spacer * .3 !default;\n$tag-close-margin-start:            $spacer * .2 !default;\n$tag-font-size:                     $font-size-base !default;\n\n$tag-active-color:                  var(--#{$prefix}highlight-color) !default;\n$tag-disabled-color:                var(--#{$prefix}disabled-color) !default;\n$tag-border-color:                  var(--#{$prefix}border-color-subtle) !default;\n$tag-active-decoration-color:       var(--#{$prefix}highlight-bg) !default;\n// scss-docs-end tag-variables\n\n// scss-docs-start tag-sm-variables\n$tag-padding-x-sm:                  $spacer * .4 !default;\n$tag-padding-y-sm:                  $spacer * .25 !default;\n$tag-icon-size-sm:                  $spacer !default;\n$tag-icon-margin-start-sm:          -$spacer * .1 !default;\n$tag-close-margin-end-sm:           -$spacer * .25 !default;\n$tag-close-margin-start-sm:         0 !default;\n$tag-font-size-sm:                  $font-size-sm !default;\n// scss-docs-end tag-sm-variables\n\n\n// Local navigation\n\n// scss-docs-start local-nav-variables\n$local-nav-padding-y:           $navbar-nav-link-padding-y !default;\n$local-nav-color:               null !default;\n$local-nav-bg:                  var(--#{$prefix}body-bg) !default;\n$local-nav-hover-color:         var(--#{$prefix}link-hover-color) !default;\n$local-nav-hover-bg:            var(--#{$prefix}secondary-bg) !default;\n$local-nav-active-color:        var(--#{$prefix}primary) !default;\n$local-nav-active-bg:           var(--#{$prefix}tertiary-active-bg) !default;\n$local-nav-active-marker-width: $spacer * .2 !default;\n$local-nav-border-color:        var(--#{$prefix}border-color-subtle) !default;\n$local-nav-border-width:        calc(var(--#{$prefix}border-width) * .5) !default; // stylelint-disable-line function-disallowed-list\n// scss-docs-end local-nav-variables\n// End mod\n\n@import \"variables-dark\"; // TODO: can be removed safely in v6, only here to avoid breaking changes in v5.3\n", "@import \"chevron-link\"; // Boosted mod\n\n.icon-link {\n  display: inline-flex;\n  gap: $icon-link-gap;\n  align-items: center;\n  text-decoration-color: rgba(var(--#{$prefix}link-color-rgb), var(--#{$prefix}link-opacity, .5));\n  text-underline-offset: $icon-link-underline-offset;\n  backface-visibility: hidden;\n\n  > .bi {\n    flex-shrink: 0;\n    width: $icon-link-icon-size;\n    height: $icon-link-icon-size;\n    fill: currentcolor;\n    @include transition($icon-link-icon-transition);\n  }\n}\n\n.icon-link-hover {\n  &:hover,\n  &:focus-visible {\n    > .bi {\n      transform: var(--#{$prefix}icon-link-transform, $icon-link-icon-transform);\n    }\n  }\n}\n", "// stylelint-disable property-disallowed-list\n@mixin transition($transition...) {\n  @if length($transition) == 0 {\n    $transition: $transition-base;\n  }\n\n  @if length($transition) > 1 {\n    @each $value in $transition {\n      @if $value == null or $value == none {\n        @warn \"The keyword 'none' or 'null' must be used as a single argument.\";\n      }\n    }\n  }\n\n  @if $enable-transitions {\n    @if nth($transition, 1) != null {\n      transition: $transition;\n    }\n\n    @if $enable-reduced-motion and nth($transition, 1) != null and nth($transition, 1) != none {\n      @media (prefers-reduced-motion: reduce) {\n        transition: none;\n      }\n    }\n  }\n}\n", "// Credit: <PERSON> and <PERSON><PERSON><PERSON> CSS.\n\n.ratio {\n  position: relative;\n  width: 100%;\n\n  &::before {\n    display: block;\n    padding-top: var(--#{$prefix}aspect-ratio);\n    content: \"\";\n  }\n\n  > * {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n  }\n}\n\n@each $key, $ratio in $aspect-ratios {\n  .ratio-#{$key} {\n    --#{$prefix}aspect-ratio: #{$ratio};\n  }\n}\n", "// Shorthand\n\n.fixed-top {\n  position: fixed;\n  top: 0;\n  right: 0;\n  left: 0;\n  z-index: $zindex-fixed;\n}\n\n.fixed-bottom {\n  position: fixed;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  z-index: $zindex-fixed;\n}\n\n// Responsive sticky top and bottom\n@each $breakpoint in map-keys($grid-breakpoints) {\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    .sticky#{$infix}-top {\n      position: sticky;\n      top: 0;\n      z-index: $zindex-sticky;\n    }\n\n    .sticky#{$infix}-bottom {\n      position: sticky;\n      bottom: 0;\n      z-index: $zindex-sticky;\n    }\n  }\n}\n", "// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px)\n//\n// The map defined in the `$grid-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl xxl))\n//    md\n@function breakpoint-next($name, $breakpoints: $grid-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @if not $n {\n    @error \"breakpoint `#{$name}` not found in `#{$breakpoints}`\";\n  }\n  @return if($n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $grid-breakpoints) {\n  $min: map-get($breakpoints, $name);\n  @return if($min != 0, $min, null);\n}\n\n// Maximum breakpoint width.\n// The maximum value is reduced by 0.02px to work around the limitations of\n// `min-` and `max-` prefixes and viewports with fractional widths.\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(md, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $grid-breakpoints) {\n  $max: map-get($breakpoints, $name);\n  @return if($max and $max > 0, $max - .02, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash in front.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $grid-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $grid-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media that spans multiple breakpoint widths.\n// Makes the @content apply between the min and max breakpoints\n@mixin media-breakpoint-between($lower, $upper, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($lower, $breakpoints);\n  $max: breakpoint-max($upper, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($lower, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($upper, $breakpoints) {\n      @content;\n    }\n  }\n}\n\n// Media between the breakpoint's minimum and maximum widths.\n// No minimum for the smallest breakpoint, and no maximum for the largest one.\n// Makes the @content apply only to the given breakpoint, not viewports any wider or narrower.\n@mixin media-breakpoint-only($name, $breakpoints: $grid-breakpoints) {\n  $min:  breakpoint-min($name, $breakpoints);\n  $next: breakpoint-next($name, $breakpoints);\n  $max:  breakpoint-max($next, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($name, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($next, $breakpoints) {\n      @content;\n    }\n  }\n}\n", "// scss-docs-start stacks\n.hstack {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  align-self: stretch;\n}\n\n.vstack {\n  display: flex;\n  flex: 1 1 auto;\n  flex-direction: column;\n  align-self: stretch;\n}\n// scss-docs-end stacks\n", "//\n// Visually hidden\n//\n\n.visually-hidden,\n.visually-hidden-focusable:not(:focus):not(:focus-within) {\n  @include visually-hidden();\n}\n", "// stylelint-disable declaration-no-important\n\n// Hide content visually while keeping it accessible to assistive technologies\n//\n// See: https://www.a11yproject.com/posts/2013-01-11-how-to-hide-content/\n// See: https://kittygiraudel.com/2016/10/13/css-hide-and-seek/\n\n@mixin visually-hidden() {\n  width: 1px !important;\n  height: 1px !important;\n  padding: 0 !important;\n  margin: -1px !important; // Fix for https://github.com/twbs/bootstrap/issues/25686\n  overflow: hidden !important;\n  clip: rect(0, 0, 0, 0) !important;\n  white-space: nowrap !important;\n  border: 0 !important;\n\n  // Fix for positioned table caption that could become anonymous cells\n  &:not(caption) {\n    position: absolute !important;\n  }\n\n  // Fix to prevent overflowing children to become focusable\n  * {\n    overflow: hidden !important;\n  }\n}\n\n// Use to only display content when it's focused, or one of its child elements is focused\n// (i.e. when focus is within the element/container that the class was applied to)\n//\n// Useful for \"Skip to main content\" links; see https://www.w3.org/WAI/WCAG22/Techniques/general/G1.html\n\n@mixin visually-hidden-focusable() {\n  &:not(:focus):not(:focus-within) {\n    @include visually-hidden();\n  }\n}\n", "//\n// Stretched link\n//\n\n.stretched-link {\n  &::#{$stretched-link-pseudo-element} {\n    position: absolute;\n    top: 0;\n    right: 0;\n    bottom: 0;\n    left: 0;\n    z-index: $stretched-link-z-index;\n    content: \"\";\n  }\n}\n", "//\n// Text truncation\n//\n\n.text-truncate {\n  @include text-truncate();\n}\n", "// Text truncate\n// Requires inline-block or block for proper styling\n\n@mixin text-truncate() {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n", ".vr {\n  display: inline-block;\n  align-self: stretch;\n  width: $vr-border-width;\n  min-height: 1em;\n  background-color: currentcolor;\n  opacity: $hr-opacity;\n}\n", "// Utility generator\n// Used to generate utilities & print utilities\n@mixin generate-utility($utility, $infix: \"\", $is-rfs-media-query: false) {\n  $values: map-get($utility, values);\n\n  // If the values are a list or string, convert it into a map\n  @if type-of($values) == \"string\" or type-of(nth($values, 1)) != \"list\" {\n    $values: zip($values, $values);\n  }\n\n  @each $key, $value in $values {\n    $properties: map-get($utility, property);\n\n    // Multiple properties are possible, for example with vertical or horizontal margins or paddings\n    @if type-of($properties) == \"string\" {\n      $properties: append((), $properties);\n    }\n\n    // Use custom class if present\n    $property-class: if(map-has-key($utility, class), map-get($utility, class), nth($properties, 1));\n    $property-class: if($property-class == null, \"\", $property-class);\n\n    // Use custom CSS variable name if present, otherwise default to `class`\n    $css-variable-name: if(map-has-key($utility, css-variable-name), map-get($utility, css-variable-name), map-get($utility, class));\n\n    // State params to generate pseudo-classes\n    $state: if(map-has-key($utility, state), map-get($utility, state), ());\n\n    $infix: if($property-class == \"\" and str-slice($infix, 1, 1) == \"-\", str-slice($infix, 2), $infix);\n\n    // Don't prefix if value key is null (e.g. with shadow class)\n    $property-class-modifier: if($key, if($property-class == \"\" and $infix == \"\", \"\", \"-\") + $key, \"\");\n\n    @if map-get($utility, rfs) {\n      // Inside the media query\n      @if $is-rfs-media-query {\n        $val: rfs-value($value);\n\n        // Do not render anything if fluid and non fluid values are the same\n        $value: if($val == rfs-fluid-value($value), null, $val);\n      }\n      @else {\n        $value: rfs-fluid-value($value);\n      }\n    }\n\n    $is-css-var: map-get($utility, css-var);\n    $is-local-vars: map-get($utility, local-vars);\n    $is-rtl: map-get($utility, rtl);\n\n    @if $value != null {\n      @if $is-rtl == false {\n        /* rtl:begin:remove */\n      }\n\n      @if $is-css-var {\n        .#{$property-class + $infix + $property-class-modifier} {\n          --#{$prefix}#{$css-variable-name}: #{$value};\n        }\n\n        @each $pseudo in $state {\n          .#{$property-class + $infix + $property-class-modifier}-#{$pseudo}:#{$pseudo} {\n            --#{$prefix}#{$css-variable-name}: #{$value};\n          }\n        }\n      } @else {\n        .#{$property-class + $infix + $property-class-modifier} {\n          @each $property in $properties {\n            @if $is-local-vars {\n              @each $local-var, $variable in $is-local-vars {\n                --#{$prefix}#{$local-var}: #{$variable};\n              }\n            }\n            #{$property}: $value if($enable-important-utilities, !important, null);\n          }\n        }\n\n        @each $pseudo in $state {\n          .#{$property-class + $infix + $property-class-modifier}-#{$pseudo}:#{$pseudo} {\n            @each $property in $properties {\n              @if $is-local-vars {\n                @each $local-var, $variable in $is-local-vars {\n                  --#{$prefix}#{$local-var}: #{$variable};\n                }\n              }\n              #{$property}: $value if($enable-important-utilities, !important, null);\n            }\n          }\n        }\n      }\n\n      @if $is-rtl == false {\n        /* rtl:end:remove */\n      }\n    }\n  }\n}\n", "// Loop over each breakpoint\n@each $breakpoint in map-keys($grid-breakpoints) {\n\n  // Generate media query if needed\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    // Loop over each utility property\n    @each $key, $utility in $utilities {\n      // The utility can be disabled with `false`, thus check if the utility is a map first\n      // Only proceed if responsive media queries are enabled or if it's the base media query\n      @if type-of($utility) == \"map\" and (map-get($utility, responsive) or $infix == \"\") {\n        @include generate-utility($utility, $infix);\n      }\n    }\n  }\n}\n\n// RFS rescaling\n@media (min-width: $rfs-mq-value) {\n  @each $breakpoint in map-keys($grid-breakpoints) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    @if (map-get($grid-breakpoints, $breakpoint) < $rfs-breakpoint) {\n      // Loop over each utility property\n      @each $key, $utility in $utilities {\n        // The utility can be disabled with `false`, thus check if the utility is a map first\n        // Only proceed if responsive media queries are enabled or if it's the base media query\n        @if type-of($utility) == \"map\" and map-get($utility, rfs) and (map-get($utility, responsive) or $infix == \"\") {\n          @include generate-utility($utility, $infix, true);\n        }\n      }\n    }\n  }\n}\n\n\n// Print utilities\n@media print {\n  @each $key, $utility in $utilities {\n    // The utility can be disabled with `false`, thus check if the utility is a map first\n    // Then check if the utility needs print styles\n    @if type-of($utility) == \"map\" and map-get($utility, print) == true {\n      @include generate-utility($utility, \"-print\");\n    }\n  }\n}\n"]}