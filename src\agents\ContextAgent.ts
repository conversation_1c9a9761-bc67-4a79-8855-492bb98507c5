import { CONTEXT_BUILDER_SYSTEM_PROMPT } from "../ToolCalling";

export interface SnippetMeta {
  fileName: string;
  startLine: number;
  endLine: number;
}

export interface AgentContext {
  intent: string;
  acceptance_criteria: string[];
  info_gaps: string[];
  search_plan: Array<{ query: string; path: string }>;
  files_to_read: Array<{ path: string; start: number; end: number }>;
  tool_plan: Array<{ tool: "t_grep" | "t_read_file" | "t_edit_file"; why: string }>;
  risks: string[];
  success_check: string[];
  next_step: string;
  // Optional history for collaboration loop
  history?: Array<{
    tool?: string;
    justification?: string;
    summary: string;
  }>;
}

export class ContextAgent {
  constructor(private apiKey: string, private model: unknown) {}

  async buildInitialContext(params: {
    user_query: string;
    code_snippets_meta: SnippetMeta[];
    available_tools: string[];
    previous_history?: AgentContext["history"];
  }, signal?: AbortSignal): Promise<AgentContext> {
    const userMessage = this._makeUserMessage(params);
    const json = await this._callLLM(CONTEXT_BUILDER_SYSTEM_PROMPT, userMessage, signal);
    return json as AgentContext;
  }

  async refineContext(prev: AgentContext, lastExecution: { summary: string; tool?: string; justification?: string }, signal?: AbortSignal): Promise<AgentContext> {
    const history = [...(prev.history ?? []), { summary: lastExecution.summary, tool: lastExecution.tool, justification: lastExecution.justification }];
    const userMessage = this._makeUserMessage({
      user_query: prev.intent,
      code_snippets_meta: [],
      available_tools: prev.tool_plan?.map(t => t.tool) ?? ["t_grep", "t_read_file", "t_edit_file"],
      previous_history: history,
    });
    const json = await this._callLLM(CONTEXT_BUILDER_SYSTEM_PROMPT, userMessage, signal);
    const next = json as AgentContext;
    next.history = history;
    return next;
  }

  private _makeUserMessage(input: { user_query: string; code_snippets_meta: SnippetMeta[]; available_tools: string[]; previous_history?: AgentContext["history"]; }): string {
    const { user_query, code_snippets_meta, available_tools, previous_history } = input;
    const parts: string[] = [];
    parts.push("Entrées:");
    parts.push(`user_query: ${JSON.stringify(user_query)}`);
    parts.push(`code_snippets_meta: ${JSON.stringify(code_snippets_meta ?? [])}`);
    parts.push(`available_tools: ${JSON.stringify(available_tools ?? [])}`);
    if (previous_history && previous_history.length > 0) {
      parts.push(`historique: ${JSON.stringify(previous_history)}`);
    }
    parts.push("Réponds avec un JSON STRICT conforme au schéma indiqué (aucun texte hors JSON).");
    return parts.join("\n");
  }

  private async _callLLM(systemPrompt: string, userContent: string, signal?: AbortSignal): Promise<unknown> {
    const body = JSON.stringify({
      model: this.model,
      messages: [
        { role: "system", content: systemPrompt },
        { role: "user", content: userContent },
      ],
      stream: false,
      temperature: 0,
    });
    const timeoutMs = 30000;
    const ctrl = new AbortController();
    const onAbort = () => ctrl.abort();
    if (signal) signal.addEventListener('abort', onAbort, { once: true });
    const timer = setTimeout(() => ctrl.abort(), timeoutMs);
    let resp: Response;
    try {
      resp = await fetch("https://openrouter.ai/api/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${this.apiKey}`,
          "HTTP-Referer": "https://github.com/RedTheFoxx/dinootoo-companion",
          "X-Title": "Dinootoo Companion",
        },
        body,
        signal: ctrl.signal,
      } as RequestInit);
    } finally {
      clearTimeout(timer);
      if (signal) signal.removeEventListener('abort', onAbort);
    }
    if (!resp.ok) {
      const t = await resp.text();
      throw new Error(`OpenRouter error: ${resp.status} ${t}`);
    }
    const data = (await resp.json()) as { choices?: { message?: { content?: string } }[] };
    const content = data.choices?.[0]?.message?.content ?? "{}";
    try {
      return JSON.parse(content);
    } catch (e) {
      // Try to salvage JSON code block if present
      const m = content.match(/\{[\s\S]*\}/);
      if (m) {
        return JSON.parse(m[0]);
      }
      throw new Error("Failed to parse context JSON from LLM");
    }
  }
}

