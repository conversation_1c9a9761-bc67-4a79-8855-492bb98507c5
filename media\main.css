body {
    display: flex;
    flex-direction: column;
    height: 100vh;
    padding: 1rem;
    box-sizing: border-box;
    background-color: var(--vscode-panel-background);
}

.top-actions {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    padding-bottom: 0.25rem;
    min-height: 40px;
    height: 40px;
    gap: 0;
}

#conversation-select-button {
    width: 100%;
    font-size: 0.875rem;
    background-color: var(--bs-body-bg);
    color: var(--bs-body-color);
    border: var(--bs-border-width) solid var(--bs-border-color-subtle);
    text-align: left;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 0 1.875rem 0 0.625rem;
    position: relative;
    border-radius: 0;
    font-weight: 700;
    line-height: 1.25;
    transition: border-color 0.2s ease-in-out;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 40px;
}

#conversation-select-button:focus,
#conversation-select-button:active,
.dropdown.show #conversation-select-button {
    border-color: var(--bs-body-color) !important;
}
#conversation-select-button:focus { outline: 0; box-shadow: none; }


#conversation-select-button:hover {
    border-color: var(--bs-border-color-subtle);
}


#conversation-select-button::after {
    display: inline-block;
    align-self: center;
    content: "";
    border-top: calc(0.3125rem + var(--bs-border-width)) solid;
    border-right: calc(0.3125rem + var(--bs-border-width)) solid transparent;
    border-bottom: 0;
    border-left: calc(0.3125rem + var(--bs-border-width)) solid transparent;
    flex-shrink: 0;
    position: absolute;
    right: 0.625rem;
    top: 50%;
    transform: translateY(-50%);
    transition: transform 0.2s ease-in-out;
}

.dropdown.show #conversation-select-button::after,
#conversation-select-button[aria-expanded="true"]::after {
    transform: translateY(-50%) rotate(180deg);
}

#conversation-select-menu {
    max-height: 200px;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    overflow-anchor: none;
    font-size: 0.875rem;
    background-color: var(--bs-dropdown-bg, #fff);
    border: var(--bs-dropdown-border-width, 1px) solid var(--bs-dropdown-border-color, rgba(0,0,0,0.15));
    box-shadow: var(--bs-dropdown-box-shadow, none);
    min-width: 100%;
    margin-top: calc(-1 * var(--bs-border-width));
    border-radius: 0;
    padding: 0;
    z-index: var(--bs-dropdown-z-index, 1000);
}

.dropdown.show #conversation-select-menu {
    border-color: var(--bs-body-color, #212529) !important;
}

#conversation-select-menu .dropdown-item {
    padding: 0.625rem;
    font-size: 0.875rem;
    font-weight: 700;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: var(--bs-body-color, #212529);
    line-height: 1.125;
    transition: background-color 0.15s ease-in-out;
}

#conversation-select-menu .dropdown-item:hover,
#conversation-select-menu .dropdown-item:focus {
    background-color: var(--bs-secondary-bg);
    color: var(--bs-body-color, #212529);
    outline: none;
}

#conversation-select-menu .dropdown-item:active {
    background-color: var(--bs-tertiary-active-bg);
}

#new-conversation-button,
#settings-button {
    color: var(--vscode-foreground);
    border: none;
    background: transparent;
    width: 24px;
    height: 24px;
    padding: 0;
    margin: 0;
    border-radius: 0;
    position: relative;
    min-width: 32px;
    min-height: 40px;
    height: 40px;
    transition: all 0.15s ease-in-out;
    display: flex;
    align-items: center;
    justify-content: center;
}
#new-conversation-button { flex-shrink: 0; }

#new-conversation-button::after,
#settings-button::after { content: ""; position: absolute; inset: -8px; }

#new-conversation-button:hover,
#settings-button:hover {
    background-color: var(--vscode-toolbar-hoverBackground);
    color: var(--bs-primary, #f16e00);
}

#new-conversation-button:focus,
#new-conversation-button:focus-visible,
#settings-button:focus,
#settings-button:focus-visible {
    outline: 2px solid var(--bs-primary, #f16e00);
    outline-offset: 2px;
}

#new-conversation-button img,
#settings-button img { transition: filter 0.15s ease-in-out; width: 18px; height: 18px; }
#new-conversation-button img { filter: none; }
#new-conversation-button:hover img,
#settings-button:hover img { filter: brightness(1.2) saturate(1.2); }

.message-list {
    flex-grow: 1;
    overflow-y: auto;
    margin-bottom: 0.25rem;
    padding: 0.5rem 0;
    list-style: none;
    padding-left: 0;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.message-list::-webkit-scrollbar {
    display: none;
}

.message {
    display: flex;
    flex-direction: column;
    padding: 1rem 0;
    border-bottom: 1px solid var(--vscode-editorWidget-border);
    font-size: 0.875rem;
    line-height: 1.4;
}

.message:last-child {
    border-bottom: none;
}

.message-user {
    align-items: flex-end;
    text-align: right;
}

.message-bot {
    align-items: flex-start;
    width: 100%;
}

.message-bot .placeholder {
    width: 100%;
    min-height: 2rem;
    background: transparent;
    box-shadow: none;
    border: 1px dotted var(--vscode-editorWidget-border);
    font-size: 0.7rem;
    line-height: 1.2;
    color: var(--vscode-descriptionForeground);
}

.input-container {
    position: relative;
    margin-top: 0;
    padding-top: 0.125rem;
}

#message-input {
    min-height: 80px;
    padding-right: 40px;
    line-height: 1.5;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

#message-input::-webkit-scrollbar {
    display: none;
}

#input-actions {
    position: absolute;
    bottom: 12px;
    right: 10px;
    display: flex;
    flex-direction: column; /* stack slider above send button */
    align-items: center;
    gap: 6px;
}

#agent-switch-container {
    padding: 0;
    margin: 0;
}

#agent-switch-container .vertical-toggle {
    position: relative;
    display: block;
    width: 13px; /* track width exactly matches button width */
    height: 28px;
    border-radius: 0; /* completely square edges */
    background: linear-gradient(180deg, rgba(0,0,0,0.08), rgba(0,0,0,0.12));
    border: 1px solid var(--vscode-editorWidget-border);
    box-shadow: inset 0 1px 1px rgba(0,0,0,0.25), inset 0 -1px 1px rgba(255,255,255,0.05);
    cursor: pointer;
}

/* Track fill (orange when ON) */
#agent-switch:checked + .vertical-toggle {
    background: linear-gradient(180deg, rgba(241,110,0,0.9), rgba(241,110,0,0.8));
    border-color: rgba(241,110,0,0.9);
}

/* Thumb */
#agent-switch-container .vertical-toggle::after {
    content: "";
    position: absolute;
    left: 0px; /* no gap: track is 12px, button is 12px, so button fills entire track width */
    width: 12px;
    height: 12px;
    border-radius: 0; /* completely square edges */
    background: linear-gradient(145deg, #ffffff, #dcdcdc);
    box-shadow: 0 1px 1px rgba(0,0,0,0.35), inset 0 1px 0 rgba(255,255,255,0.9);
    transform: translateY(2px);
    transition: transform 0.15s ease-in-out;
}

#agent-switch:checked + .vertical-toggle::after {
    transform: translateY(14px); /* keep small margins top/bottom */
}

/* Focus ring via label when input focused */
#agent-switch:focus + .vertical-toggle { outline: none; }

#send-button {
    background: transparent;
    border: none;
    padding: 3px;
    cursor: pointer;
    color: var(--vscode-icon-foreground);
}

#send-button:hover {
    background: var(--vscode-toolbar-hoverBackground);
}

#send-button:hover .send-icon {
    filter: brightness(1.3) saturate(1.2);
}

#send-button:focus-visible {
    outline: 2px solid var(--bs-primary, #f16e00);
    outline-offset: 2px;
}

#send-button .send-icon {
    width: 16px;
    height: 16px;
    filter: none;
}

.btn-outline-danger {
    border-color: var(--bs-danger, #cd3c14);
    color: var(--bs-danger, #cd3c14);
}

.btn-outline-danger:hover {
    background-color: var(--bs-danger, #cd3c14);
    border-color: var(--bs-danger, #cd3c14);
    color: white;
}

.btn:focus {
    outline: 2px solid var(--bs-primary, #f16e00);
    outline-offset: 2px;
}

.form-control:focus {
    border-color: var(--bs-primary, #f16e00);
    box-shadow: 0 0 0 0.2rem rgba(241, 110, 0, 0.25);
}

/* Styles for code blocks */
.message pre {
    background-color: #1e1e1e;
    border: 1px solid rgba(255,255,255,0.08);
    border-radius: 0.25rem;
    padding: 0.75rem 1rem;
    overflow-x: auto;
    overflow-y: hidden;
    font-size: 0.875em;
    width: 100%;
    box-sizing: border-box;
    white-space: pre-wrap;
    overflow-wrap: anywhere;
    word-break: normal;
}

.message code {
    font-family: var(--vscode-editor-font-family, "Courier New", monospace);
    background-color: rgba(255,255,255,0.04);
    border: 1px solid rgba(255,255,255,0.06);
    border-radius: 0.2rem;
    padding: 0.1em 0.3em;
    overflow-wrap: break-word;
    max-width: 100%;
}

.message pre code {
    padding: 0;
    background-color: transparent;
    border: none;
}

.message table {
    width: 100%;
    border-collapse: collapse;
    margin: 0.5rem 0;
    font-size: 0.85rem;
}

.message th,
.message td {
    border: 1px solid var(--vscode-editorWidget-border);
    padding: 0.5rem 0.75rem;
    vertical-align: top;
}

.message thead th {
    background-color: rgba(255, 255, 255, 0.04);
}

.message tbody tr:nth-child(even) td {
    background-color: rgba(255, 255, 255, 0.02);
}

.message ul,
.message ol {
    margin: 0.5rem 0 0.5rem 1.25rem;
    padding-left: 1rem;
}

.message li {
    margin: 0.25rem 0;
}

.message ul { list-style: disc; }
.message ol { list-style: decimal; }

.message ul li::marker {
    color: var(--bs-primary, #f16e00);
}

.message li ul,
.message li ol {
    margin-top: 0.25rem;
}

.snippet-container {
    display: flex;
    flex-direction: column;
    padding: 0 0.25rem;
    margin-bottom: 0.125rem;
    flex-shrink: 0;
}

.snippet-container.has-content {
    border-top: none;
    padding-top: 0;
}

.snippet-header {
    display: flex;
    align-items: center;
    padding: 0.375rem 0.5rem;
    background-color: transparent;
    border: 1px solid var(--vscode-editorWidget-border);
    border-radius: 0;
    cursor: pointer;
    transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, transform 0.15s ease-in-out;
    margin-bottom: 0.25rem;
    user-select: none;
}

.snippet-header:hover,
.snippet-icon:hover { background-color: var(--vscode-toolbar-hoverBackground); border-color: var(--vscode-editorWidget-border); }

.snippet-header:focus-visible {
    outline: 2px solid var(--vscode-focusBorder);
    outline-offset: 2px;
}

.snippet-header .arrow {
    margin-right: 0.5rem;
    transition: transform 0.15s ease-in-out;
    color: var(--vscode-foreground);
    display: flex;
    align-items: center;
}

.snippet-header.expanded .arrow {
    transform: rotate(90deg);
}

.snippet-header .title {
    flex: 1;
    font-weight: 600;
    color: var(--vscode-foreground);
    font-size: 0.875rem;
}

.snippet-header .count {
    color: var(--vscode-descriptionForeground);
    font-weight: 700;
    font-size: 0.75rem;
    margin-left: 0.5rem;
}

.snippet-content {
    transition: all 0.15s ease-in-out;
    overflow: hidden;
}

.snippet-content.collapsed {
    max-height: 0;
    opacity: 0;
    margin-bottom: 0;
}

.snippet-content.expanded {
    max-height: 500px;
    opacity: 1;
    margin-bottom: 0.0625rem;
}

.snippet-icons-row {
    display: flex;
    flex-wrap: wrap;
    gap: 0.125rem;
    align-items: center;
}

.snippet-icon {
    padding: 0.25rem 0.5rem;
    border: 1px solid var(--vscode-editorWidget-border);
    background-color: transparent;
    color: var(--vscode-foreground);
    font-size: 0.75rem;
    cursor: pointer;
    border-radius: 0;
    transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, transform 0.15s ease-in-out;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    min-width: 0;
    flex: 1;
    max-width: 200px;
}

.snippet-icon:hover { transform: translateY(-1px); }

.snippet-icon .file-name {
    font-weight: 600;
    color: var(--vscode-foreground);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%;
    margin-bottom: 0.0625rem;
}

.snippet-icon .file-lines {
    font-size: 0.625rem;
    color: var(--vscode-descriptionForeground);
    font-weight: 500;
}



.snippet-overflow {
    padding: 0.05rem 0.15rem;
    line-height: 1;
    border: none;
    background-color: transparent;
    color: var(--vscode-icon-foreground);
    font-size: 0.6rem;
    cursor: pointer;
}

.snippet-overflow:hover {
    background-color: var(--vscode-toolbar-hoverBackground);
}

.codeblock-wrapper {
    position: relative;
    margin: 0.5rem 0;
    width: 100%;
    box-sizing: border-box;
}

.codeblock-wrapper > pre {
    padding-top: 1.6rem;
    position: relative;
}

.codeblock-actions {
    position: absolute;
    top: 0.25rem;
    right: 8px;
    height: 1.6rem;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    background: transparent;
    z-index: 1;
}

.code-action {
    border: none;
    background: transparent;
    color: var(--vscode-descriptionForeground);
    font-size: 0.65rem;
    line-height: 1;
    padding: 4px;
    border-radius: 3px;
    cursor: pointer;
    transition: background-color 0.15s ease-in-out, color 0.15s ease-in-out, opacity 0.15s ease-in-out;
    opacity: 0.8;
}

.code-action:hover {
    background-color: var(--vscode-toolbar-hoverBackground);
    color: var(--vscode-foreground);
    opacity: 1;
}

.code-action:disabled {
    opacity: 0.6;
    cursor: default;
}

.code-action-icon {
    width: 16px;
    height: 16px;
    display: block;
    filter: none;
}

.codeblock-compact {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0.5rem;
    border: 1px solid var(--vscode-editorWidget-border);
    border-radius: 0.25rem;
    background: transparent;
    cursor: pointer;
    user-select: none;
    min-height: 1.75rem;
    width: 100%;
    box-sizing: border-box;
    margin: 0;
    max-width: none;
}

.codeblock-compact .arrow {
    display: inline-flex;
    width: 14px;
    height: 14px;
    flex: 0 0 14px;
    color: var(--vscode-foreground);
    transition: transform 0.15s ease-in-out;
}

.codeblock-compact .title {
    flex: 1 1 auto;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    color: var(--vscode-foreground);
    font-family: var(--vscode-editor-font-family, "Courier New", monospace);
    font-size: 0.85rem;
}

.codeblock-compact .linecount {
    flex: 0 0 auto;
    color: var(--vscode-descriptionForeground);
    font-weight: 700;
    font-size: 0.85rem;
}

.codeblock-wrapper.collapsed > pre,
.codeblock-wrapper.collapsed .codeblock-actions { display: none; }

.codeblock-wrapper .codeblock-compact .linecount {
    display: block;
}

.codeblock-wrapper.expanded .codeblock-compact .arrow {
    transform: rotate(90deg);
}

.katex-display {
    display: block;
    text-align: center;
    margin: 0.5em 0;
    padding: 0.5em;
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;
}
