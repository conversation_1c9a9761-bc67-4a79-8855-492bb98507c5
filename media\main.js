(function () {
    const vscode = acquireVsCodeApi();

    // Configure marked to use highlight.js
    marked.setOptions({
        langPrefix: 'hljs language-' // for compatibility with highlight.js
    });

    // Set the theme
    const body = document.body;
    const theme = body.dataset.vscodeThemeKind === 'vscode-dark' ? 'dark' : 'light';
    document.documentElement.dataset.bsTheme = theme;
    const ICON_COPY_URI = body.dataset.iconCopy || '';
    const ICON_INSERT_URI = body.dataset.iconInsert || '';
    const CODE_BLOCKS_EXPANDED = body.dataset.codeBlocksExpanded === 'true';

    // Dynamically load highlight.js theme
    const head = document.head;
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    const themeKind = body.dataset.vscodeThemeKind;
    let cssTheme = 'vs2015.css'; // default to dark
    if (themeKind === 'vscode-light') {
        cssTheme = 'vs2015.css';
    } else if (themeKind === 'vscode-high-contrast') {
        cssTheme = 'vs2015.css';
    } else if (themeKind === 'vscode-high-contrast-light') {
        cssTheme = 'vs2015.css';
    }
    link.href = `https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.11.1/styles/${cssTheme}`;
    head.appendChild(link);


    const oldState = vscode.getState() || { messages: [] };

    /** @type {Array<{ value: string }>} */
    let messages = oldState.messages;

    /** @type {Array} */
    let conversationHistories = [];

    /** @type {string | null} */
    let currentConversationId = null;

    // Flag pour indiquer qu'on est en mode nouvelle conversation
    let isNewConversationMode = false;

    // Placeholder for streaming bot responses
    /** @type {HTMLDivElement | null} */
    let placeholderBubble = null;
    let streamedText = '';

    updateMessageList();

    // Handle messages sent from the extension to the webview
    window.addEventListener('message', event => {
        const message = event.data; // The json data that the extension sent
        switch (message.type) {
            case 'addMessage':
                {
                    messages.push(message.data);
                    updateMessageList();
                    break;
                }
            case 'startResponse':
                {
                    startResponse();
                    break;
                }
            case 'streamResponse':
                {
                    streamResponse(message.data.value);
                    break;
                }
            case 'endResponse':
                {
                    endResponse(message.data.value);
                    break;
                }
            case 'resetUI':
                {
                    // Force UI back to idle (safety net)
                    placeholderBubble = null;
                    streamedText = '';
                    isResponding = false;
                    updateButtonState();
                    break;
                }
            case 'addCodeSnippet':
                {
                    addSnippetIcon(message.data);
                    break;
                }
            case 'conversationHistories':
                {
                    conversationHistories = message.data;
                    updateConversationSelector();
                    break;
                }
            case 'conversationLoaded':
                {
                    const { messages: conversationMessages, codeSnippets } = message.data;
                    messages = conversationMessages;
                    currentConversationId = message.data.conversation.id;
                    
                    // Désactiver le mode nouvelle conversation quand une conversation est chargée
                    isNewConversationMode = false;
                    
                    updateMessageList();

                    // Clear existing snippets and add loaded ones
                    document.getElementById('snippet-container').innerHTML = '';
                    codeSnippets.forEach(snippet => addSnippetIcon(snippet));
                    
                    // Mettre à jour le sélecteur de conversation pour afficher le bon titre
                    updateConversationSelector();
                    updateBackgroundLogoVisibility();
                    break;
                }
            case 'conversationCleared':
                {
                    messages = [];
                    currentConversationId = null;
                    updateMessageList();
                    document.getElementById('snippet-container').innerHTML = '';

                    // Activer le mode nouvelle conversation
                    isNewConversationMode = true;
                    updateConversationSelector();
                    updateBackgroundLogoVisibility();
                    break;
                }
            case 'conversationTitleUpdated':
                {
                    // Désactiver le mode nouvelle conversation
                    isNewConversationMode = false;
                    // Mettre à jour l'ID de conversation actuelle avec la nouvelle conversation créée
                    if (message.data && message.data.conversationId) {
                        currentConversationId = message.data.conversationId;
                    }
                    // Recharger la liste des conversations après mise à jour du titre
                    vscode.postMessage({ type: 'getConversationHistories' });
                    // Mettre à jour immédiatement le sélecteur
                    updateConversationSelector();
                    updateBackgroundLogoVisibility();
                    break;
                }
            case 'conversationHistoriesCleared':
                {
                    // Réinitialiser complètement l'état local
                    conversationHistories = [];
                    currentConversationId = null;
                    updateConversationSelector();
                    break;
                }
        }
    });

    function addSnippetIcon(snippet) {
        const container = document.getElementById('snippet-container');

        // Créer l'en-tête si elle n'existe pas
        let header = container.querySelector('.snippet-header');
        if (!header) {
            header = document.createElement('div');
            header.className = 'snippet-header collapsed';
            header.setAttribute('role', 'button');
            header.setAttribute('tabindex', '0');
            header.setAttribute('aria-expanded', 'false');

            const arrow = document.createElement('span');
            arrow.className = 'arrow';
            arrow.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                    <path d="M6 3l5 5-5 5" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>`;

            const title = document.createElement('span');
            title.className = 'title';
            title.textContent = 'Contexte ajouté';

            const count = document.createElement('span');
            count.className = 'count';
            count.textContent = '0';

            header.appendChild(arrow);
            header.appendChild(title);
            header.appendChild(count);

            // Créer le contenu
            const content = document.createElement('div');
            content.className = 'snippet-content collapsed';

            const iconsRow = document.createElement('div');
            iconsRow.className = 'snippet-icons-row';
            content.appendChild(iconsRow);

            container.appendChild(header);
            container.appendChild(content);

            // Gérer le clic pour replier/déplier
            function toggle() {
                const isCollapsed = header.classList.contains('collapsed');
                header.classList.toggle('collapsed', !isCollapsed);
                header.classList.toggle('expanded', isCollapsed);
                content.classList.toggle('collapsed', !isCollapsed);
                content.classList.toggle('expanded', isCollapsed);
                header.setAttribute('aria-expanded', String(isCollapsed));
            }

            header.addEventListener('click', toggle);
            header.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    toggle();
                }
            });
        }

        // Ajouter le contenu aux snippets
        container.classList.add('has-content');

        const iconsRow = container.querySelector('.snippet-icons-row');

        const button = document.createElement('button');
        button.className = 'snippet-icon';
        button.title = `${snippet.fileName}:${snippet.startLine}-${snippet.endLine}`;
        button.dataset.id = snippet.id;
        button.dataset.fileName = snippet.fileName;

        const nameEl = document.createElement('span');
        nameEl.className = 'file-name';
        nameEl.textContent = String(snippet.fileName ?? '');

        const linesEl = document.createElement('span');
        linesEl.className = 'file-lines';
        linesEl.textContent = `lignes ${snippet.startLine}-${snippet.endLine}`;

        button.appendChild(nameEl);
        button.appendChild(linesEl);
        button.addEventListener('click', () => {
            vscode.postMessage({ type: 'removeCodeSnippet', value: snippet.id });
            button.remove();
            updateSnippetContainerOverflow();
        });

        iconsRow.appendChild(button);
        updateSnippetContainerOverflow();
    }

    function updateSnippetContainerOverflow() {
        const container = document.getElementById('snippet-container');
        const iconsRow = container.querySelector('.snippet-icons-row');
        const header = container.querySelector('.snippet-header');
        const content = container.querySelector('.snippet-content');
        const countElement = container.querySelector('.count');

        if (!iconsRow) {
            // S'il n'y a pas d'icônes, nettoyer complètement
            container.classList.remove('has-content');
            if (header) header.remove();
            if (content) content.remove();
            return;
        }

        const icons = Array.from(iconsRow.querySelectorAll('.snippet-icon'));

        // Mettre à jour le compteur
        if (countElement) {
            countElement.textContent = icons.length.toString();
        }

        // Vérifier s'il reste des icônes
        if (icons.length === 0) {
            container.classList.remove('has-content');
            if (header) header.remove();
            if (content) content.remove();
        }
    }

    function updateMessageList() {
        const ul = document.querySelector('.message-list');
        ul.textContent = '';
        for (const message of messages) {
            const li = document.createElement('li');

            // Detect role from value prefix (keeps extension side intact)
            let content = message.value || '';
            const userPrefix = /^Vous\s*:\s*/i;
            const botPrefix = /^Bot\s*:\s*/i;
            let role = 'assistant';
            if (userPrefix.test(content)) {
                role = 'user';
                content = content.replace(userPrefix, '');
            } else if (botPrefix.test(content)) {
                role = 'assistant';
                content = content.replace(botPrefix, '');
            }

            li.className = `message ${role === 'user' ? 'message-user' : 'message-bot'}`;

            if (role === 'assistant') {
                // Sanitize and parse the content if it's from the bot
                li.innerHTML = marked.parse(content);

                // Inject code block actions (copy / insert) inside each code block
                attachCodeBlockActions(li);

                // Render LaTeX expressions
                renderMathInElement(li, {
                    delimiters: [
                        { left: '$$', right: '$$', display: true },
                        { left: '$', right: '$', display: false },
                        { left: '\\(', right: '\\)', display: false },
                        { left: '\\[', right: '\\]', display: true }
                    ]
                });
            } else {
                // For user messages, just set the text content to avoid any XSS issues.
                li.textContent = content;
            }
            ul.appendChild(li);
        }

        // Update the saved state
        vscode.setState({ messages: messages });

        // Highlight all code blocks
        hljs.highlightAll();

        // Contrôler la visibilité du logo décoratif
        updateBackgroundLogoVisibility();
    }

    function updateBackgroundLogoVisibility() {
        const backgroundLogo = document.getElementById('background-logo');
        if (!backgroundLogo) {
            return;
        }

        // Afficher le logo si :
        // 1. Il n'y a pas de messages dans la conversation
        // 2. ET soit on est en mode nouvelle conversation, soit on n'a pas de conversation actuelle (premier lancement)
        const shouldShowLogo = messages.length === 0 && (isNewConversationMode || !currentConversationId);
        
        if (shouldShowLogo) {
            backgroundLogo.style.opacity = '0.01';
        } else {
            backgroundLogo.style.opacity = '0';
        }
    }

    // Add discreet action buttons to each rendered code block
    function attachCodeBlockActions(root) {
        /** @type {NodeListOf<HTMLElement>} */
        const codeBlocks = root.querySelectorAll('pre > code');
        codeBlocks.forEach(codeEl => {
            const pre = codeEl.parentElement;
            if (!pre) {
                return;
            }

            // Avoid adding actions twice
            if (pre.parentElement && pre.parentElement.classList.contains('codeblock-wrapper')) {
                return;
            }

            // Wrap <pre> to position actions on top-right and add compact header
            const wrapper = document.createElement('div');
            wrapper.className = `codeblock-wrapper ${CODE_BLOCKS_EXPANDED ? 'expanded' : 'collapsed'}`;
            pre.replaceWith(wrapper);
            
            // S'assurer que l'état initial est correctement défini
            if (CODE_BLOCKS_EXPANDED) {
                wrapper.classList.remove('collapsed');
                wrapper.classList.add('expanded');
            } else {
                wrapper.classList.remove('expanded');
                wrapper.classList.add('collapsed');
            }

            // Build compact one-line header (arrow, first line excerpt, line count)
            const codeText = (codeEl.textContent || codeEl.innerText || '').replace(/\r\n/g, '\n');
            const rawLines = codeText.split('\n');
            while (rawLines.length > 1 && rawLines[rawLines.length - 1] === '') {
                rawLines.pop();
            }
            const firstLine = (rawLines[0] || '').trim();
            const lineCount = rawLines.length;            
            const compact = document.createElement('div');
            compact.className = 'codeblock-compact';
            compact.setAttribute('role', 'button');
            compact.setAttribute('tabindex', '0');
            compact.setAttribute('aria-expanded', String(CODE_BLOCKS_EXPANDED));

            const arrow = document.createElement('span');
            arrow.className = 'arrow';
            arrow.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                    <path d="M6 3l5 5-5 5" stroke="currentColor" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>`;

            const title = document.createElement('span');
            title.className = 'title';
            title.textContent = firstLine + (lineCount > 1 ? '…' : '');

            const count = document.createElement('span');
            count.className = 'linecount';
            count.textContent = `${lineCount} ligne${lineCount > 1 ? 's' : ''}`;            

            compact.appendChild(arrow);
            compact.appendChild(title);
            compact.appendChild(count);
            // Toggle expand/collapse
            function toggle() {
                const isCollapsed = wrapper.classList.contains('collapsed');
                wrapper.classList.toggle('collapsed', !isCollapsed);
                wrapper.classList.toggle('expanded', isCollapsed);
                const expanded = isCollapsed;
                compact.setAttribute('aria-expanded', String(expanded));
                pre.setAttribute('aria-hidden', String(!expanded));
                if (expanded) {
                    // focus non intrusif
                    pre.focus({ preventScroll: false });
                }
            }
            compact.addEventListener('click', toggle);
            compact.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    toggle();
                }
            });

            wrapper.appendChild(compact);
            wrapper.appendChild(pre);
            
            // Définir l'état initial de aria-hidden pour le pre
            pre.setAttribute('aria-hidden', String(!CODE_BLOCKS_EXPANDED));

            const actions = document.createElement('div');
            actions.className = 'codeblock-actions';

            const btnCopy = document.createElement('button');
            btnCopy.type = 'button';
            btnCopy.className = 'code-action';
            btnCopy.setAttribute('aria-label', 'Copier le code');
            btnCopy.title = 'Copier le code';
            btnCopy.innerHTML = ICON_COPY_URI ? `<img src="${ICON_COPY_URI}" alt="Copier" class="code-action-icon">` : 'Copier';
            btnCopy.addEventListener('click', async (e) => {
                e.preventDefault();
                try {
                    const text = codeEl.innerText || codeEl.textContent || '';
                    await navigator.clipboard.writeText(text);
                    feedback(btnCopy, 'Copié !');
                } catch {
                    feedback(btnCopy, 'Échec');
                }
            });

            const btnInsert = document.createElement('button');
            btnInsert.type = 'button';
            btnInsert.className = 'code-action';
            btnInsert.setAttribute('aria-label', "Insérer au curseur");
            btnInsert.title = "Insérer au curseur dans l'éditeur";
            btnInsert.innerHTML = ICON_INSERT_URI ? `<img src="${ICON_INSERT_URI}" alt="Insérer" class="code-action-icon">` : 'Insérer';
            btnInsert.addEventListener('click', (e) => {
                e.preventDefault();
                const text = codeEl.innerText || codeEl.textContent || '';
                vscode.postMessage({ type: 'insertCodeAtCursor', value: text });
                feedback(btnInsert, 'Envoyé');
            });

            actions.appendChild(btnCopy);
            actions.appendChild(btnInsert);
            // Position actions inside <pre> so they align to code area
            pre.appendChild(actions);
        });

        function feedback(button, msg) {
            const prevHtml = button.innerHTML;
            const prevTitle = button.title;
            button.innerHTML = msg;
            button.disabled = true;
            setTimeout(() => {
                button.innerHTML = prevHtml;
                button.title = prevTitle;
                button.disabled = false;
            }, 1200);
        }
    }

    const sendButton = document.getElementById('send-button');
    const messageInput = document.getElementById('message-input');
    const settingsButton = document.getElementById('settings-button');
    const conversationSelectButton = document.getElementById('conversation-select-button');
    const conversationSelectMenu = document.getElementById('conversation-select-menu');
    const newConversationButton = document.getElementById('new-conversation-button');
    const agentSwitch = document.getElementById('agent-switch');
    let isResponding = false;
    
    // Récupérer l'URI de l'icône d'envoi depuis l'élément img existant
    const sendIconImg = sendButton.querySelector('img.send-icon');
    const sendIconUri = sendIconImg ? sendIconImg.src : '';

    sendButton.addEventListener('click', () => {
        if (isResponding) {
            stopResponse();
        } else {
            sendMessage();
        }
    });

    settingsButton.addEventListener('click', () => {
        vscode.postMessage({ type: 'openSettings' });
    });

    conversationSelectMenu.addEventListener('click', (event) => {
        const dropdownItem = event.target.closest('.dropdown-item');
        if (dropdownItem) {
            event.preventDefault();
            const selectedValue = dropdownItem.dataset.value;
            if (selectedValue) {
                loadConversation(selectedValue);
            }
        }
    });

    newConversationButton.addEventListener('click', async () => {
        await createNewConversation();
    });

    agentSwitch.addEventListener('change', (event) => {
        const isAgentMode = event.target.checked;
        vscode.postMessage({ 
            type: 'toggleAgentMode', 
            value: isAgentMode 
        });
    });

    messageInput.addEventListener('focus', () => {
        vscode.postMessage({ type: 'captureStart' });
    });

    messageInput.addEventListener('blur', () => {
        vscode.postMessage({ type: 'captureEnd' });
    });

    messageInput.addEventListener('keydown', (event) => {
        if (event.key === 'Enter' && !event.shiftKey) {
            event.preventDefault();
            if (isResponding) {
                stopResponse();
            } else {
                sendMessage();
            }
        }
    });

    // Auto-resize textarea
    messageInput.addEventListener('input', () => {
        messageInput.style.height = 'auto';
        messageInput.style.height = Math.min(messageInput.scrollHeight, 120) + 'px';
    });

    function sendMessage() {
        const message = messageInput.value;
        if (message) {
            // Si c'est la première interaction d'une nouvelle conversation, générer un titre
            const isFirstInteraction = messages.length === 0 && !currentConversationId;
            const isAgentMode = agentSwitch.checked;

            vscode.postMessage({
                type: 'sendMessage',
                value: {
                    text: message,
                    generateTitle: isFirstInteraction,
                    isAgentMode: isAgentMode,
                }
            });
            messageInput.value = '';
            messageInput.style.height = 'auto';
        }
    }

    function stopResponse() {
        vscode.postMessage({ type: 'stopResponse' });
    }

    function updateButtonState() {
        if (isResponding) {
            // Change to stop button
            sendButton.className = 'btn btn-danger btn-sm';
            sendButton.setAttribute('aria-label', 'Arrêter');
            sendButton.setAttribute('title', 'Arrêter');
            sendButton.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16" aria-hidden="true">
                    <rect x="3" y="3" width="10" height="10" rx="0" ry="0"/>
                </svg>
            `;
            // Disable input field
            messageInput.disabled = true;
            messageInput.placeholder = 'Réponse en cours ...';
        } else {
            // Change back to send button
            sendButton.className = 'btn btn-primary btn-sm';
            sendButton.setAttribute('aria-label', 'Envoyer');
            sendButton.setAttribute('title', 'Envoyer');
            sendButton.innerHTML = `<img src="${sendIconUri}" class="send-icon" alt="Envoyer">`;
            // Enable input field
            messageInput.disabled = false;
            messageInput.placeholder = 'Utilisez SHIFT+ENTER pour aller à la ligne';
        }
    }

    function startResponse() {
        const ul = document.querySelector('.message-list');
        const li = document.createElement('li');
        li.className = 'message message-bot me-auto';
        placeholderBubble = document.createElement('div');
        placeholderBubble.className = 'bubble px-3 py-2 placeholder d-flex align-items-center';
        const spinner = document.createElement('div');
        spinner.className = 'spinner-border spinner-border-sm';
        spinner.setAttribute('role', 'status');
        spinner.setAttribute('aria-hidden', 'true');
        placeholderBubble.appendChild(spinner);
        li.appendChild(placeholderBubble);
        ul.appendChild(li);
        ul.scrollTop = ul.scrollHeight;
        streamedText = '';
        
        // Change button to stop button
        isResponding = true;
        updateButtonState();
    }

    function streamResponse(token) {
        if (!placeholderBubble) {
            return;
        }
        streamedText += token;
        if (placeholderBubble.innerHTML !== '') {
            // Remove spinner on first token
            placeholderBubble.innerHTML = '';
        }
        placeholderBubble.textContent = streamedText;
        const ul = document.querySelector('.message-list');
        ul.scrollTop = ul.scrollHeight;
    }

    function endResponse(finalText) {
        // Always reset UI first to avoid being stuck even if rendering fails
        isResponding = false;
        updateButtonState();

        const ul = document.querySelector('.message-list');
        try {
            if (placeholderBubble && placeholderBubble.parentElement) {
                ul.removeChild(placeholderBubble.parentElement);
            }
        } catch {}
        placeholderBubble = null;

        // Now try to display the final text
        try {
            if (finalText) {
                messages.push({ value: `Bot : ${finalText}` });
                updateMessageList();
            }
        } catch (e) {
            // If rendering fails, keep UI responsive and at least clear the stream buffer
            console.error('endResponse render error', e);
        }
        streamedText = '';
    }

function updateConversationSelector() {
    const button = document.getElementById('conversation-select-button');
    const menu = document.getElementById('conversation-select-menu');

    // Clear all existing menu items
    menu.innerHTML = '';

    // Ajouter les conversations existantes
    if (conversationHistories.length > 0) {
        // Add conversation options (exclude the currently selected one)
        const historiesToShow = (!isNewConversationMode && currentConversationId)
            ? conversationHistories.filter(h => h.id !== currentConversationId)
            : conversationHistories;

        historiesToShow.forEach(history => {
            const li = document.createElement('li');
            const link = document.createElement('a');
            link.className = 'dropdown-item';
            link.href = '#';
            link.dataset.value = history.id;
            link.textContent = `${history.title} (${new Date(history.lastActivity).toLocaleDateString()})`;
            li.appendChild(link);
            menu.appendChild(li);
        });
    }

    // Mettre à jour le texte du bouton selon l'état actuel
    if (isNewConversationMode) {
        // En mode nouvelle conversation, afficher "Nouvelle conversation"
        button.textContent = 'Nouvelle conversation';
    } else if (currentConversationId && conversationHistories.length > 0) {
        // Si on a une conversation actuelle, afficher son titre
        const currentHistory = conversationHistories.find(h => h.id === currentConversationId);
        if (currentHistory) {
            button.textContent = `${currentHistory.title} (${new Date(currentHistory.lastActivity).toLocaleDateString()})`;
        } else {
            button.textContent = 'Nouvelle conversation';
        }
    } else if (conversationHistories.length > 0 && messages.length > 0) {
        // Si on a des historiques et des messages mais pas d'ID de conversation actuelle,
        // c'est probablement la dernière conversation qui a été restaurée automatiquement
        const lastHistory = conversationHistories[0]; // La plus récente
        button.textContent = `${lastHistory.title} (${new Date(lastHistory.lastActivity).toLocaleDateString()})`;
        // Mettre à jour l'ID de conversation actuelle
        currentConversationId = lastHistory.id;
    } else {
        // Par défaut, afficher "Nouvelle conversation"
        button.textContent = 'Nouvelle conversation';
    }
}

function loadConversation(conversationId) {
    if (conversationId === 'new') {
        // Si l'utilisateur sélectionne "Nouvelle conversation", activer le mode nouvelle conversation
        isNewConversationMode = true;
        currentConversationId = null;
        messages = [];
        updateMessageList();
        document.getElementById('snippet-container').innerHTML = '';
        updateConversationSelector();
    } else if (conversationId && conversationId !== currentConversationId) {
        // Désactiver le mode nouvelle conversation si on charge une conversation existante
        isNewConversationMode = false;
        vscode.postMessage({ type: 'loadConversation', conversationId });
    }
}

async function createNewConversation() {
    // Activer le mode nouvelle conversation
    isNewConversationMode = true;
    
    // Réinitialiser seulement l'ID de conversation actuelle
    currentConversationId = null;
    
    // Vider les messages pour permettre la génération de titre lors du premier message
    messages = [];
    updateMessageList();
    
    // Vider les snippets
    document.getElementById('snippet-container').innerHTML = '';
    
    // Mettre à jour la liste déroulante pour afficher "Nouvelle conversation" + les anciennes
    updateConversationSelector();
    
    // Créer une nouvelle conversation côté extension
    vscode.postMessage({ type: 'createNewConversation' });
}

// Initialize conversation selector
updateConversationSelector();

// Request conversation histories on load
vscode.postMessage({ type: 'getConversationHistories' });

// Initialiser la visibilité du logo
updateBackgroundLogoVisibility();
}());
