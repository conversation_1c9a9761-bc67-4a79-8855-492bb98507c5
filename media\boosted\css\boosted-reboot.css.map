{"version": 3, "sources": ["../../scss/mixins/_banner.scss", "../../scss/_root.scss", "boosted-reboot.css", "../../scss/vendor/_rfs.scss", "../../scss/mixins/_color-mode.scss", "../../scss/_reboot.scss", "../../scss/mixins/_breakpoints.scss", "../../scss/_variables.scss", "../../scss/mixins/_focus.scss"], "names": [], "mappings": "AACE;;;;;;;;;EAAA;ACAF;;EAEE,2BAAA;EACA,mCAAA;ACUF;;ADJA;;EAEE,mBAAA;EASE,kBAAA;EAAA,oBAAA;EAAA,oBAAA;EAAA,kBAAA;EAAA,iBAAA;EAAA,oBAAA;EAAA,iBAAA;EAAA,mBAAA;EAAA,kBAAA;EAAA,kBAAA;EAAA,gBAAA;EAAA,gBAAA;EAAA,eAAA;EAAA,uBAAA;EAIA,sBAAA;EAAA,sBAAA;EAAA,mBAAA;EAAA,mBAAA;EAAA,mBAAA;EAAA,mBAAA;EAAA,mBAAA;EAAA,sBAAA;EAAA,mBAAA;EAAA,sBAAA;EAIA,qBAAA;EAAA,oBAAA;EAAA,qBAAA;EAAA,kBAAA;EAAA,kBAAA;EAAA,oBAAA;EAAA,gBAAA;EAAA,eAAA;EAIA,6BAAA;EAAA,2BAAA;EAAA,6BAAA;EAAA,2BAAA;EAAA,6BAAA;EAAA,4BAAA;EAAA,6BAAA;EAAA,sBAAA;EAIA,mCAAA;EAAA,kCAAA;EAAA,mCAAA;EAAA,gCAAA;EAAA,gCAAA;EAAA,kCAAA;EAAA,8BAAA;EAAA,6BAAA;EAIA,+BAAA;EAAA,8BAAA;EAAA,+BAAA;EAAA,4BAAA;EAAA,4BAAA;EAAA,8BAAA;EAAA,0BAAA;EAAA,yBAAA;EAIA,mCAAA;EAAA,kCAAA;EAAA,mCAAA;EAAA,gCAAA;EAAA,gCAAA;EAAA,kCAAA;EAAA,8BAAA;EAAA,6BAAA;EAGF,6BAAA;EACA,uBAAA;EAIE,mKAAA;EAAA,ojBAAA;EAAA,8KAAA;EAAA,yaAAA;EAAA,gjBAAA;EAQF,uNAAA;EACA,yGAAA;EACA,yFAAA;EAOA,gDAAA;EEyNI,yBALI;EFlNR,0BAAA;EACA,4BAAA;EAKA,qBAAA;EACA,4BAAA;EACA,kBAAA;EACA,+BAAA;EAEA,yBAAA;EACA,gCAAA;EAEA,0BAAA;EACA,uCAAA;EACA,uBAAA;EACA,oCAAA;EAEA,yBAAA;EACA,sCAAA;EACA,yBAAA;EACA,mCAAA;EAGA,2BAAA;EAEA,qBAAA;EACA,4BAAA;EACA,+BAAA;EAEA,8BAAA;EACA,sCAAA;EAMA,qBAAA;EACA,0BAAA;EACA,uBAAA;EACA,6CAAA;EACA,6BAAA;EAGA,2BAAA;EACA,wBAAA;EACA,uBAAA;EACA,8BAAA;EACA,mDAAA;EAEA,4BAAA;EACA,8BAAA;EACA,6BAAA;EACA,2BAAA;EACA,4BAAA;EACA,mDAAA;EACA,8BAAA;EAGA,iBAAA;EACA,oBAAA;EACA,oBAAA;EACA,uBAAA;EAEA,oCAAA;EACA,oCAAA;EAIA,8BAAA;EACA,6BAAA;EACA,8CAAA;EAIA,sDAAA;EACA,+CAAA;EACA,uDAAA;EACA,gDAAA;EAGA,iCAAA;EACA,0CAAA;EACA,wEAAA;EACA,+JAAA;EACA,sLAAA;EACA,gCAAA;EACA,wDAAA;EAIA,kCAAA;EACA,iCAAA;EACA,mCAAA;EAGA,oCAAA;ACXF;;AE/II;EHgKA,kBAAA;EAGA,qBAAA;EACA,kCAAA;EACA,qBAAA;EACA,4BAAA;EAEA,yBAAA;EACA,sCAAA;EAEA,0BAAA;EACA,uCAAA;EACA,uBAAA;EACA,iCAAA;EAEA,yBAAA;EACA,sCAAA;EACA,sBAAA;EACA,6BAAA;EAIE,qBAAA;EAAA,oBAAA;EAAA,kBAAA;EAAA,eAAA;EAAA,kBAAA;EAAA,oBAAA;EAAA,gBAAA;EAAA,eAAA;EAIA,6BAAA;EAAA,iCAAA;EAAA,+BAAA;EAAA,4BAAA;EAAA,6BAAA;EAAA,4BAAA;EAAA,6BAAA;EAAA,sBAAA;EAKA,mCAAA;EAAA,kCAAA;EAAA,gCAAA;EAAA,6BAAA;EAAA,gCAAA;EAAA,kCAAA;EAAA,8BAAA;EAAA,6BAAA;EAIA,+BAAA;EAAA,8BAAA;EAAA,4BAAA;EAAA,yBAAA;EAAA,4BAAA;EAAA,8BAAA;EAAA,0BAAA;EAAA,yBAAA;EAIA,mCAAA;EAAA,kCAAA;EAAA,gCAAA;EAAA,6BAAA;EAAA,gCAAA;EAAA,kCAAA;EAAA,8BAAA;EAAA,6BAAA;EAKA,saAAA;EAAA,gjBAAA;EAIF,2BAAA;EAEA,qBAAA;EACA,8BAAA;EACA,kCAAA;EACA,sCAAA;EAEA,qBAAA;EACA,0BAAA;EACA,uBAAA;EACA,6CAAA;EACA,6BAAA;EAEA,uBAAA;EACA,8BAAA;EACA,wDAAA;EAEA,oCAAA;EACA,oCAAA;EAEA,8CAAA;EAEA,sDAAA;EACA,+CAAA;EACA,uDAAA;EACA,gDAAA;EACA,4BAAA;EACA,0CAAA;EACA,6KAAA;EACA,sLAAA;EACA,wEAAA;EACA,mCAAA;EACA,iEAAA;EAIA,iCAAA;EACA,iCAAA;EACA,+BAAA;EAGA,yCAAA;AChBJ;;AG1OA;;;EAGE,sBAAA;AH6OF;;AGpOA;EAKI,2BAAA;AHmOJ;AIpMI;EDpCJ;IAQM,0BEusE2B;ELn+D/B;AACF;AG1NI;EAnBJ;IAoBM,uBAAA;EH6NJ;AACF;;AG9MA;EACE,kBAAA;EACA,SAAA;EACA,uCAAA;EACA,oBAAA;EF4NI,mCALI;EErNR,uCAAA;EACA,uCAAA;EAEA,qCAAA;EAEA,eAAA;EACA,2BEuqB4B;EFtqB5B,mCAAA;EACA,8BAAA;EACA,6CAAA;EACA,4CAAA;EACA,6BAAA;AH+MF;;AG/LA;EG9FE,kBAAA;EACA,sDAAA;EACA,mBD2oBoC;EC1oBpC,yDAAA;ANiSF;;AGlMA;;EAEE,qBAAA;EACA,gBAAA;AHqMF;;AGlMA;EACE,qBAAA;EACA,gBAAA;AHqMF;;AG3LA;EACE,iBAAA;EACA,cEitB4B;EFhtB5B,SAAA;EACA,wCAAA;AH8LF;;AGnLA;;;;EACE,aAAA;EACA,sBEmVO;EJ3LH,eALI;EE/IR,gBEqpB4B;EFppB5B,kBE8lB4B;EF7lB5B,8BAAA;EAEA,eAAA;EACA,2BEkmB4B;EFjmB5B,mCAAA;EACA,kCAAA;EACA,kCAAA;AHsLF;;AGnLA;EFyIM,kBALI;EEjIR,gBE6nB4B;EF3nB5B,eAAA;EACA,yBEgnB4B;AL5b9B;;AGjLA;;EFgIM,mBALI;EEvHR,yBEonB4B;EFlnB5B,eAAA;EACA,0BEumB4B;ALrb9B;;AGnKA;EACE,aAAA;EACA,mBE+W0B;ALzM5B;;AG5JA;EACE,yCAAA;EAAA,iCAAA;EACA,YAAA;EACA,sCAAA;EAAA,8BAAA;AH+JF;;AGzJA;EACE,mBAAA;EACA,kBAAA;EACA,oBAAA;AH4JF;;AGtJA;;EAEE,kBAAA;AHyJF;;AGtJA;;;EAGE,aAAA;EACA,mBAAA;AHyJF;;AGtJA;;;;EAIE,gBAAA;AHyJF;;AGpJA;EACE,uBAAA;AHuJF;;AGjJA;EACE,wBAAA;EACA,sBAAA;AHoJF;AGlJE;EACE,cAAA;AHoJJ;;AGhJA;EAAgB,gCAAA;AHoJhB;;AGlJA;EAAmB,+BAAA;AHsJnB;;AGjJA;EACE,wBAAA;EACA,wBAAA;AHoJF;AGlJE;EACE,cAAA;AHoJJ;;AGhJA;EAAgB,gCAAA;AHoJhB;;AGlJA;EAAmB,+BAAA;AHsJnB;;AGlJA;EACE,gBEqd4B;ALhU9B;;AGhJA;EACE,qBAAA;EACA,cAAA;AHmJF;;AG7IA;EACE,gBAAA;AHgJF;;AGxIA;;;EAGE,gBE4b4B;ALjT9B;;AGnIA;EFjBM,mBALI;EEyBR,gBE8a4B;EF7a5B,yBEub4B;ALlT9B;;AG9HA;EACE,mBEoiB4B;EFniB5B,gCAAA;EACA,wCAAA;AHiIF;;AGxHA;;EAEE,kBAAA;EF1CI,iBALI;EEiDR,cAAA;EACA,wBAAA;AH2HF;;AGxHA;EAAM,eAAA;AH4HN;;AG3HA;EAAM,WAAA;AH+HN;;AG1HA;EACE,gEAAA;EACA,0BEkLwC;ALrD1C;AG3HE;EACE,mDAAA;AH6HJ;;AGlHE;EAEE,cAAA;EACA,qBAAA;AHoHJ;;AG7GA;;;;;EAKE,qCE6V4B;EJlbxB,cALI;AD2MV;;AGzGA;EACE,cAAA;EACA,aAAA;EACA,mBAAA;EACA,cAAA;EFjGI,kBALI;EEwGR,iBEg2DkC;EF/1DlC,2BE81DkC;ALlvDpC;AGzGE;EFvGI,kBALI;EE8GN,cAAA;EACA,kBAAA;AH2GJ;;AGvGA;;EF9GM,kBALI;EEsHR,kBAAA;EACA,yBE0V4B;EFzV5B,2BAAA;EACA,qBAAA;AH0GF;AGvGE;;EACE,cAAA;AH0GJ;;AGtGA;EACE,4BAAA;EF7HI,kBALI;EEoIR,gCE+zDkC;EF9zDlC,wCE+zDkC;ALttDpC;AGtGE;EACE,UAAA;EFpIE,cALI;ADkPV;;AG9FA;EACE,gBAAA;AHiGF;;AG3FA;;EAEE,sBAAA;AH8FF;;AGjFA;EACE,6BAAA;EACA,kCAAA;EACA,iBAAA;EACA,yBAAA;AHoFF;;AGjFA;EACE,oBEycsC;EFxctC,uBEwcsC;EJtnBlC,mBALI;EEqLR,gBEqR4B;EFpR5B,wDEocsC;EFnctC,gBAAA;EAEA,eAAA;EACA,0BEoT4B;EFnT5B,mCAAA;EACA,kCAAA;EACA,kCAAA;AHmFF;;AG5EA;EAEE,mBAAA;EACA,gCAAA;AH8EF;;AG3EA;;;;;;EAME,qBAAA;EACA,mBAAA;EACA,eAAA;AH8EF;;AGtEA;EACE,qBAAA;EACA,gBE8O4B;ALrK9B;;AGnEA;EAEE,gBAAA;AHqEF;;AG1DA;EACE,UAAA;EACA,gBAAA;AH6DF;;AGxDA;;;;;EAKE,SAAA;EACA,oBAAA;EFvPI,kBALI;EE8PR,oBAAA;EAEA,eAAA;EACA,uBAAA;EACA,gBAAA;AH0DF;;AGtDA;;EAEE,oBAAA;AHyDF;;AGpDA;EACE,eAAA;AHuDF;;AGpDA;EAGE,iBAAA;AHqDF;AGlDE;EACE,UAAA;AHoDJ;;AG7CA;EACE,wBAAA;AHgDF;;AGxCA;;;;EAIE,0BAAA;AH2CF;AGxCI;;;;EACE,eAAA;AH6CN;;AGtCA;EACE,UAAA;EACA,kBAAA;AHyCF;;AGpCA;EACE,gBAAA;AHuCF;;AG7BA;EACE,YAAA;EACA,UAAA;EACA,SAAA;EACA,SAAA;AHgCF;;AGxBA;EACE,WAAA;EACA,WAAA;EACA,UAAA;EACA,wBEqO4B;EJzjBxB,kBALI;EE2VR,gBE+G4B;EF9G5B,oBAAA;AH2BF;AGzBE;EACE,WAAA;AH2BJ;;AGpBA;;;;;;;EAOE,UAAA;AHuBF;;AGpBA;EACE,YAAA;AHuBF;;AGdA;EACE,6BAAA;EACA,oBAAA;AHiBF;;AGTA;;;;;;;CAAA;AAWA;EACE,wBAAA;AHSF;;AGJA;EACE,UAAA;AHOF;;AGAA;EACE,aAAA;EACA,0BAAA;AHGF;;AGLA;EACE,aAAA;EACA,0BAAA;AHGF;;AGEA;EACE,qBAAA;AHCF;;AGIA;EACE,SAAA;AHDF;;AGQA;EACE,kBAAA;EACA,eAAA;AHLF;;AGaA;EACE,wBAAA;AHVF;;AGkBA;EACE,wBAAA;AHfF", "file": "boosted-reboot.css", "sourcesContent": ["@mixin bsBanner($file) {\n  /*!\n   * Boosted #{$file} v5.3.7 (https://boosted.orange.com/)\n   * Copyright 2014-2025 The Boosted Authors\n   * Copyright 2014-2025 Orange SA\n   * Licensed under MIT (https://github.com/Orange-OpenSource/Orange-Boosted-Bootstrap/blob/main/LICENSE)\n   * This a fork of Bootstrap: Initial license below\n   * Bootstrap #{$file} v5.3.7 (https://getbootstrap.com/)\n   * Copyright 2011-2025 The Bootstrap Authors\n   * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n   */\n}\n", "// Boosted mod\n:root,\n[data-bs-theme] {\n  color: var(--#{$prefix}body-color);\n  background-color: var(--#{$prefix}body-bg);\n}\n\n// Note that some of the following variables in `:root, [data-bs-theme=\"light\"]` could be extracted into `:root` only selector since they are not modified by other color modes!\n// End mod\n\n:root,\n[data-bs-theme=\"light\"] {\n  color-scheme: light; // Boosted mod\n\n  // Note: Custom variable values only support SassScript inside `#{}`.\n\n  // Colors\n  //\n  // Generate palettes for full colors, grays, and theme colors.\n\n  @each $color, $value in $colors {\n    --#{$prefix}#{$color}: #{$value};\n  }\n\n  @each $color, $value in $grays {\n    --#{$prefix}gray-#{$color}: #{$value};\n  }\n\n  @each $color, $value in $theme-colors {\n    --#{$prefix}#{$color}: #{$value};\n  }\n\n  @each $color, $value in $theme-colors-rgb {\n    --#{$prefix}#{$color}-rgb: #{$value};\n  }\n\n  @each $color, $value in $theme-colors-text {\n    --#{$prefix}#{$color}-text-emphasis: #{$value};\n  }\n\n  @each $color, $value in $theme-colors-bg-subtle {\n    --#{$prefix}#{$color}-bg-subtle: #{$value};\n  }\n\n  @each $color, $value in $theme-colors-border-subtle {\n    --#{$prefix}#{$color}-border-subtle: #{$value};\n  }\n\n  --#{$prefix}white-rgb: #{to-rgb($white)};\n  --#{$prefix}black-rgb: #{to-rgb($black)};\n\n  // Boosted mod\n  @each $icon, $svg in $svg-as-custom-props {\n    --#{$prefix}#{$icon}-icon: #{escape-svg($svg)};\n  }\n  // End mod\n\n  // Fonts\n\n  // Note: Use `inspect` for lists so that quoted items keep the quotes.\n  // See https://github.com/sass/sass/issues/2383#issuecomment-336349172\n  --#{$prefix}font-sans-serif: #{inspect($font-family-sans-serif)};\n  --#{$prefix}font-monospace: #{inspect($font-family-monospace)};\n  --#{$prefix}gradient: #{$gradient};\n\n  // Root and body\n  // scss-docs-start root-body-variables\n  @if $font-size-root != null {\n    --#{$prefix}root-font-size: #{$font-size-root};\n  }\n  --#{$prefix}body-font-family: #{inspect($font-family-base)};\n  @include rfs($font-size-base, --#{$prefix}body-font-size);\n  --#{$prefix}body-font-weight: #{$font-weight-base};\n  --#{$prefix}body-line-height: #{$line-height-base};\n  @if $body-text-align != null {\n    --#{$prefix}body-text-align: #{$body-text-align};\n  }\n\n  --#{$prefix}body-color: #{$body-color};\n  --#{$prefix}body-color-rgb: #{to-rgb($body-color)};\n  --#{$prefix}body-bg: #{$body-bg};\n  --#{$prefix}body-bg-rgb: #{to-rgb($body-bg)};\n\n  --#{$prefix}emphasis-color: #{$body-emphasis-color};\n  --#{$prefix}emphasis-color-rgb: #{to-rgb($body-emphasis-color)};\n\n  --#{$prefix}secondary-color: #{$body-secondary-color};\n  --#{$prefix}secondary-color-rgb: #{to-rgb($body-secondary-color)};\n  --#{$prefix}secondary-bg: #{$body-secondary-bg};\n  --#{$prefix}secondary-bg-rgb: #{to-rgb($body-secondary-bg)};\n\n  --#{$prefix}tertiary-color: #{$body-tertiary-color};\n  --#{$prefix}tertiary-color-rgb: #{to-rgb($body-tertiary-color)};\n  --#{$prefix}tertiary-bg: #{$body-tertiary-bg};\n  --#{$prefix}tertiary-bg-rgb: #{to-rgb($body-tertiary-bg)};\n  // scss-docs-end root-body-variables\n\n  --#{$prefix}heading-color: #{$headings-color};\n\n  --#{$prefix}link-color: #{$link-color};\n  --#{$prefix}link-color-rgb: #{to-rgb($link-color)};\n  --#{$prefix}link-decoration: #{$link-decoration};\n\n  --#{$prefix}link-hover-color: #{$link-hover-color};\n  --#{$prefix}link-hover-color-rgb: #{to-rgb($link-hover-color)};\n\n  @if $link-hover-decoration != null {\n    --#{$prefix}link-hover-decoration: #{$link-hover-decoration};\n  }\n\n  --#{$prefix}code-color: #{$code-color};\n  --#{$prefix}highlight-color: #{$mark-color};\n  --#{$prefix}highlight-bg: #{$mark-bg};\n  --#{$prefix}disabled-color: #{$disabled-color}; // Boosted mod\n  --#{$prefix}tertiary-active-bg: #{$tertiary-active-bg}; // Boosted mod\n\n  // scss-docs-start root-border-var\n  --#{$prefix}border-width: #{$border-width};\n  --#{$prefix}border-style: #{$border-style};\n  --#{$prefix}border-color: #{$border-color};\n  --#{$prefix}border-color-subtle: #{$border-color-subtle}; // Boosted mod\n  --#{$prefix}border-color-translucent: #{$border-color-translucent};\n\n  --#{$prefix}border-radius: #{$border-radius};\n  --#{$prefix}border-radius-sm: #{$border-radius-sm};\n  --#{$prefix}border-radius-lg: #{$border-radius-lg};\n  --#{$prefix}border-radius-xl: #{$border-radius-xl};\n  --#{$prefix}border-radius-xxl: #{$border-radius-xxl};\n  --#{$prefix}border-radius-2xl: var(--#{$prefix}border-radius-xxl); // Deprecated in v5.3.0 for consistency\n  --#{$prefix}border-radius-pill: #{$border-radius-pill};\n  // scss-docs-end root-border-var\n\n  --#{$prefix}box-shadow: #{$box-shadow};\n  --#{$prefix}box-shadow-sm: #{$box-shadow-sm};\n  --#{$prefix}box-shadow-lg: #{$box-shadow-lg};\n  --#{$prefix}box-shadow-inset: #{$box-shadow-inset};\n\n  --#{$prefix}focus-visible-inner-color: #{$focus-visible-inner-color}; // Boosted mod\n  --#{$prefix}focus-visible-outer-color: #{$focus-visible-outer-color}; // Boosted mod\n\n  // Focus styles\n  // scss-docs-start root-focus-variables\n  --#{$prefix}focus-ring-width: #{$focus-ring-width};\n  --#{$prefix}focus-ring-opacity: #{$focus-ring-opacity};\n  --#{$prefix}focus-ring-color: #{$focus-ring-color};\n  // scss-docs-end root-focus-variables\n\n  // scss-docs-start root-form-validation-variables\n  --#{$prefix}form-valid-color: #{$form-valid-color};\n  --#{$prefix}form-valid-border-color: #{$form-valid-border-color};\n  --#{$prefix}form-invalid-color: #{$form-invalid-color};\n  --#{$prefix}form-invalid-border-color: #{$form-invalid-border-color};\n  // scss-docs-end root-form-validation-variables\n\n  --#{$prefix}form-check-filter: #{$form-check-filter}; // Boosted mod\n  --#{$prefix}form-check-input-disabled-color: #{$form-check-input-disabled-color}; // Boosted mod\n  --#{$prefix}form-color-disabled-filter: #{$form-color-disabled-filter}; // Boosted mod\n  --#{$prefix}form-select-indicator: #{$form-select-indicator}; // Boosted mod\n  --#{$prefix}form-select-disabled-indicator: #{$form-select-disabled-indicator}; // Boosted mod\n  --#{$prefix}form-switch-square-bg: #{$form-switch-square-bg}; // Boosted mod\n  --#{$prefix}form-switch-unchecked-invalid-border-color: #{$form-switch-unchecked-invalid-border-color}; // Boosted mod\n\n  // Boosted mod\n  // Table-specific styles\n  --#{$prefix}table-active-bg-factor: #{$table-active-bg-factor};\n  --#{$prefix}table-hover-bg-factor: #{$table-hover-bg-factor};\n  --#{$prefix}table-striped-bg-factor: #{$table-striped-bg-factor};\n\n  // Breadcrumb-specific styles\n  --#{$prefix}breadcrumb-divider-filter: #{$breadcrumb-divider-filter};\n  // End mod\n}\n\n@if $enable-dark-mode {\n  @include color-mode(dark, true) {\n    color-scheme: dark;\n\n    // scss-docs-start root-dark-mode-vars\n    --#{$prefix}body-color: #{$body-color-dark};\n    --#{$prefix}body-color-rgb: #{to-rgb($body-color-dark)};\n    --#{$prefix}body-bg: #{$body-bg-dark};\n    --#{$prefix}body-bg-rgb: #{to-rgb($body-bg-dark)};\n\n    --#{$prefix}emphasis-color: #{$body-emphasis-color-dark};\n    --#{$prefix}emphasis-color-rgb: #{to-rgb($body-emphasis-color-dark)};\n\n    --#{$prefix}secondary-color: #{$body-secondary-color-dark};\n    --#{$prefix}secondary-color-rgb: #{to-rgb($body-secondary-color-dark)};\n    --#{$prefix}secondary-bg: #{$body-secondary-bg-dark};\n    --#{$prefix}secondary-bg-rgb: #{to-rgb($body-secondary-bg-dark)};\n\n    --#{$prefix}tertiary-color: #{$body-tertiary-color-dark};\n    --#{$prefix}tertiary-color-rgb: #{to-rgb($body-tertiary-color-dark)};\n    --#{$prefix}tertiary-bg: #{$body-tertiary-bg-dark};\n    --#{$prefix}tertiary-bg-rgb: #{to-rgb($body-tertiary-bg-dark)};\n\n    // Boosted mod\n    @each $color, $value in $theme-colors-dark {\n      --#{$prefix}#{$color}: #{$value};\n    }\n\n    @each $color, $value in $theme-colors-rgb-dark {\n      --#{$prefix}#{$color}-rgb: #{$value};\n    }\n    // End mod\n\n    @each $color, $value in $theme-colors-text-dark {\n      --#{$prefix}#{$color}-text-emphasis: #{$value};\n    }\n\n    @each $color, $value in $theme-colors-bg-subtle-dark {\n      --#{$prefix}#{$color}-bg-subtle: #{$value};\n    }\n\n    @each $color, $value in $theme-colors-border-subtle-dark {\n      --#{$prefix}#{$color}-border-subtle: #{$value};\n    }\n\n    // Boosted mod\n    @each $icon, $svg in $svg-as-custom-props-dark {\n      --#{$prefix}#{$icon}-icon: #{escape-svg($svg)};\n    }\n    // End mod\n\n    --#{$prefix}heading-color: #{$headings-color-dark};\n\n    --#{$prefix}link-color: #{$link-color-dark};\n    --#{$prefix}link-hover-color: #{$link-hover-color-dark};\n    --#{$prefix}link-color-rgb: #{to-rgb($link-color-dark)};\n    --#{$prefix}link-hover-color-rgb: #{to-rgb($link-hover-color-dark)};\n\n    --#{$prefix}code-color: #{$code-color-dark};\n    --#{$prefix}highlight-color: #{$mark-color-dark};\n    --#{$prefix}highlight-bg: #{$mark-bg-dark};\n    --#{$prefix}disabled-color: #{$disabled-color-dark}; // Boosted mod\n    --#{$prefix}tertiary-active-bg: #{$tertiary-active-bg-dark}; // Boosted mod\n\n    --#{$prefix}border-color: #{$border-color-dark};\n    --#{$prefix}border-color-subtle: #{$border-color-subtle-dark}; // Boosted mod\n    --#{$prefix}border-color-translucent: #{$border-color-translucent-dark};\n\n    --#{$prefix}focus-visible-inner-color: #{$focus-visible-inner-color-dark}; // Boosted mod\n    --#{$prefix}focus-visible-outer-color: #{$focus-visible-outer-color-dark}; // Boosted mod\n\n    --#{$prefix}focus-ring-color: #{$focus-ring-color-dark}; // Boosted mod\n\n    --#{$prefix}form-valid-color: #{$form-valid-color-dark};\n    --#{$prefix}form-valid-border-color: #{$form-valid-border-color-dark};\n    --#{$prefix}form-invalid-color: #{$form-invalid-color-dark};\n    --#{$prefix}form-invalid-border-color: #{$form-invalid-border-color-dark};\n    --#{$prefix}form-check-filter: #{$form-check-filter-dark}; // Boosted mod\n    --#{$prefix}form-check-input-disabled-color: #{$form-check-input-disabled-color-dark}; // Boosted mod\n    --#{$prefix}form-select-indicator: #{$form-select-indicator-dark}; // Boosted mod\n    --#{$prefix}form-select-disabled-indicator: #{$form-select-disabled-indicator-dark}; // Boosted mod\n    --#{$prefix}form-color-disabled-filter: #{$form-color-disabled-filter-dark};\n    --#{$prefix}form-switch-square-bg: #{$form-switch-square-bg-dark}; // Boosted mod\n    --#{$prefix}form-switch-unchecked-invalid-border-color: #{$form-switch-unchecked-invalid-border-color-dark}; // Boosted mod\n\n    // Boosted mod\n    // Table-specific styles\n    --#{$prefix}table-active-bg-factor: #{$table-active-bg-factor-dark};\n    --#{$prefix}table-hover-bg-factor: #{$table-hover-bg-factor-dark};\n    --#{$prefix}table-striped-bg-factor: #{$table-striped-bg-factor-dark};\n\n    // Breadcrumb-specific styles\n    --#{$prefix}breadcrumb-divider-filter: #{$breadcrumb-divider-filter-dark};\n    // End mod\n    // scss-docs-end root-dark-mode-vars\n  }\n}\n", "/*!\n * Boosted Reboot v5.3.7 (https://boosted.orange.com/)\n * Copyright 2014-2025 The Boosted Authors\n * Copyright 2014-2025 Orange SA\n * Licensed under MIT (https://github.com/Orange-OpenSource/Orange-Boosted-Bootstrap/blob/main/LICENSE)\n * This a fork of Bootstrap: Initial license below\n * Bootstrap Reboot v5.3.7 (https://getbootstrap.com/)\n * Copyright 2011-2025 The Bootstrap Authors\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n */\n:root,\n[data-bs-theme] {\n  color: var(--bs-body-color);\n  background-color: var(--bs-body-bg);\n}\n\n:root,\n[data-bs-theme=light] {\n  color-scheme: light;\n  --bs-blue: #4170d8;\n  --bs-indigo: #a885d8;\n  --bs-purple: #a885d8;\n  --bs-pink: #ffb4e6;\n  --bs-red: #cd3c14;\n  --bs-orange: #f16e00;\n  --bs-yellow: #fc0;\n  --bs-green: #228722;\n  --bs-teal: #50be87;\n  --bs-cyan: #4bb4e6;\n  --bs-black: #000;\n  --bs-white: #fff;\n  --bs-gray: #999;\n  --bs-gray-dark: #595959;\n  --bs-gray-100: #fafafa;\n  --bs-gray-200: #f6f6f6;\n  --bs-gray-300: #eee;\n  --bs-gray-400: #ddd;\n  --bs-gray-500: #ccc;\n  --bs-gray-600: #999;\n  --bs-gray-700: #666;\n  --bs-gray-800: #595959;\n  --bs-gray-900: #333;\n  --bs-gray-950: #141414;\n  --bs-primary: #f16e00;\n  --bs-secondary: #000;\n  --bs-success: #228722;\n  --bs-info: #4170d8;\n  --bs-warning: #fc0;\n  --bs-danger: #cd3c14;\n  --bs-light: #ccc;\n  --bs-dark: #000;\n  --bs-primary-rgb: 241, 110, 0;\n  --bs-secondary-rgb: 0, 0, 0;\n  --bs-success-rgb: 34, 135, 34;\n  --bs-info-rgb: 65, 112, 216;\n  --bs-warning-rgb: 255, 204, 0;\n  --bs-danger-rgb: 205, 60, 20;\n  --bs-light-rgb: 204, 204, 204;\n  --bs-dark-rgb: 0, 0, 0;\n  --bs-primary-text-emphasis: #f16e00;\n  --bs-secondary-text-emphasis: #000;\n  --bs-success-text-emphasis: #228722;\n  --bs-info-text-emphasis: #4170d8;\n  --bs-warning-text-emphasis: #fc0;\n  --bs-danger-text-emphasis: #cd3c14;\n  --bs-light-text-emphasis: #ccc;\n  --bs-dark-text-emphasis: #000;\n  --bs-primary-bg-subtle: #f16e00;\n  --bs-secondary-bg-subtle: #000;\n  --bs-success-bg-subtle: #228722;\n  --bs-info-bg-subtle: #4170d8;\n  --bs-warning-bg-subtle: #fc0;\n  --bs-danger-bg-subtle: #cd3c14;\n  --bs-light-bg-subtle: #ccc;\n  --bs-dark-bg-subtle: #000;\n  --bs-primary-border-subtle: #f16e00;\n  --bs-secondary-border-subtle: #000;\n  --bs-success-border-subtle: #228722;\n  --bs-info-border-subtle: #4170d8;\n  --bs-warning-border-subtle: #fc0;\n  --bs-danger-border-subtle: #cd3c14;\n  --bs-light-border-subtle: #ccc;\n  --bs-dark-border-subtle: #000;\n  --bs-white-rgb: 255, 255, 255;\n  --bs-black-rgb: 0, 0, 0;\n  --bs-chevron-icon: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 9 14'%3e%3cpath d='M9 2 7 0 0 7l7 7 2-2-5-5 5-5z'/%3e%3c/svg%3e\");\n  --bs-close-icon: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='116 116 767 767' fill='%23000'%3e%3cpath d='M817.493 676.165a49.977 49.977 0 0 1 0 70.664l-70.664 70.664a49.977 49.977 0 0 1-70.664 0L499.5 640.828 322.835 817.493a49.977 49.977 0 0 1-70.664 0l-70.664-70.664a49.977 49.977 0 0 1 0-70.664L358.172 499.5 181.507 322.835a49.977 49.977 0 0 1 0-70.664l70.664-70.664a49.977 49.977 0 0 1 70.664 0L499.5 358.172l176.665-176.665a49.977 49.977 0 0 1 70.664 0l70.664 70.664a49.977 49.977 0 0 1 0 70.664L640.828 499.5Z'/%3e%3c/svg%3e\");\n  --bs-check-icon: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 15 12'%3e%3cpath fill='%23000' d='M13 0 5 8 2 5 0 7l5 5L15 2z'/%3e%3c/svg%3e\");\n  --bs-success-icon: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 125 125'%3e%3cpath fill='%23228722' d='M62.5 0a62.5 62.5 0 1 0 0 125 62.5 62.5 0 0 0 0-125zm28 29.4c3.3 0 6 2.6 6 5.9a5.9 5.9 0 0 1-1.3 3.7L57.7 86a5.8 5.8 0 0 1-9.1 0L29.8 62.5c-.8-1-1.2-2.3-1.2-3.7a5.9 5.9 0 0 1 1.7-4.1l2.3-2.4a5.8 5.8 0 0 1 4.2-1.7 5.8 5.8 0 0 1 3.8 1.4L52 64.7 86.6 31a5.8 5.8 0 0 1 4-1.6z'/%3e%3c/svg%3e\");\n  --bs-error-icon: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 140 125'%3e%3cpath fill='%23cd3c14' d='M70.3 0c-5.8 0-10.8 3.1-13.5 7.8L2.3 101.3l-.2.2A15.6 15.6 0 0 0 15.6 125H125a15.6 15.6 0 0 0 13.5-23.5L83.8 7.8A15.6 15.6 0 0 0 70.3 0zm19.2 50a6.4 6.4 0 0 1 4.4 1.9 6.4 6.4 0 0 1 0 9L79.4 75.6l15 15a6.4 6.4 0 0 1 0 9.2 6.4 6.4 0 0 1-4.5 1.9 6.4 6.4 0 0 1-4.6-2l-15-15-15 15a6.4 6.4 0 0 1-4.6 2 6.4 6.4 0 0 1-4.6-2 6.4 6.4 0 0 1 0-9l15-15L46.8 61a6.4 6.4 0 1 1 9-9.1l14.6 14.5L84.8 52a6.4 6.4 0 0 1 4.7-1.9z'/%3e%3c/svg%3e\");\n  --bs-font-sans-serif: HelvNeueOrange/*rtl:insert:Arabic*/, \"Helvetica Neue\", Helvetica, \"Noto Sans\", \"Liberation Sans\", Arial, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n  --bs-font-monospace: SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace;\n  --bs-gradient: linear-gradient(180deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));\n  --bs-body-font-family: var(--bs-font-sans-serif);\n  --bs-body-font-size: 1rem;\n  --bs-body-font-weight: 400;\n  --bs-body-line-height: 1.125;\n  --bs-body-color: #000;\n  --bs-body-color-rgb: 0, 0, 0;\n  --bs-body-bg: #fff;\n  --bs-body-bg-rgb: 255, 255, 255;\n  --bs-emphasis-color: #000;\n  --bs-emphasis-color-rgb: 0, 0, 0;\n  --bs-secondary-color: #666;\n  --bs-secondary-color-rgb: 102, 102, 102;\n  --bs-secondary-bg: #eee;\n  --bs-secondary-bg-rgb: 238, 238, 238;\n  --bs-tertiary-color: #ccc;\n  --bs-tertiary-color-rgb: 204, 204, 204;\n  --bs-tertiary-bg: #fafafa;\n  --bs-tertiary-bg-rgb: 250, 250, 250;\n  --bs-heading-color: inherit;\n  --bs-link-color: #000;\n  --bs-link-color-rgb: 0, 0, 0;\n  --bs-link-decoration: underline;\n  --bs-link-hover-color: #f16e00;\n  --bs-link-hover-color-rgb: 241, 110, 0;\n  --bs-code-color: #666;\n  --bs-highlight-color: #fff;\n  --bs-highlight-bg: #000;\n  --bs-disabled-color: var(--bs-tertiary-color);\n  --bs-tertiary-active-bg: #ddd;\n  --bs-border-width: 0.125rem;\n  --bs-border-style: solid;\n  --bs-border-color: #000;\n  --bs-border-color-subtle: #ccc;\n  --bs-border-color-translucent: rgba(0, 0, 0, 0.175);\n  --bs-border-radius: 0.375rem;\n  --bs-border-radius-sm: 0.25rem;\n  --bs-border-radius-lg: 0.5rem;\n  --bs-border-radius-xl: 1rem;\n  --bs-border-radius-xxl: 2rem;\n  --bs-border-radius-2xl: var(--bs-border-radius-xxl);\n  --bs-border-radius-pill: 50rem;\n  --bs-box-shadow: ;\n  --bs-box-shadow-sm: ;\n  --bs-box-shadow-lg: ;\n  --bs-box-shadow-inset: ;\n  --bs-focus-visible-inner-color: #fff;\n  --bs-focus-visible-outer-color: #000;\n  --bs-focus-ring-width: 0.25rem;\n  --bs-focus-ring-opacity: 0.25;\n  --bs-focus-ring-color: rgba(241, 110, 0, 0.25);\n  --bs-form-valid-color: var(--bs-success-text-emphasis);\n  --bs-form-valid-border-color: var(--bs-success);\n  --bs-form-invalid-color: var(--bs-danger-text-emphasis);\n  --bs-form-invalid-border-color: var(--bs-danger);\n  --bs-form-check-filter: invert(1);\n  --bs-form-check-input-disabled-color: #333;\n  --bs-form-color-disabled-filter: brightness(0) invert(1) brightness(0.8);\n  --bs-form-select-indicator: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 14 7'%3e%3cpath d='M7 7 0 0h14L7 7z'/%3e%3c/svg%3e\");\n  --bs-form-select-disabled-indicator: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 14 7'%3e%3cpath fill='%23666' d='M7 7 0 0h14L7 7z'/%3e%3c/svg%3e\");\n  --bs-form-switch-square-bg: #000;\n  --bs-form-switch-unchecked-invalid-border-color: #31c3eb;\n  --bs-table-active-bg-factor: 0.135;\n  --bs-table-hover-bg-factor: 0.065;\n  --bs-table-striped-bg-factor: 0.035;\n  --bs-breadcrumb-divider-filter: none;\n}\n\n[data-bs-theme=dark] {\n  color-scheme: dark;\n  --bs-body-color: #fff;\n  --bs-body-color-rgb: 255, 255, 255;\n  --bs-body-bg: #141414;\n  --bs-body-bg-rgb: 20, 20, 20;\n  --bs-emphasis-color: #fff;\n  --bs-emphasis-color-rgb: 255, 255, 255;\n  --bs-secondary-color: #999;\n  --bs-secondary-color-rgb: 153, 153, 153;\n  --bs-secondary-bg: #333;\n  --bs-secondary-bg-rgb: 51, 51, 51;\n  --bs-tertiary-color: #666;\n  --bs-tertiary-color-rgb: 102, 102, 102;\n  --bs-tertiary-bg: #000;\n  --bs-tertiary-bg-rgb: 0, 0, 0;\n  --bs-primary: #ff7900;\n  --bs-secondary: #fff;\n  --bs-success: #6c6;\n  --bs-info: #69f;\n  --bs-warning: #fc0;\n  --bs-danger: #ff4d4d;\n  --bs-light: #ccc;\n  --bs-dark: #000;\n  --bs-primary-rgb: 255, 121, 0;\n  --bs-secondary-rgb: 255, 255, 255;\n  --bs-success-rgb: 102, 204, 102;\n  --bs-info-rgb: 102, 153, 255;\n  --bs-warning-rgb: 255, 204, 0;\n  --bs-danger-rgb: 255, 77, 77;\n  --bs-light-rgb: 204, 204, 204;\n  --bs-dark-rgb: 0, 0, 0;\n  --bs-primary-text-emphasis: #ff7900;\n  --bs-secondary-text-emphasis: #fff;\n  --bs-success-text-emphasis: #6c6;\n  --bs-info-text-emphasis: #69f;\n  --bs-warning-text-emphasis: #fc0;\n  --bs-danger-text-emphasis: #ff4d4d;\n  --bs-light-text-emphasis: #ccc;\n  --bs-dark-text-emphasis: #000;\n  --bs-primary-bg-subtle: #ff7900;\n  --bs-secondary-bg-subtle: #fff;\n  --bs-success-bg-subtle: #6c6;\n  --bs-info-bg-subtle: #69f;\n  --bs-warning-bg-subtle: #fc0;\n  --bs-danger-bg-subtle: #ff4d4d;\n  --bs-light-bg-subtle: #ccc;\n  --bs-dark-bg-subtle: #000;\n  --bs-primary-border-subtle: #ff7900;\n  --bs-secondary-border-subtle: #fff;\n  --bs-success-border-subtle: #6c6;\n  --bs-info-border-subtle: #69f;\n  --bs-warning-border-subtle: #fc0;\n  --bs-danger-border-subtle: #ff4d4d;\n  --bs-light-border-subtle: #ccc;\n  --bs-dark-border-subtle: #000;\n  --bs-success-icon: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 125 125'%3e%3cpath fill='%236c6' d='M62.5 0a62.5 62.5 0 1 0 0 125 62.5 62.5 0 0 0 0-125zm28 29.4c3.3 0 6 2.6 6 5.9a5.9 5.9 0 0 1-1.3 3.7L57.7 86a5.8 5.8 0 0 1-9.1 0L29.8 62.5c-.8-1-1.2-2.3-1.2-3.7a5.9 5.9 0 0 1 1.7-4.1l2.3-2.4a5.8 5.8 0 0 1 4.2-1.7 5.8 5.8 0 0 1 3.8 1.4L52 64.7 86.6 31a5.8 5.8 0 0 1 4-1.6z'/%3e%3c/svg%3e\");\n  --bs-error-icon: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 140 125'%3e%3cpath fill='%23ff4d4d' d='M70.3 0c-5.8 0-10.8 3.1-13.5 7.8L2.3 101.3l-.2.2A15.6 15.6 0 0 0 15.6 125H125a15.6 15.6 0 0 0 13.5-23.5L83.8 7.8A15.6 15.6 0 0 0 70.3 0zm19.2 50a6.4 6.4 0 0 1 4.4 1.9 6.4 6.4 0 0 1 0 9L79.4 75.6l15 15a6.4 6.4 0 0 1 0 9.2 6.4 6.4 0 0 1-4.5 1.9 6.4 6.4 0 0 1-4.6-2l-15-15-15 15a6.4 6.4 0 0 1-4.6 2 6.4 6.4 0 0 1-4.6-2 6.4 6.4 0 0 1 0-9l15-15L46.8 61a6.4 6.4 0 1 1 9-9.1l14.6 14.5L84.8 52a6.4 6.4 0 0 1 4.7-1.9z'/%3e%3c/svg%3e\");\n  --bs-heading-color: inherit;\n  --bs-link-color: #fff;\n  --bs-link-hover-color: #ff7900;\n  --bs-link-color-rgb: 255, 255, 255;\n  --bs-link-hover-color-rgb: 255, 121, 0;\n  --bs-code-color: #999;\n  --bs-highlight-color: #000;\n  --bs-highlight-bg: #fff;\n  --bs-disabled-color: var(--bs-tertiary-color);\n  --bs-tertiary-active-bg: #666;\n  --bs-border-color: #fff;\n  --bs-border-color-subtle: #666;\n  --bs-border-color-translucent: rgba(255, 255, 255, 0.15);\n  --bs-focus-visible-inner-color: #000;\n  --bs-focus-visible-outer-color: #fff;\n  --bs-focus-ring-color: rgba(255, 121, 0, 0.25);\n  --bs-form-valid-color: var(--bs-success-text-emphasis);\n  --bs-form-valid-border-color: var(--bs-success);\n  --bs-form-invalid-color: var(--bs-danger-text-emphasis);\n  --bs-form-invalid-border-color: var(--bs-danger);\n  --bs-form-check-filter: none;\n  --bs-form-check-input-disabled-color: #666;\n  --bs-form-select-indicator: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 14 7'%3e%3cpath fill='%23fff' d='M7 7 0 0h14L7 7z'/%3e%3c/svg%3e\");\n  --bs-form-select-disabled-indicator: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 14 7'%3e%3cpath fill='%23999' d='M7 7 0 0h14L7 7z'/%3e%3c/svg%3e\");\n  --bs-form-color-disabled-filter: brightness(0) invert(1) brightness(0.4);\n  --bs-form-switch-square-bg: #141414;\n  --bs-form-switch-unchecked-invalid-border-color: var(--bs-danger);\n  --bs-table-active-bg-factor: 0.35;\n  --bs-table-hover-bg-factor: 0.135;\n  --bs-table-striped-bg-factor: 1;\n  --bs-breadcrumb-divider-filter: invert(1);\n}\n\n*,\n*::before,\n*::after {\n  box-sizing: border-box;\n}\n\n:root {\n  scroll-padding-top: 3.75rem;\n}\n@media (min-width: 1024px) {\n  :root {\n    scroll-padding-top: 7.5rem;\n  }\n}\n@media (prefers-reduced-motion: no-preference) {\n  :root {\n    scroll-behavior: smooth;\n  }\n}\n\nbody {\n  position: relative;\n  margin: 0;\n  font-family: var(--bs-body-font-family);\n  font-synthesis: none;\n  font-size: var(--bs-body-font-size);\n  font-weight: var(--bs-body-font-weight);\n  line-height: var(--bs-body-line-height);\n  text-align: var(--bs-body-text-align);\n  /* rtl:remove */\n  letter-spacing: -0.00625rem;\n  background-color: var(--bs-body-bg);\n  -webkit-text-size-adjust: 100%;\n  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);\n  -webkit-font-smoothing: subpixel-antialiased;\n  text-rendering: optimizespeed;\n}\n\n:focus {\n  isolation: isolate;\n  outline: 3px solid var(--bs-focus-visible-outer-color);\n  outline-offset: 2px;\n  box-shadow: 0 0 0 2px var(--bs-focus-visible-inner-color);\n}\n\n.js-focus-visible :focus:not([data-focus-visible-added]):not(.focus-ring):not(.form-select:invalid):not(.form-control[type=file]:invalid),\n.js-focus-visible .focus:not([data-focus-visible-added]):not(.focus-ring):not(.form-select:invalid):not(.form-control[type=file]:invalid) {\n  outline: 0 !important;\n  box-shadow: none;\n}\n\n:focus:not(:focus-visible):not(.focus-ring):not(.form-select:invalid):not(.form-control[type=file]:invalid) {\n  outline: 0 !important;\n  box-shadow: none;\n}\n\nhr {\n  margin: 1.25rem 0;\n  color: inherit;\n  border: 0;\n  border-top: var(--bs-border-width) solid;\n}\n\nh4,\nh5,\nh6, h2,\nh3, h1 {\n  margin-top: 0;\n  margin-bottom: 1.25rem;\n  font-size: 1rem;\n  font-weight: 700;\n  line-height: 1.125;\n  color: var(--bs-heading-color);\n  /* rtl:remove */\n  letter-spacing: -0.00625rem;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  text-rendering: optimizelegibility;\n}\n\nh1 {\n  font-size: 1.25rem;\n  line-height: 1.1;\n  /* rtl:remove */\n  letter-spacing: -0.025rem;\n}\n\nh2,\nh3 {\n  font-size: 1.125rem;\n  line-height: 1.1111111111;\n  /* rtl:remove */\n  letter-spacing: -0.0125rem;\n}\n\np {\n  margin-top: 0;\n  margin-bottom: 1rem;\n}\n\nabbr[title] {\n  text-decoration: underline dotted;\n  cursor: help;\n  text-decoration-skip-ink: none;\n}\n\naddress {\n  margin-bottom: 1rem;\n  font-style: normal;\n  line-height: inherit;\n}\n\nol,\nul {\n  padding-left: 2rem;\n}\n\nol,\nul,\ndl {\n  margin-top: 0;\n  margin-bottom: 1rem;\n}\n\nol ol,\nul ul,\nol ul,\nul ol {\n  margin-bottom: 0;\n}\n\nul {\n  list-style-type: square;\n}\n\nli::marker {\n  color: var(--bs-primary);\n  vertical-align: middle;\n}\nol li::marker {\n  color: inherit;\n}\n\nli li::marker {\n  color: var(--bs-secondary-color);\n}\n\nli li li::marker {\n  color: var(--bs-tertiary-color);\n}\n\nli::before {\n  color: var(--bs-primary);\n  vertical-align: text-top;\n}\nol li::before {\n  color: inherit;\n}\n\nli li::before {\n  color: var(--bs-secondary-color);\n}\n\nli li li::before {\n  color: var(--bs-tertiary-color);\n}\n\ndt {\n  font-weight: 700;\n}\n\ndd {\n  margin-bottom: 0.5rem;\n  margin-left: 0;\n}\n\nblockquote {\n  margin: 0 0 1rem;\n}\n\nb,\nem,\nstrong {\n  font-weight: 700;\n}\n\nsmall {\n  font-size: 0.875rem;\n  font-weight: 400;\n  line-height: 1.1428571429;\n}\n\nmark {\n  padding: 0 0.1875em;\n  color: var(--bs-highlight-color);\n  background-color: var(--bs-highlight-bg);\n}\n\nsub,\nsup {\n  position: relative;\n  font-size: 0.75em;\n  line-height: 0;\n  vertical-align: baseline;\n}\n\nsub {\n  bottom: -0.25em;\n}\n\nsup {\n  top: -0.5em;\n}\n\na {\n  color: rgba(var(--bs-link-color-rgb), var(--bs-link-opacity, 1));\n  text-decoration: underline;\n}\na:hover {\n  --bs-link-color-rgb: var(--bs-link-hover-color-rgb);\n}\n\na:not([href]):not([class]), a:not([href]):not([class]):hover {\n  color: inherit;\n  text-decoration: none;\n}\n\nvar,\npre,\ncode,\nkbd,\nsamp {\n  font-family: var(--bs-font-monospace);\n  font-size: 1em;\n}\n\npre {\n  display: block;\n  margin-top: 0;\n  margin-bottom: 1rem;\n  overflow: auto;\n  font-size: 0.875em;\n  line-height: 1.25;\n  color: var(--bs-code-color);\n}\npre code {\n  font-size: inherit;\n  color: inherit;\n  word-break: normal;\n}\n\nvar,\ncode {\n  font-size: 0.875em;\n  font-style: normal;\n  line-height: 1.1428571429;\n  color: var(--bs-code-color);\n  word-wrap: break-word;\n}\na > var,\na > code {\n  color: inherit;\n}\n\nkbd {\n  padding: 0.0625rem 0.0625rem;\n  font-size: 0.875em;\n  color: var(--bs-kbd-color, #000);\n  background-color: var(--bs-kbd-bg, #eee);\n}\nkbd kbd {\n  padding: 0;\n  font-size: 1em;\n}\n\nfigure {\n  margin: 0 0 1rem;\n}\n\nimg,\nsvg {\n  vertical-align: middle;\n}\n\ntable {\n  font-feature-settings: \"tnum\";\n  font-variant-numeric: tabular-nums;\n  caption-side: top;\n  border-collapse: collapse;\n}\n\ncaption {\n  padding-top: 0.75rem;\n  padding-bottom: 0.75rem;\n  font-size: 2.125rem;\n  font-weight: 700;\n  color: var(--bs-caption-color, var(--bs-emphasis-color));\n  text-align: left;\n  /* rtl:remove */\n  letter-spacing: -0.0625rem;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  text-rendering: optimizelegibility;\n}\n\nth {\n  text-align: inherit;\n  text-align: -webkit-match-parent;\n}\n\nthead,\ntbody,\ntfoot,\ntr,\ntd,\nth {\n  border-color: inherit;\n  border-style: solid;\n  border-width: 0;\n}\n\nlabel {\n  display: inline-block;\n  font-weight: 700;\n}\n\nbutton {\n  border-radius: 0;\n}\n\nbutton:focus:not(:focus-visible):not(.focus-ring) {\n  outline: 0;\n  box-shadow: none;\n}\n\ninput,\nbutton,\nselect,\noptgroup,\ntextarea {\n  margin: 0;\n  font-family: inherit;\n  font-size: inherit;\n  line-height: inherit;\n  /* rtl:remove */\n  letter-spacing: inherit;\n  box-shadow: none;\n}\n\nbutton,\nselect {\n  text-transform: none;\n}\n\n[role=button] {\n  cursor: pointer;\n}\n\nselect {\n  word-wrap: normal;\n}\nselect:disabled {\n  opacity: 1;\n}\n\n[list]:not([type=date]):not([type=datetime-local]):not([type=month]):not([type=week]):not([type=time])::-webkit-calendar-picker-indicator {\n  display: none !important;\n}\n\nbutton,\n[type=button],\n[type=reset],\n[type=submit] {\n  -webkit-appearance: button;\n}\nbutton:not(:disabled),\n[type=button]:not(:disabled),\n[type=reset]:not(:disabled),\n[type=submit]:not(:disabled) {\n  cursor: pointer;\n}\n\n::-moz-focus-inner {\n  padding: 0;\n  border-style: none;\n}\n\ntextarea {\n  resize: vertical;\n}\n\nfieldset {\n  min-width: 0;\n  padding: 0;\n  margin: 0;\n  border: 0;\n}\n\nlegend {\n  float: left;\n  width: 100%;\n  padding: 0;\n  margin-bottom: 0.3125rem;\n  font-size: 1.25rem;\n  font-weight: 700;\n  line-height: inherit;\n}\nlegend + * {\n  clear: left;\n}\n\n::-webkit-datetime-edit-fields-wrapper,\n::-webkit-datetime-edit-text,\n::-webkit-datetime-edit-minute,\n::-webkit-datetime-edit-hour-field,\n::-webkit-datetime-edit-day-field,\n::-webkit-datetime-edit-month-field,\n::-webkit-datetime-edit-year-field {\n  padding: 0;\n}\n\n::-webkit-inner-spin-button {\n  height: auto;\n}\n\n[type=search] {\n  -webkit-appearance: textfield;\n  outline-offset: -2px;\n}\n\n/* rtl:raw:\n[type=\"tel\"],\n[type=\"url\"],\n[type=\"email\"],\n[type=\"number\"] {\n  direction: ltr;\n}\n*/\n::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n\n::-webkit-color-swatch-wrapper {\n  padding: 0;\n}\n\n::file-selector-button {\n  font: inherit;\n  -webkit-appearance: button;\n}\n\noutput {\n  display: inline-block;\n}\n\niframe {\n  border: 0;\n}\n\nsummary {\n  display: list-item;\n  cursor: pointer;\n}\n\nprogress {\n  vertical-align: baseline;\n}\n\n[hidden] {\n  display: none !important;\n}\n\n/*# sourceMappingURL=boosted-reboot.css.map */\n", "// stylelint-disable scss/dimension-no-non-numeric-values\n\n// SCSS RFS mixin\n//\n// Automated responsive values for font sizes, paddings, margins and much more\n//\n// Licensed under MIT (https://github.com/twbs/rfs/blob/main/LICENSE)\n\n// Configuration\n\n// Base value\n$rfs-base-value: 1.25rem !default;\n$rfs-unit: rem !default;\n\n@if $rfs-unit != rem and $rfs-unit != px {\n  @error \"`#{$rfs-unit}` is not a valid unit for $rfs-unit. Use `px` or `rem`.\";\n}\n\n// Breakpoint at where values start decreasing if screen width is smaller\n$rfs-breakpoint: 1200px !default;\n$rfs-breakpoint-unit: px !default;\n\n@if $rfs-breakpoint-unit != px and $rfs-breakpoint-unit != em and $rfs-breakpoint-unit != rem {\n  @error \"`#{$rfs-breakpoint-unit}` is not a valid unit for $rfs-breakpoint-unit. Use `px`, `em` or `rem`.\";\n}\n\n// Resize values based on screen height and width\n$rfs-two-dimensional: false !default;\n\n// Factor of decrease\n$rfs-factor: 10 !default;\n\n@if type-of($rfs-factor) != number or $rfs-factor <= 1 {\n  @error \"`#{$rfs-factor}` is not a valid  $rfs-factor, it must be greater than 1.\";\n}\n\n// Mode. Possibilities: \"min-media-query\", \"max-media-query\"\n$rfs-mode: min-media-query !default;\n\n// Generate enable or disable classes. Possibilities: false, \"enable\" or \"disable\"\n$rfs-class: false !default;\n\n// 1 rem = $rfs-rem-value px\n$rfs-rem-value: 16 !default;\n\n// Safari iframe resize bug: https://github.com/twbs/rfs/issues/14\n$rfs-safari-iframe-resize-bug-fix: false !default;\n\n// Disable RFS by setting $enable-rfs to false\n$enable-rfs: true !default;\n\n// Cache $rfs-base-value unit\n$rfs-base-value-unit: unit($rfs-base-value);\n\n@function divide($dividend, $divisor, $precision: 10) {\n  $sign: if($dividend > 0 and $divisor > 0 or $dividend < 0 and $divisor < 0, 1, -1);\n  $dividend: abs($dividend);\n  $divisor: abs($divisor);\n  @if $dividend == 0 {\n    @return 0;\n  }\n  @if $divisor == 0 {\n    @error \"Cannot divide by 0\";\n  }\n  $remainder: $dividend;\n  $result: 0;\n  $factor: 10;\n  @while ($remainder > 0 and $precision >= 0) {\n    $quotient: 0;\n    @while ($remainder >= $divisor) {\n      $remainder: $remainder - $divisor;\n      $quotient: $quotient + 1;\n    }\n    $result: $result * 10 + $quotient;\n    $factor: $factor * .1;\n    $remainder: $remainder * 10;\n    $precision: $precision - 1;\n    @if ($precision < 0 and $remainder >= $divisor * 5) {\n      $result: $result + 1;\n    }\n  }\n  $result: $result * $factor * $sign;\n  $dividend-unit: unit($dividend);\n  $divisor-unit: unit($divisor);\n  $unit-map: (\n    \"px\": 1px,\n    \"rem\": 1rem,\n    \"em\": 1em,\n    \"%\": 1%\n  );\n  @if ($dividend-unit != $divisor-unit and map-has-key($unit-map, $dividend-unit)) {\n    $result: $result * map-get($unit-map, $dividend-unit);\n  }\n  @return $result;\n}\n\n// Remove px-unit from $rfs-base-value for calculations\n@if $rfs-base-value-unit == px {\n  $rfs-base-value: divide($rfs-base-value, $rfs-base-value * 0 + 1);\n}\n@else if $rfs-base-value-unit == rem {\n  $rfs-base-value: divide($rfs-base-value, divide($rfs-base-value * 0 + 1, $rfs-rem-value));\n}\n\n// Cache $rfs-breakpoint unit to prevent multiple calls\n$rfs-breakpoint-unit-cache: unit($rfs-breakpoint);\n\n// Remove unit from $rfs-breakpoint for calculations\n@if $rfs-breakpoint-unit-cache == px {\n  $rfs-breakpoint: divide($rfs-breakpoint, $rfs-breakpoint * 0 + 1);\n}\n@else if $rfs-breakpoint-unit-cache == rem or $rfs-breakpoint-unit-cache == \"em\" {\n  $rfs-breakpoint: divide($rfs-breakpoint, divide($rfs-breakpoint * 0 + 1, $rfs-rem-value));\n}\n\n// Calculate the media query value\n$rfs-mq-value: if($rfs-breakpoint-unit == px, #{$rfs-breakpoint}px, #{divide($rfs-breakpoint, $rfs-rem-value)}#{$rfs-breakpoint-unit});\n$rfs-mq-property-width: if($rfs-mode == max-media-query, max-width, min-width);\n$rfs-mq-property-height: if($rfs-mode == max-media-query, max-height, min-height);\n\n// Internal mixin used to determine which media query needs to be used\n@mixin _rfs-media-query {\n  @if $rfs-two-dimensional {\n    @if $rfs-mode == max-media-query {\n      @media (#{$rfs-mq-property-width}: #{$rfs-mq-value}), (#{$rfs-mq-property-height}: #{$rfs-mq-value}) {\n        @content;\n      }\n    }\n    @else {\n      @media (#{$rfs-mq-property-width}: #{$rfs-mq-value}) and (#{$rfs-mq-property-height}: #{$rfs-mq-value}) {\n        @content;\n      }\n    }\n  }\n  @else {\n    @media (#{$rfs-mq-property-width}: #{$rfs-mq-value}) {\n      @content;\n    }\n  }\n}\n\n// Internal mixin that adds disable classes to the selector if needed.\n@mixin _rfs-rule {\n  @if $rfs-class == disable and $rfs-mode == max-media-query {\n    // Adding an extra class increases specificity, which prevents the media query to override the property\n    &,\n    .disable-rfs &,\n    &.disable-rfs {\n      @content;\n    }\n  }\n  @else if $rfs-class == enable and $rfs-mode == min-media-query {\n    .enable-rfs &,\n    &.enable-rfs {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Internal mixin that adds enable classes to the selector if needed.\n@mixin _rfs-media-query-rule {\n\n  @if $rfs-class == enable {\n    @if $rfs-mode == min-media-query {\n      @content;\n    }\n\n    @include _rfs-media-query () {\n      .enable-rfs &,\n      &.enable-rfs {\n        @content;\n      }\n    }\n  }\n  @else {\n    @if $rfs-class == disable and $rfs-mode == min-media-query {\n      .disable-rfs &,\n      &.disable-rfs {\n        @content;\n      }\n    }\n    @include _rfs-media-query () {\n      @content;\n    }\n  }\n}\n\n// Helper function to get the formatted non-responsive value\n@function rfs-value($values) {\n  // Convert to list\n  $values: if(type-of($values) != list, ($values,), $values);\n\n  $val: \"\";\n\n  // Loop over each value and calculate value\n  @each $value in $values {\n    @if $value == 0 {\n      $val: $val + \" 0\";\n    }\n    @else {\n      // Cache $value unit\n      $unit: if(type-of($value) == \"number\", unit($value), false);\n\n      @if $unit == px {\n        // Convert to rem if needed\n        $val: $val + \" \" + if($rfs-unit == rem, #{divide($value, $value * 0 + $rfs-rem-value)}rem, $value);\n      }\n      @else if $unit == rem {\n        // Convert to px if needed\n        $val: $val + \" \" + if($rfs-unit == px, #{divide($value, $value * 0 + 1) * $rfs-rem-value}px, $value);\n      } @else {\n        // If $value isn't a number (like inherit) or $value has a unit (not px or rem, like 1.5em) or $ is 0, just print the value\n        $val: $val + \" \" + $value;\n      }\n    }\n  }\n\n  // Remove first space\n  @return unquote(str-slice($val, 2));\n}\n\n// Helper function to get the responsive value calculated by RFS\n@function rfs-fluid-value($values) {\n  // Convert to list\n  $values: if(type-of($values) != list, ($values,), $values);\n\n  $val: \"\";\n\n  // Loop over each value and calculate value\n  @each $value in $values {\n    @if $value == 0 {\n      $val: $val + \" 0\";\n    } @else {\n      // Cache $value unit\n      $unit: if(type-of($value) == \"number\", unit($value), false);\n\n      // If $value isn't a number (like inherit) or $value has a unit (not px or rem, like 1.5em) or $ is 0, just print the value\n      @if not $unit or $unit != px and $unit != rem {\n        $val: $val + \" \" + $value;\n      } @else {\n        // Remove unit from $value for calculations\n        $value: divide($value, $value * 0 + if($unit == px, 1, divide(1, $rfs-rem-value)));\n\n        // Only add the media query if the value is greater than the minimum value\n        @if abs($value) <= $rfs-base-value or not $enable-rfs {\n          $val: $val + \" \" + if($rfs-unit == rem, #{divide($value, $rfs-rem-value)}rem, #{$value}px);\n        }\n        @else {\n          // Calculate the minimum value\n          $value-min: $rfs-base-value + divide(abs($value) - $rfs-base-value, $rfs-factor);\n\n          // Calculate difference between $value and the minimum value\n          $value-diff: abs($value) - $value-min;\n\n          // Base value formatting\n          $min-width: if($rfs-unit == rem, #{divide($value-min, $rfs-rem-value)}rem, #{$value-min}px);\n\n          // Use negative value if needed\n          $min-width: if($value < 0, -$min-width, $min-width);\n\n          // Use `vmin` if two-dimensional is enabled\n          $variable-unit: if($rfs-two-dimensional, vmin, vw);\n\n          // Calculate the variable width between 0 and $rfs-breakpoint\n          $variable-width: #{divide($value-diff * 100, $rfs-breakpoint)}#{$variable-unit};\n\n          // Return the calculated value\n          $val: $val + \" calc(\" + $min-width + if($value < 0, \" - \", \" + \") + $variable-width + \")\";\n        }\n      }\n    }\n  }\n\n  // Remove first space\n  @return unquote(str-slice($val, 2));\n}\n\n// RFS mixin\n@mixin rfs($values, $property: font-size) {\n  @if $values != null {\n    $val: rfs-value($values);\n    $fluid-val: rfs-fluid-value($values);\n\n    // Do not print the media query if responsive & non-responsive values are the same\n    @if $val == $fluid-val {\n      #{$property}: $val;\n    }\n    @else {\n      @include _rfs-rule () {\n        #{$property}: if($rfs-mode == max-media-query, $val, $fluid-val);\n\n        // Include safari iframe resize fix if needed\n        min-width: if($rfs-safari-iframe-resize-bug-fix, (0 * 1vw), null);\n      }\n\n      @include _rfs-media-query-rule () {\n        #{$property}: if($rfs-mode == max-media-query, $fluid-val, $val);\n      }\n    }\n  }\n}\n\n// Shorthand helper mixins\n@mixin font-size($value) {\n  @include rfs($value);\n}\n\n@mixin padding($value) {\n  @include rfs($value, padding);\n}\n\n@mixin padding-top($value) {\n  @include rfs($value, padding-top);\n}\n\n@mixin padding-right($value) {\n  @include rfs($value, padding-right);\n}\n\n@mixin padding-bottom($value) {\n  @include rfs($value, padding-bottom);\n}\n\n@mixin padding-left($value) {\n  @include rfs($value, padding-left);\n}\n\n@mixin margin($value) {\n  @include rfs($value, margin);\n}\n\n@mixin margin-top($value) {\n  @include rfs($value, margin-top);\n}\n\n@mixin margin-right($value) {\n  @include rfs($value, margin-right);\n}\n\n@mixin margin-bottom($value) {\n  @include rfs($value, margin-bottom);\n}\n\n@mixin margin-left($value) {\n  @include rfs($value, margin-left);\n}\n", "// scss-docs-start color-mode-mixin\n@mixin color-mode($mode: light, $root: false) {\n  @if $color-mode-type == \"media-query\" {\n    @if $root == true {\n      @media (prefers-color-scheme: $mode) {\n        :root {\n          @content;\n        }\n      }\n    } @else {\n      @media (prefers-color-scheme: $mode) {\n        @content;\n      }\n    }\n  } @else {\n    [data-bs-theme=\"#{$mode}\"] {\n      @content;\n    }\n  }\n}\n// scss-docs-end color-mode-mixin\n", "// stylelint-disable declaration-no-important, selector-no-qualifying-type, property-no-vendor-prefix\n\n\n// Reboot\n//\n// Normalization of HTML elements, manually forked from Normalize.css to remove\n// styles targeting irrelevant browsers while applying new styles.\n//\n// Normalize is licensed MIT. https://github.com/necolas/normalize.css\n\n\n// Document\n//\n// Change from `box-sizing: content-box` so that `width` is not affected by `padding` or `border`.\n\n*,\n*::before,\n*::after {\n  box-sizing: border-box;\n}\n\n\n// Root\n//\n// Ability to the value of the root font sizes, affecting the value of `rem`.\n// null by default, thus nothing is generated.\n\n:root {\n  // Boosted mod: Improve focus visibility when fixed/sticky header is used\n  // See https://caniuse.com/?search=scroll-padding\n  // scss-docs-start scroll-offset\n  @if $enable-fixed-header {\n    scroll-padding-top: $scroll-offset-top * .5;\n\n    @include media-breakpoint-up(lg) {\n      scroll-padding-top: $scroll-offset-top;\n    }\n  }\n  // scss-docs-end scroll-offset\n  // End mod\n\n  @if $font-size-root != null {\n    @include font-size(var(--#{$prefix}root-font-size));\n  }\n\n  @if $enable-smooth-scroll {\n    @media (prefers-reduced-motion: no-preference) {\n      scroll-behavior: smooth;\n    }\n  }\n}\n\n\n// Body\n//\n// 1. Remove the margin in all browsers.\n// 2. As a best practice, apply a default `background-color`.\n// 3. Prevent adjustments of font size after orientation changes in iOS.\n// 4. Change the default tap highlight to be completely transparent in iOS.\n// 5. Prevent faux-bold/italic\n//    See https://developer.mozilla.org/fr/docs/Web/CSS/font-synthesis\n\n// scss-docs-start reboot-body-rules\nbody {\n  position: relative; // Boosted mod: required for back-to-top component\n  margin: 0; // 1\n  font-family: var(--#{$prefix}body-font-family);\n  font-synthesis: none; // Boosted mod // 5\n  @include font-size(var(--#{$prefix}body-font-size));\n  font-weight: var(--#{$prefix}body-font-weight);\n  line-height: var(--#{$prefix}body-line-height);\n  // Boosted mod: no color\n  text-align: var(--#{$prefix}body-text-align);\n\n  /* rtl:remove */\n  letter-spacing: $letter-spacing-base; // Boosted mod\n  background-color: var(--#{$prefix}body-bg); // 2\n  -webkit-text-size-adjust: 100%; // 3\n  -webkit-tap-highlight-color: rgba($black, 0); // 4\n  -webkit-font-smoothing: subpixel-antialiased; // Boosted mod\n  text-rendering: optimizespeed; // Boosted mod\n}\n// scss-docs-end reboot-body-rules\n\n\n// Boosted mod: focus state\n//\n// 1. Default focus state\n// 2. Using the :focus-visible polyfill to hide outline defensively\n//    See https://github.com/WICG/focus-visible\n//    Note 1: this rule is not applied for our focus ring helper which\n//    handles itself outline and box shadow\n//    Note 2: this rule is also not applied for invalid select and invalid input files to ensure\n//    sufficient contrast between select elements on error focused vs. non focused\n// 3. Using the :focus-visible pseudo-class if supported by the browser\n// scss-docs-start focus-visibility\n:focus {\n  @include focus-visible(); // 1\n}\n\n.js-focus-visible :focus:not([data-focus-visible-added]):not(.focus-ring):not(.form-select:invalid):not(.form-control[type=\"file\"]:invalid),\n.js-focus-visible .focus:not([data-focus-visible-added]):not(.focus-ring):not(.form-select:invalid):not(.form-control[type=\"file\"]:invalid) { // 2\n  outline: 0 !important;\n  box-shadow: none;\n}\n\n:focus:not(:focus-visible):not(.focus-ring):not(.form-select:invalid):not(.form-control[type=\"file\"]:invalid) { // 3\n  outline: 0 !important;\n  box-shadow: none;\n}\n// scss-docs-end focus-visibility\n// End mod\n\n\n// Content grouping\n//\n// 1. Reset Firefox's gray color\n\nhr {\n  margin: $hr-margin-y 0;\n  color: $hr-color; // 1\n  border: 0;\n  border-top: $hr-border-width solid $hr-border-color;\n  opacity: $hr-opacity;\n}\n\n\n// Typography\n//\n// 1. Remove top margins from headings\n//    By default, `<h1>`-`<h6>` all receive top and bottom margins. We nuke the top\n//    margin for easier control within type scales as it avoids margin collapsing.\n\n%heading {\n  margin-top: 0; // 1\n  margin-bottom: $headings-margin-bottom;\n  @include font-size($font-size-base);  // Boosted mod\n  font-family: $headings-font-family;\n  font-style: $headings-font-style;\n  font-weight: $headings-font-weight;\n  line-height: $headings-line-height;\n  color: var(--#{$prefix}heading-color);\n\n  /* rtl:remove */\n  letter-spacing: $letter-spacing-base; // Boosted mod\n  -webkit-font-smoothing: antialiased;  // Boosted mod\n  -moz-osx-font-smoothing: grayscale;   // Boosted mod\n  text-rendering: optimizelegibility;   // Boosted mod\n}\n\nh1 {\n  @extend %heading;\n  @include font-size($h4-font-size);\n  line-height: $h4-line-height;\n\n  /* rtl:remove */\n  letter-spacing: $h4-spacing;\n}\n\nh2,\nh3 {\n  @extend %heading;\n  @include font-size($h5-font-size);\n  line-height: $h5-line-height;\n\n  /* rtl:remove */\n  letter-spacing: $h5-spacing;\n}\n\nh4,\nh5,\nh6 {\n  @extend %heading;\n}\n\n\n// Reset margins on paragraphs\n//\n// Similarly, the top margin on `<p>`s get reset. However, we also reset the\n// bottom margin to use `rem` units instead of `em`.\n\np {\n  margin-top: 0;\n  margin-bottom: $paragraph-margin-bottom;\n}\n\n\n// Abbreviations\n//\n// 1. Add the correct text decoration in Chrome, Edge, Opera, and Safari.\n// 2. Add explicit cursor to indicate changed behavior.\n// 3. Prevent the text-decoration to be skipped.\n\nabbr[title] {\n  text-decoration: underline dotted; // 1\n  cursor: help; // 2\n  text-decoration-skip-ink: none; // 3\n}\n\n\n// Address\n\naddress {\n  margin-bottom: 1rem;\n  font-style: normal;\n  line-height: inherit;\n}\n\n\n// Lists\n\nol,\nul {\n  padding-left: 2rem;\n}\n\nol,\nul,\ndl {\n  margin-top: 0;\n  margin-bottom: 1rem;\n}\n\nol ol,\nul ul,\nol ul,\nul ol {\n  margin-bottom: 0;\n}\n\n// Boosted mod\n// Orange square list-style\nul {\n  list-style-type: square;\n}\n\n// Future-proof markers' color\n// See https://developer.mozilla.org/fr/docs/Web/CSS/::marker\n// stylelint-disable selector-max-type\nli::marker {\n  color: var(--#{$prefix}primary);\n  vertical-align: middle;\n\n  ol & {\n    color: inherit;\n  }\n}\n\nli li::marker { color: var(--#{$prefix}secondary-color); }\n\nli li li::marker { color: var(--#{$prefix}tertiary-color); }\n\n// Bullet-proof markers' color\n// @todo To remove when ::marker support is OK\n// See https://caniuse.com/#search=%3A%3Amarker\nli::before {\n  color: var(--#{$prefix}primary);\n  vertical-align: text-top;\n\n  ol & {\n    color: inherit;\n  }\n}\n\nli li::before { color: var(--#{$prefix}secondary-color); }\n\nli li li::before { color: var(--#{$prefix}tertiary-color); }\n// stylelint-enable selector-max-type\n// End mod\n\ndt {\n  font-weight: $dt-font-weight;\n}\n\n// 1. Undo browser default\n\ndd {\n  margin-bottom: .5rem;\n  margin-left: 0; // 1\n}\n\n\n// Blockquote\n\nblockquote {\n  margin: 0 0 1rem;\n}\n\n\n// Strong\n//\n// Add the correct font weight in Chrome, Edge, and Safari\n\nb,\nem, // Boosted mod\nstrong {\n  font-weight: $font-weight-bold; // Boosted mod: ensure 700\n}\n\n\n// Small\n//\n// Add the correct font size in all browsers\n\nsmall {\n  @include font-size($small-font-size);\n  // Boosted mod\n  font-weight: $font-weight-normal;\n  line-height: $line-height-sm;\n  // End mod\n}\n\n\n// Mark\n\nmark {\n  padding: $mark-padding;\n  color: var(--#{$prefix}highlight-color);\n  background-color: var(--#{$prefix}highlight-bg);\n}\n\n\n// Sub and Sup\n//\n// Prevent `sub` and `sup` elements from affecting the line height in\n// all browsers.\n\nsub,\nsup {\n  position: relative;\n  @include font-size($sub-sup-font-size);\n  line-height: 0;\n  vertical-align: baseline;\n}\n\nsub { bottom: -.25em; }\nsup { top: -.5em; }\n\n\n// Links\n\na {\n  color: rgba(var(--#{$prefix}link-color-rgb), var(--#{$prefix}link-opacity, 1));\n  text-decoration: $link-decoration;\n\n  &:hover {\n    --#{$prefix}link-color-rgb: var(--#{$prefix}link-hover-color-rgb);\n    text-decoration: $link-hover-decoration;\n  }\n}\n\n// And undo these styles for placeholder links/named anchors (without href).\n// It would be more straightforward to just use a[href] in previous block, but that\n// causes specificity issues in many other styles that are too complex to fix.\n// See https://github.com/twbs/bootstrap/issues/19402\n\na:not([href]):not([class]) {\n  &,\n  &:hover {\n    color: inherit;\n    text-decoration: none;\n  }\n}\n\n\n// Code\n\nvar, // Boosted mod\npre,\ncode,\nkbd,\nsamp {\n  font-family: $font-family-code;\n  @include font-size(1em); // Correct the odd `em` font sizing in all browsers.\n}\n\n// 1. Remove browser default top margin\n// 2. Reset browser default of `1em` to use `rem`s\n// 3. Don't allow content to break outside\n\npre {\n  display: block;\n  margin-top: 0; // 1\n  margin-bottom: 1rem; // 2\n  overflow: auto; // 3\n  @include font-size($code-font-size);\n  line-height: $pre-line-height; // Boosted mod\n  color: $pre-color;\n\n  // Account for some code outputs that place code tags in pre tags\n  code {\n    @include font-size(inherit);\n    color: inherit;\n    word-break: normal;\n  }\n}\n\nvar, // Boosted mod\ncode {\n  @include font-size($code-font-size);\n  font-style: normal; // Boosted mod: <var> is italic in all browsers\n  line-height: $line-height-sm; // Boosted mod\n  color: var(--#{$prefix}code-color);\n  word-wrap: break-word;\n\n  // Streamline the style when inside anchors to avoid broken underline and more\n  a > & {\n    color: inherit;\n  }\n}\n\nkbd {\n  padding: $kbd-padding-y $kbd-padding-x;\n  @include font-size($kbd-font-size);\n  color: $kbd-color;\n  background-color: $kbd-bg;\n  @include border-radius($border-radius-sm);\n\n  kbd {\n    padding: 0;\n    @include font-size(1em);\n    font-weight: $nested-kbd-font-weight;\n  }\n}\n\n\n// Figures\n//\n// Apply a consistent margin strategy (matches our type styles).\n\nfigure {\n  margin: 0 0 1rem;\n}\n\n\n// Images and content\n\nimg,\nsvg {\n  vertical-align: middle;\n}\n\n\n// Tables\n//\n// 1. Prevent double borders\n// 2. Ensure horizontal alignment in table when using numbers\n//    See https://x.com/wesbos/status/932644812582522880\n//    See https://caniuse.com/#feat=font-variant-numeric\n//    See https://caniuse.com/#feat=font-feature\n//    See https://helpx.adobe.com/fonts/using/open-type-syntax.html#tnum\n\ntable {\n  font-feature-settings: \"tnum\";      // Boosted mod: 2\n  font-variant-numeric: tabular-nums; // Boosted mod: 2\n  caption-side: top;                  // Boosted mod\n  border-collapse: collapse;\n}\n\ncaption {\n  padding-top: $table-caption-padding-y;\n  padding-bottom: $table-caption-padding-y;\n  @include font-size($h1-font-size);    // Boosted mod\n  font-weight: $font-weight-bold;       // Boosted mod\n  color: $table-caption-color;\n  text-align: left;\n\n  /* rtl:remove */\n  letter-spacing: $h1-spacing;          // Boosted mod\n  -webkit-font-smoothing: antialiased;  // Boosted mod\n  -moz-osx-font-smoothing: grayscale;   // Boosted mod\n  text-rendering: optimizelegibility;   // Boosted mod\n}\n\n// 1. Removes font-weight bold by inheriting\n// 2. Matches default `<td>` alignment by inheriting `text-align`.\n// 3. Fix alignment for Safari\n\nth {\n  font-weight: $table-th-font-weight; // 1\n  text-align: inherit; // 2\n  text-align: -webkit-match-parent; // 3\n}\n\nthead,\ntbody,\ntfoot,\ntr,\ntd,\nth {\n  border-color: inherit;\n  border-style: solid;\n  border-width: 0;\n}\n\n\n// Forms\n//\n// 1. Allow labels to use `margin` for spacing.\n\nlabel {\n  display: inline-block; // 1\n  font-weight: $form-label-font-weight; // Boosted mod\n}\n\n// Remove the default `border-radius` that macOS Chrome adds.\n// See https://github.com/twbs/bootstrap/issues/24093\n\nbutton {\n  // stylelint-disable-next-line property-disallowed-list\n  border-radius: 0;\n}\n\n// Explicitly remove focus outline in Chromium when it shouldn't be\n// visible (e.g. as result of mouse click or touch tap). It already\n// should be doing this automatically, but seems to currently be\n// confused and applies its very visible two-tone outline anyway.\n//\n// This rule is not applied with the focus ring utility which handles\n// itself outline and box shadow\n\nbutton:focus:not(:focus-visible):not(.focus-ring) {\n  outline: 0;\n  box-shadow: none; // Bosted mod\n}\n\n// 1. Remove the margin in Firefox and Safari\n\ninput,\nbutton,\nselect,\noptgroup,\ntextarea {\n  margin: 0; // 1\n  font-family: inherit;\n  @include font-size(inherit);\n  line-height: inherit;\n\n  /* rtl:remove */\n  letter-spacing: inherit; // Boosted mod\n  box-shadow: none; // Boosted mod: prevent native validation styles to apply\n}\n\n// Remove the inheritance of text transform in Firefox\nbutton,\nselect {\n  text-transform: none;\n}\n// Set the cursor for non-`<button>` buttons\n//\n// Details at https://github.com/twbs/bootstrap/pull/30562\n[role=\"button\"] {\n  cursor: pointer;\n}\n\nselect {\n  // Remove the inheritance of word-wrap in Safari.\n  // See https://github.com/twbs/bootstrap/issues/24990\n  word-wrap: normal;\n\n  // Undo the opacity change from Chrome\n  &:disabled {\n    opacity: 1;\n  }\n}\n\n// Remove the dropdown arrow only from text type inputs built with datalists in Chrome.\n// See https://stackoverflow.com/a/54997118\n\n[list]:not([type=\"date\"]):not([type=\"datetime-local\"]):not([type=\"month\"]):not([type=\"week\"]):not([type=\"time\"])::-webkit-calendar-picker-indicator {\n  display: none !important;\n}\n\n// 1. Prevent a WebKit bug where (2) destroys native `audio` and `video`\n//    controls in Android 4.\n// 2. Correct the inability to style clickable types in iOS and Safari.\n// 3. Opinionated: add \"hand\" cursor to non-disabled button elements.\n\nbutton,\n[type=\"button\"], // 1\n[type=\"reset\"],\n[type=\"submit\"] {\n  -webkit-appearance: button; // 2\n\n  @if $enable-button-pointers {\n    &:not(:disabled) {\n      cursor: pointer; // 3\n    }\n  }\n}\n\n// Remove inner border and padding from Firefox, but don't restore the outline like Normalize.\n\n::-moz-focus-inner {\n  padding: 0;\n  border-style: none;\n}\n\n// 1. Textareas should really only resize vertically so they don't break their (horizontal) containers.\n\ntextarea {\n  resize: vertical; // 1\n}\n\n// 1. Browsers set a default `min-width: min-content;` on fieldsets,\n//    unlike e.g. `<div>`s, which have `min-width: 0;` by default.\n//    So we reset that to ensure fieldsets behave more like a standard block element.\n//    See https://github.com/twbs/bootstrap/issues/12359\n//    and https://html.spec.whatwg.org/multipage/#the-fieldset-and-legend-elements\n// 2. Reset the default outline behavior of fieldsets so they don't affect page layout.\n\nfieldset {\n  min-width: 0; // 1\n  padding: 0; // 2\n  margin: 0; // 2\n  border: 0; // 2\n}\n\n// 1. By using `float: left`, the legend will behave like a block element.\n//    This way the border of a fieldset wraps around the legend if present.\n// 2. Fix wrapping bug.\n//    See https://github.com/twbs/bootstrap/issues/29712\n\nlegend {\n  float: left; // 1\n  width: 100%;\n  padding: 0;\n  margin-bottom: $legend-margin-bottom;\n  @include font-size($legend-font-size);\n  font-weight: $legend-font-weight;\n  line-height: inherit;\n\n  + * {\n    clear: left; // 2\n  }\n}\n\n// Fix height of inputs with a type of datetime-local, date, month, week, or time\n// See https://github.com/twbs/bootstrap/issues/18842\n\n::-webkit-datetime-edit-fields-wrapper,\n::-webkit-datetime-edit-text,\n::-webkit-datetime-edit-minute,\n::-webkit-datetime-edit-hour-field,\n::-webkit-datetime-edit-day-field,\n::-webkit-datetime-edit-month-field,\n::-webkit-datetime-edit-year-field {\n  padding: 0;\n}\n\n::-webkit-inner-spin-button {\n  height: auto;\n}\n\n// 1. This overrides the extra rounded corners on search inputs in iOS so that our\n//    `.form-control` class can properly style them. Note that this cannot simply\n//    be added to `.form-control` as it's not specific enough. For details, see\n//    https://github.com/twbs/bootstrap/issues/11586.\n// 2. Correct the outline style in Safari.\n\n[type=\"search\"] {\n  -webkit-appearance: textfield; // 1\n  outline-offset: -#{$focus-visible-outer-offset}; // 2 // Boosted mod\n}\n\n// 1. A few input types should stay LTR\n// See https://rtlstyling.com/posts/rtl-styling#form-inputs\n// 2. RTL only output\n// See https://rtlcss.com/learn/usage-guide/control-directives/#raw\n\n/* rtl:raw:\n[type=\"tel\"],\n[type=\"url\"],\n[type=\"email\"],\n[type=\"number\"] {\n  direction: ltr;\n}\n*/\n\n// Remove the inner padding in Chrome and Safari on macOS.\n\n::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n\n// Remove padding around color pickers in webkit browsers\n\n::-webkit-color-swatch-wrapper {\n  padding: 0;\n}\n\n\n// 1. Inherit font family and line height for file input buttons\n// 2. Correct the inability to style clickable types in iOS and Safari.\n\n::file-selector-button {\n  font: inherit; // 1\n  -webkit-appearance: button; // 2\n}\n\n// Correct element displays\n\noutput {\n  display: inline-block;\n}\n\n// Remove border from iframe\n\niframe {\n  border: 0;\n}\n\n// Summary\n//\n// 1. Add the correct display in all browsers\n\nsummary {\n  display: list-item; // 1\n  cursor: pointer;\n}\n\n\n// Progress\n//\n// Add the correct vertical alignment in Chrome, Firefox, and Opera.\n\nprogress {\n  vertical-align: baseline;\n}\n\n\n// Hidden attribute\n//\n// Always hide an element with the `hidden` HTML attribute.\n\n[hidden] {\n  display: none !important;\n}\n", "// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px)\n//\n// The map defined in the `$grid-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl xxl))\n//    md\n@function breakpoint-next($name, $breakpoints: $grid-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @if not $n {\n    @error \"breakpoint `#{$name}` not found in `#{$breakpoints}`\";\n  }\n  @return if($n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $grid-breakpoints) {\n  $min: map-get($breakpoints, $name);\n  @return if($min != 0, $min, null);\n}\n\n// Maximum breakpoint width.\n// The maximum value is reduced by 0.02px to work around the limitations of\n// `min-` and `max-` prefixes and viewports with fractional widths.\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(md, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $grid-breakpoints) {\n  $max: map-get($breakpoints, $name);\n  @return if($max and $max > 0, $max - .02, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash in front.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $grid-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $grid-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media that spans multiple breakpoint widths.\n// Makes the @content apply between the min and max breakpoints\n@mixin media-breakpoint-between($lower, $upper, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($lower, $breakpoints);\n  $max: breakpoint-max($upper, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($lower, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($upper, $breakpoints) {\n      @content;\n    }\n  }\n}\n\n// Media between the breakpoint's minimum and maximum widths.\n// No minimum for the smallest breakpoint, and no maximum for the largest one.\n// Makes the @content apply only to the given breakpoint, not viewports any wider or narrower.\n@mixin media-breakpoint-only($name, $breakpoints: $grid-breakpoints) {\n  $min:  breakpoint-min($name, $breakpoints);\n  $next: breakpoint-next($name, $breakpoints);\n  $max:  breakpoint-max($next, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($name, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($next, $breakpoints) {\n      @content;\n    }\n  }\n}\n", "@import \"color-palette\";\n\n// Variables\n//\n// Variables should follow the `$component-state-property-size` formula for\n// consistent naming. Ex: $nav-link-disabled-color and $modal-content-box-shadow-xs.\n\n// Color system\n\n// scss-docs-start gray-color-variables\n$white:    $ods-white-100 !default;\n$gray-100: #fafafa !default;\n$gray-200: #f6f6f6 !default;\n$gray-300: $ods-gray-200 !default;\n$gray-400: $ods-gray-300 !default;\n$gray-500: $ods-gray-400 !default;\n$gray-600: $ods-gray-500 !default;\n$gray-700: $ods-gray-600 !default;\n$gray-800: $ods-gray-700 !default;\n$gray-900: $ods-gray-800 !default;\n$gray-950: $ods-gray-900 !default;\n$black:    $ods-black-900 !default;\n// scss-docs-end gray-color-variables\n\n// fusv-disable\n// scss-docs-start gray-colors-map\n$grays: (\n  \"100\": $gray-100,\n  \"200\": $gray-200,\n  \"300\": $gray-300,\n  \"400\": $gray-400,\n  \"500\": $gray-500,\n  \"600\": $gray-600,\n  \"700\": $gray-700,\n  \"800\": $gray-800,\n  \"900\": $gray-900,\n  \"950\": $gray-950,\n) !default;\n// scss-docs-end gray-colors-map\n// fusv-enable\n\n// Boosted mod\n// scss-docs-start brand-colors\n//// Functional colors\n$functional-green:  $ods-forest-200 !default;\n$functional-blue:   $ods-water-200 !default;\n$functional-yellow: $ods-sun-100 !default;\n$functional-red:    $ods-fire-200 !default;\n//// Supporting colors\n$supporting-blue:   $ods-blue-300 !default;\n$supporting-yellow: $ods-yellow-300 !default;\n$supporting-green:  $ods-green-300 !default;\n$supporting-purple: $ods-purple-300 !default;\n$supporting-pink:   $ods-pink-300 !default;\n$supporting-orange: $ods-orange-100 !default;\n// scss-docs-end brand-colors\n// End mod\n\n\n// scss-docs-start color-variables\n$blue:    $functional-blue !default;\n$indigo:  $supporting-purple !default;\n$purple:  $supporting-purple !default;\n$pink:    $supporting-pink !default;\n$red:     $functional-red !default;\n$orange:  $ods-orange-200 !default;\n$yellow:  $functional-yellow !default;\n$green:   $functional-green !default;\n$teal:    $supporting-green !default;\n$cyan:    $supporting-blue !default;\n// scss-docs-end color-variables\n\n// scss-docs-start colors-map\n$colors: (\n  \"blue\":       $blue,\n  \"indigo\":     $indigo,\n  \"purple\":     $purple,\n  \"pink\":       $pink,\n  \"red\":        $red,\n  \"orange\":     $orange,\n  \"yellow\":     $yellow,\n  \"green\":      $green,\n  \"teal\":       $teal,\n  \"cyan\":       $cyan,\n  \"black\":      $black,\n  \"white\":      $white,\n  \"gray\":       $gray-600,\n  \"gray-dark\":  $gray-800\n) !default;\n// scss-docs-end colors-map\n\n// The contrast ratio to reach against white, to determine if color changes from \"light\" to \"dark\". Acceptable values for WCAG 2.2 are 3, 4.5 and 7.\n// See https://www.w3.org/TR/WCAG/#contrast-minimum\n$min-contrast-ratio:   4.5 !default;\n\n// Customize the light and dark text colors for use in our YIQ color contrast function.\n$color-contrast-dark:  $black !default;\n$color-contrast-light: $white !default;\n\n// fusv-disable\n$blue-100: tint-color($blue, 80%) !default;\n$blue-200: tint-color($blue, 60%) !default;\n$blue-300: tint-color($blue, 40%) !default;\n$blue-400: tint-color($blue, 20%) !default;\n$blue-500: $blue !default;\n$blue-600: shade-color($blue, 20%) !default;\n$blue-700: shade-color($blue, 40%) !default;\n$blue-800: shade-color($blue, 60%) !default;\n$blue-900: shade-color($blue, 80%) !default;\n\n$indigo-100: $ods-purple-100 !default;\n$indigo-200: $ods-purple-200 !default;\n$indigo-300: $ods-purple-300 !default;\n$indigo-400: $ods-purple-400 !default;\n$indigo-500: $ods-purple-500 !default;\n$indigo-600: $ods-purple-600 !default;\n$indigo-700: shade-color($ods-purple-600, 20%) !default;\n$indigo-800: shade-color($ods-purple-600, 40%) !default;\n$indigo-900: shade-color($ods-purple-600, 60%) !default;\n\n$purple-100: $ods-purple-100 !default;\n$purple-200: $ods-purple-200 !default;\n$purple-300: $ods-purple-300 !default;\n$purple-400: $ods-purple-400 !default;\n$purple-500: $ods-purple-500 !default;\n$purple-600: $ods-purple-600 !default;\n$purple-700: shade-color($ods-purple-600, 20%) !default;\n$purple-800: shade-color($ods-purple-600, 40%) !default;\n$purple-900: shade-color($ods-purple-600, 60%) !default;\n\n$pink-100: $ods-pink-100 !default;\n$pink-200: $ods-pink-200 !default;\n$pink-300: $ods-pink-300 !default;\n$pink-400: $ods-pink-400 !default;\n$pink-500: $ods-pink-500 !default;\n$pink-600: $ods-pink-600 !default;\n$pink-700: shade-color($ods-pink-600, 20%) !default;\n$pink-800: shade-color($ods-pink-600, 40%) !default;\n$pink-900: shade-color($ods-pink-600, 60%) !default;\n\n$red-100: tint-color($red, 80%) !default;\n$red-200: tint-color($red, 60%) !default;\n$red-300: tint-color($red, 40%) !default;\n$red-400: tint-color($red, 20%) !default;\n$red-500: $red !default;\n$red-600: shade-color($red, 20%) !default;\n$red-700: shade-color($red, 40%) !default;\n$red-800: shade-color($red, 60%) !default;\n$red-900: shade-color($red, 80%) !default;\n\n$orange-100: tint-color($orange, 80%) !default;\n$orange-200: tint-color($orange, 60%) !default;\n$orange-300: tint-color($orange, 40%) !default;\n$orange-400: tint-color($orange, 20%) !default;\n$orange-500: $orange !default;\n$orange-600: shade-color($orange, 20%) !default;\n$orange-700: shade-color($orange, 40%) !default;\n$orange-800: shade-color($orange, 60%) !default;\n$orange-900: shade-color($orange, 80%) !default;\n\n$yellow-100: $ods-yellow-100 !default;\n$yellow-200: $ods-yellow-200 !default;\n$yellow-300: $ods-yellow-300 !default;\n$yellow-400: $ods-yellow-400 !default;\n$yellow-500: $ods-yellow-500 !default;\n$yellow-600: $ods-yellow-600 !default;\n$yellow-700: shade-color($ods-yellow-600, 20%) !default;\n$yellow-800: shade-color($ods-yellow-600, 40%) !default;\n$yellow-900: shade-color($ods-yellow-600, 60%) !default;\n\n$green-100: tint-color($green, 80%) !default;\n$green-200: tint-color($green, 60%) !default;\n$green-300: tint-color($green, 40%) !default;\n$green-400: tint-color($green, 20%) !default;\n$green-500: $green !default;\n$green-600: shade-color($green, 20%) !default;\n$green-700: shade-color($green, 40%) !default;\n$green-800: shade-color($green, 60%) !default;\n$green-900: shade-color($green, 80%) !default;\n\n$teal-100: $ods-green-100 !default;\n$teal-200: $ods-green-200 !default;\n$teal-300: $ods-green-300 !default;\n$teal-400: $ods-green-400 !default;\n$teal-500: $ods-green-500 !default;\n$teal-600: $ods-green-600 !default;\n$teal-700: shade-color($ods-green-600, 20%) !default;\n$teal-800: shade-color($ods-green-600, 40%) !default;\n$teal-900: shade-color($ods-green-600, 60%) !default;\n\n$cyan-100: $ods-blue-100 !default;\n$cyan-200: $ods-blue-200 !default;\n$cyan-300: $ods-blue-300 !default;\n$cyan-400: $ods-blue-400 !default;\n$cyan-500: $ods-blue-500 !default;\n$cyan-600: $ods-blue-600 !default;\n$cyan-700: shade-color($ods-blue-600, 20%) !default;\n$cyan-800: shade-color($ods-blue-600, 40%) !default;\n$cyan-900: shade-color($ods-blue-600, 60%) !default;\n\n$blues: (\n  \"blue-100\": $blue-100,\n  \"blue-200\": $blue-200,\n  \"blue-300\": $blue-300,\n  \"blue-400\": $blue-400,\n  \"blue-500\": $blue-500,\n  \"blue-600\": $blue-600,\n  \"blue-700\": $blue-700,\n  \"blue-800\": $blue-800,\n  \"blue-900\": $blue-900\n) !default;\n\n$indigos: (\n  \"indigo-100\": $indigo-100,\n  \"indigo-200\": $indigo-200,\n  \"indigo-300\": $indigo-300,\n  \"indigo-400\": $indigo-400,\n  \"indigo-500\": $indigo-500,\n  \"indigo-600\": $indigo-600,\n  \"indigo-700\": $indigo-700,\n  \"indigo-800\": $indigo-800,\n  \"indigo-900\": $indigo-900\n) !default;\n\n$purples: (\n  \"purple-100\": $purple-100,\n  \"purple-200\": $purple-200,\n  \"purple-300\": $purple-300,\n  \"purple-400\": $purple-400,\n  \"purple-500\": $purple-500,\n  \"purple-600\": $purple-600,\n  \"purple-700\": $purple-700,\n  \"purple-800\": $purple-800,\n  \"purple-900\": $purple-900\n) !default;\n\n$pinks: (\n  \"pink-100\": $pink-100,\n  \"pink-200\": $pink-200,\n  \"pink-300\": $pink-300,\n  \"pink-400\": $pink-400,\n  \"pink-500\": $pink-500,\n  \"pink-600\": $pink-600,\n  \"pink-700\": $pink-700,\n  \"pink-800\": $pink-800,\n  \"pink-900\": $pink-900\n) !default;\n\n$reds: (\n  \"red-100\": $red-100,\n  \"red-200\": $red-200,\n  \"red-300\": $red-300,\n  \"red-400\": $red-400,\n  \"red-500\": $red-500,\n  \"red-600\": $red-600,\n  \"red-700\": $red-700,\n  \"red-800\": $red-800,\n  \"red-900\": $red-900\n) !default;\n\n$oranges: (\n  \"orange-100\": $orange-100,\n  \"orange-200\": $orange-200,\n  \"orange-300\": $orange-300,\n  \"orange-400\": $orange-400,\n  \"orange-500\": $orange-500,\n  \"orange-600\": $orange-600,\n  \"orange-700\": $orange-700,\n  \"orange-800\": $orange-800,\n  \"orange-900\": $orange-900\n) !default;\n\n$yellows: (\n  \"yellow-100\": $yellow-100,\n  \"yellow-200\": $yellow-200,\n  \"yellow-300\": $yellow-300,\n  \"yellow-400\": $yellow-400,\n  \"yellow-500\": $yellow-500,\n  \"yellow-600\": $yellow-600,\n  \"yellow-700\": $yellow-700,\n  \"yellow-800\": $yellow-800,\n  \"yellow-900\": $yellow-900\n) !default;\n\n$greens: (\n  \"green-100\": $green-100,\n  \"green-200\": $green-200,\n  \"green-300\": $green-300,\n  \"green-400\": $green-400,\n  \"green-500\": $green-500,\n  \"green-600\": $green-600,\n  \"green-700\": $green-700,\n  \"green-800\": $green-800,\n  \"green-900\": $green-900\n) !default;\n\n$teals: (\n  \"teal-100\": $teal-100,\n  \"teal-200\": $teal-200,\n  \"teal-300\": $teal-300,\n  \"teal-400\": $teal-400,\n  \"teal-500\": $teal-500,\n  \"teal-600\": $teal-600,\n  \"teal-700\": $teal-700,\n  \"teal-800\": $teal-800,\n  \"teal-900\": $teal-900\n) !default;\n\n$cyans: (\n  \"cyan-100\": $cyan-100,\n  \"cyan-200\": $cyan-200,\n  \"cyan-300\": $cyan-300,\n  \"cyan-400\": $cyan-400,\n  \"cyan-500\": $cyan-500,\n  \"cyan-600\": $cyan-600,\n  \"cyan-700\": $cyan-700,\n  \"cyan-800\": $cyan-800,\n  \"cyan-900\": $cyan-900\n) !default;\n// fusv-enable\n\n// scss-docs-start theme-color-variables\n$primary:       $orange !default;\n$secondary:     $black !default;\n$success:       $green !default;\n$info:          $blue !default;\n$warning:       $yellow !default;\n$danger:        $red !default;\n$light:         $gray-500 !default;\n$dark:          $black !default;\n// scss-docs-end theme-color-variables\n\n// scss-docs-start theme-colors-map\n$theme-colors: (\n  \"primary\":    $primary,\n  \"secondary\":  $secondary,\n  \"success\":    $success,\n  \"info\":       $info,\n  \"warning\":    $warning,\n  \"danger\":     $danger,\n  \"light\":      $light,\n  \"dark\":       $dark\n) !default;\n// scss-docs-end theme-colors-map\n\n// scss-docs-start theme-text-variables\n$primary-text-emphasis:   $primary !default; // Boosted mod: instead of `shade-color($primary, 60%)`\n$secondary-text-emphasis: $secondary !default; // Boosted mod: instead of `shade-color($secondary, 60%)`\n$success-text-emphasis:   $success !default; // Boosted mod: instead of `shade-color($success, 60%)`\n$info-text-emphasis:      $info !default; // Boosted mod: instead of `shade-color($info, 60%)`\n$warning-text-emphasis:   $warning !default; // Boosted mod: instead of `shade-color($warning, 60%)`\n$danger-text-emphasis:    $danger !default; // Boosted mod: instead of `shade-color($danger, 60%)`\n$light-text-emphasis:     $light !default; // Boosted mod: instead of `$gray-700`\n$dark-text-emphasis:      $dark !default; // Boosted mod: instead of `$gray-700`\n// scss-docs-end theme-text-variables\n\n// scss-docs-start theme-bg-subtle-variables\n$primary-bg-subtle:       $primary !default; // Boosted mod: instead of `tint-color($primary, 80%)`\n$secondary-bg-subtle:     $secondary !default; // Boosted mod: instead of `tint-color($secondary, 80%)`\n$success-bg-subtle:       $success !default; // Boosted mod: instead of `tint-color($success, 80%)`\n$info-bg-subtle:          $info !default; // Boosted mod: instead of `tint-color($info, 80%)`\n$warning-bg-subtle:       $warning !default; // Boosted mod: instead of `tint-color($warning, 80%)`\n$danger-bg-subtle:        $danger !default; // Boosted mod: instead of `tint-color($danger, 80%)`\n$light-bg-subtle:         $light !default; // Boosted mod: instead of `mix($gray-100, $white)`\n$dark-bg-subtle:          $dark !default; // Boosted mod: instead of `$gray-400`\n// scss-docs-end theme-bg-subtle-variables\n\n// scss-docs-start theme-border-subtle-variables\n$primary-border-subtle:   $primary !default; // Boosted mod: instead of `tint-color($primary, 60%)`\n$secondary-border-subtle: $secondary !default; // Boosted mod: instead of `tint-color($secondary, 60%)`\n$success-border-subtle:   $success !default; // Boosted mod: instead of `tint-color($success, 60%)`\n$info-border-subtle:      $info !default; // Boosted mod: instead of `tint-color($info, 60%)`\n$warning-border-subtle:   $warning !default; // Boosted mod: instead of `tint-color($warning, 60%)`\n$danger-border-subtle:    $danger !default; // Boosted mod: instead of `tint-color($danger, 60%)`\n$light-border-subtle:     $light !default; // Boosted mod: instead of `$gray-200`\n$dark-border-subtle:      $dark !default; // Boosted mod: instead of `$gray-500`\n// scss-docs-end theme-border-subtle-variables\n\n// Characters which are escaped by the escape-svg function\n$escaped-characters: (\n  (\"<\", \"%3c\"),\n  (\">\", \"%3e\"),\n  (\"#\", \"%23\"),\n  (\"(\", \"%28\"),\n  (\")\", \"%29\"),\n) !default;\n\n// Boosted mod\n//// SVG as Data-URi\n$chevron-icon:          url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 9 14'><path d='M9 2 7 0 0 7l7 7 2-2-5-5 5-5z'/></svg>\") !default;\n$cross-icon:            url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30' fill='#{$black}'><path d='m15 17.121-8.132 8.132-2.121-2.12L12.879 15 4.747 6.868l2.12-2.121L15 12.879l8.132-8.132 2.12 2.121L17.122 15l8.132 8.132-2.121 2.12L15 17.123z'/></svg>\") !default;\n$cross-icon-stroke:     url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='116 116 767 767' fill='#{$black}'><path d='M817.493 676.165a49.977 49.977 0 0 1 0 70.664l-70.664 70.664a49.977 49.977 0 0 1-70.664 0L499.5 640.828 322.835 817.493a49.977 49.977 0 0 1-70.664 0l-70.664-70.664a49.977 49.977 0 0 1 0-70.664L358.172 499.5 181.507 322.835a49.977 49.977 0 0 1 0-70.664l70.664-70.664a49.977 49.977 0 0 1 70.664 0L499.5 358.172l176.665-176.665a49.977 49.977 0 0 1 70.664 0l70.664 70.664a49.977 49.977 0 0 1 0 70.664L640.828 499.5Z'/></svg>\") !default;\n$check-icon:            url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 15 12'><path fill='#{$black}' d='M13 0 5 8 2 5 0 7l5 5L15 2z'/></svg>\") !default;\n$burger-icon:           url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30' fill='#{$black}'><path d='M28 21v2H2v-2h26Zm0-7v2H2v-2h26Zm0-7v2H2V7h26Z'/></svg>\") !default;\n$burger-icon-small:     url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 25 25' fill='#{$black}'><path d='M2 19h21v-2H2v2Zm0-6h21v-2H2v2Zm0-6h21V5H2v2Z'/></svg>\") !default;\n$success-icon:          url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 125 125'><path fill='#{$success}' d='M62.5 0a62.5 62.5 0 1 0 0 125 62.5 62.5 0 0 0 0-125zm28 29.4c3.3 0 6 2.6 6 5.9a5.9 5.9 0 0 1-1.3 3.7L57.7 86a5.8 5.8 0 0 1-9.1 0L29.8 62.5c-.8-1-1.2-2.3-1.2-3.7a5.9 5.9 0 0 1 1.7-4.1l2.3-2.4a5.8 5.8 0 0 1 4.2-1.7 5.8 5.8 0 0 1 3.8 1.4L52 64.7 86.6 31a5.8 5.8 0 0 1 4-1.6z'/></svg>\") !default;\n$info-icon:             url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 125 125'><path fill='#{$info}' d='M62.5 0a62.5 62.5 0 1 0 0 125 62.5 62.5 0 0 0 0-125zm0 14.7a11 11 0 1 1 0 22 11 11 0 0 1 0-22zM47.8 44.1h25.7v46.2c0 4.7 1.3 6.5 1.8 7.2.8 1 2.3 1.5 4.8 1.6h.8v3.8H47.8v-3.7h.8c2.3-.1 4-.8 5-2 .4-.4 1-2 1-7V57c0-4.8-.6-6.6-1.2-7.3-.8-1-2.4-1.5-4.9-1.6h-.7V44z'/></svg>\") !default;\n$warning-icon:          url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='75 125 850 750'><path fill='#{$warning}' fill-rule='evenodd' d='M828.111 875H170.889A93.71 93.71 0 0 1 89.8 734.017l-.008-.005.772-1.321.036-.062 327.8-561.117h.008a93.94 93.94 0 0 1 162.182 0h.008l328.612 562.5-.009.005A93.709 93.709 0 0 1 828.111 875ZM500.5 775a47.5 47.5 0 1 1 47.507-47.5A47.5 47.5 0 0 1 500.5 775Zm47.368-424.038-15.7 258.121c-.009 17.482-14.185 24.05-31.671 24.05s-31.662-6.568-31.671-24.05l-15.74-258.716c-.057-.949-.094-1.9-.094-2.867a47.507 47.507 0 0 1 95.014 0 47.782 47.782 0 0 1-.138 3.462Z'/></svg>\") !default;\n$warning-icon-filled:   url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='75 125 850 750'><path fill='#{$warning}' d='M828.111 875H170.889A93.71 93.71 0 0 1 89.8 734.017l-.008-.005.772-1.321.036-.062 327.8-561.117h.008a93.94 93.94 0 0 1 162.182 0h.008l328.612 562.5-.009.005A93.709 93.709 0 0 1 828.111 875Z'/><path fill='#{$black}' d='M500.5 775a47.5 47.5 0 1 1 47.507-47.5A47.5 47.5 0 0 1 500.5 775Zm47.368-424.038-15.7 258.121c-.009 17.482-14.185 24.05-31.671 24.05s-31.662-6.568-31.671-24.05l-15.74-258.716c-.057-.949-.094-1.9-.094-2.867a47.507 47.507 0 0 1 95.014 0 47.782 47.782 0 0 1-.138 3.462Z'/></svg>\") !default;\n$danger-icon:           url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 140 125'><path fill='#{$danger}' d='M70.3 0c-5.8 0-10.8 3.1-13.5 7.8L2.3 101.3l-.2.2A15.6 15.6 0 0 0 15.6 125H125a15.6 15.6 0 0 0 13.5-23.5L83.8 7.8A15.6 15.6 0 0 0 70.3 0zm19.2 50a6.4 6.4 0 0 1 4.4 1.9 6.4 6.4 0 0 1 0 9L79.4 75.6l15 15a6.4 6.4 0 0 1 0 9.2 6.4 6.4 0 0 1-4.5 1.9 6.4 6.4 0 0 1-4.6-2l-15-15-15 15a6.4 6.4 0 0 1-4.6 2 6.4 6.4 0 0 1-4.6-2 6.4 6.4 0 0 1 0-9l15-15L46.8 61a6.4 6.4 0 1 1 9-9.1l14.6 14.5L84.8 52a6.4 6.4 0 0 1 4.7-1.9z'/></svg>\") !default;\n$add-icon:              url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 14 14'><path fill='currentColor' d='M14 6H8V0H6v6H0v2h6v6h2V8h6V6z'/></svg>\") !default;\n$remove-icon:           url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 14 2'><path fill='currentColor' d='M0 0h14v2H0z'/></svg>\") !default;\n$add-icon-sm:           url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 10 10'><path d='M10 4H6V0H4v4H0v2h4v4h2V6h4V4z'/></svg>\") !default;\n$remove-icon-sm:        url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 10 2'><path d='M0 0h10v2H0z'/></svg>\") !default;\n$play-icon:             url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'><path fill='currentColor' d='M12.138 16.8 3 21.6V2.4l9.138 4.8L21 12z' fill-rule='evenodd'/></svg>\") !default;\n$pause-icon:            url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'><path fill='currentColor' d='M10.2 21H3V3h7.2v18ZM21 21h-7.2V3H21v18Z' fill-rule='evenodd'/></svg>\") !default;\n$helper-icon:           url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1000 1000'><path fill='#{$info}' d='M500 75C265.277 75 75 265.279 75 500s190.277 425 425 425 425-190.279 425-425S734.721 75 500 75Zm30.8 680.633a54.149 54.149 0 0 1-37.069 14.267 56.1 56.1 0 0 1-37.95-14.085q-16.233-14.079-16.226-39.384 0-22.458 15.679-37.781t38.5-15.324q22.464 0 37.789 15.324t15.324 37.781q-.003 24.951-16.047 39.202Zm133.12-330.046a162.251 162.251 0 0 1-29.23 39.38q-16.92 16.574-60.772 55.785A248.236 248.236 0 0 0 554.5 540.18a79.146 79.146 0 0 0-10.868 15.32 75.1 75.1 0 0 0-5.529 13.9q-1.953 6.954-5.879 24.42-6.762 37.068-42.413 37.069-18.541 0-31.192-12.119t-12.647-36q0-29.945 9.262-51.863a131.346 131.346 0 0 1 24.6-38.491q15.319-16.577 41.35-39.4 22.789-19.946 32.962-30.113a101.987 101.987 0 0 0 17.105-22.632 54.714 54.714 0 0 0 6.955-27.086q0-28.517-21.213-48.119t-54.7-19.6q-39.213 0-57.743 19.783t-31.359 58.272Q401.059 423.8 367.2 423.8q-19.964 0-33.683-14.079T319.8 379.248q0-33.852 21.739-68.606t63.447-57.562q41.7-22.814 97.3-22.813 51.66 0 91.244 19.069 39.549 19.074 61.119 51.856t21.571 71.286q.004 30.297-12.297 53.109Z'/></svg>\") !default;\n\n//// SVG used several times\n$svg-as-custom-props: (\n  \"chevron\": $chevron-icon,\n  \"close\":   $cross-icon-stroke,\n  \"check\":   $check-icon,\n  \"success\": $success-icon,\n  \"error\":   $danger-icon\n) !default;\n\n//// Filters\n// see https://codepen.io/sosuke/pen/Pjoqqp\n$invert-filter:         invert(1) !default;\n// fusv-disable\n$orange-filter:         invert(46%) sepia(60%) saturate(2878%) hue-rotate(6deg) brightness(98%) contrast(104%) !default; // Deprecated in v5.3.3\n// fusv-enable\n// End mod\n\n// Options\n//\n// Quickly modify global styling by enabling or disabling optional features.\n\n$enable-caret:                true !default;\n$enable-rounded:              false !default;\n$enable-shadows:              false !default;\n$enable-gradients:            false !default;\n$enable-transitions:          true !default;\n$enable-reduced-motion:       true !default;\n$enable-smooth-scroll:        true !default;\n$enable-grid-classes:         true !default;\n$enable-container-classes:    true !default;\n$enable-cssgrid:              false !default;\n$enable-button-pointers:      true !default;\n$enable-rfs:                  false !default;\n$enable-validation-icons:     true !default;\n$enable-negative-margins:     false !default;\n$enable-deprecation-messages: false !default;\n$enable-important-utilities:  true !default;\n$enable-fixed-header:         true !default; // Boosted mod: used to apply scroll-padding-top\n\n$enable-dark-mode:            true !default;\n$color-mode-type:             data !default; // `data` or `media-query`\n\n// Prefix for :root CSS variables\n\n$variable-prefix:             bs- !default; // Deprecated in v5.2.0 for the shorter `$prefix`\n$prefix:                      $variable-prefix !default;\n// fusv-disable\n$boosted-variable-prefix:     o- !default; // Deprecated in v5.2.0 for the shorter `$prefix`\n$boosted-prefix:              $boosted-variable-prefix !default; // Deprecated in v5.3.0 for the shorter `$prefix`\n// fusv-enable\n\n// Gradient\n//\n// The gradient which is added to components if `$enable-gradients` is `true`\n// This gradient is also added to elements with `.bg-gradient`\n// scss-docs-start variable-gradient\n$gradient: linear-gradient(180deg, rgba($white, .15), rgba($white, 0)) !default;\n// scss-docs-end variable-gradient\n\n// Spacing\n//\n// Control the default styling of most Boosted elements by modifying these\n// variables. Mostly focused on spacing.\n// You can add more entries to the $spacers map, should you need more variation.\n\n// scss-docs-start spacer-variables-maps\n$spacer: 1.25rem !default; // Boosted mod\n$spacers: (\n  0: 0,\n  1: $spacer * .25,\n  2: $spacer * .5,\n  3: $spacer,\n  4: $spacer * 1.5,\n  5: $spacer * 3,\n) !default;\n// scss-docs-end spacer-variables-maps\n\n$target-size: 2.75rem !default; // Boosted mod: minimum target size (44×44px)\n\n\n// Position\n//\n// Define the edge positioning anchors of the position utilities.\n\n// scss-docs-start position-map\n$position-values: (\n  0: 0,\n  50: 50%,\n  100: 100%\n) !default;\n// scss-docs-end position-map\n\n// Body\n//\n// Settings for the `<body>` element.\n\n$body-text-align:           null !default;\n$body-color:                $black !default; // Boosted mod: instead of `$gray-900`\n$body-bg:                   $white !default;\n\n$body-secondary-color:      $gray-700 !default; // Boosted mod: instead of `rgba($body-color, .75)`\n$body-secondary-bg:         $gray-300 !default; // Boosted mod: instead of `$gray-200`\n\n$body-tertiary-color:       $gray-500 !default; // Boosted mod: instead of `rgba($body-color, .5)`\n$body-tertiary-bg:          $gray-100 !default;\n\n$body-emphasis-color:       $black !default;\n\n// Links\n//\n// Style anchor elements.\n\n$link-color:                              $black !default; // Boosted mod\n$link-decoration:                         underline !default;\n$link-shade-percentage:                   20% !default;\n$link-hover-color:                        $primary !default; // Boosted mod\n$link-hover-decoration:                   null !default;\n\n$stretched-link-pseudo-element:           after !default;\n$stretched-link-z-index:                  1 !default;\n\n// Boosted mod\n$linked-chevron-icon-width:   subtract(.5rem, 1px) !default;\n$linked-chevron-icon-height:  $spacer * .5 !default;\n$linked-chevron-transform:    rotate(.5turn) translateY(1px) !default;\n$linked-chevron-margin-left:  $spacer * .25 !default;\n// End mod\n\n// Icon links\n// scss-docs-start icon-link-variables\n$icon-link-gap:               .3125rem !default; // Boosted mod: instead of `.375rem`\n$icon-link-underline-offset:  .25em !default;\n$icon-link-icon-size:         1em !default;\n$icon-link-icon-transition:   .2s ease-in-out transform !default;\n$icon-link-icon-transform:    translate3d(.25em, 0, 0) !default;\n// scss-docs-end icon-link-variables\n\n\n// Paragraphs\n//\n// Style p element.\n\n$paragraph-margin-bottom:   1rem !default;\n\n\n// Grid breakpoints\n//\n// Define the minimum dimensions at which your layout will change,\n// adapting to different screen sizes, for use in media queries.\n\n// scss-docs-start grid-breakpoints\n$grid-breakpoints: (\n  xs: 0,\n  sm: 480px,\n  md: 768px,\n  lg: 1024px,\n  xl: 1280px,\n  xxl: 1440px\n) !default;\n// scss-docs-end grid-breakpoints\n\n@include _assert-ascending($grid-breakpoints, \"$grid-breakpoints\");\n@include _assert-starts-at-zero($grid-breakpoints, \"$grid-breakpoints\");\n\n\n// Grid containers\n//\n// Define the maximum width of `.container` for different screen sizes.\n\n// scss-docs-start container-max-widths\n$container-max-widths: (\n  xs: 312px,\n  sm: 468px,\n  md: 744px,\n  lg: 960px,\n  xl: 1200px,\n  xxl: 1320px\n) !default;\n\n// Boosted mod\n$container-fluid-margin: (\n  xs: 4px,\n  sm: 6px,\n  md: 12px,\n  lg: 32px,\n  xl: 40px,\n  xxl: 60px\n) !default;\n// End mod\n// scss-docs-end container-max-widths\n\n@include _assert-ascending($container-max-widths, \"$container-max-widths\");\n\n\n// Grid columns\n//\n// Set the number of columns and specify the width of the gutters.\n\n$grid-columns:                12 !default;\n$grid-gutter-width:           $spacer !default;\n$grid-gutter-breakpoint:      \"md\" !default; // Boosted mod: gutter depends on breakpoint\n$grid-row-columns:            6 !default;\n\n// Container padding\n\n$container-padding-x: $grid-gutter-width !default;\n\n\n// Components\n//\n// Define common padding and border radius sizes and more.\n\n// scss-docs-start border-variables\n$border-width:                .125rem !default;\n$border-widths: (\n  1: $border-width * .5,\n  2: $border-width,\n  3: $border-width * 1.5,\n  4: $border-width * 2,\n  5: $border-width * 2.5\n) !default;\n$border-style:                solid !default;\n$border-color:                $black !default; // Boosted mod: instead of `$gray-300`\n$border-color-subtle:         $gray-500 !default; // Boosted mod\n$border-color-translucent:    rgba($black, .175) !default;\n// scss-docs-end border-variables\n\n// scss-docs-start border-radius-variables\n$border-radius:               .375rem !default;\n$border-radius-sm:            .25rem !default;\n$border-radius-lg:            .5rem !default;\n$border-radius-xl:            1rem !default;\n$border-radius-xxl:           2rem !default;\n$border-radius-pill:          50rem !default;\n// scss-docs-end border-radius-variables\n// fusv-disable\n$border-radius-2xl:           $border-radius-xxl !default; // Deprecated in v5.3.0\n// fusv-enable\n\n// fusv-disable\n$outline-width:               var(--#{$prefix}border-width) !default; // Deprecated in v5.2.3\n$outline-offset:              $outline-width !default; // Deprecated in v5.2.3\n// fusv-enable\n\n// scss-docs-start focus-visible-variables\n$focus-visible-zindex:                5 !default; // Boosted mod\n\n$focus-visible-inner-width:           2px !default; // Boosted mod\n$focus-visible-inner-color:           $white !default; // Boosted mod\n\n$focus-visible-outer-width:           3px !default;  // Boosted mod\n$focus-visible-outer-offset:          $focus-visible-inner-width !default; // Boosted mod\n$focus-visible-outer-color:           $black !default; // Boosted mod\n// scss-docs-end focus-visible-variables\n\n// scss-docs-start box-shadow-variables\n$box-shadow:        null !default; // Boosted mod: instead of `0 .5rem 1rem rgba($black, .15)`\n$box-shadow-sm:     null !default; // Boosted mod: instead of `0 .125rem .25rem rgba($black, .075)`\n$box-shadow-lg:     null !default; // Boosted mod: instead of `0 1rem 3rem rgba($black, .175)`\n$box-shadow-inset:  null !default; // Boosted mod: instead of `inset 0 1px 2px rgba($black, .075)`\n// scss-docs-end box-shadow-variables\n\n$component-active-color:      $black !default;\n$component-active-bg:         $supporting-orange !default;\n$disabled-color:              var(--#{$prefix}tertiary-color) !default; // Boosted mod\n$tertiary-active-bg:          $gray-400 !default; // Boosted mod\n\n// scss-docs-start focus-ring-variables\n$focus-ring-width:      .25rem !default;\n$focus-ring-opacity:    .25 !default;\n$focus-ring-color:      rgba($primary, $focus-ring-opacity) !default;\n// Boosted mod: no `$focus-ring-blur`\n$focus-ring-box-shadow: null !default; // Boosted mod: instead of `0 0 $focus-ring-blur $focus-ring-width $focus-ring-color`\n// scss-docs-end focus-ring-variables\n\n// scss-docs-start caret-variables\n$caret-width:                 add($spacer * .25, var(--#{$prefix}border-width)) !default;\n$caret-vertical-align:        center !default;\n$caret-spacing:               $spacer * .5 !default;\n// scss-docs-end caret-variables\n\n$transition-duration: .2s !default; // Boosted mod\n$transition-timing:   ease-in-out !default; // Boosted mod\n$transition-base:     all $transition-duration $transition-timing !default;\n$transition-fade:     opacity $transition-timing linear !default;\n// scss-docs-start collapse-transition\n$transition-collapse:         height .35s ease !default;\n$transition-collapse-width:   width .35s ease !default;\n// scss-docs-end collapse-transition\n$transition-focus:    null !default; // Boosted mod\n\n// stylelint-disable function-disallowed-list\n// scss-docs-start aspect-ratios\n$aspect-ratios: (\n  \"1x1\": 100%,\n  \"4x3\": calc(3 / 4 * 100%),\n  \"16x9\": calc(9 / 16 * 100%),\n  \"21x9\": calc(9 / 21 * 100%),\n  \"9x16\": calc(16 / 9 * 100%) // Boosted mod: additional ratio for portrait videos\n) !default;\n// scss-docs-end aspect-ratios\n// stylelint-enable function-disallowed-list\n\n// Typography\n//\n// Font, line-height, and color for body text, headings, and more.\n\n// scss-docs-start font-variables\n// stylelint-disable value-keyword-case\n$font-family-sans-serif:      HelvNeueOrange#{\"/*rtl:insert:Arabic*/\"}, \"Helvetica Neue\", Helvetica, \"Noto Sans\", \"Liberation Sans\", Arial, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\" !default;\n$font-family-monospace:       SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace !default;\n// stylelint-enable value-keyword-case\n$font-family-base:            var(--#{$prefix}font-sans-serif) !default;\n$font-family-code:            var(--#{$prefix}font-monospace) !default;\n\n\n// Boosted mod\n//// Type scale & vertical rhythm completely revamped to match Orange Web Guidelines\n\n// $font-size-root affects the value of `rem`, which is used for as well font sizes, paddings and margins\n// $font-size-base affects the font size of the body text\n$font-size-root:              null !default;\n$font-size-base:              1rem !default; // Assumes the browser default, typically `16px`\n$font-size-sm:                $font-size-base * .875 !default;  // 14px\n$font-size-lg:                $font-size-base * 1.125 !default; // 18px\n$font-size-xlg:               $font-size-base * 1.25 !default;  // 20px\n\n$font-weight-lighter:         null !default;\n$font-weight-light:           null !default;\n$font-weight-normal:          400 !default;\n$font-weight-medium:          500 !default;\n$font-weight-semibold:        600 !default;\n$font-weight-bold:            700 !default;\n$font-weight-bolder:          null !default;\n\n$font-weight-base:            $font-weight-normal !default;\n\n// stylelint-disable function-disallowed-list\n$line-height-base:            calc(18 / 16) !default;\n$line-height-sm:              calc(16 / 14) !default;\n$line-height-lg:              calc(30 / 16) !default;\n// stylelint-enable function-disallowed-list\n\n$line-length-sm:              40ch !default;\n$line-length-md:              80ch !default;\n\n$letter-spacing-base:         $spacer * -.005 !default; // -0.1px\n\n$h1-font-size:                $font-size-base * 2.125 !default;   // 34px\n$h2-font-size:                $font-size-base * 1.875 !default;   // 30px\n$h3-font-size:                $font-size-base * 1.5 !default;     // 24px\n$h4-font-size:                $font-size-xlg !default;            // 20px\n$h5-font-size:                $font-size-lg !default;             // 18px\n$h6-font-size:                $font-size-base !default;           // 16px\n// scss-docs-end font-variables\n\n// scss-docs-start font-sizes\n$font-sizes: (\n  1: $h1-font-size,\n  2: $h2-font-size,\n  3: $h3-font-size,\n  4: $h4-font-size,\n  5: $h5-font-size,\n  6: $h6-font-size\n) !default;\n// scss-docs-end font-sizes\n\n// scss-docs-start letter-spacing\n$h1-spacing:                  $letter-spacing-base * 10 !default; // -1px\n$h2-spacing:                  $letter-spacing-base * 8 !default;  // -0.8px\n$mid-spacing:                 $letter-spacing-base * 6 !default;  // -0.6px\n$h3-spacing:                  $letter-spacing-base * 5 !default;  // -0.5px\n$h4-spacing:                  $letter-spacing-base * 4 !default;  // -0.4px\n$h5-spacing:                  $letter-spacing-base * 2 !default;  // -0.2px\n$h6-spacing:                  $letter-spacing-base !default;\n// scss-docs-end letter-spacing\n\n// stylelint-disable function-disallowed-list\n// scss-docs-start line-height\n$h1-line-height:              1 !default;\n$h2-line-height:              calc(32 / 30) !default;\n$h3-line-height:              calc(26 / 24) !default;\n$h4-line-height:              calc(22 / 20) !default;\n$h5-line-height:              calc(20 / 18) !default;\n$h6-line-height:              $line-height-base !default;\n// scss-docs-end line-height\n// stylelint-enable function-disallowed-list\n\n// scss-docs-start headings-variables\n$headings-margin-bottom:      $spacer !default; // Boosted mod\n$headings-font-family:        null !default;\n$headings-font-style:         null !default;\n$headings-font-weight:        700 !default;\n$headings-line-height:        $h6-line-height !default;\n$headings-color:              inherit !default;\n// scss-docs-end headings-variables\n\n// scss-docs-start display-headings\n$display1-size:               $font-size-xlg * 3 !default;        // 60px\n$display2-size:               $font-size-xlg * 2.5 !default;      // 50px\n$display3-size:               $font-size-xlg * 2 !default;        // 40px\n$display4-size:               $h1-font-size !default;             // 34px\n$display1-spacing:            $letter-spacing-base * 20 !default; // -2px\n$display2-spacing:            $letter-spacing-base * 16 !default; // -1.6px\n$display3-spacing:            $h1-spacing !default;               // -1px\n$display4-spacing:            $h1-spacing !default;               // -1px\n$display-line-height:         $h1-line-height !default;\n// scss-docs-end display-headings\n\n// scss-docs-start type-variables\n$lead-font-size:              $font-size-xlg !default;\n$lead-font-weight:            400 !default;\n$lead-line-height:            1.5 !default;\n$lead-letter-spacing:         $letter-spacing-base * 4 !default;\n\n$small-font-size:             .875rem !default;\n\n$sub-sup-font-size:           .75em !default;\n\n// fusv-disable\n$text-muted:                  var(--#{$prefix}secondary-color) !default; // Deprecated in 5.3.0\n// fusv-enable\n\n$initialism-font-size:        $small-font-size !default;\n\n$blockquote-margin-y:         $spacer !default;\n$blockquote-font-size:        $font-size-xlg !default;\n$blockquote-footer-color:     var(--#{$prefix}secondary-color) !default; // Boosted mod: instead of `$gray-600`\n$blockquote-footer-font-size: $small-font-size !default;\n$blockquote-line-height:      1.5 !default; // Boosted mod\n$blockquote-letter-spacing:   $letter-spacing-base * .25 !default; // Boosted mod\n\n$hr-margin-y:                 $spacer !default;\n$hr-color:                    inherit !default;\n\n// fusv-disable\n$hr-bg-color:                 null !default; // Deprecated in v5.2.0\n$hr-height:                   null !default; // Deprecated in v5.2.0\n// fusv-enable\n\n$hr-border-color:             null !default; // Allows for inherited colors\n$hr-border-width:             var(--#{$prefix}border-width) !default;\n$hr-opacity:                  null !default;\n\n// scss-docs-start vr-variables\n$vr-border-width:             2px !default; // Boosted mod: instead of `var(--#{$prefix}border-width)`\n// scss-docs-end vr-variables\n\n$legend-margin-bottom:        $spacer * .25 !default;\n$legend-font-size:            $font-size-xlg !default;\n$legend-font-weight:          $font-weight-bold !default;\n\n$dt-font-weight:              $font-weight-bold !default;\n\n$list-inline-padding:         $spacer * .25 !default;\n\n$mark-padding:                0 .1875em !default; // Boosted mod\n$mark-color:                  $white !default; // Boosted mod: instead of `$body-color`\n$mark-bg:                     $black !default; // Boosted mod: instead of `$yellow-100`\n// scss-docs-end type-variables\n// End mod\n\n// Tables\n//\n// Customizes the `.table` component with basic values, each used across all table variations.\n\n// scss-docs-start table-variables\n$table-cell-padding-y:                  .875rem !default; // Boosted mod\n$table-cell-padding-x:                  $spacer * .5 !default; // Boosted mod\n$table-cell-padding-y-sm:               .5625rem !default; // Boosted mod\n$table-cell-padding-x-sm:               $table-cell-padding-x !default; // Boosted mod\n\n$table-cell-icon-margin-top:            -.75rem !default; // Boosted mod\n$table-cell-icon-margin-bottom:         -.625rem !default; // Boosted mod\n$table-cell-vertical-align:             top !default;\n$table-line-height:                     1.25 !default; // Boosted mod\n\n$table-color:                           var(--#{$prefix}emphasis-color) !default;\n$table-bg:                              var(--#{$prefix}body-bg) !default;\n$table-accent-bg:                       transparent !default;\n\n$table-th-font-weight:                  null !default;\n\n$table-striped-color:                   $table-color !default;\n$table-striped-bg-factor:               .035 !default; // Boosted mod: equivalent to `$gray-200`\n$table-striped-bg:                      rgba(var(--#{$prefix}black-rgb), var(--#{$prefix}table-striped-bg-factor)) !default; // Boosted mod: instead of `rgba(var(--#{$prefix}emphasis-color-rgb), $table-striped-bg-factor)`\n$table-variant-striped-bg-factor:       .08 !default; // Boosted mod\n\n$table-active-color:                    $table-color !default;\n$table-active-bg-factor:                .135 !default; // Boosted mod\n$table-active-bg:                       rgba(var(--#{$prefix}emphasis-color-rgb), var(--#{$prefix}table-active-bg-factor)) !default; // Boosted mod: instead of `rgba(var(--#{$prefix}emphasis-color-rgb), $table-active-bg-factor)`\n$table-variant-active-bg-factor:        .4 !default; // Boosted mod\n\n$table-hover-color:                     $table-color !default;\n$table-hover-bg-factor:                 .065 !default; // Boosted mod\n$table-hover-bg:                        rgba(var(--#{$prefix}emphasis-color-rgb), var(--#{$prefix}table-hover-bg-factor)) !default; // Boosted mod: instead of `rgba(var(--#{$prefix}emphasis-color-rgb), $table-hover-bg-factor)`\n$table-variant-hover-bg-factor:         .2 !default; // Boosted mod\n\n$table-border-factor:                   .4 !default; // Boosted mod\n// stylelint-disable-next-line function-disallowed-list\n$table-border-width:                    calc(var(--#{$prefix}border-width) * .5) !default;  // Boosted mod\n$table-border-color:                    var(--#{$prefix}border-color-subtle) !default; // Boosted mod: instead of `var(--#{$prefix}border-color)`\n\n$table-striped-order:                   odd !default;\n$table-striped-columns-order:           even !default;\n\n$table-group-separator-color:           currentcolor !default;\n\n$table-caption-color:                   var(--#{$prefix}caption-color, var(--#{$prefix}emphasis-color)) !default; // Boosted mod: instead of `var(--#{$prefix}secondary-color)`\n$table-caption-padding-y:               .75rem !default; // Boosted mod\n\n$table-bg-scale:                        -60% !default;\n// scss-docs-end table-variables\n\n// scss-docs-start table-loop\n$table-variants: (\n  \"primary\":    shift-color($primary, $table-bg-scale),\n  \"secondary\":  shift-color($secondary, $table-bg-scale),\n  \"success\":    shift-color($success, $table-bg-scale),\n  \"info\":       shift-color($info, $table-bg-scale),\n  \"warning\":    shift-color($warning, $table-bg-scale),\n  \"danger\":     shift-color($danger, $table-bg-scale),\n  \"light\":      $light,\n  \"dark\":       $dark,\n) !default;\n// scss-docs-end table-loop\n\n// Buttons + Forms\n//\n// Shared variables that are reassigned to `$input-` and `$btn-` specific variables.\n\n// scss-docs-start input-btn-variables\n$input-btn-padding-y:         .5rem !default;\n$input-btn-padding-x:         1.125rem !default;\n$input-btn-font-family:       inherit !default;\n$input-btn-font-size:         $font-size-base !default;\n$input-btn-line-height:       1.25 !default;\n\n$input-btn-focus-width:       $focus-visible-outer-offset !default; // Boosted mod: instead of `$focus-ring-width`\n// Boosted mod: no `$input-btn-focus-color-opacity`\n// Boosted mod: no `$input-btn-focus-color`\n// Boosted mod: no `$input-btn-focus-blur`\n$input-btn-focus-box-shadow:  $focus-ring-box-shadow !default;\n\n$input-btn-padding-y-sm:      $spacer * .25 !default;\n$input-btn-padding-x-sm:      $spacer * .5 !default;\n$input-btn-font-size-sm:      $font-size-sm !default;\n\n$input-btn-padding-y-lg:      .8125rem !default;\n$input-btn-padding-x-lg:      $spacer !default;\n$input-btn-font-size-lg:      $font-size-lg !default;\n\n$input-btn-border-width:      var(--#{$prefix}border-width) !default;\n// scss-docs-end input-btn-variables\n\n// Buttons\n//\n// For each of Boosted's buttons, define text, background, and border color.\n\n// scss-docs-start btn-variables\n$btn-color:                   var(--#{$prefix}body-color) !default;\n$btn-hover-color:             $btn-color !default; // Boosted mod\n$btn-padding-y:               $input-btn-padding-y !default;\n$btn-padding-x:               $input-btn-padding-x !default;\n$btn-font-family:             $input-btn-font-family !default;\n$btn-font-size:               $input-btn-font-size !default;\n$btn-line-height:             $input-btn-line-height !default;\n$btn-letter-spacing:          $letter-spacing-base !default; // Boosted mod\n$btn-white-space:             null !default; // Set to `nowrap` to prevent text wrapping\n\n$btn-padding-y-sm:            $input-btn-padding-y-sm !default;\n$btn-padding-x-sm:            $input-btn-padding-x-sm !default;\n$btn-font-size-sm:            $input-btn-font-size-sm !default;\n$btn-line-height-sm:          $line-height-sm !default; // Boosted mod\n$btn-letter-spacing-sm:       $letter-spacing-base !default; // Boosted mod\n\n$btn-padding-y-lg:            $input-btn-padding-y-lg !default;\n$btn-padding-x-lg:            $input-btn-padding-x-lg !default;\n$btn-font-size-lg:            $input-btn-font-size-lg !default;\n$btn-line-height-lg:          $h5-line-height !default; // Boosted mod\n$btn-letter-spacing-lg:       $letter-spacing-base * 2 !default; // Boosted mod\n\n$btn-border-width:            $input-btn-border-width !default;\n\n$btn-default-hover-bg:        var(--#{$prefix}highlight-bg) !default; // Boosted mod\n$btn-default-hover-border:    var(--#{$prefix}border-color) !default; // Boosted mod\n$btn-default-hover-color:     var(--#{$prefix}highlight-color) !default; // Boosted mod\n$btn-default-active-bg:       $supporting-orange !default; // Boosted mod\n$btn-default-active-border:   $supporting-orange !default; // Boosted mod\n$btn-default-active-color:    $black !default; // Boosted mod\n$btn-default-disabled-bg:     var(--#{$prefix}disabled-color) !default; // Boosted mod\n$btn-default-disabled-border: var(--#{$prefix}disabled-color) !default; // Boosted mod\n$btn-default-disabled-color:  var(--#{$prefix}highlight-color) !default; // Boosted mod\n\n$btn-outline-default-hover-bg:        var(--#{$prefix}btn-color) !default; // Boosted mod\n$btn-outline-default-hover-border:    var(--#{$prefix}btn-border-color) !default; // Boosted mod\n$btn-outline-default-hover-color:     $white !default; // Boosted mod\n$btn-outline-default-active-bg:       $supporting-orange !default; // Boosted mod\n$btn-outline-default-active-border:   $supporting-orange !default; // Boosted mod\n$btn-outline-default-active-color:    $black !default; // Boosted mod\n$btn-outline-default-disabled-bg:     transparent !default; // Boosted mod\n$btn-outline-default-disabled-border: var(--#{$prefix}disabled-color) !default; // Boosted mod\n$btn-outline-default-disabled-color:  var(--#{$prefix}disabled-color) !default; // Boosted mod\n\n$btn-font-weight:             $font-weight-bold !default;\n$btn-box-shadow:              null !default;\n$btn-focus-width:             $input-btn-focus-width !default;\n$btn-focus-box-shadow:        0 0 0 $btn-focus-width $white !default;\n$btn-disabled-opacity:        1 !default;\n$btn-active-box-shadow:       null !default;\n\n$btn-link-color:              var(--#{$prefix}link-color) !default;\n$btn-link-hover-color:        var(--#{$prefix}link-hover-color) !default;\n$btn-link-disabled-color:     var(--#{$prefix}disabled-color) !default; // Boosted mod: instead of `$gray-600`\n// Boosted mod: no `$btn-link-focus-shadow-rgb`\n\n// Allows for customizing button radius independently from global border radius\n$btn-border-radius:           var(--#{$prefix}border-radius) !default;\n$btn-border-radius-sm:        var(--#{$prefix}border-radius-sm) !default;\n$btn-border-radius-lg:        var(--#{$prefix}border-radius-lg) !default;\n\n$btn-transition:              $transition-focus !default; // Boosted mod\n// scss-docs-end btn-variables\n\n// Boosted mod: icon button\n$btn-icon-padding-x:          subtract($spacer * .5, var(--#{$prefix}border-width)) !default;\n$btn-icon-padding-x-sm:       $spacer * .25 !default;\n$btn-icon-padding-x-lg:       add($spacer * .5, calc(var(--#{$prefix}border-width) * 1.5)) !default; // stylelint-disable-line function-disallowed-list\n// Boosted mod: social button\n// scss-docs-start social-buttons\n$btn-social-networks: (\n  \"facebook\": (\n    \"color\": #3b5998,\n    \"icon\": \"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'><path d='M19 6h5V0h-5c-4 0-7 3-7 7v3H8v6h4v16h6V16h5l1-6h-6V7l1-1z'></path></svg>\"\n  ),\n  \"twitter\": (\n    \"color\": #1da1f2,\n    \"icon\": \"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'><path d='M32 7a13 13 0 01-3.8 1.1 6.6 6.6 0 003-3.6c-1.4.7-2.8 1.3-4.3 1.6a6.6 6.6 0 00-11.1 6A18.6 18.6 0 012.2 5a6.6 6.6 0 002 8.9c-1 0-2-.4-3-.9v.1c0 3.2 2.4 5.9 5.4 6.5a6.6 6.6 0 01-3 0 6.6 6.6 0 006.1 4.6A13.2 13.2 0 010 27.1a18.6 18.6 0 0028.7-16.6C30 9.5 31.1 8.4 32 7z'/></svg>\"\n  ),\n  \"instagram\": (\n    \"color\": #e1306c,\n    \"icon\": \"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'><path d='M16 2.9h6.5c1.5.1 2.4.4 3 .6a5 5 0 011.8 1.2c.5.6.9 1.1 1.2 1.9.2.5.4 1.4.5 3a112.7 112.7 0 01-.5 15.8 5 5 0 01-1.2 1.9c-.6.5-1.1.9-1.9 1.2-.5.2-1.4.4-3 .5a112.7 112.7 0 01-15.8-.5 5 5 0 01-1.9-1.2 5 5 0 01-1.2-1.9c-.2-.5-.4-1.4-.5-3a112.7 112.7 0 01.5-15.8 5 5 0 011.2-1.9c.6-.5 1.1-.9 1.9-1.2C7 3.3 8 3 9.6 3l6.4-.1zM16 0H9.4C7.7.3 6.5.5 5.5.9s-2 1-2.8 1.9c-1 .9-1.5 1.8-1.9 2.8-.4 1-.6 2.2-.7 3.9a117.6 117.6 0 00.7 17c.5 1.1 1 2 1.9 3 .9.8 1.8 1.4 2.8 1.8 1 .4 2.2.6 3.9.7a117.2 117.2 0 0017-.7c1.1-.4 2-1 2.9-1.9s1.4-1.8 1.8-2.8c.4-1 .7-2.2.8-3.9a117.2 117.2 0 00-.8-17A7.8 7.8 0 0026.4.8c-1-.5-2.1-.7-3.8-.8L16 0z'/><path d='M16 7.8a8.2 8.2 0 100 16.4 8.2 8.2 0 000-16.4zm0 13.5a5.3 5.3 0 110-10.6 5.3 5.3 0 010 10.6zM26.5 7.5a2 2 0 11-3.9 0 2 2 0 013.9 0z'/></svg>\"\n  ),\n  \"youtube\": (\n    \"color\": #f00,\n    \"icon\": \"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'><path d='M31.7 9.6s-.3-2.2-1.3-3.2c-1.2-1.3-2.6-1.3-3.2-1.3-4.5-.4-11.2-.4-11.2-.4s-6.7 0-11.2.4c-.6 0-2 0-3.2 1.3C.6 7.4.3 9.6.3 9.6S0 12.2 0 14.8v2.4c0 2.6.3 5.2.3 5.2s.3 2.2 1.3 3.2c1.2 1.2 2.8 1.2 3.5 1.3 2.6.3 11 .4 11 .4s6.6 0 11.1-.4c.6 0 2 0 3.2-1.3 1-1 1.3-3.2 1.3-3.2s.3-2.6.3-5.2v-2.4c0-2.6-.3-5.2-.3-5.2zm-19 10.5v-9l8.6 4.6-8.6 4.4z'/></svg>\"\n  ),\n  \"linkedin\": (\n    \"color\": #0077b5,\n    \"icon\": \"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'><path d='M12 12h5.5v2.8h.1a6.1 6.1 0 015.5-2.8c5.8 0 6.9 3.6 6.9 8.4V30h-5.8v-8.5c0-2 0-4.7-3-4.7s-3.4 2.2-3.4 4.5V30H12V12zM2 12h6v18H2V12zm6-5a3 3 0 11-6 0 3 3 0 016 0z'/></svg>\",\n  ),\n  \"whatsapp\": (\n    \"color\": #25d366,\n    \"icon\": \"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'><path d='M27.3 4.7a15.9 15.9 0 00-25 19.1L.1 32l8.4-2.2A15.9 15.9 0 0027.3 4.7zM16 29c-2.4 0-4.7-.6-6.7-1.8l-.5-.3-5 1.3 1.3-4.8-.3-.5A13.2 13.2 0 1116.1 29zm7.2-9.8l-2.7-1.3c-.3-.1-.6-.2-1 .2l-1.2 1.5c-.2.3-.4.3-.8.1s-1.7-.6-3.2-2c-1.2-1-2-2.3-2.2-2.7s0-.6.2-.8l.6-.7.4-.6v-.7l-1.3-3c-.3-.7-.6-.6-.9-.7h-.7c-.2 0-.7.1-1.1.5C9 9.4 8 10.4 8 12.3s1.4 3.9 1.6 4.1c.2.3 2.8 4.3 6.8 6l2.3.9c.9.3 1.8.2 2.4.1.8-.1 2.4-1 2.7-1.9s.4-1.7.3-1.9l-.8-.4z'/></svg>\"\n  ),\n  \"mail\": (\n    \"color\": $supporting-orange,\n    \"icon\": \"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'><path d='M3.2 14.3c0 9.5 0 9 .2 9.5.3.8 1 1.4 1.7 1.7l12.2.1h11.5v-8.8c0-9.3 0-8.9-.2-9.3-.2-.7-.7-1.2-1.3-1.6l-.8-.3H3.2v8.7zm22.9-2.4a246.2 246.2 0 01-4.9 4.7l-.8.7-.5.6-.7.6c-.6.6-1 .9-1.3 1a4 4 0 01-1.8.5 4 4 0 01-2.4-.6 13 13 0 01-1.9-1.7l-2.4-2.4-.6-.6-1.4-1.3L6.1 12l-.5-.5V8.9l.6.5L7.9 11l1.4 1.4 1.3 1.2 1.3 1.3a195 195 0 012.6 2.4c.4.3 1 .5 1.6.4.5 0 1-.1 1.4-.4L19 16l1-1 1-1a214.7 214.7 0 012.2-2l1-1 2-2 .2-.2v2.8l-.3.3z'/></svg>\",\n    \"size\": 1.5rem\n  ),\n  \"snapchat\": (\n    \"color\": #fffc00,\n    \"icon\": \"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 26 28'><path d='M13 2c3 0 5 2 7 4v6h2l1 1-3 2v1l4 4h1l1 1-4 1-1 2h-2-1c-1 0-2 2-5 2s-4-2-5-2H5l-1-2-4-1 1-1h1l4-4v-1l-3-2 1-1h2V9 6c2-3 4-4 7-4z'/></svg>\"\n  ),\n  \"pinterest\": (\n    \"color\": red,\n    \"icon\": \"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'><path d='M16 2a14 14 0 00-5 27v-4l2-7-1-2c0-2 1-3 3-3l1 2-1 4c0 2 1 3 2 3 3 0 5-3 5-7 0-3-3-5-6-5-4 0-6 3-6 6l1 3a302 302 0 01-1 2c-2-1-3-3-3-5 0-5 3-9 9-9 5 0 9 4 9 8 0 5-3 9-7 9l-4-2v4l-2 3a14 14 0 0018-13c0-8-6-14-14-14z'/></svg>\",\n    \"size\": 1.375rem\n  ),\n  \"tiktok\": (\n    \"color\": #ff2c55,\n    \"icon\": \"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'><path d='M7.024 30.054C4.584 28.212 3 25.235 3 21.876c0-5.59 4.39-10.123 9.805-10.123.45 0 .899.031 1.345.094v5.6a4.363 4.363 0 0 0-1.361-.218c-2.477 0-4.485 2.074-4.485 4.631 0 1.809 1.003 3.374 2.467 4.137l.31.146a4.348 4.348 0 0 0 1.708.348c2.471 0 4.476-2.065 4.484-4.615V0h5.335v.704c.02.211.046.42.082.63l.08.404a7.668 7.668 0 0 0 3.306 4.769A7.22 7.22 0 0 0 30 7.665V8.83l-.199-.047-.182-.047.381.094v4.312a12.4 12.4 0 0 1-7.392-2.443v11.177c0 5.591-4.39 10.124-9.804 10.124-2.02 0-3.898-.63-5.458-1.712l-.322-.234Z'/></svg>\"\n  ),\n  \"x\": (\n    \"color\": #1da1f2,\n    \"icon\": \"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 19'><path d='m15.751 0-5.053 5.776L6.328 0H0l7.561 9.888-7.166 8.19h3.068l5.531-6.32 4.834 6.32H20l-7.883-10.42L18.817 0h-3.066ZM3.581 1.74h1.824l10.97 14.502h-1.7L3.58 1.74Z'/></svg>\"\n  )\n) !default;\n// scss-docs-end social-buttons\n// End mod\n\n// Forms\n\n// scss-docs-start form-text-variables\n$form-text-margin-top:                  .4375rem !default; // Boosted mod\n$form-text-font-size:                   $small-font-size !default;\n$form-text-font-style:                  null !default;\n$form-text-font-weight:                 $font-weight-bold !default; // Boosted mod: instead of `null`\n$form-text-line-height:                 $line-height-sm !default; // Boosted mod\n$form-text-color:                       var(--#{$prefix}secondary-color) !default;\n// scss-docs-end form-text-variables\n\n// scss-docs-start form-label-variables\n$form-label-margin-bottom:              .5rem !default; // Boosted mod\n$form-label-font-size:                  null !default;\n$form-label-font-style:                 null !default;\n$form-label-font-weight:                $font-weight-bold !default;\n$form-label-color:                      null !default;\n$form-label-disabled-color:             var(--#{$prefix}disabled-color) !default; // Boosted mod\n$form-label-required-margin-left:       .1875rem !default; // Boosted mod\n$form-label-required-color:             var(--#{$prefix}primary) !default; // Boosted mod\n// scss-docs-end form-label-variables\n\n// scss-docs-start form-helper-variables\n$form-helper-size:                      1.25rem !default; // Boosted mod\n$form-helper-color:                     var(--#{$prefix}info) !default; // Boosted mod\n$form-helper-bg:                        var(--#{$prefix}highlight-color) !default; // Boosted mod\n$form-helper-icon:                      escape-svg($helper-icon) !default; // Boosted mod\n$form-helper-label-margin-bottom:       $form-label-margin-bottom - divide(($form-helper-size - $font-size-base), 2) !default; // Boosted mod\n// scss-docs-end form-helper-variables\n\n// scss-docs-start form-input-variables\n$input-padding-y:                       $input-btn-padding-y !default;\n$input-padding-x:                       $spacer * .5 !default;\n$input-font-family:                     $input-btn-font-family !default;\n$input-font-size:                       $input-btn-font-size !default;\n$input-font-weight:                     $font-weight-bold !default;\n$input-line-height:                     $input-btn-line-height !default;\n\n$input-padding-y-sm:                    divide($input-padding-y, 2) !default; // Boosted mod\n$input-padding-x-sm:                    $input-btn-padding-x-sm !default;\n$input-font-size-sm:                    $input-btn-font-size-sm !default;\n\n$input-padding-y-lg:                    $input-btn-padding-y-lg !default;\n$input-padding-x-lg:                    $input-btn-padding-x-lg !default;\n$input-font-size-lg:                    $input-btn-font-size-lg !default;\n\n$input-bg:                              var(--#{$prefix}body-bg) !default;\n$input-disabled-color:                  var(--#{$prefix}secondary-color) !default; // Boosted mod\n$input-disabled-bg:                     var(--#{$prefix}secondary-bg) !default;\n$input-disabled-border-color:           null !default;\n\n$input-color:                           var(--#{$prefix}body-color) !default;\n$input-border-color:                    var(--#{$prefix}border-color-subtle) !default; // Boosted mod: instead of var(--#{$prefix}border-color)\n$input-border-width:                    $input-btn-border-width !default;\n$input-box-shadow:                      none !default; // Boosted mod\n\n$input-border-radius:                   var(--#{$prefix}border-radius) !default;\n$input-border-radius-sm:                var(--#{$prefix}border-radius-sm) !default;\n$input-border-radius-lg:                var(--#{$prefix}border-radius-lg) !default;\n\n$input-focus-bg:                        $input-bg !default;\n$input-focus-border-color:              currentcolor !default;\n$input-focus-color:                     $input-color !default;\n$input-focus-width:                     $input-btn-focus-width !default;\n$input-focus-box-shadow:                none !default; // Boosted mod\n\n$input-placeholder-color:               var(--#{$prefix}secondary-color) !default;\n$input-plaintext-color:                 null !default; // Boosted mod: instead of `var(--#{$prefix}body-color)`\n\n// Boosted mod: no $input-height-border\n\n$input-height-inner:                    add($input-line-height * 1em, $input-padding-y * 2) !default;\n$input-height-inner-half:               $spacer !default; // Boosted mod\n$input-height-inner-quarter:            map-get($spacers, 2) !default; // Boosted mod\n\n$input-height:                          2.5rem !default;\n$input-height-sm:                       1.875rem !default;\n$input-height-lg:                       3.125rem !default;\n$input-line-height-lg:                  $h5-line-height !default; // Boosted mod\n\n$input-transition:                      border-color $transition-duration $transition-timing, $transition-focus !default;\n\n$form-color-width:                      2.5rem !default; // Boosted mod: instead of `3rem`\n$form-color-border-color:               var(--#{$prefix}emphasis-color) !default; // Boosted mod\n$form-color-hover-bg-color:             var(--#{$prefix}emphasis-color) !default; // Boosted mod\n$form-color-disabled-bg-color:          $input-bg !default; // Boosted mod\n$form-color-disabled-border-color:      var(--#{$prefix}disabled-color) !default; // Boosted mod\n$form-color-disabled-background-swatch: var(--#{$prefix}form-color-disabled-filter) !default; // Boosted mod\n$form-color-disabled-filter:            brightness(0) invert(1) brightness(.8) !default; // Boosted mod\n// scss-docs-end form-input-variables\n\n// scss-docs-start form-check-variables\n$form-check-input-width:                  1em !default;\n$form-check-min-height:                   $font-size-base * $input-btn-line-height !default;\n$form-check-padding-start:                $form-check-input-width + .5em !default;\n$form-check-margin-bottom:                .125rem !default;\n$form-check-label-padding-top:            .4375rem !default; // Boosted mod\n$form-check-label-color:                  null !default;\n$form-check-label-cursor:                 null !default;\n$form-check-transition:                   null !default;\n$form-check-filter:                       $invert-filter !default; // Boosted mod\n\n$form-check-input-active-filter:          null !default;\n$form-check-input-active-bg-color:        $component-active-bg !default; // Boosted mod\n\n$form-check-input-bg:                     $input-bg !default;\n$form-check-input-border:                 var(--#{$prefix}border-width) solid $input-border-color !default; // Boosted mod: instead of `var(--#{$prefix}border-width) solid var(--#{$prefix}border-color)`\n$form-check-input-border-radius:          0 !default;\n$form-check-radio-border-radius:          50% !default;\n$form-check-input-focus-border:           null !default;\n$form-check-input-focus-box-shadow:       $focus-ring-box-shadow !default;\n\n$form-check-input-checked-color:          $component-active-color !default;\n$form-check-input-checked-bg-color:       $component-active-bg !default;\n$form-check-input-checked-border-color:   $form-check-input-checked-bg-color !default;\n$form-check-input-checked-bg-image:       var(--#{$prefix}check-icon) !default;\n$form-check-input-disabled-color:         $gray-900 !default; // Boosted mod\n$form-check-input-disabled-filter:        var(--#{$prefix}form-check-filter) !default; // Boosted mod\n$form-check-radio-checked-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='2' fill='#{$form-check-input-checked-color}'/></svg>\") !default;\n\n$form-check-input-indeterminate-color:          $form-check-input-checked-color !default;\n$form-check-input-indeterminate-bg-color:       $form-check-input-checked-bg-color !default;\n$form-check-input-indeterminate-border-color:   $form-check-input-indeterminate-bg-color !default;\n$form-check-input-indeterminate-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 10 3'><path fill='#{$form-check-input-indeterminate-color}' d='M0 0h10v3H0z'/></svg>\") !default;\n\n$form-check-input-disabled-opacity:        null !default;\n$form-check-label-disabled-opacity:        null !default;\n$form-check-btn-check-disabled-opacity:    null !default;\n\n$form-check-inline-margin-end:    1rem !default;\n\n// Boosted mod: Star rating\n$form-star-size:                        1.5625rem !default;\n$form-star-size-sm:                     1.25rem !default;\n$form-star-margin-between:              -.125rem !default;\n\n$form-star-rating-checked-color:        var(--#{$prefix}primary) !default;\n$form-star-rating-unchecked-color:      var(--#{$prefix}secondary-color) !default;\n$form-star-rating-hover-color:          var(--#{$prefix}highlight-bg) !default;\n$form-star-rating-disabled-color:       var(--#{$prefix}disabled-color) !default;\n\n$form-star-rating-checked-icon:         escape-svg(url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 25 25'><path fill='#{$black}' stroke='#{$black}' d='m12.5 4.523 2.016 6.227 6.542-.005-5.296 3.843 2.027 6.224L12.5 16.96l-5.289 3.852 2.027-6.224-5.296-3.843 6.542.005L12.5 4.523Z'/></svg>\")) !default;\n$form-star-rating-unchecked-icon:       escape-svg(url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 25 25'><path fill='transparent' stroke='#{$black}' d='m12.5 4.523 2.016 6.227 6.542-.005-5.296 3.843 2.027 6.224L12.5 16.96l-5.289 3.852 2.027-6.224-5.296-3.843 6.542.005L12.5 4.523Z'/></svg>\")) !default;\n$form-star-rating-sm-checked-icon:      escape-svg(url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='#{$black}' stroke='#{$black}' d='M10 3.943 11.54 8.7l4.998-.004-4.046 2.936 1.548 4.755L10 13.444l-4.04 2.943 1.548-4.755-4.046-2.936L8.46 8.7 10 3.943Z'/></svg>\")) !default;\n$form-star-rating-sm-unchecked-icon:    escape-svg(url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='transparent' stroke='#{$black}' d='M10 3.943 11.54 8.7l4.998-.004-4.046 2.936 1.548 4.755L10 13.444l-4.04 2.943 1.548-4.755-4.046-2.936L8.46 8.7 10 3.943Z'/></svg>\")) !default;\n//fusv-disable\n$form-star-focus-color:                 $black !default; // Deprecated in v5.2.3\n$form-star-focus-outline:               var(--#{$prefix}border-width) solid $form-star-focus-color !default; // Deprecated in v5.2.3\n$form-star-focus-color-dark:            $white !default; // Deprecated in v5.2.3\n$form-star-focus-outline-dark:          var(--#{$prefix}border-width) solid $form-star-focus-color-dark !default; // Deprecated in v5.2.3\n$form-star-focus-box-shadow:            $input-btn-focus-box-shadow !default; // Deprecated in v5.2.3\n//fusv-enable\n\n// End mod\n// scss-docs-end form-check-variables\n\n// scss-docs-start form-switch-variables\n// Boosted mod: no $form-switch-color\n$form-switch-width:               $spacer * 3 !default; // Boosted mod\n$form-switch-padding-start:       $form-switch-width + .625rem !default; // Boosted mod\n$form-switch-bg-image:            var(--#{$prefix}close-icon) !default;  // Boosted mod\n$form-switch-bg-position:         right .5rem top 50% !default;  // Boosted mod\n$form-switch-bg-size:             .75rem !default;  // Boosted mod\n$form-switch-bg-square-size:      add(1rem, $spacer * .5) !default;  // Boosted mod\n$form-switch-border-radius:       null !default; // Boosted mod\n$form-switch-transition:          background-position .15s ease-in-out, $transition-focus !default; // Boosted mod\n\n$form-switch-square-bg:             $black !default; // Boosted mod\n$form-switch-bg:                    $white !default; // Boosted mod\n$form-switch-border-color:          $white !default; // Boosted mod\n$form-switch-filter:                var(--#{$prefix}form-check-filter) !default; // Boosted mod\n$form-switch-focus-visible-inner:   $black !default; // Boosted mod\n$form-switch-focus-visible-outer:   $white !default; // Boosted mod\n\n// Boosted mod: no $form-switch-focus-color\n// Boosted mod: no $form-switch-focus-bg-image\n\n// Boosted mod: no $form-switch-checked-color\n$form-switch-checked-bg-image:    $form-check-input-checked-bg-image !default; // Boosted mod\n$form-switch-checked-bg-size:     add(map-get($spacers, 2), map-get($spacers, 1)) !default; // Boosted mod\n// stylelint-disable-next-line function-disallowed-list\n$form-switch-checked-bg-position: calc(var(--#{$prefix}border-width) * 3) 50% !default; // Boosted mod\n\n$form-switch-checked-square-bg:                 var(--#{$prefix}body-bg) !default; // Boosted mod\n$form-switch-checked-bg:                        $supporting-orange !default; // Boosted mod\n$form-switch-checked-border-color:              $supporting-orange !default; // Boosted mod\n$form-switch-checked-filter:                    none !default; // Boosted mod\n$form-switch-checked-focus-inner:               var(--#{$prefix}focus-visible-inner-color) !default; // Boosted mod\n$form-switch-checked-focus-outer:               var(--#{$prefix}focus-visible-outer-color) !default; // Boosted mod\n$form-switch-unchecked-invalid-border-color:    #31c3eb !default; // Boosted mod: will be rendered red when mixed with the filter\n// scss-docs-end form-switch-variables\n\n// scss-docs-start input-group-variables\n$input-group-addon-padding-y:           $input-padding-y !default;\n$input-group-addon-padding-x:           $input-padding-x !default;\n$input-group-addon-font-weight:         $input-font-weight !default;\n$input-group-addon-color:               $input-color !default; // Boosted mod: instead of `null`\n$input-group-addon-bg:                  null !default; // Boosted mod: instead of `var(--#{$prefix}tertiary-bg)`\n$input-group-addon-border-color:        null !default;\n// scss-docs-end input-group-variables\n\n// scss-docs-start form-select-variables\n$form-select-padding-y:             $input-padding-y !default;\n$form-select-padding-x:             $input-padding-x !default;\n$form-select-font-family:           $input-font-family !default;\n$form-select-font-size:             $input-font-size !default;\n$form-select-indicator-padding:     $form-select-padding-x * 3 !default; // Extra padding for background-image\n$form-select-font-weight:           $input-font-weight !default;\n$form-select-line-height:           $input-line-height !default;\n$form-select-color:                 $input-color !default;\n$form-select-bg:                    $input-bg !default;\n$form-select-disabled-color:        $input-disabled-color !default; // Boosted mod: instead of `null`\n$form-select-disabled-bg:           $input-disabled-bg !default;\n$form-select-disabled-border-color: $input-disabled-border-color !default;\n$form-select-bg-position:           right $form-select-padding-x top add(50%, 1px) !default;\n$form-select-bg-size:               .875rem 1rem !default; // In pixels because image dimensions\n$form-select-indicator:             escape-svg(url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 14 7'><path d='M7 7 0 0h14L7 7z'/></svg>\")) !default; // Boosted mod: instead of Bootstrap svg\n$form-select-disabled-indicator:    escape-svg(url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 14 7'><path fill='#{$gray-700}' d='M7 7 0 0h14L7 7z'/></svg>\")) !default; // Boosted mod\n\n$form-select-feedback-icon-padding-end: $form-select-padding-x * 2.5 + $form-select-indicator-padding !default;\n$form-select-feedback-icon-position:    center right $form-select-indicator-padding !default;\n$form-select-feedback-icon-size:        $input-height-inner-half $input-height-inner-half !default;\n\n$form-select-border-width:        $input-border-width !default;\n$form-select-border-color:        $input-border-color !default;\n$form-select-border-radius:       $input-border-radius !default;\n$form-select-box-shadow:          none !default; // Boosted mod\n\n$form-select-focus-border-color:  $input-color !default; // Boosted mod: handle a Firefox-specific visible focus rendering where we remove the border from the select box (see `.form-select` rule)\n// Boosted mod: no $form-select-focus-width\n$form-select-focus-box-shadow:    none !default; // Boosted mod\n\n$form-select-padding-y-sm:        add($input-padding-y-sm, 1px) !default; // Boosted mod\n$form-select-padding-x-sm:        $input-padding-x-sm !default;\n$form-select-font-size-sm:        $input-font-size-sm !default;\n$form-select-border-radius-sm:    $input-border-radius-sm !default;\n\n$form-select-padding-y-lg:        $spacer * .5 !default; // Boosted mod\n$form-select-padding-x-lg:        $input-padding-x-lg !default;\n$form-select-font-size-lg:        $input-font-size-lg !default;\n$form-select-border-radius-lg:    $input-border-radius-lg !default;\n\n$form-select-transition:          $input-transition !default;\n// scss-docs-end form-select-variables\n\n// scss-docs-start form-range-variables\n$form-range-track-width:          100% !default;\n$form-range-track-height:         .375rem !default; // Boosted mod: instead of `.5rem`\n$form-range-track-cursor:         pointer !default;\n$form-range-track-bg:             var(--#{$prefix}secondary-bg) !default;\n$form-range-track-filled-bg:      var(--#{$prefix}primary) !default; // Boosted mod\n$form-range-track-border-radius:  null !default; // Boosted mod: instead of `1rem`\n$form-range-track-box-shadow:     var(--#{$prefix}box-shadow-inset) !default;\n\n$form-range-thumb-width:                   1rem !default;\n$form-range-thumb-height:                  $form-range-thumb-width !default;\n$form-range-thumb-bg:                      var(--#{$prefix}body-bg) !default; // Boosted mod: instead of `$component-active-bg`\n$form-range-thumb-border:                  var(--#{$prefix}border-width) solid var(--#{$prefix}border-color) !default; // Boosted mod: instead of `0`\n$form-range-thumb-border-radius:           50% !default;\n$form-range-thumb-box-shadow:              0 .1rem .25rem rgba($black, .1) !default;\n$form-range-thumb-focus-box-shadow:        null !default; // Boosted mod\n$form-range-thumb-focus-box-shadow-width:  $input-focus-width !default; // For focus box shadow issue in Edge\n$form-range-thumb-hover-bg:                var(--#{$prefix}highlight-bg) !default; // Boosted mod\n$form-range-thumb-active-bg:               var(--#{$prefix}primary) !default; // Boosted mod: instead of `tint-color($component-active-bg, 70%)`\n$form-range-thumb-active-border:           var(--#{$prefix}primary) !default; // Boosted mod\n$form-range-thumb-disabled-bg:             var(--#{$prefix}disabled-color) !default; // Boosted mod: instead of `var(--#{$prefix}secondary-color)`\n$form-range-thumb-transition:              background-color $transition-duration $transition-timing, border-color $transition-duration $transition-timing !default; // Boosted mod: no box shadow\n// scss-docs-end form-range-variables\n\n// scss-docs-start form-file-variables\n$form-file-button-color:          $input-color !default;\n$form-file-button-bg:             $input-bg !default; // Boosted mod: instead of `var(--#{$prefix}tertiary-bg)`\n$form-file-button-hover-bg:       var(--#{$prefix}secondary-bg) !default;\n// scss-docs-end form-file-variables\n\n// Boosted mod: no floating labels\n\n// Form validation\n\n// scss-docs-start form-feedback-variables\n$form-feedback-margin-top:          $form-text-margin-top !default;\n$form-feedback-font-size:           $small-font-size !default;\n$form-feedback-font-style:          null !default;\n// fusv-disable\n$form-feedback-valid-color:         $success !default; // Boosted mod: deprecated in v5.3.0\n$form-feedback-invalid-color:       $danger !default; // Boosted mod: deprecated in v5.3.0\n// fusv-enable\n\n$form-feedback-icon-valid:          var(--#{$prefix}success-icon) !default;\n$form-feedback-icon-invalid:        var(--#{$prefix}error-icon) !default;\n$form-feedback-icon-size:           add($spacer * .25, $spacer * .5) !default; // Boosted mod\n$form-feedback-line-height:         $line-height-sm !default; // Boosted mod\n$form-feedback-color:               null !default; // Boosted mod\n// scss-docs-end form-feedback-variables\n\n// scss-docs-start form-validation-colors\n$form-valid-color:                  var(--#{$prefix}success-text-emphasis) !default; // Boosted mod: instead of `$form-feedback-valid-color`\n$form-valid-border-color:           var(--#{$prefix}success) !default; // Boosted mod: instead of `$form-feedback-valid-color`\n$form-invalid-color:                var(--#{$prefix}danger-text-emphasis) !default; // Boosted mod: instead of `$form-feedback-invalid-color`\n$form-invalid-border-color:         var(--#{$prefix}danger) !default; // Boosted mod: instead of `$form-feedback-invalid-color`\n// scss-docs-end form-validation-colors\n\n// scss-docs-start form-validation-states\n$form-validation-states: (\n  \"valid\": (\n    \"color\": var(--#{$prefix}form-valid-color),\n    \"icon\": $form-feedback-icon-valid,\n    // Boosted mod: no `tooltip-color`\n    // Boosted mod: no `tooltip-bg-color`\n    // Boosted mod: no `focus-box-shadow`\n    \"border-color\": var(--#{$prefix}form-valid-border-color),\n  ),\n  \"invalid\": (\n    \"color\": var(--#{$prefix}form-invalid-color),\n    \"icon\": $form-feedback-icon-invalid,\n    // Boosted mod: no `tooltip-color`\n    // Boosted mod: no `tooltip-bg-color`\n    // Boosted mod: no `focus-box-shadow`\n    \"border-color\": var(--#{$prefix}form-invalid-border-color),\n  )\n) !default;\n// scss-docs-end form-validation-states\n\n// Z-index master list\n//\n// Warning: Avoid customizing these values. They're used for a bird's eye view\n// of components dependent on the z-axis and are designed to all work together.\n\n// scss-docs-start zindex-stack\n$zindex-dropdown:                   1000 !default;\n$zindex-sticky:                     1020 !default;\n$zindex-fixed:                      1030 !default;\n$zindex-back-to-top:                1035 !default; // Boosted mod\n$zindex-offcanvas-backdrop:         1040 !default;\n$zindex-offcanvas:                  1045 !default;\n$zindex-modal-backdrop:             1050 !default;\n$zindex-modal:                      1055 !default;\n$zindex-popover:                    1070 !default;\n$zindex-tooltip:                    1080 !default;\n$zindex-toast:                      1090 !default;\n// scss-docs-end zindex-stack\n\n// scss-docs-start zindex-levels-map\n$zindex-levels: (\n  n1: -1,\n  0: 0,\n  1: 1,\n  2: 2,\n  3: 3\n) !default;\n// scss-docs-end zindex-levels-map\n\n\n// Navs\n\n// scss-docs-start nav-variables\n$nav-link-padding-y:                $spacer * .5 !default;\n$nav-link-padding-x:                $spacer !default;\n$nav-link-font-size:                null !default;\n$nav-link-font-weight:              $font-weight-bold !default;\n$nav-link-color:                    inherit !default; // Boosted mod: instead of `var(--#{$prefix}link-color)`\n$nav-link-hover-color:              var(--#{$prefix}link-hover-color) !default;\n$nav-link-transition:               null !default; // Boosted mod\n$nav-link-disabled-color:           var(--#{$prefix}disabled-color) !default; // Boosted mod: instead of `var(--#{$prefix}secondary-color)`\n// Boosted mod: no `$nav-link-focus-box-shadow`\n\n$nav-tabs-border-color:             var(--#{$prefix}border-color) !default;\n$nav-tabs-border-width:             var(--#{$prefix}border-width) !default;\n$nav-tabs-border-radius:            var(--#{$prefix}border-radius) !default;\n$nav-tabs-link-padding-x:           1.8125rem !default; // Boosted mod\n$nav-tabs-link-hover-color:         var(--#{$prefix}highlight-color) !default; // Boosted mod\n$nav-tabs-link-hover-bg:            var(--#{$prefix}highlight-bg) !default; // Boosted mod\n$nav-tabs-link-hover-border-color:  var(--#{$prefix}border-color) !default; // Boosted mod: instead of `var(--#{$prefix}secondary-bg) var(--#{$prefix}secondary-bg) $nav-tabs-border-color`\n$nav-tabs-link-active-color:        var(--#{$prefix}emphasis-color) !default;\n$nav-tabs-link-active-bg:           var(--#{$prefix}body-bg) !default;\n$nav-tabs-link-active-border-color: $nav-tabs-link-active-color !default; // Boosted mod: instead of `var(--#{$prefix}border-color) var(--#{$prefix}border-color) $nav-tabs-link-active-bg`\n\n$nav-pills-padding-x:               1.8125rem !default; // Boosted mod\n$nav-pills-border-radius:           var(--#{$prefix}border-radius) !default;\n$nav-pills-link-active-color:       $component-active-color !default;\n$nav-pills-link-active-bg:          $component-active-bg !default;\n\n$nav-underline-gap:                       0 !default; // Boosted mod: instead of 1rem\n$nav-underline-gap-lg:                    $spacer * .5 !default; // Boosted mod\n// stylelint-disable-next-line function-disallowed-list\n$nav-underline-border-width:              calc(var(--#{$prefix}border-width) * .5) !default; // Boosted mod: instead of `.125rem`\n$nav-underline-border-color:              var(--#{$prefix}border-color-subtle) !default; // Boosted mod\n$nav-underline-border-radius:             var(--#{$prefix}border-radius) !default; // Boosted mod\n$nav-underline-link-active-color:         var(--#{$prefix}emphasis-color) !default;\n$nav-underline-link-padding-x:            1.8125rem !default; // Boosted mod\n$nav-underline-link-hover-color:          var(--#{$prefix}link-hover-color) !default; // Boosted mod\n// stylelint-disable-next-line function-disallowed-list\n$nav-underline-link-border-width:         0 0 calc(var(--#{$prefix}nav-underline-border-width) * 4) !default; // Boosted mod\n$nav-underline-link-active-bg:            transparent !default; // Boosted mod\n$nav-underline-link-active-border-color:  var(--#{$prefix}primary) !default; // Boosted mod\n// scss-docs-end nav-variables\n\n\n// Navbar\n\n// scss-docs-start navbar-variables\n$navbar-padding-y:                    .375rem !default; // Boosted mod\n$navbar-padding-x:                    null !default;\n$navbar-font-weight:                  $font-weight-bold !default; // Boosted mod\n\n$navbar-nav-link-padding-y:           1rem !default; // Boosted mod\n$navbar-nav-link-padding-x-xs:        $spacer * .25 !default; // Boosted mod\n$navbar-nav-link-padding-x:           $spacer * .5 !default; // Boosted mod\n\n$navbar-brand-font-size:              2.1875rem !default; // Boosted mod\n// Boosted mod: no nav-link-height calculation\n$navbar-brand-padding-y:              0 !default; // Boosted mod\n$navbar-brand-margin-end:             $spacer * 1.5 !default; // Boosted mod\n\n$navbar-toggler-icon-close-bg:        $cross-icon !default; // Boosted mod\n$navbar-toggler-padding-y:            $spacer * .6 !default; // Boosted mod: same as $navbar-nav-icon-padding-y-xs\n$navbar-toggler-padding-x:            $spacer * .75 !default; // Boosted mod: same as $navbar-nav-icon-padding-x-xs\n$navbar-toggler-font-size-xs:         1.04166666rem !default; // Boosted mod\n$navbar-toggler-font-size:            1.25rem !default; // Boosted mod\n$navbar-toggler-border-radius:        $btn-border-radius !default;\n$navbar-toggler-focus-width:          null !default; // Boosted mod\n$navbar-toggler-transition:           $transition-focus !default; // Boosted mod\n\n$navbar-light-color:                  var(--#{$prefix}emphasis-color) !default; // Boosted mod: instead of `rgba(var(--#{$prefix}emphasis-color-rgb), .65)`\n$navbar-light-bg:                     var(--#{$prefix}highlight-color) !default; // Boosted mod\n$navbar-light-hover-color:            var(--#{$prefix}link-hover-color) !default; // Boosted mod: instead of `rgba(var(--#{$prefix}emphasis-color-rgb), .8)`\n$navbar-light-active-color:           var(--#{$prefix}primary) !default; // Boosted mod: instead of `rgba(var(--#{$prefix}emphasis-color-rgb), 1)`\n$navbar-light-disabled-color:         var(--#{$prefix}disabled-color) !default; // Boosted mod: instead of `rgba(var(--#{$prefix}emphasis-color-rgb), .3)`\n$navbar-light-icon-color:             var(--#{$prefix}emphasis-color) !default; // Boosted mod: instead of `rgba($body-color, .75)`\n$navbar-light-icon-hover-color:       var(--#{$prefix}link-hover-color) !default; // Boosted mod\n$navbar-light-toggler-icon-bg:        $burger-icon !default; // Boosted mod: instead of inline SVG\n$navbar-light-toggler-icon-bg-small:  $burger-icon-small !default; // Boosted mod: slightly different burger icon for small breakpoints\n$navbar-light-toggler-border-color:   null !default; // Boosted mod: instead of `rgba(var(--#{$prefix}emphasis-color-rgb), .15)`\n$navbar-light-brand-color:            $navbar-light-color !default; // Boosted mod: instead of `$navbar-light-active-color`\n$navbar-light-brand-hover-color:      $navbar-light-active-color !default;\n// scss-docs-end navbar-variables\n\n// Boosted mod: Orange navbar\n// scss-docs-start orange-navbar-variables\n$navbar-transition-duration:                $transition-duration !default;\n$navbar-transition-timing-function:         $transition-timing !default;\n$navbar-transition:                         padding-top $navbar-transition-duration $navbar-transition-timing-function, padding-bottom $navbar-transition-duration $navbar-transition-timing-function, $transition-focus !default;\n$navbar-brand-transition:                   margin $navbar-transition-duration $navbar-transition-timing-function, $transition-focus !default;\n$navbar-brand-logo-transition:              width $navbar-transition-duration $navbar-transition-timing-function, height $navbar-transition-duration $navbar-transition-timing-function !default;\n$navbar-active-transition:                  bottom $navbar-transition-duration $navbar-transition-timing-function !default;\n\n$navbar-border-width:                       calc(var(--#{$prefix}border-width) * .5) !default; // stylelint-disable-line function-disallowed-list\n$navbar-border-color:                       var(--#{$prefix}border-color-subtle) !default;\n\n$navbar-brand-margin-y-xs:                  $spacer * .5 !default;\n$navbar-brand-logo-size-xs:                 $spacer * 1.5 !default;\n$navbar-brand-font-size-xs:                 1.3125rem !default;\n$navbar-brand-letter-spacing-xs:            $letter-spacing-base * 5 !default;\n$navbar-brand-font-size-two-lined-xs:       1.0625rem !default;\n$navbar-brand-letter-spacing-two-lined-xs:  $letter-spacing-base * 4 !default;\n\n$navbar-brand-margin-y:                     $spacer * .95 !default;\n$navbar-brand-logo-size:                    $spacer * 2.5 !default;\n$navbar-brand-letter-spacing:               $letter-spacing-base * 10 !default;\n$navbar-brand-font-size-two-lined:          1.8125rem !default;\n$navbar-brand-letter-spacing-two-lined:     $letter-spacing-base * 8 !default;\n\n$navbar-icon-size-xs:                       $spacer * 1.25 !default;\n$navbar-icon-size:                          $spacer * 1.5 !default;\n\n$navbar-nav-icon-padding-y-xs:              $spacer * .6 !default;\n$navbar-nav-icon-padding-x-xs:              $spacer * .75 !default;\n$navbar-nav-icon-padding-y:                 $navbar-brand-margin-y !default;\n$navbar-nav-icon-padding-x:                 $spacer !default;\n\n$navbar-supra-link-padding-y:               $spacer * .6 !default;\n$navbar-supra-link-padding-x:               .46875rem !default;\n$navbar-supra-icon-padding-y:               $spacer * .25 !default;\n$navbar-supra-icon-padding-x:               $navbar-nav-icon-padding-x-xs !default;\n$navbar-supra-icon-size:                    $navbar-icon-size-xs !default;\n\n$navbar-minimized-brand-margin-y:           $spacer * .75 !default;\n$navbar-minimized-nav-icon-padding-y:       $navbar-minimized-brand-margin-y !default;\n$navbar-minimized-toggler-padding-y:        $navbar-minimized-brand-margin-y !default;\n\n$navbar-badge-padding-y:                    .125rem !default;\n$navbar-badge-padding-x:                    .375rem !default;\n$navbar-badge-margin-top:                   .375rem !default;\n// scss-docs-end orange-navbar-variables\n// End mod\n\n// Deprecated in v5.3.3: all `$navbar-dark-*`\n$navbar-dark-border-color:          $gray-700 !default; // Boosted mod\n$navbar-dark-color:                 $white !default; // Boosted mod: instead of `rgba($white, .55)`\n$navbar-dark-hover-color:           $supporting-orange !default; // Boosted mod: instead of `rgba($white, .75)`\n$navbar-dark-active-color:          $supporting-orange !default; // Boosted mod: instead of `$white`\n$navbar-dark-disabled-color:        $gray-700 !default; // Boosted mod: instead of `rgba($white, .25)`\n// Boosted mod: no $navbar-dark-icon-color\n// Boosted mod: no $navbar-dark-toggler-icon-bg since dark toggler are handled with filter\n$navbar-dark-toggler-border-color:  transparent !default; // Boosted mod: instead of `rgba($white, .1)`\n$navbar-dark-brand-color:           inherit !default; // Boosted mod: instead of `$navbar-dark-active-color`\n$navbar-dark-brand-hover-color:     $navbar-dark-active-color !default;\n\n\n// Dropdowns\n//\n// Dropdown menu container and contents.\n\n// scss-docs-start dropdown-variables\n$dropdown-min-width:                10rem !default;\n$dropdown-padding-x:                $spacer * .5 !default; // Boosted mod: instead of `0`\n$dropdown-padding-y:                0 !default; // Boosted mod: instead of `.5rem`\n$dropdown-spacer:                   0 !default; // Boosted mod: instead of `.125rem`\n$dropdown-font-size:                $font-size-base !default;\n$dropdown-line-height:              $line-height-base !default; // Boosted mod\n$dropdown-color:                    var(--#{$prefix}body-color) !default;\n$dropdown-bg:                       var(--#{$prefix}body-bg) !default;\n$dropdown-border-color:             var(--#{$prefix}border-color-subtle) !default; // Boosted mod: instead of `var(--#{$prefix}border-color-translucent)`\n$dropdown-border-radius:            var(--#{$prefix}border-radius) !default;\n$dropdown-border-width:             var(--#{$prefix}border-width) !default;\n$dropdown-inner-border-radius:      0 !default; // Boosted mod: instead of `calc(#{$dropdown-border-radius} - #{$dropdown-border-width})`\n$dropdown-divider-bg:               $dropdown-border-color !default;\n$dropdown-divider-margin-y:         $spacer * .25 !default; // Boosted mod: instead of `$spacer * .5`\n$dropdown-box-shadow:               var(--#{$prefix}box-shadow) !default;\n\n$dropdown-link-color:               null !default; // Boosted mod: instead of `var(--#{$prefix}body-color)`\n$dropdown-link-hover-color:         $dropdown-link-color !default;\n$dropdown-link-hover-bg:            var(--#{$prefix}secondary-bg) !default; // Boosted mod: instead of `var(--#{$prefix}tertiary-bg)`\n\n$dropdown-link-active-color:        $dropdown-link-color !default; // Boosted mod: instead of `$component-active-color`\n$dropdown-link-active-bg:           var(--#{$prefix}tertiary-active-bg) !default; // Boosted mod: instead of `$component-active-bg`\n\n$dropdown-link-disabled-color:      var(--#{$prefix}disabled-color) !default; // Boosted mod: instead of `var(--#{$prefix}tertiary-color)`\n\n$dropdown-item-padding-y:           $spacer * .5 !default; // Boosted mod: instead of `$spacer * .25`\n$dropdown-item-padding-x:           $spacer * .5 !default; // Boosted mod: instead of `$spacer`\n\n$dropdown-header-color:             null !default; // Boosted mod: instead of `$gray-600`\n$dropdown-header-padding-x:         $dropdown-item-padding-x !default;\n$dropdown-header-padding-y:         $spacer !default; // Boosted mod: instead of `$dropdown-padding-y`\n// fusv-disable\n$dropdown-header-padding:           $dropdown-header-padding-y $dropdown-header-padding-x !default; // Deprecated in v5.2.0\n// fusv-enable\n// scss-docs-end dropdown-variables\n\n// Deprecated in v5.3.3: all `$dropdown-dark-*`\n$dropdown-dark-color:               $white !default; // Boosted mod\n$dropdown-dark-bg:                  $black !default; // Boosted mod\n$dropdown-dark-border-color:        $gray-700 !default; // Boosted mod\n$dropdown-dark-divider-bg:          $dropdown-dark-border-color !default; // Boosted mod\n$dropdown-dark-box-shadow:          null !default;\n$dropdown-dark-link-color:          $dropdown-dark-color !default;\n$dropdown-dark-link-hover-color:    $white !default;\n$dropdown-dark-link-hover-bg:       $gray-700 !default; // Boosted mod\n$dropdown-dark-link-active-color:   $black !default; // Boosted mod\n$dropdown-dark-link-active-bg:      $white !default; // Boosted mod\n$dropdown-dark-link-disabled-color: $gray-700 !default; // Boosted mod\n$dropdown-dark-header-color:        $white !default; // Boosted mod\n\n// Pagination\n\n// scss-docs-start pagination-variables\n$pagination-padding-y:              null !default; // Boosted mod: instead of `.375rem`\n$pagination-padding-x:              null !default; // Boosted mod: instead of `.75rem`\n// Boosted mod: no $pagination-padding-y-sm\n// Boosted mod: no $pagination-padding-x-sm\n// Boosted mod: no $pagination-padding-y-lg\n// Boosted mod: no $pagination-padding-x-lg\n\n$pagination-font-size:              $font-size-base !default;\n\n$pagination-color:                  inherit !default; // Boosted mod: instead of `var(--#{$prefix}link-color)`\n$pagination-bg:                     transparent !default; // Boosted mod: instead of `var(--#{$prefix}body-bg)`\n$pagination-border-radius:          var(--#{$prefix}border-radius) !default;\n$pagination-border-width:           var(--#{$prefix}border-width) !default;\n$pagination-margin-y:               $spacer !default; // Boosted mod\n$pagination-margin-start:           0 !default; // Boosted mod: instead of `calc(-1 * $pagination-border-width)`\n$pagination-margin-x-first-last:    $spacer * .5 !default; // Boosted mod\n$pagination-border-color:           transparent !default; // Boosted mod: instead of `var(--#{$prefix}border-color)`\n\n// Deprecated in v5.3.3\n// fusv-disable\n$pagination-focus-color:            null !default; // Boosted mod\n$pagination-focus-bg:               null !default; // Boosted mod: instead of `var(--#{$prefix}secondary-bg)`\n$pagination-focus-box-shadow:       0 0 0 $focus-visible-inner-width var(--#{$prefix}focus-visible-inner-color) !default; // Boosted mod: no `$focus-ring-box-shadow`\n$pagination-focus-outline:          null !default; // Boosted mod\n// fusv-enable\n\n$pagination-hover-color:            var(--#{$prefix}body-color) !default; // Boosted mod: instead of `var(--#{$prefix}link-hover-color)`\n$pagination-hover-bg:               var(--#{$prefix}secondary-bg) !default; // Boosted mod: instead of `var(--#{$prefix}tertiary-bg)`\n$pagination-hover-border-color:     $pagination-hover-bg !default; // Boosted mod: instead of `var(--#{$prefix}border-color)`\n\n$pagination-active-color:           var(--#{$prefix}highlight-color) !default; // Boosted mod: instead of `$component-active-color`\n$pagination-active-bg:              var(--#{$prefix}highlight-bg) !default; // Boosted mod: instead of `$component-active-bg`\n$pagination-active-border-color:    $pagination-active-bg !default; // Boosted mod: instead of `$component-active-bg`\n\n$pagination-disabled-color:         var(--#{$prefix}disabled-color) !default; // Boosted mod: instead of `var(--#{$prefix}secondary-color)`\n$pagination-disabled-bg:            transparent !default; // Boosted mod: instead of `var(--#{$prefix}secondary-bg)`\n$pagination-disabled-border-color:  transparent !default; // Boosted mod: instead of `var(--#{$prefix}border-color)`\n\n$pagination-transition:             $transition-focus !default; // Boosted mod: no color, bg-color, border-color, box-shadow\n\n// Boosted mod: no $pagination-border-radius-sm\n// Boosted mod: no $pagination-border-radius-lg\n\n// Boosted mod\n$pagination-padding-end:            1.125rem !default;\n$pagination-icon:                   var(--#{$prefix}chevron-icon) !default;\n$pagination-icon-size:              subtract($spacer * 2, calc(var(--#{$prefix}border-width) * 2)) !default; // stylelint-disable-line function-disallowed-list\n$pagination-icon-width:             add(.5rem, 1px) !default;\n$pagination-icon-height:            subtract(1rem, 1px) !default;\n\n$pagination-active-item-bg:         $supporting-orange !default;\n$pagination-active-item-color:      $black !default;\n$pagination-active-item-border:     $pagination-active-item-bg !default;\n// End mod\n// scss-docs-end pagination-variables\n\n\n// Placeholders\n\n// scss-docs-start placeholders\n$placeholder-opacity-max:           .5 !default;\n$placeholder-opacity-min:           .2 !default;\n// scss-docs-end placeholders\n\n// Cards\n\n// scss-docs-start card-variables\n$card-spacer-top:                   $spacer * .75 !default; // Boosted mod\n$card-spacer-bottom:                $spacer !default; // Boosted mod\n// fusv-disable\n$card-spacer-y:                     $spacer !default; // Deprecated in v5.2.3\n// fusv-enable\n$card-spacer-x:                     $spacer !default;\n$card-title-spacer-y:               $spacer * .5 !default;\n$card-title-color:                  null !default;\n$card-subtitle-color:               null !default;\n$card-border-width:                 var(--#{$prefix}border-width) !default;\n$card-border-color:                 var(--#{$prefix}border-color-subtle) !default; // Boosted mod: instead of `var(--#{$prefix}border-color-translucent)`\n$card-border-radius:                var(--#{$prefix}border-radius) !default;\n$card-box-shadow:                   null !default;\n$card-inner-border-radius:          subtract($card-border-radius, $card-border-width) !default;\n$card-cap-padding-y:                $card-spacer-bottom * .5 !default; // Boosted mod\n$card-cap-padding-x:                $card-spacer-x !default;\n$card-cap-bg:                       var(--#{$prefix}highlight-bg) !default; // Boosted mod: instead of `rgba(var(--#{$prefix}body-color-rgb), .03)`\n$card-cap-color:                    var(--#{$prefix}highlight-color) !default; // Boosted mod: instead of `null`\n$card-cap-font-weight:              $font-weight-bold !default; // Boosted mod\n$card-height:                       null !default;\n$card-color:                        null !default;\n$card-bg:                           var(--#{$prefix}body-bg) !default;\n$card-img-overlay-padding:          $spacer !default;\n$card-group-margin:                 $grid-gutter-width * .5 !default;\n$card-footer-color:                 var(--#{$prefix}secondary-color) !default; // Boosted mod\n// scss-docs-end card-variables\n\n// Accordion\n\n// scss-docs-start accordion-variables\n$accordion-padding-y:                     $spacer * .5 !default; // Boosted mod\n$accordion-padding-x:                     0 !default; // Boosted mod\n$accordion-color:                         null !default; // Boosted mod: instead of `var(--#{$prefix}body-color)`\n$accordion-bg:                            transparent !default; // Boosted mod: instead of `var(--#{$prefix}body-bg)`\n// stylelint-disable-next-line function-disallowed-list\n$accordion-border-width:                  calc(var(--#{$prefix}border-width) * .5) !default; // Boosted mod\n$accordion-border-color:                  var(--#{$prefix}border-color-subtle) !default; // Boosted mod: instead of `var(--#{$prefix}border-color)`\n$accordion-border-radius:                 var(--#{$prefix}border-radius) !default;\n$accordion-inner-border-radius:           subtract($accordion-border-radius, #{$accordion-border-width}) !default;\n\n$accordion-body-padding-top:              $spacer !default; // Boosted mod\n$accordion-body-padding-end:              0 !default; // Boosted mod\n$accordion-body-padding-bottom:           $spacer * 1.5 !default; // Boosted mod\n$accordion-body-padding-start:            0 !default; // Boosted mod\n// fusv-disable\n$accordion-body-padding-y:                $spacer !default; // Deprecated in Boosted 5.2.3 to divide it in -padding<top | end | bottom |start>\n$accordion-body-padding-x:                $spacer !default; // Deprecated in Boosted 5.2.3 to divide it in -padding<top | end | bottom |start>\n// fusv-enable\n\n$accordion-button-padding-y:              $accordion-padding-y !default;\n$accordion-button-padding-x:              $accordion-padding-x !default;\n$accordion-button-color:                  null !default; // Boosted mod: instead of `var(--#{$prefix}body-color)`\n$accordion-button-bg:                     var(--#{$prefix}accordion-bg) !default;\n$accordion-transition:                    $btn-transition, border-radius .15s ease !default;\n$accordion-button-hover-bg:               var(--#{$prefix}secondary-bg) !default; // Boosted mod\n$accordion-button-active-bg:              null !default; // Boosted mod: instead of `var(--#{$prefix}primary-bg-subtle)`\n$accordion-button-active-color:           $accordion-button-color !default; // Boosted mod: instead of `var(--#{$prefix}primary-text-emphasis)`\n\n// Boosted mod: no $accordion-button-focus-border-color\n// Boosted mod: no $accordion-button-focus-box-shadow\n\n// Boosted mod: no $accordion-icon-width\n// Boosted mod: no $accordion-icon-color\n// Boosted mod: no $accordion-icon-active-color\n// Boosted mod: no $accordion-icon-transition\n$accordion-icon-transform:                scaleY(-1) !default;\n\n// Boosted mod: no $accordion-button-icon\n// Boosted mod: no $accordion-button-active-icon\n\n// Boosted mod: accordion sizes\n$accordion-button-font-size:              $h3-font-size !default;\n$accordion-button-line-height:            null !default;\n$accordion-button-font-weight:            $font-weight-bold !default;\n$accordion-button-letter-spacing:         $h3-spacing !default;\n$accordion-button-font-size-sm:           $h5-font-size !default;\n$accordion-button-line-height-sm:         $h5-line-height !default;\n$accordion-button-letter-spacing-sm:      $h5-spacing !default;\n$accordion-button-font-size-lg:           $h2-font-size !default;\n$accordion-button-line-height-lg:         calc(40 / 30) !default; // stylelint-disable-line function-disallowed-list\n$accordion-button-letter-spacing-lg:      $h2-spacing !default;\n// End mod\n// scss-docs-end accordion-variables\n\n// Tooltips\n\n// scss-docs-start tooltip-variables\n$tooltip-font-size:                 $font-size-sm !default;\n$tooltip-font-weight:               $font-weight-bold !default; // Boosted mod\n$tooltip-line-height:               $line-height-sm !default; // Boosted mod\n$tooltip-max-width:                 $spacer * 10 !default;\n$tooltip-color:                     null !default; // Boosted mod: instead of `var(--#{$prefix}body-bg)`\n$tooltip-bg:                        var(--#{$prefix}body-bg) !default; // Boosted mod: instead of `var(--#{$prefix}emphasis-color)`\n// stylelint-disable-next-line function-disallowed-list\n$tooltip-border-width:              calc(var(--#{$prefix}border-width) * .5) !default; // Boosted mod\n$tooltip-border-color:              var(--#{$prefix}emphasis-color) !default; // Boosted mod\n$tooltip-border-radius:             var(--#{$prefix}border-radius) !default;\n$tooltip-opacity:                   1 !default;\n$tooltip-padding-y:                 $spacer * .5 !default;\n$tooltip-padding-x:                 $spacer * .5 !default;\n$tooltip-margin:                    null !default; // TODO: remove this in v6\n\n$tooltip-arrow-width:               $spacer * .5 !default;\n$tooltip-arrow-height:              $tooltip-arrow-width * .5 !default;\n// fusv-disable\n$tooltip-arrow-color:               null !default; // Deprecated in Boosted 5.2.0 for CSS variables\n// fusv-enable\n// scss-docs-end tooltip-variables\n\n// Boosted mod: no form tooltips\n\n\n// Popovers\n\n// scss-docs-start popover-variables\n$popover-font-size:                 $font-size-base !default; // Boosted mod: instead of `$font-size-sm`\n$popover-line-height:               1.5 !default; // Boosted mod\n$popover-font-weight:               $font-weight-bold !default; // Boosted mod\n$popover-bg:                        var(--#{$prefix}body-bg) !default;\n$popover-max-width:                 $spacer * 19 !default; // Boosted mod: instead of `276px`\n$popover-padding-y:                 $spacer !default; // Boosted mod\n$popover-border-width:              var(--#{$prefix}border-width) !default;\n$popover-border-color:              var(--#{$prefix}border-color-subtle) !default; // Boosted mod: instead of `var(--#{$prefix}border-color-translucent)`\n$popover-border-radius:             var(--#{$prefix}border-radius-lg) !default;\n$popover-inner-border-radius:       calc(#{$popover-border-radius} - #{$popover-border-width}) !default; // stylelint-disable-line function-disallowed-list\n$popover-box-shadow:                var(--#{$prefix}box-shadow) !default;\n\n$popover-header-font-size:          $font-size-lg !default; // Boosted mod: instead of `$font-size-base`\n$popover-header-line-height:        1.11 !default; // Boosted mod\n$popover-header-bg:                 $popover-bg !default; // Boosted mod: instead of `var(--#{$prefix}secondary-bg)`\n$popover-header-color:              var(--#{$prefix}heading-color) !default; // Boosted mod: instead of `$headings-color`\n$popover-header-padding-top:        $popover-padding-y !default; // Boosted mod\n$popover-header-padding-bottom:     map-get($spacers, 2) !default; // Boosted mod\n$popover-header-padding-y:          initial !default; // Boosted mod: instead of `.5rem`\n$popover-header-padding-x:          $spacer * .9 !default; // Boosted mod: instead of `$spacer`\n\n$popover-body-color:                null !default; // Boosted mod: instead of `var(--#{$prefix}body-color)`\n$popover-body-padding-top:          0 !default; // Boosted mod\n$popover-body-padding-bottom:       $popover-padding-y !default; // Boosted mod\n$popover-body-padding-y:            initial !default; // Boosted mod: instead of `$spacer`\n$popover-body-padding-x:            $popover-header-padding-x !default; // Boosted mod: instead of `$spacer`\n\n$popover-arrow-width:               $spacer !default; // Boosted mod: instead of `1rem`\n$popover-arrow-height:              $popover-arrow-width * .5 !default; // Boosted mod: instead of `.5rem`\n// scss-docs-end popover-variables\n\n// fusv-disable\n// Deprecated in Bootstrap 5.2.0 for CSS variables\n$popover-arrow-color:               $popover-bg !default;\n$popover-arrow-outer-color:         $popover-border-color !default; // Boosted mod: instead of `var(--#{$prefix}border-color-translucent)`\n// fusv-enable\n\n// Toasts\n\n// scss-docs-start toast-variables\n$toast-max-width:                   21.875rem !default;\n$toast-padding-x:                   $spacer * .5 !default;\n$toast-padding-y:                   $spacer * .25 !default;\n$toast-font-size:                   .875rem !default;\n$toast-color:                       var(--#{$prefix}emphasis-color) !default; // Boosted mod: instead of `null` due to some `bg-dark` issue\n$toast-background-color:            rgba(var(--#{$prefix}body-bg-rgb), .85) !default;\n$toast-border-width:                var(--#{$prefix}border-width) !default;\n$toast-border-color:                var(--#{$prefix}border-color-subtle) !default; // Boosted mod: instead of `var(--#{$prefix}border-color-translucent)`\n$toast-border-radius:               var(--#{$prefix}border-radius) !default;\n$toast-box-shadow:                  var(--#{$prefix}box-shadow) !default;\n$toast-spacing:                     $container-padding-x !default;\n\n$toast-header-color:                null !default; // Boosted mod: instead of `var(--#{$prefix}secondary-color)`\n$toast-header-background-color:     rgba(var(--#{$prefix}body-bg-rgb), .85) !default;\n$toast-header-border-color:         rgba($black, .05) !default; // Boosted mod: instead of `$toast-border-color`\n// scss-docs-end toast-variables\n\n// Badges\n\n// scss-docs-start badge-variables\n$badge-font-size:                   .75em !default;\n$badge-font-weight:                 $font-weight-bold !default;\n$badge-color:                       $white !default;\n$badge-padding-y:                   .35em !default;\n$badge-padding-x:                   .65em !default;\n$badge-border-radius:               var(--#{$prefix}border-radius) !default;\n// scss-docs-end badge-variables\n\n// Modals\n\n// scss-docs-start modal-variables\n$modal-inner-padding:               $spacer * .5 $spacer !default;\n\n$modal-footer-margin-between:       $spacer * .5 !default;\n$modal-footer-padding:              $spacer * .5 subtract($spacer, $modal-footer-margin-between * .5) 0 !default; // Boosted mod\n\n$modal-dialog-margin:               $spacer * .5 !default;\n$modal-dialog-margin-y-sm-up:       $spacer * 1.5 !default;\n\n$modal-title-line-height:           $line-height-base !default;\n\n$modal-content-padding-y:           $spacer !default; // Boosted mod\n$modal-content-padding-x:           0 !default; // Boosted mod\n$modal-content-padding:             $modal-content-padding-y $modal-content-padding-x !default; // Boosted mod\n$modal-content-color:               var(--#{$prefix}body-color) !default;\n$modal-content-bg:                  var(--#{$prefix}body-bg) !default;\n$modal-content-border-color:        var(--#{$prefix}border-color-subtle) !default; // Boosted mod: instead of `var(--#{$prefix}border-color-translucent)`\n$modal-content-border-width:        var(--#{$prefix}border-width) !default;\n$modal-content-border-radius:       var(--#{$prefix}border-radius-lg) !default;\n$modal-content-inner-border-radius: var(--#{$prefix}border-radius) !default; // Boosted mod: instead of `subtract($modal-content-border-radius, $modal-content-border-width)`\n$modal-content-box-shadow-xs:       var(--#{$prefix}box-shadow-sm) !default;\n$modal-content-box-shadow-sm-up:    var(--#{$prefix}box-shadow) !default;\n\n$modal-backdrop-bg:                 $black !default;\n$modal-backdrop-opacity:            .5 !default;\n\n$modal-header-border-color:         null !default; // Boosted mod\n$modal-header-border-width:         $modal-content-border-width !default;\n$modal-header-padding-y:            0 !default;\n$modal-header-padding-x:            $spacer !default;\n$modal-header-padding:              $modal-header-padding-y $modal-header-padding-x !default; // Keep this for backwards compatibility\n\n$modal-footer-bg:                   null !default;\n$modal-footer-border-color:         null !default; // Boosted mod\n$modal-footer-border-width:         $modal-header-border-width !default;\n$modal-footer-margin-top:           $spacer * .5 !default; // Boosted mod\n$modal-footer-margin-top-sm:        $spacer * .75 !default; // Boosted mod\n\n// Boosted mod\n//// Scrollable modal\n$modal-scrollable-inner-padding:     $spacer !default;\n$modal-scrollable-inner-margin:      $spacer 0 0 !default;\n$modal-scrollable-footer-margin-top: $spacer * .5 !default;\n\n//// Modal with top image\n$modal-img-margin:                  -$modal-content-padding-y 0 $modal-content-padding-y !default; // Boosted mod\n$modal-img-btn-close-offset:        $modal-content-padding-y !default;\n// End mod\n\n$modal-sm:                          300px !default;\n$modal-md:                          460px !default;\n$modal-lg:                          700px !default;\n$modal-xl:                          940px !default;\n\n$modal-fade-transform:              translate(0, -50px) !default;\n$modal-show-transform:              none !default;\n$modal-transition:                  transform .3s ease-out !default;\n$modal-scale-transform:             scale(1.02) !default;\n// scss-docs-end modal-variables\n\n// Alerts\n//\n// Define alert colors, border radius, and padding.\n\n// scss-docs-start alert-variables\n$alert-padding-y:                   1rem !default;\n$alert-padding-x:                   $spacer !default;\n$alert-margin-bottom:               $spacer !default;\n$alert-color:                       var(--#{$prefix}body-color) !default; // Boosted mod\n$alert-border-radius:               var(--#{$prefix}border-radius) !default;\n$alert-link-font-weight:            null !default; // Boosted mod\n$alert-heading-font-weight:         $font-weight-bold !default; // Boosted mod\n$alert-border-width:                var(--#{$prefix}border-width) !default;\n\n// Boosted mod\n$alert-padding-sm:                  $spacer * .5 !default;\n$alert-icons: (\n  \"success\": var(--#{$prefix}success-icon),\n  \"info\":    escape-svg($info-icon),\n  // Create a list for this warning icon to indicate that the mask needs to be replaced by a background image\n  // Be aware that the background of the icon won't change anymore\n  // Note: `true` parameter is only used to create a list, it could be empty (e.g. `(escape-svg($warning-icon),)`)\n  \"warning\": (escape-svg($warning-icon-filled), true),\n  \"danger\":  var(--#{$prefix}error-icon)\n) !default;\n$alert-logo-size:                   add($spacer * .5, 1rem) !default;\n$alert-logo-size-sm:                add(1rem, 1px) !default;\n$alert-icon-size:                   3rem !default;\n$alert-icon-size-sm:                $alert-icon-size * .5 !default;\n$alert-icon-margin-y:               $spacer * .1 !default;\n$alert-btn-close-offset:            .5rem !default;\n$alert-btn-close-offset-sm:         $spacer * .25 !default;\n// End mod\n\n$alert-dismissible-padding-r:       $alert-padding-y * 3 !default; // 3x covers width of x plus default padding on either side\n// scss-docs-end alert-variables\n\n// Progress bars\n\n// scss-docs-start progress-variables\n$progress-height:                   $spacer !default;\n$progress-font-size:                $font-size-base !default;\n$progress-bg:                       var(--#{$prefix}tertiary-active-bg) !default; // Boosted mod: instead of `var(--#{$prefix}secondary-bg)`\n$progress-border-radius:            var(--#{$prefix}border-radius) !default;\n$progress-box-shadow:               var(--#{$prefix}box-shadow-inset) !default;\n$progress-bar-color:                var(--#{$prefix}highlight-color) !default; // Boosted mod: instead of `$white`\n$progress-bar-font-weight:          $font-weight-bold !default; // Boosted mod\n$progress-bar-text-indent:          map-get($spacers, 2) !default; // Boosted mod\n$progress-bar-bg:                   var(--#{$prefix}primary) !default; // Boosted mod: instead of `$primary`\n$progress-bar-animation-timing:     1s linear infinite !default;\n$progress-bar-transition:           width .6s ease !default;\n\n// Boosted mod\n$progress-height-sm:                $spacer * .5 !default;\n$progress-height-xs:                $spacer * .25 !default;\n// End mod\n// scss-docs-end progress-variables\n\n// List group\n\n// scss-docs-start list-group-variables\n$list-group-font-weight:              $font-weight-bold !default; // Boosted mod\n$list-group-color:                    null !default; // Boosted mod: instead of `var(--#{$prefix}body-color)`\n$list-group-bg:                       transparent !default; // Boosted mod: instead of `var(--#{$prefix}body-bg)`\n$list-group-border-color:             var(--#{$prefix}border-color-subtle) !default; // Boosted mod: instead of `var(--#{$prefix}border-color)`\n$list-group-border-width:             var(--#{$prefix}border-width) !default;\n$list-group-border-radius:            var(--#{$prefix}border-radius) !default;\n\n$list-group-divider-size:             map-get($border-widths, 1) !default;\n\n$list-group-item-padding-y:           10px !default; // Boosted mod: instead of $spacer * .5\n$list-group-item-padding-x:           13px !default; // Boosted mod: instead of $spacer\n$list-group-item-icon-size:           $spacer * .85 !default; // Boosted mod\n$list-group-item-icon-margin-x:       subtract(var(--#{$prefix}list-group-item-padding-x), 5px) !default; // Boosted mod\n\n$list-group-numbered-item-margin-end: 14px !default; // Boosted mod\n\n$list-group-hover-bg:                 var(--#{$prefix}secondary-bg) !default; // Boosted mod: instead of `var(--#{$prefix}tertiary-bg)`\n$list-group-active-color:             var(--#{$prefix}emphasis-color) !default; // Boosted mod: instead of `$component-active-color`\n$list-group-active-bg:                no-repeat linear-gradient(to right, var(--#{$prefix}primary) 4px, var(--#{$prefix}tertiary-active-bg) 4px) !default; // Boosted mod: instead of `$component-active-bg`\n$list-group-active-border-color:      $list-group-border-color !default; // Boosted mod: instead of `$list-group-active-bg`\n\n$list-group-disabled-color:           var(--#{$prefix}disabled-color) !default; // Boosted mod: instead of `var(--#{$prefix}secondary-color)`\n$list-group-disabled-bg:              $list-group-bg !default;\n\n$list-group-action-color:             var(--#{$prefix}emphasis-color) !default; // Boosted mod: instead of `var(--#{$prefix}secondary-color)`\n$list-group-action-hover-color:       var(--#{$prefix}emphasis-color) !default;\n\n$list-group-action-active-color:      $list-group-active-color !default; // Boosted mod: instead of `var(--#{$prefix}body-color)`\n$list-group-action-active-bg:         $list-group-active-bg !default; // Boosted mod: instead of `var(--#{$prefix}secondary-bg)`\n\n// Boosted mod\n$list-group-icons: (\n  \"success\": var(--#{$prefix}success-icon),\n  \"info\":    escape-svg($info-icon),\n  \"warning\": escape-svg($warning-icon),\n  \"danger\":  var(--#{$prefix}error-icon)\n) !default;\n// End mod\n// scss-docs-end list-group-variables\n\n\n// Image thumbnails\n\n// scss-docs-start thumbnail-variables\n$thumbnail-padding:                 0 !default; // Boosted mod\n$thumbnail-bg:                      var(--#{$prefix}body-bg) !default;\n$thumbnail-border-width:            var(--#{$prefix}border-width) !default;\n$thumbnail-border-color:            var(--#{$prefix}border-color-subtle) !default; // Boosted mod: instead of `var(--#{$prefix}border-color-translucent)`\n$thumbnail-border-radius:           var(--#{$prefix}border-radius) !default;\n$thumbnail-box-shadow:              var(--#{$prefix}box-shadow-sm) !default;\n// scss-docs-end thumbnail-variables\n\n// Figures\n\n// scss-docs-start figure-variables\n$figure-caption-font-size:          $small-font-size !default;\n$figure-caption-color:              var(--#{$prefix}secondary-color) !default;\n// scss-docs-end figure-variables\n\n// Boosted mod\n// Title bars\n\n// scss-docs-start title-bars-variables\n$title-bar-bg:                      var(--#{$prefix}body-bg) !default;\n$title-bar-color:                   var(--#{$prefix}body-color) !default;\n$title-bar-image-ratio:             1.8em !default;\n$title-bar-padding-y:               .3333333em !default;\n$title-bar-font-size:               $h2-font-size !default;\n$title-bar-line-height:             $display-line-height !default;\n$title-bar-letter-spacing:          $h2-spacing !default;\n$title-bar-border-width:            calc(var(--#{$prefix}border-width) * .5) !default; // stylelint-disable-line function-disallowed-list\n$title-bar-border-color:            var(--#{$prefix}border-color-subtle) !default;\n\n$title-bar-font-size-md:            $display2-size !default;\n$title-bar-letter-spacing-md:       $display2-spacing !default;\n\n$title-bar-font-size-xl:            $display1-size !default;\n$title-bar-letter-spacing-xl:       $display1-spacing !default;\n\n// fusv-disable\n$title-bar-border-color-dark:       $gray-700 !default; // Deprecated in v5.3.3\n// fusv-enable\n// scss-docs-end title-bars-variables\n// End mod\n\n\n// Breadcrumbs\n\n// scss-docs-start breadcrumb-variables\n$breadcrumb-font-size:              $font-size-sm !default;\n$breadcrumb-font-weight:            $font-weight-bold !default; // Boosted mod\n\n$breadcrumb-padding-y:              .5rem !default;\n$breadcrumb-padding-x:              0 !default;\n$breadcrumb-item-padding-x:         $spacer * .5 !default;\n$breadcrumb-margin-bottom:          1rem !default;\n$breadcrumb-color:                  var(--#{$prefix}emphasis-color) !default; // Boosted mod\n$breadcrumb-bg:                     null !default;\n$breadcrumb-divider-color:          $black !default; // Boosted mod: instead of `var(--#{$prefix}secondary-color)`\n$breadcrumb-active-color:           null !default; // Boosted mod: instead of `var(--#{$prefix}secondary-color)`\n$breadcrumb-divider:                url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 9 14' width='7' height='10'><path d='m-.4 12 2 2 7-7-7-7-2 2 5 5z'/></svg>\") !default;\n$breadcrumb-divider-filter:         none !default; // Boosted mod\n$breadcrumb-divider-flipped:        $breadcrumb-divider !default;\n$breadcrumb-border-radius:          null !default;\n// scss-docs-end breadcrumb-variables\n\n\n// Carousel\n\n// scss-docs-start carousel-variables\n$carousel-control-color:             $black !default;\n$carousel-control-width:             $spacer * 3 !default;\n$carousel-control-opacity:           null !default;\n$carousel-control-hover-opacity:     null !default;\n$carousel-control-transition:        $transition-focus !default;\n// Boosted mod: no $carousel-control-icon-filter\n\n$carousel-indicator-width:           .5rem !default;\n$carousel-indicator-height:          .5rem !default;\n$carousel-indicator-hit-area-height: $spacer * 1.5 !default;\n$carousel-indicator-spacer:          $spacer * .5 !default;\n$carousel-indicator-opacity:         null !default;\n$carousel-indicator-active-bg:       $white !default;\n$carousel-indicator-active-opacity:  null !default;\n$carousel-indicator-transition:      null !default;\n// Boosted mod\n$carousel-indicator-hover-scale:        1.5 !default;\n$carousel-indicator-active-scale:       calc(2 / 3) !default; // stylelint-disable-line function-disallowed-list\n$carousel-indicator-active-radius:      0 100% 100% 0 / 50% !default;\n$carousel-indicator-animation-duration: 5000ms !default;\n$carousel-indicator-animation-interval: var(--#{$prefix}carousel-interval, #{$carousel-indicator-animation-duration}) !default;\n$carousel-indicators-padding-y:         $spacer * .5 !default;\n$carousel-indicators-margin-bottom:     $spacer !default;\n// End mod\n\n$carousel-caption-width:             70% !default;\n$carousel-caption-color:             null !default; // Boosted mod: instead of `var(--#{$prefix}body-color)`\n$carousel-caption-bg:                var(--#{$prefix}body-bg) !default; // Boosted mod\n$carousel-caption-padding-y:         $spacer !default;\n$carousel-caption-padding-x:         $spacer !default; // Boosted mod\n$carousel-caption-spacer:            $spacer * 3 !default;\n\n$carousel-control-icon-width:        2.5rem !default;\n// Boosted mod\n$carousel-control-icon-size:         1rem 1.5rem !default;\n$carousel-control-icon-bg:           var(--#{$prefix}chevron-icon) !default;\n$carousel-control-icon-color:        $black !default; // Boosted mod\n$carousel-control-icon-active-bg:    $component-active-bg !default;\n\n$carousel-control-pause-indicators-spacing: 10px !default;\n$carousel-control-pause-icon:               $pause-icon !default;\n$carousel-control-play-icon:                $play-icon !default;\n$carousel-control-pause-button-size:        .75rem !default;\n$carousel-control-pause-icon-size:          .75rem .75rem !default;\n// End mod\n\n$carousel-transition-duration:       .6s !default;\n$carousel-transition:                transform $carousel-transition-duration $transition-timing !default; // Define transform transition first if using multiple transitions (e.g., `transform 2s ease, opacity .5s ease-out`)\n// scss-docs-end carousel-variables\n\n// Boosted mod: no dark carousel variables\n\n\n// Spinners\n\n// scss-docs-start spinner-variables\n$spinner-color:           null !default; // Boosted mod\n$spinner-width:           $spacer * 2 !default;\n$spinner-height:          $spinner-width !default;\n$spinner-vertical-align:  -.125em !default;\n// stylelint-disable-next-line function-disallowed-list\n$spinner-border-width:    calc(var(--#{$prefix}border-width) * 3) !default; // Boosted mod\n$spinner-animation-speed: .75s !default;\n\n$spinner-width-sm:        $spacer !default;\n$spinner-height-sm:       $spinner-width-sm !default;\n// stylelint-disable-next-line function-disallowed-list\n$spinner-border-width-sm: calc(var(--#{$prefix}border-width) * 2) !default; // Boosted mod\n\n$spinner-width-lg:        $spacer * 4 !default; // Boosted mod\n$spinner-height-lg:       $spinner-width-lg !default; // Boosted mod\n// stylelint-disable-next-line function-disallowed-list\n$spinner-border-width-lg: calc(var(--#{$prefix}border-width) * 4) !default; // Boosted mod\n// scss-docs-end spinner-variables\n\n\n// Close\n\n// scss-docs-start close-variables\n$btn-close-width:               $spacer !default; // Boosted mod\n$btn-close-height:              $btn-close-width !default;\n$btn-close-padding:             var(--#{$prefix}icon-spacing, #{$btn-icon-padding-x}) !default; // Boosted mod\n$btn-close-border-width:        var(--#{$prefix}border-width) !default; // Boosted mod\n$btn-close-border-color:        transparent !default; // Boosted mod\n$btn-close-color:               var(--#{$prefix}emphasis-color) !default;\n$btn-close-bg:                  var(--#{$prefix}close-icon) !default; // Boosted mod\n// Boosted mod\n// fusv-disable\n$btn-close-focus-shadow:        $btn-focus-box-shadow !default; // Deprecated in v5.3.0\n// fusv-enable\n// End mod\n\n// Boosted mod: no opacity/filter\n\n// Boosted mod\n$btn-close-hover-color:         $btn-close-color !default;\n$btn-close-active-color:        var(--#{$prefix}primary) !default;\n$btn-close-active-border-color: var(--#{$prefix}border-color-subtle) !default;\n$btn-close-disabled-color:      var(--#{$prefix}disabled-color) !default;\n\n$btn-close-icon-size:           1rem auto !default;\n$btn-close-padding-sm:          subtract($btn-icon-padding-x, $spacer * .25) !default;\n// End mod\n// scss-docs-end close-variables\n\n// Deprecated in v5.3.3: all `$btn-close-white-*`\n$btn-close-white-color:               $white !default; // Boosted mod\n$btn-close-white-bg:                  transparent !default; // Boosted mod\n$btn-close-white-border-color:        transparent !default; // Boosted mod\n$btn-close-white-hover-color:         $btn-close-white-color !default; // Boosted mod\n$btn-close-white-active-color:        $supporting-orange !default; // Boosted mod\n$btn-close-white-active-border-color: $gray-700 !default; // Boosted mod\n$btn-close-white-disabled-color:      $gray-700 !default; // Boosted mod\n\n// Offcanvas\n\n// scss-docs-start offcanvas-variables\n$offcanvas-padding-y:               $modal-inner-padding !default;\n$offcanvas-padding-x:               $modal-inner-padding !default;\n$offcanvas-horizontal-width:        400px !default;\n$offcanvas-vertical-height:         30vh !default;\n$offcanvas-transition-duration:     .3s !default;\n$offcanvas-border-color:            $modal-content-border-color !default;\n$offcanvas-border-width:            $modal-content-border-width !default;\n$offcanvas-title-line-height:       $modal-title-line-height !default;\n$offcanvas-bg-color:                $modal-content-bg !default; // Boosted mod: instead of `var(--#{$prefix}body-bg)`\n$offcanvas-color:                   $modal-content-color !default; // Boosted mod: instead of `var(--#{$prefix}body-color)`\n$offcanvas-box-shadow:              none !default; // Boosted mod\n$offcanvas-backdrop-bg:             $modal-backdrop-bg !default;\n$offcanvas-backdrop-opacity:        $modal-backdrop-opacity !default;\n// scss-docs-end offcanvas-variables\n\n// Code\n// Boosted mod\n$code-font-size:                    .875em !default;\n$code-color:                        $gray-700 !default;\n\n$kbd-padding-y:                     $spacer * .05 !default;\n$kbd-padding-x:                     $spacer * .05 !default;\n$kbd-font-size:                     $code-font-size !default;\n$kbd-color:                         var(--#{$prefix}kbd-color, $black) !default;\n$kbd-bg:                            var(--#{$prefix}kbd-bg, $gray-300) !default;\n$nested-kbd-font-weight:            null !default; // Deprecated in v5.2.0, removing in v6\n\n$pre-color:                         var(--#{$prefix}code-color) !default;\n$pre-line-height:                   1.25 !default;\n// End mod\n\n//\n// Boosted mod\n//\n\n//// Scroll margin\n$scroll-offset-top:              $spacer * 6 !default; // Matching .navbar computed height\n\n//// Back to top\n// scss-docs-start back-to-top\n$back-to-top-display-threshold:  100vh !default;\n$back-to-top-target-id:          \"top\" !default;\n$back-to-top-target-offset-top:  $scroll-offset-top !default;\n$back-to-top-offset:             $spacer * 1.5 !default;\n$back-to-top-offset-right:       $back-to-top-offset !default;\n$back-to-top-offset-bottom:      $back-to-top-offset !default;\n$back-to-top-link-offset-top:    subtract(100vh, $back-to-top-offset * 4) !default;\n$back-to-top-link-offset-top-xl: subtract(100vh, $spacer * 5) !default;\n$back-to-top-title-offset-right: add(100%, var(--#{$prefix}border-width)) !default;\n$back-to-top-title-padding:      subtract($btn-padding-y, 1px) $btn-padding-x add($btn-padding-y, 1px) !default;\n$back-to-top-title-color:        var(--#{$prefix}body-color) !default;\n$back-to-top-title-bg-color:     var(--#{$prefix}body-bg) !default;\n$back-to-top-bg:                 var(--#{$prefix}highlight-color) !default;\n$back-to-top-icon:               var(--#{$prefix}chevron-icon) !default;\n$back-to-top-icon-width:         add(.5rem, 1px) !default;\n$back-to-top-icon-height:        subtract(1rem, 1px) !default;\n// scss-docs-end back-to-top\n\n//// Stepped process\n// scss-docs-start stepped-process\n$stepped-process-font-size:   $small-font-size !default;\n$stepped-process-font-weight: $font-weight-bold !default;\n$stepped-process-max-items:   5 !default;\n$stepped-process-counter:     step !default; // Used as a counter name\n$stepped-process-bg:          var(--#{$prefix}body-bg) !default;\n\n$step-item-padding:           7px !default;\n// fusv-disable\n$step-item-padding-end:       $step-item-padding * 2 !default; // Deprecated in v5.2.0\n// fusv-enable\n$step-item-margin-end:        var(--#{$prefix}border-width) !default;\n$step-item-bg:                var(--#{$prefix}secondary) !default;\n$step-item-active-bg:         $supporting-orange !default;\n$step-item-next-bg:           var(--#{$prefix}border-color-subtle) !default;\n$step-item-shadow-size:       calc(var(--#{$prefix}border-width) * 1.5) !default; // stylelint-disable-line function-disallowed-list\n$step-item-drop-shadow:       drop-shadow($step-item-shadow-size 0 0 var(--#{$prefix}stepped-process-bg)) #{\"/* rtl:\"} drop-shadow(calc(-1 * #{$step-item-shadow-size}) 0 0 var(--#{$prefix}stepped-process-bg)) #{\"*/\"} !default; // stylelint-disable-line function-disallowed-list\n\n$step-item-arrow-width:       .8125rem !default;\n$step-item-arrow-shape:       polygon(0% 0%, 1px 0%, subtract(100%, var(--#{$prefix}border-width)) 50%, 1px 100%, 0% 100%) #{\"/* rtl:\"} polygon(100% 0%, subtract(100%, 1px) 0%, var(--#{$prefix}border-width) 50%, subtract(100%, 1px) 100%, 100% 100%) #{\"*/\"} !default; // Used in clip-path\n\n$step-link-width:             1.25ch !default; // Matches width of a single number\n$step-link-color:             var(--#{$prefix}highlight-color) !default;\n$step-link-active-color:      $black !default;\n$step-link-active-outline:    $black !default;\n$step-link-next-color:        var(--#{$prefix}link-color) !default;\n$step-link-line-height:       $line-height-sm !default;\n$step-link-marker:            counter(var(--bs-stepped-process-counter)) inspect(\"\\A0\") !default;\n$step-link-marker-lg:         counter(var(--bs-stepped-process-counter)) inspect(\".\\A0\") !default;\n$step-link-text-decoration:   $link-decoration !default;\n// scss-docs-end stepped-process\n\n\n//// Sticker\n// scss-docs-start sticker\n$sticker-color:                         $black !default;\n$sticker-background-color:              $supporting-orange !default;\n$sticker-font-weight:                   $font-weight-bold !default;\n\n$sticker-size-sm:                       $spacer * 7 !default;\n$sticker-size-md:                       $spacer * 9 !default;\n$sticker-size-lg:                       $spacer * 14 !default;\n\n// Considering @use \"sass:math\", math.sqrt(2) / 2 is approximated to 0.7071067812\n$sticker-content-max-width-sm:          $sticker-size-sm * .7071067812 !default;\n$sticker-content-max-width-md:          $sticker-size-md * .7071067812 !default;\n$sticker-content-max-width-lg:          $sticker-size-lg * .7071067812 !default;\n// scss-docs-end sticker\n\n//// Quantity selector\n// scss-docs-start quantity-selector\n$quantity-selector-width:                 7.5rem !default;\n$quantity-selector-sm-width:              5.625rem !default;\n\n$quantity-selector-btn-padding-x:         add($btn-icon-padding-x, 2px) !default;\n$quantity-selector-btn-padding-x-sm:      add($btn-icon-padding-x-sm, 2px) !default;\n$quantity-selector-btn-bg:                var(--#{$prefix}body-bg) !default;\n$quantity-selector-btn-border:            var(--#{$prefix}border-width) solid var(--#{$prefix}border-color-subtle) !default;\n\n$quantity-selector-disabled-color:        var(--#{$prefix}disabled-color) !default;\n$quantity-selector-disabled-bg:           var(--#{$prefix}body-bg) !default;\n\n$quantity-selector-icon-width:            .875rem !default;\n$quantity-selector-icon-sm-width:         .625rem !default;\n\n$quantity-selector-icon-add:              $add-icon !default;\n$quantity-selector-icon-add-sm:           $add-icon-sm !default;\n$quantity-selector-icon-add-height:       .875rem !default;\n$quantity-selector-icon-sm-add-height:    .625rem !default;\n\n$quantity-selector-icon-remove:           $remove-icon !default;\n$quantity-selector-icon-remove-sm:        $remove-icon-sm !default;\n$quantity-selector-icon-remove-height:    .125rem !default;\n$quantity-selector-icon-sm-remove-height: .125rem !default;\n\n$quantity-selector-input-max-width:       2.5rem !default;\n$quantity-selector-input-sm-max-width:    1.875rem !default;\n// scss-docs-end quantity-selector\n\n//// Footer\n// scss-docs-start footer\n$footer-color:                            var(--#{$prefix}body-color) !default;\n$footer-font-size-sm:                     $font-size-sm !default;\n$footer-line-height-sm:                   $line-height-sm !default;\n$footer-font-size-md:                     $font-size-base !default;\n$footer-line-height-md:                   $line-height-base !default;\n$footer-title-font-weight:                $font-weight-bold !default;\n$footer-letter-spacing:                   $letter-spacing-base !default;\n$footer-accordion-line-height:            $spacer * 1.45 !default;\n$footer-accordion-active-color:           var(--#{$prefix}primary) !default;\n$footer-accordion-btn-hover-bg:           null !default;\n// fusv-disable\n$footer-nav-link-font-weight:             $font-weight-bold !default; // Deprecated in v5.3.3\n// fusv-enable\n\n$footer-title-content-padding-top:        $spacer * 1.25 !default;\n$footer-title-content-padding-bottom:     $spacer * 1.45 !default;\n$footer-title-content-padding-bottom-md:  $spacer * 1.95 !default;\n$footer-title-margin-bottom:              $spacer * .85 !default;\n\n$footer-social-padding-top:               $spacer * .85 !default;\n$footer-social-padding-top-md:            $spacer * 1.5 !default;\n$footer-social-padding-bottom:            $spacer * 1.45 !default;\n$footer-social-title-margin-bottom-md:    $spacer * .1 !default;\n\n$footer-title-margin-bottom-md:           $spacer * 1.05 !default;\n$footer-nav-padding-top:                  $spacer * 1.55 !default;\n$footer-nav-list-padding-top:             $spacer * .85 !default;\n$footer-nav-list-padding-top-md:          $spacer * .05 !default;\n$footer-nav-list-padding-bottom:          $spacer * 1.3 !default;\n$footer-nav-list-padding-bottom-md:       $spacer * 1.75 !default;\n$footer-nav-list-padding-bottom-lg:       $spacer * 2 !default;\n\n$footer-service-padding-y:                $spacer !default;\n$footer-service-padding-y-md:             $spacer * 1.2 !default;\n$footer-service-link-padding-top:         $spacer * .1 !default;\n\n$footer-terms-padding-top:                $spacer * .85 !default;\n$footer-terms-padding-bottom:             $spacer * 1.35 !default;\n$footer-terms-padding-y-md:               $spacer * 1.1 !default;\n\n$footer-gap:                              $spacer * .75 !default;\n$footer-gap-xl:                           $spacer * 1.7 !default;\n// scss-docs-end footer\n\n\n// Tags\n\n// scss-docs-start tag-variables\n$tag-color:                         var(--#{$prefix}body-color) !default;\n$tag-bg:                            var(--#{$prefix}body-bg) !default;\n\n$tag-gap:                           map-get($spacers, 1) !default;\n$tag-font-shift:                    $spacer * .1 !default;\n$tag-font-weight:                   $font-weight-bold !default;\n$tag-border-width:                  var(--#{$prefix}border-width) !default;\n$tag-border-radius:                 var(--#{$prefix}border-radius-pill) !default;\n\n$tag-padding-x:                     $spacer * .65 !default;\n$tag-padding-y:                     $spacer * .45 !default;\n$tag-icon-size:                     $spacer * 1.2 !default;\n$tag-icon-margin-start:             -$spacer * .35 !default;\n$tag-close-margin-end:              -$spacer * .3 !default;\n$tag-close-margin-start:            $spacer * .2 !default;\n$tag-font-size:                     $font-size-base !default;\n\n$tag-active-color:                  var(--#{$prefix}highlight-color) !default;\n$tag-disabled-color:                var(--#{$prefix}disabled-color) !default;\n$tag-border-color:                  var(--#{$prefix}border-color-subtle) !default;\n$tag-active-decoration-color:       var(--#{$prefix}highlight-bg) !default;\n// scss-docs-end tag-variables\n\n// scss-docs-start tag-sm-variables\n$tag-padding-x-sm:                  $spacer * .4 !default;\n$tag-padding-y-sm:                  $spacer * .25 !default;\n$tag-icon-size-sm:                  $spacer !default;\n$tag-icon-margin-start-sm:          -$spacer * .1 !default;\n$tag-close-margin-end-sm:           -$spacer * .25 !default;\n$tag-close-margin-start-sm:         0 !default;\n$tag-font-size-sm:                  $font-size-sm !default;\n// scss-docs-end tag-sm-variables\n\n\n// Local navigation\n\n// scss-docs-start local-nav-variables\n$local-nav-padding-y:           $navbar-nav-link-padding-y !default;\n$local-nav-color:               null !default;\n$local-nav-bg:                  var(--#{$prefix}body-bg) !default;\n$local-nav-hover-color:         var(--#{$prefix}link-hover-color) !default;\n$local-nav-hover-bg:            var(--#{$prefix}secondary-bg) !default;\n$local-nav-active-color:        var(--#{$prefix}primary) !default;\n$local-nav-active-bg:           var(--#{$prefix}tertiary-active-bg) !default;\n$local-nav-active-marker-width: $spacer * .2 !default;\n$local-nav-border-color:        var(--#{$prefix}border-color-subtle) !default;\n$local-nav-border-width:        calc(var(--#{$prefix}border-width) * .5) !default; // stylelint-disable-line function-disallowed-list\n// scss-docs-end local-nav-variables\n// End mod\n\n@import \"variables-dark\"; // TODO: can be removed safely in v6, only here to avoid breaking changes in v5.3\n", "// scss-docs-start focus-visible\n@mixin focus-visible($color: var(--#{$prefix}focus-visible-outer-color), $width: $focus-visible-outer-width, $offset: $focus-visible-outer-offset, $box-width: $focus-visible-inner-width, $box-color: var(--#{$prefix}focus-visible-inner-color)) {\n  isolation: isolate;\n  outline: $width solid $color;\n  outline-offset: $offset;\n  box-shadow: 0 0 0 $box-width $box-color;\n  @include transition($transition-focus);\n}\n// scss-docs-end focus-visible\n"]}