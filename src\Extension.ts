import * as vscode from 'vscode';
import { ChatViewProvider } from './ChatViewProvider';
import * as ChildProcess from 'child_process';
import { registerEditFeature } from './Edits';

// Fonction pour récupérer les variables d'environnement système
function getSystemEnvVar(varName: string): string | undefined {
        try {
                let result = ChildProcess.execSync(`powershell -Command "[Environment]::GetEnvironmentVariable('${varName}', 'User')"`, { encoding: 'utf8' });
                let value = result.trim();
                
                if (!value || value === '') {
                        // Si pas trouvé dans les variables utilisateur, essayer les variables système
                        result = ChildProcess.execSync(`powershell -Command "[Environment]::GetEnvironmentVariable('${varName}', 'Machine')"`, { encoding: 'utf8' });
                        value = result.trim();
                }
                
                return value && value !== '' ? value : undefined;
        } catch (error) {
                return undefined;
        }
}

export function activate(context: vscode.ExtensionContext) {
        const statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);
        statusBarItem.text = '$(robot) Dinootoo : Prêt';
        statusBarItem.show();

        const provider = new ChatViewProvider(context.extensionUri, statusBarItem, context);
        // Enregistrer la fonctionnalité d'édition inline (séparée)
        registerEditFeature(context, statusBarItem);
        const systemOpenRouterApi = getSystemEnvVar('OPENROUTER_API');
        const envApiKey = process.env.OPENROUTER_API || systemOpenRouterApi;
        
        if (envApiKey && envApiKey.trim().length > 0) {
                void (async () => {
                        try {
                                const existing = await context.secrets.get('openrouterApiKey');
                                if (existing !== envApiKey) {
                                        await context.secrets.store('openrouterApiKey', envApiKey);
                                }
                                await context.globalState.update('openrouterApiKeySource', 'env');
                                vscode.window.showInformationMessage('Clef API OpenRouter chargée depuis la variable d\'environnement.');
                        } catch (error) {
                                vscode.window.showErrorMessage('Erreur lors du stockage de la clef API depuis la variable d\'environnement.');
                        }
                })();
        }

        const sendToDinootooCommand = vscode.commands.registerCommand('dinootoo-companion.sendToDinootoo', () => {
                const editor = vscode.window.activeTextEditor;
                if (editor) {
                        const selection = editor.selection;
                        if (!selection.isEmpty) {
                                const selectedText = editor.document.getText(selection);
                                const fileName = editor.document.fileName.split('\\').pop()?.split('/').pop() ?? 'unknown';
                                const startLine = selection.start.line + 1;
                                const endLine = selection.end.line + 1;

                                provider.sendCodeSnippet(selectedText, fileName, startLine, endLine);
                        }
                }
        });


        context.subscriptions.push(
                vscode.window.registerWebviewViewProvider(ChatViewProvider.viewType, provider),
                statusBarItem,
                sendToDinootooCommand
        );
}

export function deactivate() {}
