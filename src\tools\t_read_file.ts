import * as vscode from 'vscode';
import * as fs from 'fs/promises';
import * as path from 'path';

export const description = `Lit des sections d'un fichier pour confirmer une hypothèse. Utilise des fenêtres de lignes (ex: 1-200 puis 201-400) pour les gros fichiers. Doit précéder t_edit_file. Évite de lire la totalité d’un gros fichier d’un seul coup.`;

export type ReadFileArgs = { target_file: string; start_line?: number; end_line?: number };
export type ReadFileResult = {
  file: string;
  start_line: number;
  end_line: number;
  total_lines: number;
  content: string;
};

function getWorkspaceRoot(): string {
  const root = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
  if (!root) throw new Error('Workspace root not found');
  return root;
}

export async function t_read_file(args: ReadFileArgs): Promise<ReadFileResult> {
  const root = getWorkspaceRoot();
  const full = path.resolve(root, args.target_file);
  const buf = await fs.readFile(full, 'utf8');
  const linesRaw = buf.split(/\r?\n/);
  // Normalize: ignore trailing empty line caused by terminal newline
  const lines = (linesRaw.length > 0 && linesRaw[linesRaw.length - 1] === '') ? linesRaw.slice(0, -1) : linesRaw;
  const total = lines.length;
  const start = Math.max(1, args.start_line ?? 1);
  const end = Math.min(total, args.end_line ?? total);
  if (start > end) throw new Error('Plage de lignes invalide (start > end)');
  const slice = lines.slice(start - 1, end).join('\n');
  return { file: path.relative(root, full), start_line: start, end_line: end, total_lines: total, content: slice };
}
