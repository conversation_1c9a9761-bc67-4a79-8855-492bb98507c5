/* Styles spécifiques pour le panneau des paramètres - Style simple avec bords carrés */

/* Fond avec les couleurs du thème VS Code */
body {
    background-color: var(--vscode-editor-background);
    color: var(--vscode-editor-foreground);
    margin: 0;
    padding: 0;
}

.settings-container {
    padding: 1rem;
    font-size: 0.8rem;
    background-color: var(--vscode-editor-background);
    color: var(--vscode-editor-foreground);
    min-height: 100vh;
    box-sizing: border-box;
}

.settings-container h3 {
    margin-bottom: 1rem;
    font-size: 1.1rem;
    font-weight: 600;
}

/* Groupes de formulaire - même style que la webview principale */
.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.25rem;
    font-weight: 500;
    font-size: 0.8rem;
}

/* Champs de saisie - bords carrés, même style que l'input principal */
.form-control,
.form-control:focus {
    border-radius: 0 !important; /* Bords carrés (y compris au focus) */
}

/* Boutons - même style que les boutons de la webview principale */
.btn,
.btn:focus {
    border-radius: 0 !important; /* Bords carrés (y compris au focus) */
}

/* Styles pour l'accordéon - cohérent avec le design du panneau */
.accordion {
    margin-top: 1rem;
}

.accordion * {
    border: none !important;
}

.accordion-item {
    border-radius: 0 !important;
    margin-bottom: 0.5rem;
    background-color: var(--vscode-input-background);
}

.accordion-header {
    margin: 0;
}

.accordion-button,
.accordion-button:not(.collapsed),
.accordion-button:focus {
    background-color: var(--vscode-input-background) !important;
    color: var(--vscode-input-foreground) !important;
    box-shadow: none !important;
}
.accordion-button {
    border-radius: 0 !important;
    padding: 0.5rem 0.75rem;
    font-size: 0.85rem;
    font-weight: 600;
}

/* states consolidés ci-dessus */

.accordion-button::after {
    background-size: 0.8rem;
    width: 0.8rem;
    height: 0.8rem;
}

/* borders supprimés via .accordion * */

.accordion-body {
    padding: 0.75rem;
    background-color: var(--vscode-input-background);
    font-size: 0.8rem;
    line-height: 1.4;
}

.accordion-body p {
    margin-bottom: 0.5rem;
}

.accordion-body ul, .accordion-body ol {
    margin-bottom: 0.5rem;
    padding-left: 1rem;
}

.accordion-body li {
    margin-bottom: 0.25rem;
}

.accordion-body code {
    background-color: var(--vscode-textBlockQuote-background);
    padding: 0.1rem 0.3rem;
    border-radius: 2px;
    font-size: 0.7rem;
}

.accordion-body a {
    color: var(--vscode-textLink-foreground);
    text-decoration: none;
}

.accordion-body a:hover {
    text-decoration: underline;
}
