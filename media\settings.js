(function () {
    const vscode = acquireVsCodeApi();

    const saveButton = document.getElementById('save-button');
    const clearHistoryButton = document.getElementById('clear-history-button');

            if (saveButton) {
            saveButton.addEventListener('click', () => {
                const apiKeyEl = document.getElementById('api-key');
                const modelEl = document.getElementById('model');
                const systemPromptEl = document.getElementById('system-prompt');
                const codeBlocksExpandedEl = document.getElementById('code-blocks-expanded');
                if (!apiKeyEl || !modelEl || !systemPromptEl || !codeBlocksExpandedEl) {
                    console.error('[settings] Champs requis manquants: api-key / model / system-prompt / code-blocks-expanded');
                    return;
                }
                const apiKey = apiKeyEl.value?.trim?.() ?? '';
                const model = modelEl.value ?? '';
                const systemPrompt = systemPromptEl.value ?? '';
                const codeBlocksExpanded = codeBlocksExpandedEl.value === 'true';
                vscode.postMessage({
                    type: 'saveSettings',
                    apiKey: apiKey,
                    model: model,
                    systemPrompt: systemPrompt,
                    codeBlocksExpanded: codeBlocksExpanded
                });
            });
        }

    if (clearHistoryButton) {
        clearHistoryButton.addEventListener('click', () => {
            vscode.postMessage({
                type: 'clearHistory'
            });
        });
    }
}());
