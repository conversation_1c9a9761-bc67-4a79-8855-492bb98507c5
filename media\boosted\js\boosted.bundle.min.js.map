{"version": 3, "names": ["elementMap", "Map", "Data", "set", "element", "key", "instance", "has", "instanceMap", "get", "size", "console", "error", "Array", "from", "keys", "remove", "delete", "TRANSITION_END", "parseSelector", "selector", "window", "CSS", "escape", "replace", "match", "id", "triggerTransitionEnd", "dispatchEvent", "Event", "isElement", "object", "j<PERSON>y", "nodeType", "getElement", "length", "document", "querySelector", "isVisible", "getClientRects", "elementIsVisible", "getComputedStyle", "getPropertyValue", "closedDetails", "closest", "summary", "parentNode", "isDisabled", "Node", "ELEMENT_NODE", "classList", "contains", "disabled", "hasAttribute", "getAttribute", "findShadowRoot", "documentElement", "attachShadow", "getRootNode", "root", "ShadowRoot", "noop", "reflow", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "body", "DOMContentLoadedCallbacks", "isRTL", "dir", "defineJQueryPlugin", "plugin", "callback", "$", "name", "NAME", "JQUERY_NO_CONFLICT", "fn", "jQueryInterface", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "readyState", "addEventListener", "push", "execute", "<PERSON><PERSON><PERSON><PERSON>", "args", "defaultValue", "call", "executeAfterTransition", "transitionElement", "waitForTransition", "emulatedDuration", "transitionDuration", "transitionDelay", "floatTransitionDuration", "Number", "parseFloat", "floatTransitionDelay", "split", "getTransitionDurationFromElement", "called", "handler", "target", "removeEventListener", "setTimeout", "getNextActiveElement", "list", "activeElement", "shouldGetNext", "isCycleAllowed", "listLength", "index", "indexOf", "Math", "max", "min", "namespaceRegex", "stripNameRegex", "stripUidRegex", "eventRegistry", "uidEvent", "customEvents", "mouseenter", "mouseleave", "nativeEvents", "Set", "makeEventUid", "uid", "getElementEvents", "<PERSON><PERSON><PERSON><PERSON>", "events", "callable", "delegationSelector", "Object", "values", "find", "event", "normalizeParameters", "originalTypeEvent", "delegationFunction", "isDelegated", "typeEvent", "getTypeEvent", "add<PERSON><PERSON><PERSON>", "oneOff", "wrapFunction", "relatedTarget", "<PERSON><PERSON><PERSON><PERSON>", "this", "handlers", "previousFunction", "dom<PERSON><PERSON>s", "querySelectorAll", "dom<PERSON>lement", "hydrateObj", "EventHandler", "off", "type", "apply", "bootstrapDelegationHandler", "bootstrapHandler", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "removeNamespacedHandlers", "namespace", "storeElementEvent", "handler<PERSON><PERSON>", "entries", "includes", "on", "one", "inNamespace", "isNamespace", "startsWith", "elementEvent", "slice", "keyHandlers", "trigger", "jQueryEvent", "bubbles", "nativeDispatch", "defaultPrevented", "isPropagationStopped", "isImmediatePropagationStopped", "isDefaultPrevented", "evt", "cancelable", "preventDefault", "obj", "meta", "value", "_unused", "defineProperty", "configurable", "normalizeData", "toString", "JSON", "parse", "decodeURIComponent", "normalizeDataKey", "chr", "toLowerCase", "Manipulator", "setDataAttribute", "setAttribute", "removeDataAttribute", "removeAttribute", "getDataAttributes", "attributes", "bs<PERSON><PERSON>s", "dataset", "filter", "pureKey", "char<PERSON>t", "getDataAttribute", "Config", "<PERSON><PERSON><PERSON>", "DefaultType", "Error", "_getConfig", "config", "_mergeConfigObj", "_configAfterMerge", "_typeCheckConfig", "jsonConfig", "constructor", "configTypes", "property", "expectedTypes", "valueType", "prototype", "RegExp", "test", "TypeError", "toUpperCase", "BaseComponent", "super", "_element", "_config", "DATA_KEY", "dispose", "EVENT_KEY", "propertyName", "getOwnPropertyNames", "_queueCallback", "isAnimated", "getInstance", "getOrCreateInstance", "VERSION", "eventName", "getSelector", "hrefAttribute", "trim", "map", "sel", "join", "SelectorEngine", "concat", "Element", "findOne", "children", "child", "matches", "parents", "ancestor", "prev", "previous", "previousElementSibling", "next", "nextElement<PERSON><PERSON>ling", "focusableC<PERSON><PERSON>n", "focusables", "el", "getSelectorFromElement", "getElementFromSelector", "getMultipleElementsFromSelector", "enableDismissTrigger", "component", "method", "clickEvent", "tagName", "EVENT_CLOSE", "EVENT_CLOSED", "<PERSON><PERSON>", "close", "_destroyElement", "each", "data", "undefined", "SELECTOR_DATA_TOGGLE", "<PERSON><PERSON>", "toggle", "button", "EVENT_TOUCHSTART", "EVENT_TOUCHMOVE", "EVENT_TOUCHEND", "EVENT_POINTERDOWN", "EVENT_POINTERUP", "endCallback", "leftCallback", "<PERSON><PERSON><PERSON><PERSON>", "Swipe", "isSupported", "_deltaX", "_supportPointerEvents", "PointerEvent", "_initEvents", "_start", "_eventIsPointerPenTouch", "clientX", "touches", "_end", "_handleSwipe", "_move", "absDeltaX", "abs", "direction", "add", "pointerType", "navigator", "maxTouchPoints", "DATA_API_KEY", "ARROW_LEFT_KEY", "ARROW_RIGHT_KEY", "ORDER_NEXT", "ORDER_PREV", "DIRECTION_LEFT", "DIRECTION_RIGHT", "EVENT_SLIDE", "EVENT_SLID", "EVENT_KEYDOWN", "EVENT_MOUSEENTER", "EVENT_MOUSELEAVE", "EVENT_DRAG_START", "EVENT_LOAD_DATA_API", "EVENT_CLICK_DATA_API", "CLASS_NAME_CAROUSEL", "CLASS_NAME_ACTIVE", "CLASS_NAME_PAUSED", "CLASS_NAME_DONE", "CLASS_NAME_PAUSE", "CLASS_NAME_PLAY", "SELECTOR_ACTIVE", "SELECTOR_ITEM", "SELECTOR_ACTIVE_ITEM", "SELECTOR_CONTROL_PAUSE", "SELECTOR_CAROUSEL_TO_PAUSE", "SELECTOR_CAROUSEL_PLAY_TEXT", "SELECTOR_CAROUSEL_PAUSE_TEXT", "SELECTOR_CAROUSEL_DEFAULT_PLAY_TEXT", "SELECTOR_CAROUSEL_DEFAULT_PAUSE_TEXT", "KEY_TO_DIRECTION", "ARROW_LEFT_KEY$1", "ARROW_RIGHT_KEY$1", "interval", "keyboard", "pause", "ride", "touch", "wrap", "Carousel", "_interval", "_activeElement", "_isSliding", "touchTimeout", "_swipe<PERSON><PERSON>per", "_indicatorsElement", "_playPauseButton", "_addEventListeners", "cycle", "_slide", "nextWhenVisible", "hidden", "innerHTML", "_stayPaused", "_clearInterval", "_updateInterval", "setInterval", "_maybeEnableCycle", "to", "items", "_getItems", "activeIndex", "_getItemIndex", "_getActive", "order", "defaultInterval", "_keydown", "_addTouchEventListeners", "img", "swipeConfig", "_directionToOrder", "endCallBack", "clearTimeout", "_disableControl", "nodeName", "_enableControl", "_setActiveIndicatorElement", "activeIndicator", "newActiveIndicator", "elementInterval", "parseInt", "currentIndex", "style", "setProperty", "isNext", "isPrev", "lastItemIndex", "nextElement", "nextElementIndex", "triggerEvent", "_orderToDirection", "isCycling", "prevControl", "nextControl", "directionalClassName", "orderClassName", "completeCallBack", "_isAnimated", "clearInterval", "PauseCarousel", "pauseButton", "pauseButtonAttribute", "carouselToPause", "carousel", "slideIndex", "carousels", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_HIDE", "EVENT_HIDDEN", "CLASS_NAME_SHOW", "CLASS_NAME_COLLAPSE", "CLASS_NAME_COLLAPSING", "CLASS_NAME_DEEPER_CHILDREN", "parent", "Collapse", "_isTransitioning", "_triggerArray", "toggleList", "elem", "filterElement", "foundElement", "_initializeC<PERSON><PERSON>n", "_addAriaAndCollapsedClass", "_isShown", "hide", "show", "activeC<PERSON><PERSON>n", "_getFirstLevelChildren", "activeInstance", "dimension", "_getDimension", "scrollSize", "complete", "getBoundingClientRect", "selected", "trigger<PERSON><PERSON>y", "isOpen", "top", "bottom", "right", "left", "auto", "basePlacements", "start", "end", "clippingParents", "viewport", "popper", "reference", "variationPlacements", "reduce", "acc", "placement", "placements", "beforeRead", "read", "afterRead", "<PERSON><PERSON><PERSON>", "main", "<PERSON><PERSON><PERSON>", "beforeWrite", "write", "afterWrite", "modifierPhases", "getNodeName", "getWindow", "node", "ownerDocument", "defaultView", "isHTMLElement", "HTMLElement", "isShadowRoot", "applyStyles$1", "enabled", "phase", "_ref", "state", "elements", "for<PERSON>ach", "styles", "assign", "effect", "_ref2", "initialStyles", "position", "options", "strategy", "margin", "arrow", "hasOwnProperty", "attribute", "requires", "getBasePlacement", "round", "getUAString", "uaData", "userAgentData", "brands", "isArray", "item", "brand", "version", "userAgent", "isLayoutViewport", "includeScale", "isFixedStrategy", "clientRect", "scaleX", "scaleY", "offsetWidth", "width", "height", "visualViewport", "addVisualOffsets", "x", "offsetLeft", "y", "offsetTop", "getLayoutRect", "rootNode", "isSameNode", "host", "isTableElement", "getDocumentElement", "getParentNode", "assignedSlot", "getTrueOffsetParent", "offsetParent", "getOffsetParent", "isFirefox", "currentNode", "css", "transform", "perspective", "contain", "<PERSON><PERSON><PERSON><PERSON>", "getContainingBlock", "getMainAxisFromPlacement", "within", "mathMax", "mathMin", "mergePaddingObject", "paddingObject", "expandToHashMap", "hashMap", "arrow$1", "_state$modifiersData$", "arrowElement", "popperOffsets", "modifiersData", "basePlacement", "axis", "len", "padding", "rects", "toPaddingObject", "arrowRect", "minProp", "maxProp", "endDiff", "startDiff", "arrowOffsetParent", "clientSize", "clientHeight", "clientWidth", "centerToReference", "center", "offset", "axisProp", "centerOffset", "_options$element", "requiresIfExists", "getVariation", "unsetSides", "mapToStyles", "_Object$assign2", "popperRect", "variation", "offsets", "gpuAcceleration", "adaptive", "roundOffsets", "isFixed", "_offsets$x", "_offsets$y", "_ref3", "hasX", "hasY", "sideX", "sideY", "win", "heightProp", "widthProp", "_Object$assign", "commonStyles", "_ref4", "dpr", "devicePixelRatio", "roundOffsetsByDPR", "computeStyles$1", "_ref5", "_options$gpuAccelerat", "_options$adaptive", "_options$roundOffsets", "passive", "eventListeners", "_options$scroll", "scroll", "_options$resize", "resize", "scrollParents", "scrollParent", "update", "hash", "getOppositePlacement", "matched", "getOppositeVariationPlacement", "getWindowScroll", "scrollLeft", "pageXOffset", "scrollTop", "pageYOffset", "getWindowScrollBarX", "isScrollParent", "_getComputedStyle", "overflow", "overflowX", "overflowY", "getScrollParent", "listScrollParents", "_element$ownerDocumen", "isBody", "updatedList", "rectToClientRect", "rect", "getClientRectFromMixedType", "clippingParent", "html", "layoutViewport", "getViewportRect", "clientTop", "clientLeft", "getInnerBoundingClientRect", "winScroll", "scrollWidth", "scrollHeight", "getDocumentRect", "computeOffsets", "commonX", "commonY", "mainAxis", "detectOverflow", "_options", "_options$placement", "_options$strategy", "_options$boundary", "boundary", "_options$rootBoundary", "rootBoundary", "_options$elementConte", "elementContext", "_options$altBoundary", "altBoundary", "_options$padding", "altContext", "clippingClientRect", "mainClippingParents", "clipperElement", "getClippingParents", "firstClippingParent", "clippingRect", "accRect", "getClippingRect", "contextElement", "referenceClientRect", "popperClientRect", "elementClientRect", "overflowOffsets", "offsetData", "multiply", "computeAutoPlacement", "flipVariations", "_options$allowedAutoP", "allowedAutoPlacements", "allPlacements", "allowedPlacements", "overflows", "sort", "a", "b", "flip$1", "_skip", "_options$mainAxis", "checkMainAxis", "_options$altAxis", "altAxis", "checkAltAxis", "specifiedFallbackPlacements", "fallbackPlacements", "_options$flipVariatio", "preferredPlacement", "oppositePlacement", "getExpandedFallbackPlacements", "referenceRect", "checksMap", "makeFallbackChecks", "firstFittingPlacement", "i", "_basePlacement", "isStartVariation", "isVertical", "mainVariationSide", "altVariationSide", "checks", "every", "check", "_loop", "_i", "fittingPlacement", "reset", "getSideOffsets", "preventedOffsets", "isAnySideFullyClipped", "some", "side", "hide$1", "preventOverflow", "referenceOverflow", "popperAltOverflow", "referenceClippingOffsets", "popperEscapeOffsets", "isReferenceHidden", "hasPopperEscaped", "offset$1", "_options$offset", "invertDistance", "skidding", "distance", "distanceAndSkiddingToXY", "_data$state$placement", "popperOffsets$1", "preventOverflow$1", "_options$tether", "tether", "_options$tetherOffset", "tetherOffset", "isBasePlacement", "tetherOffsetValue", "normalizedTetherOffsetValue", "offsetModifierState", "_offsetModifierState$", "mainSide", "altSide", "additive", "minLen", "maxLen", "arrowPaddingObject", "arrowPaddingMin", "arrowPaddingMax", "arrowLen", "minOffset", "maxOffset", "clientOffset", "offsetModifierValue", "tetherMax", "preventedOffset", "_offsetModifierState$2", "_mainSide", "_altSide", "_offset", "_len", "_min", "_max", "isOriginSide", "_offsetModifierValue", "_tetherMin", "_tetherMax", "_preventedOffset", "v", "withinMaxClamp", "getCompositeRect", "elementOrVirtualElement", "isOffsetParentAnElement", "offsetParentIsScaled", "isElementScaled", "modifiers", "visited", "result", "modifier", "dep", "depModifier", "DEFAULT_OPTIONS", "areValidElements", "arguments", "_key", "popperGenerator", "generatorOptions", "_generatorOptions", "_generatorOptions$def", "defaultModifiers", "_generatorOptions$def2", "defaultOptions", "pending", "orderedModifiers", "effectCleanupFns", "isDestroyed", "setOptions", "setOptionsAction", "cleanupModifierEffects", "merged", "orderModifiers", "current", "existing", "m", "_ref$options", "cleanupFn", "forceUpdate", "_state$elements", "_state$orderedModifie", "_state$orderedModifie2", "Promise", "resolve", "then", "destroy", "onFirstUpdate", "createPopper", "computeStyles", "applyStyles", "flip", "ARROW_UP_KEY", "ARROW_DOWN_KEY", "EVENT_KEYDOWN_DATA_API", "EVENT_KEYUP_DATA_API", "SELECTOR_DATA_TOGGLE_SHOWN", "SELECTOR_MENU", "PLACEMENT_TOP", "PLACEMENT_TOPEND", "PLACEMENT_BOTTOM", "PLACEMENT_BOTTOMEND", "PLACEMENT_RIGHT", "PLACEMENT_LEFT", "autoClose", "display", "popperConfig", "Dropdown", "_popper", "_parent", "_menu", "_inNavbar", "_detectNavbar", "_createPopper", "focus", "_completeHide", "<PERSON><PERSON>", "referenceElement", "_getPopperConfig", "_getPlacement", "parentDropdown", "isEnd", "_getOffset", "popperData", "defaultBsPopperConfig", "_selectMenuItem", "clearMenus", "openToggles", "context", "<PERSON><PERSON><PERSON>", "isMenuTarget", "dataApiKeydownHandler", "isInput", "isEscapeEvent", "isUpOrDownEvent", "getToggleButton", "stopPropagation", "EVENT_MOUSEDOWN", "className", "clickCallback", "rootElement", "Backdrop", "_isAppended", "_append", "_getElement", "_emulateAnimation", "backdrop", "createElement", "append", "EVENT_FOCUSIN", "EVENT_KEYDOWN_TAB", "TAB_NAV_BACKWARD", "autofocus", "trapElement", "FocusTrap", "_isActive", "_lastTabNavDirection", "activate", "_handleFocusin", "_handleKeydown", "deactivate", "shift<PERSON>ey", "SELECTOR_FIXED_CONTENT", "SELECTOR_STICKY_CONTENT", "PROPERTY_PADDING", "PROPERTY_MARGIN", "ScrollBarHelper", "getWidth", "documentWidth", "innerWidth", "_disableOver<PERSON>low", "_setElementAttributes", "calculatedValue", "_resetElementAttributes", "isOverflowing", "_saveInitialAttribute", "styleProperty", "scrollbarWidth", "_applyManipulationCallback", "actualValue", "removeProperty", "callBack", "EVENT_HIDE_PREVENTED", "EVENT_RESIZE", "EVENT_CLICK_DISMISS", "EVENT_MOUSEDOWN_DISMISS", "EVENT_KEYDOWN_DISMISS", "CLASS_NAME_OPEN", "CLASS_NAME_STATIC", "Modal", "_dialog", "_backdrop", "_initializeBackDrop", "_focustrap", "_initializeFocusTrap", "_scrollBar", "_adjustDialog", "_showElement", "_hideModal", "handleUpdate", "modalBody", "transitionComplete", "_triggerBackdropTransition", "event2", "_resetAdjustments", "isModalOverflowing", "initialOverflowY", "isBodyOverflowing", "paddingLeft", "paddingRight", "showEvent", "alreadyOpen", "CLASS_NAME_SHOWING", "CLASS_NAME_HIDING", "OPEN_SELECTOR", "<PERSON><PERSON><PERSON>", "blur", "completeCallback", "EVENT_SCROLL_DATA_API", "SELECTOR_STICKY_TOP", "OrangeNavbar", "enableMinimizing", "scrollY", "DefaultAllowlist", "area", "br", "col", "code", "dd", "div", "dl", "dt", "em", "hr", "h1", "h2", "h3", "h4", "h5", "h6", "li", "ol", "p", "pre", "s", "small", "span", "sub", "sup", "strong", "u", "ul", "uriAttributes", "SAFE_URL_PATTERN", "allowedAttribute", "allowedAttributeList", "attributeName", "nodeValue", "attributeRegex", "regex", "allowList", "content", "extraClass", "sanitize", "sanitizeFn", "template", "DefaultContentType", "entry", "TemplateFactory", "get<PERSON>ontent", "_resolvePossibleFunction", "<PERSON><PERSON><PERSON><PERSON>", "changeContent", "_checkContent", "toHtml", "templateWrapper", "_maybeSanitize", "text", "_setContent", "arg", "templateElement", "_putElementInTemplate", "textContent", "unsafeHtml", "sanitizeFunction", "createdDocument", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "elementName", "attributeList", "allowedAttributes", "sanitizeHtml", "DISALLOWED_ATTRIBUTES", "CLASS_NAME_FADE", "SELECTOR_TOOLTIP_INNER", "SELECTOR_MODAL", "EVENT_MODAL_HIDE", "TRIGGER_HOVER", "TRIGGER_FOCUS", "TRIGGER_CLICK", "AttachmentMap", "AUTO", "TOP", "RIGHT", "BOTTOM", "LEFT", "animation", "container", "customClass", "delay", "title", "<PERSON><PERSON><PERSON>", "_isEnabled", "_timeout", "_isHovered", "_activeTrigger", "_templateFactory", "_newContent", "tip", "_setListeners", "_fixTitle", "enable", "disable", "toggle<PERSON>nabled", "_leave", "_enter", "_hideModalHandler", "_disposePopper", "_isWithContent", "isInTheDom", "_getTipElement", "_isWithActiveTrigger", "_getTitle", "_createTipElement", "_getContentForTemplate", "_getTemplateFactory", "tipId", "prefix", "floor", "random", "getElementById", "getUID", "<PERSON><PERSON><PERSON><PERSON>", "_initializeOnDelegatedTarget", "_getDelegateConfig", "attachment", "triggers", "eventIn", "eventOut", "_setTimeout", "timeout", "dataAttributes", "dataAttribute", "SELECTOR_TITLE", "SELECTOR_CONTENT", "Popover", "_getContent", "EVENT_CHANGE_DATA_API", "SELECTOR_STEP_UP_BUTTON", "SELECTOR_STEP_DOWN_BUTTON", "SELECTOR_COUNTER_INPUT", "SELECTOR_QUANTITY_SELECTOR", "QuantitySelector", "ValueOnLoad", "counterInput", "btnUp", "btnDown", "step", "StepUp", "eventChange", "toFixed", "StepDown", "CheckIfDisabledOnChange", "EVENT_ACTIVATE", "EVENT_CLICK", "SELECTOR_TARGET_LINKS", "SELECTOR_NAV_LINKS", "SELECTOR_LINK_ITEMS", "rootMargin", "smoothScroll", "threshold", "ScrollSpy", "_targetLinks", "_observableSections", "_rootElement", "_activeTarget", "_observer", "_previousScrollData", "visibleEntryTop", "parentScrollTop", "refresh", "_initializeTargetsAndObservables", "_maybeEnableSmoothScroll", "disconnect", "_getNewObserver", "section", "observe", "observableSection", "scrollTo", "behavior", "IntersectionObserver", "_<PERSON><PERSON><PERSON><PERSON>", "targetElement", "_process", "userScrollsDown", "isIntersecting", "_clearActiveClass", "entryIsLowerThanPrevious", "targetLinks", "anchor", "decodeURI", "_activateParents", "listGroup", "activeNodes", "spy", "HOME_KEY", "END_KEY", "SELECTOR_DROPDOWN_TOGGLE", "NOT_SELECTOR_DROPDOWN_TOGGLE", "SELECTOR_INNER_ELEM", "SELECTOR_DATA_TOGGLE_ACTIVE", "Tab", "_setInitialAttributes", "_get<PERSON><PERSON><PERSON>n", "innerElem", "_elemIsActive", "active", "_getActiveElem", "hideEvent", "_deactivate", "_activate", "relatedElem", "_toggleDropDown", "nextActiveElement", "preventScroll", "_setAttributeIfNotExists", "_setInitialAttributesOnChild", "_getInnerElement", "isActive", "outerElem", "_getOuterElement", "_setInitialAttributesOnTargetPanel", "open", "EVENT_MOUSEOVER", "EVENT_MOUSEOUT", "EVENT_FOCUSOUT", "CLASS_NAME_HIDE", "autohide", "Toast", "_hasMouseInteraction", "_hasKeyboardInteraction", "_clearTimeout", "_maybeScheduleHide", "isShown", "_onInteraction", "isInteracting", "factory", "applyFocusVisiblePolyfill", "scope", "hadKeyboardEvent", "hadFocusVisibleRecently", "hadFocusVisibleRecentlyTimeout", "inputTypesAllowlist", "search", "url", "tel", "email", "password", "number", "date", "month", "week", "time", "datetime", "isValidFocusTarget", "addFocusVisibleClass", "onPointerDown", "e", "addInitialPointerMoveListeners", "onInitialPointerMove", "metaKey", "altKey", "ctrl<PERSON>ey", "visibilityState", "readOnly", "isContentEditable", "DOCUMENT_FRAGMENT_NODE", "DOCUMENT_NODE", "CustomEvent", "createEvent", "initCustomEvent", "exports", "module", "define", "amd"], "sources": ["../../js/src/dom/data.js", "../../js/src/util/index.js", "../../js/src/dom/event-handler.js", "../../js/src/dom/manipulator.js", "../../js/src/util/config.js", "../../js/src/base-component.js", "../../js/src/dom/selector-engine.js", "../../js/src/util/component-functions.js", "../../js/src/alert.js", "../../js/src/button.js", "../../js/src/util/swipe.js", "../../js/src/carousel.js", "../../js/src/collapse.js", "../../node_modules/@popperjs/core/lib/enums.js", "../../node_modules/@popperjs/core/lib/dom-utils/getNodeName.js", "../../node_modules/@popperjs/core/lib/dom-utils/getWindow.js", "../../node_modules/@popperjs/core/lib/dom-utils/instanceOf.js", "../../node_modules/@popperjs/core/lib/modifiers/applyStyles.js", "../../node_modules/@popperjs/core/lib/utils/getBasePlacement.js", "../../node_modules/@popperjs/core/lib/utils/math.js", "../../node_modules/@popperjs/core/lib/utils/userAgent.js", "../../node_modules/@popperjs/core/lib/dom-utils/isLayoutViewport.js", "../../node_modules/@popperjs/core/lib/dom-utils/getBoundingClientRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getLayoutRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/contains.js", "../../node_modules/@popperjs/core/lib/dom-utils/getComputedStyle.js", "../../node_modules/@popperjs/core/lib/dom-utils/isTableElement.js", "../../node_modules/@popperjs/core/lib/dom-utils/getDocumentElement.js", "../../node_modules/@popperjs/core/lib/dom-utils/getParentNode.js", "../../node_modules/@popperjs/core/lib/dom-utils/getOffsetParent.js", "../../node_modules/@popperjs/core/lib/utils/getMainAxisFromPlacement.js", "../../node_modules/@popperjs/core/lib/utils/within.js", "../../node_modules/@popperjs/core/lib/utils/mergePaddingObject.js", "../../node_modules/@popperjs/core/lib/utils/getFreshSideObject.js", "../../node_modules/@popperjs/core/lib/utils/expandToHashMap.js", "../../node_modules/@popperjs/core/lib/modifiers/arrow.js", "../../node_modules/@popperjs/core/lib/utils/getVariation.js", "../../node_modules/@popperjs/core/lib/modifiers/computeStyles.js", "../../node_modules/@popperjs/core/lib/modifiers/eventListeners.js", "../../node_modules/@popperjs/core/lib/utils/getOppositePlacement.js", "../../node_modules/@popperjs/core/lib/utils/getOppositeVariationPlacement.js", "../../node_modules/@popperjs/core/lib/dom-utils/getWindowScroll.js", "../../node_modules/@popperjs/core/lib/dom-utils/getWindowScrollBarX.js", "../../node_modules/@popperjs/core/lib/dom-utils/isScrollParent.js", "../../node_modules/@popperjs/core/lib/dom-utils/getScrollParent.js", "../../node_modules/@popperjs/core/lib/dom-utils/listScrollParents.js", "../../node_modules/@popperjs/core/lib/utils/rectToClientRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getClippingRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getViewportRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getDocumentRect.js", "../../node_modules/@popperjs/core/lib/utils/computeOffsets.js", "../../node_modules/@popperjs/core/lib/utils/detectOverflow.js", "../../node_modules/@popperjs/core/lib/utils/computeAutoPlacement.js", "../../node_modules/@popperjs/core/lib/modifiers/flip.js", "../../node_modules/@popperjs/core/lib/modifiers/hide.js", "../../node_modules/@popperjs/core/lib/modifiers/offset.js", "../../node_modules/@popperjs/core/lib/modifiers/popperOffsets.js", "../../node_modules/@popperjs/core/lib/modifiers/preventOverflow.js", "../../node_modules/@popperjs/core/lib/utils/getAltAxis.js", "../../node_modules/@popperjs/core/lib/dom-utils/getCompositeRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getNodeScroll.js", "../../node_modules/@popperjs/core/lib/dom-utils/getHTMLElementScroll.js", "../../node_modules/@popperjs/core/lib/utils/orderModifiers.js", "../../node_modules/@popperjs/core/lib/createPopper.js", "../../node_modules/@popperjs/core/lib/utils/debounce.js", "../../node_modules/@popperjs/core/lib/utils/mergeByName.js", "../../node_modules/@popperjs/core/lib/popper-lite.js", "../../node_modules/@popperjs/core/lib/popper.js", "../../js/src/dropdown.js", "../../js/src/util/backdrop.js", "../../js/src/util/focustrap.js", "../../js/src/util/scrollbar.js", "../../js/src/modal.js", "../../js/src/offcanvas.js", "../../js/src/orange-navbar.js", "../../js/src/util/sanitizer.js", "../../js/src/util/template-factory.js", "../../js/src/tooltip.js", "../../js/src/popover.js", "../../js/src/quantity-selector.js", "../../js/src/scrollspy.js", "../../js/src/tab.js", "../../js/src/toast.js", "../../node_modules/focus-visible/dist/focus-visible.js", "../../js/index.umd.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap dom/data.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * Constants\n */\n\nconst elementMap = new Map()\n\nexport default {\n  set(element, key, instance) {\n    if (!elementMap.has(element)) {\n      elementMap.set(element, new Map())\n    }\n\n    const instanceMap = elementMap.get(element)\n\n    // make it clear we only want one instance per element\n    // can be removed later when multiple key/instances are fine to be used\n    if (!instanceMap.has(key) && instanceMap.size !== 0) {\n      // eslint-disable-next-line no-console\n      console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(instanceMap.keys())[0]}.`)\n      return\n    }\n\n    instanceMap.set(key, instance)\n  },\n\n  get(element, key) {\n    if (elementMap.has(element)) {\n      return elementMap.get(element).get(key) || null\n    }\n\n    return null\n  },\n\n  remove(element, key) {\n    if (!elementMap.has(element)) {\n      return\n    }\n\n    const instanceMap = elementMap.get(element)\n\n    instanceMap.delete(key)\n\n    // free up element references if there are no instances left for an element\n    if (instanceMap.size === 0) {\n      elementMap.delete(element)\n    }\n  }\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1_000_000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n/**\n * Properly escape IDs selectors to handle weird IDs\n * @param {string} selector\n * @returns {string}\n */\nconst parseSelector = selector => {\n  if (selector && window.CSS && window.CSS.escape) {\n    // document.querySelector needs escaping to handle IDs (html5+) containing for instance /\n    selector = selector.replace(/#([^\\s\"#']+)/g, (match, id) => `#${CSS.escape(id)}`)\n  }\n\n  return selector\n}\n\n// Shout-out AngusCroll (https://goo.gl/pxwQGp)\nconst toType = object => {\n  if (object === null || object === undefined) {\n    return `${object}`\n  }\n\n  return Object.prototype.toString.call(object).match(/\\s([a-z]+)/i)[1].toLowerCase()\n}\n\n/**\n * Public Util API\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let { transitionDuration, transitionDelay } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = Number.parseFloat(transitionDuration)\n  const floatTransitionDelay = Number.parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (Number.parseFloat(transitionDuration) + Number.parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = object => {\n  if (!object || typeof object !== 'object') {\n    return false\n  }\n\n  if (typeof object.jquery !== 'undefined') {\n    object = object[0]\n  }\n\n  return typeof object.nodeType !== 'undefined'\n}\n\nconst getElement = object => {\n  // it's a jQuery object or a node element\n  if (isElement(object)) {\n    return object.jquery ? object[0] : object\n  }\n\n  if (typeof object === 'string' && object.length > 0) {\n    return document.querySelector(parseSelector(object))\n  }\n\n  return null\n}\n\nconst isVisible = element => {\n  if (!isElement(element) || element.getClientRects().length === 0) {\n    return false\n  }\n\n  const elementIsVisible = getComputedStyle(element).getPropertyValue('visibility') === 'visible'\n  // Handle `details` element as its content may falsie appear visible when it is closed\n  const closedDetails = element.closest('details:not([open])')\n\n  if (!closedDetails) {\n    return elementIsVisible\n  }\n\n  if (closedDetails !== element) {\n    const summary = element.closest('summary')\n    if (summary && summary.parentNode !== closedDetails) {\n      return false\n    }\n\n    if (summary === null) {\n      return false\n    }\n  }\n\n  return elementIsVisible\n}\n\nconst isDisabled = element => {\n  if (!element || element.nodeType !== Node.ELEMENT_NODE) {\n    return true\n  }\n\n  if (element.classList.contains('disabled')) {\n    return true\n  }\n\n  if (typeof element.disabled !== 'undefined') {\n    return element.disabled\n  }\n\n  return element.hasAttribute('disabled') && element.getAttribute('disabled') !== 'false'\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => {}\n\n/**\n * Trick to restart an element's animation\n *\n * @param {HTMLElement} element\n * @return void\n *\n * @see https://www.harrytheo.com/blog/2021/02/restart-a-css-animation-with-javascript/#restarting-a-css-animation\n */\nconst reflow = element => {\n  element.offsetHeight // eslint-disable-line no-unused-expressions\n}\n\nconst getjQuery = () => {\n  if (window.jQuery && !document.body.hasAttribute('data-bs-no-jquery')) {\n    return window.jQuery\n  }\n\n  return null\n}\n\nconst DOMContentLoadedCallbacks = []\n\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    // add listener on the first call when the document is in loading state\n    if (!DOMContentLoadedCallbacks.length) {\n      document.addEventListener('DOMContentLoaded', () => {\n        for (const callback of DOMContentLoadedCallbacks) {\n          callback()\n        }\n      })\n    }\n\n    DOMContentLoadedCallbacks.push(callback)\n  } else {\n    callback()\n  }\n}\n\nconst isRTL = () => document.documentElement.dir === 'rtl'\n\nconst defineJQueryPlugin = plugin => {\n  onDOMContentLoaded(() => {\n    const $ = getjQuery()\n    /* istanbul ignore if */\n    if ($) {\n      const name = plugin.NAME\n      const JQUERY_NO_CONFLICT = $.fn[name]\n      $.fn[name] = plugin.jQueryInterface\n      $.fn[name].Constructor = plugin\n      $.fn[name].noConflict = () => {\n        $.fn[name] = JQUERY_NO_CONFLICT\n        return plugin.jQueryInterface\n      }\n    }\n  })\n}\n\nconst execute = (possibleCallback, args = [], defaultValue = possibleCallback) => {\n  return typeof possibleCallback === 'function' ? possibleCallback.call(...args) : defaultValue\n}\n\nconst executeAfterTransition = (callback, transitionElement, waitForTransition = true) => {\n  if (!waitForTransition) {\n    execute(callback)\n    return\n  }\n\n  const durationPadding = 5\n  const emulatedDuration = getTransitionDurationFromElement(transitionElement) + durationPadding\n\n  let called = false\n\n  const handler = ({ target }) => {\n    if (target !== transitionElement) {\n      return\n    }\n\n    called = true\n    transitionElement.removeEventListener(TRANSITION_END, handler)\n    execute(callback)\n  }\n\n  transitionElement.addEventListener(TRANSITION_END, handler)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(transitionElement)\n    }\n  }, emulatedDuration)\n}\n\n/**\n * Return the previous/next element of a list.\n *\n * @param {array} list    The list of elements\n * @param activeElement   The active element\n * @param shouldGetNext   Choose to get next or previous element\n * @param isCycleAllowed\n * @return {Element|elem} The proper element\n */\nconst getNextActiveElement = (list, activeElement, shouldGetNext, isCycleAllowed) => {\n  const listLength = list.length\n  let index = list.indexOf(activeElement)\n\n  // if the element does not exist in the list return an element\n  // depending on the direction and if cycle is allowed\n  if (index === -1) {\n    return !shouldGetNext && isCycleAllowed ? list[listLength - 1] : list[0]\n  }\n\n  index += shouldGetNext ? 1 : -1\n\n  if (isCycleAllowed) {\n    index = (index + listLength) % listLength\n  }\n\n  return list[Math.max(0, Math.min(index, listLength - 1))]\n}\n\nexport {\n  defineJQueryPlugin,\n  execute,\n  executeAfterTransition,\n  findShadowRoot,\n  getElement,\n  getjQuery,\n  getNextActiveElement,\n  getTransitionDurationFromElement,\n  getUID,\n  isDisabled,\n  isElement,\n  isRTL,\n  isVisible,\n  noop,\n  onDOMContentLoaded,\n  parseSelector,\n  reflow,\n  triggerTransitionEnd,\n  toType\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap dom/event-handler.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery } from '../util/index.js'\n\n/**\n * Constants\n */\n\nconst namespaceRegex = /[^.]*(?=\\..*)\\.|.*/\nconst stripNameRegex = /\\..*/\nconst stripUidRegex = /::\\d+$/\nconst eventRegistry = {} // Events storage\nlet uidEvent = 1\nconst customEvents = {\n  mouseenter: 'mouseover',\n  mouseleave: 'mouseout'\n}\n\nconst nativeEvents = new Set([\n  'click',\n  'dblclick',\n  'mouseup',\n  'mousedown',\n  'contextmenu',\n  'mousewheel',\n  'DOMMouseScroll',\n  'mouseover',\n  'mouseout',\n  'mousemove',\n  'selectstart',\n  'selectend',\n  'keydown',\n  'keypress',\n  'keyup',\n  'orientationchange',\n  'touchstart',\n  'touchmove',\n  'touchend',\n  'touchcancel',\n  'pointerdown',\n  'pointermove',\n  'pointerup',\n  'pointerleave',\n  'pointercancel',\n  'gesturestart',\n  'gesturechange',\n  'gestureend',\n  'focus',\n  'blur',\n  'change',\n  'reset',\n  'select',\n  'submit',\n  'focusin',\n  'focusout',\n  'load',\n  'unload',\n  'beforeunload',\n  'resize',\n  'move',\n  'DOMContentLoaded',\n  'readystatechange',\n  'error',\n  'abort',\n  'scroll'\n])\n\n/**\n * Private methods\n */\n\nfunction makeEventUid(element, uid) {\n  return (uid && `${uid}::${uidEvent++}`) || element.uidEvent || uidEvent++\n}\n\nfunction getElementEvents(element) {\n  const uid = makeEventUid(element)\n\n  element.uidEvent = uid\n  eventRegistry[uid] = eventRegistry[uid] || {}\n\n  return eventRegistry[uid]\n}\n\nfunction bootstrapHandler(element, fn) {\n  return function handler(event) {\n    hydrateObj(event, { delegateTarget: element })\n\n    if (handler.oneOff) {\n      EventHandler.off(element, event.type, fn)\n    }\n\n    return fn.apply(element, [event])\n  }\n}\n\nfunction bootstrapDelegationHandler(element, selector, fn) {\n  return function handler(event) {\n    const domElements = element.querySelectorAll(selector)\n\n    for (let { target } = event; target && target !== this; target = target.parentNode) {\n      for (const domElement of domElements) {\n        if (domElement !== target) {\n          continue\n        }\n\n        hydrateObj(event, { delegateTarget: target })\n\n        if (handler.oneOff) {\n          EventHandler.off(element, event.type, selector, fn)\n        }\n\n        return fn.apply(target, [event])\n      }\n    }\n  }\n}\n\nfunction findHandler(events, callable, delegationSelector = null) {\n  return Object.values(events)\n    .find(event => event.callable === callable && event.delegationSelector === delegationSelector)\n}\n\nfunction normalizeParameters(originalTypeEvent, handler, delegationFunction) {\n  const isDelegated = typeof handler === 'string'\n  // TODO: tooltip passes `false` instead of selector, so we need to check\n  const callable = isDelegated ? delegationFunction : (handler || delegationFunction)\n  let typeEvent = getTypeEvent(originalTypeEvent)\n\n  if (!nativeEvents.has(typeEvent)) {\n    typeEvent = originalTypeEvent\n  }\n\n  return [isDelegated, callable, typeEvent]\n}\n\nfunction addHandler(element, originalTypeEvent, handler, delegationFunction, oneOff) {\n  if (typeof originalTypeEvent !== 'string' || !element) {\n    return\n  }\n\n  let [isDelegated, callable, typeEvent] = normalizeParameters(originalTypeEvent, handler, delegationFunction)\n\n  // in case of mouseenter or mouseleave wrap the handler within a function that checks for its DOM position\n  // this prevents the handler from being dispatched the same way as mouseover or mouseout does\n  if (originalTypeEvent in customEvents) {\n    const wrapFunction = fn => {\n      return function (event) {\n        if (!event.relatedTarget || (event.relatedTarget !== event.delegateTarget && !event.delegateTarget.contains(event.relatedTarget))) {\n          return fn.call(this, event)\n        }\n      }\n    }\n\n    callable = wrapFunction(callable)\n  }\n\n  const events = getElementEvents(element)\n  const handlers = events[typeEvent] || (events[typeEvent] = {})\n  const previousFunction = findHandler(handlers, callable, isDelegated ? handler : null)\n\n  if (previousFunction) {\n    previousFunction.oneOff = previousFunction.oneOff && oneOff\n\n    return\n  }\n\n  const uid = makeEventUid(callable, originalTypeEvent.replace(namespaceRegex, ''))\n  const fn = isDelegated ?\n    bootstrapDelegationHandler(element, handler, callable) :\n    bootstrapHandler(element, callable)\n\n  fn.delegationSelector = isDelegated ? handler : null\n  fn.callable = callable\n  fn.oneOff = oneOff\n  fn.uidEvent = uid\n  handlers[uid] = fn\n\n  element.addEventListener(typeEvent, fn, isDelegated)\n}\n\nfunction removeHandler(element, events, typeEvent, handler, delegationSelector) {\n  const fn = findHandler(events[typeEvent], handler, delegationSelector)\n\n  if (!fn) {\n    return\n  }\n\n  element.removeEventListener(typeEvent, fn, Boolean(delegationSelector))\n  delete events[typeEvent][fn.uidEvent]\n}\n\nfunction removeNamespacedHandlers(element, events, typeEvent, namespace) {\n  const storeElementEvent = events[typeEvent] || {}\n\n  for (const [handlerKey, event] of Object.entries(storeElementEvent)) {\n    if (handlerKey.includes(namespace)) {\n      removeHandler(element, events, typeEvent, event.callable, event.delegationSelector)\n    }\n  }\n}\n\nfunction getTypeEvent(event) {\n  // allow to get the native events from namespaced events ('click.bs.button' --> 'click')\n  event = event.replace(stripNameRegex, '')\n  return customEvents[event] || event\n}\n\nconst EventHandler = {\n  on(element, event, handler, delegationFunction) {\n    addHandler(element, event, handler, delegationFunction, false)\n  },\n\n  one(element, event, handler, delegationFunction) {\n    addHandler(element, event, handler, delegationFunction, true)\n  },\n\n  off(element, originalTypeEvent, handler, delegationFunction) {\n    if (typeof originalTypeEvent !== 'string' || !element) {\n      return\n    }\n\n    const [isDelegated, callable, typeEvent] = normalizeParameters(originalTypeEvent, handler, delegationFunction)\n    const inNamespace = typeEvent !== originalTypeEvent\n    const events = getElementEvents(element)\n    const storeElementEvent = events[typeEvent] || {}\n    const isNamespace = originalTypeEvent.startsWith('.')\n\n    if (typeof callable !== 'undefined') {\n      // Simplest case: handler is passed, remove that listener ONLY.\n      if (!Object.keys(storeElementEvent).length) {\n        return\n      }\n\n      removeHandler(element, events, typeEvent, callable, isDelegated ? handler : null)\n      return\n    }\n\n    if (isNamespace) {\n      for (const elementEvent of Object.keys(events)) {\n        removeNamespacedHandlers(element, events, elementEvent, originalTypeEvent.slice(1))\n      }\n    }\n\n    for (const [keyHandlers, event] of Object.entries(storeElementEvent)) {\n      const handlerKey = keyHandlers.replace(stripUidRegex, '')\n\n      if (!inNamespace || originalTypeEvent.includes(handlerKey)) {\n        removeHandler(element, events, typeEvent, event.callable, event.delegationSelector)\n      }\n    }\n  },\n\n  trigger(element, event, args) {\n    if (typeof event !== 'string' || !element) {\n      return null\n    }\n\n    const $ = getjQuery()\n    const typeEvent = getTypeEvent(event)\n    const inNamespace = event !== typeEvent\n\n    let jQueryEvent = null\n    let bubbles = true\n    let nativeDispatch = true\n    let defaultPrevented = false\n\n    if (inNamespace && $) {\n      jQueryEvent = $.Event(event, args)\n\n      $(element).trigger(jQueryEvent)\n      bubbles = !jQueryEvent.isPropagationStopped()\n      nativeDispatch = !jQueryEvent.isImmediatePropagationStopped()\n      defaultPrevented = jQueryEvent.isDefaultPrevented()\n    }\n\n    const evt = hydrateObj(new Event(event, { bubbles, cancelable: true }), args)\n\n    if (defaultPrevented) {\n      evt.preventDefault()\n    }\n\n    if (nativeDispatch) {\n      element.dispatchEvent(evt)\n    }\n\n    if (evt.defaultPrevented && jQueryEvent) {\n      jQueryEvent.preventDefault()\n    }\n\n    return evt\n  }\n}\n\nfunction hydrateObj(obj, meta = {}) {\n  for (const [key, value] of Object.entries(meta)) {\n    try {\n      obj[key] = value\n    } catch {\n      Object.defineProperty(obj, key, {\n        configurable: true,\n        get() {\n          return value\n        }\n      })\n    }\n  }\n\n  return obj\n}\n\nexport default EventHandler\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap dom/manipulator.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nfunction normalizeData(value) {\n  if (value === 'true') {\n    return true\n  }\n\n  if (value === 'false') {\n    return false\n  }\n\n  if (value === Number(value).toString()) {\n    return Number(value)\n  }\n\n  if (value === '' || value === 'null') {\n    return null\n  }\n\n  if (typeof value !== 'string') {\n    return value\n  }\n\n  try {\n    return JSON.parse(decodeURIComponent(value))\n  } catch {\n    return value\n  }\n}\n\nfunction normalizeDataKey(key) {\n  return key.replace(/[A-Z]/g, chr => `-${chr.toLowerCase()}`)\n}\n\nconst Manipulator = {\n  setDataAttribute(element, key, value) {\n    element.setAttribute(`data-bs-${normalizeDataKey(key)}`, value)\n  },\n\n  removeDataAttribute(element, key) {\n    element.removeAttribute(`data-bs-${normalizeDataKey(key)}`)\n  },\n\n  getDataAttributes(element) {\n    if (!element) {\n      return {}\n    }\n\n    const attributes = {}\n    const bsKeys = Object.keys(element.dataset).filter(key => key.startsWith('bs') && !key.startsWith('bsConfig'))\n\n    for (const key of bsKeys) {\n      let pureKey = key.replace(/^bs/, '')\n      pureKey = pureKey.charAt(0).toLowerCase() + pureKey.slice(1)\n      attributes[pureKey] = normalizeData(element.dataset[key])\n    }\n\n    return attributes\n  },\n\n  getDataAttribute(element, key) {\n    return normalizeData(element.getAttribute(`data-bs-${normalizeDataKey(key)}`))\n  }\n}\n\nexport default Manipulator\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap util/config.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Manipulator from '../dom/manipulator.js'\nimport { isElement, toType } from './index.js'\n\n/**\n * Class definition\n */\n\nclass Config {\n  // Getters\n  static get Default() {\n    return {}\n  }\n\n  static get DefaultType() {\n    return {}\n  }\n\n  static get NAME() {\n    throw new Error('You have to implement the static method \"NAME\", for each component!')\n  }\n\n  _getConfig(config) {\n    config = this._mergeConfigObj(config)\n    config = this._configAfterMerge(config)\n    this._typeCheckConfig(config)\n    return config\n  }\n\n  _configAfterMerge(config) {\n    return config\n  }\n\n  _mergeConfigObj(config, element) {\n    const jsonConfig = isElement(element) ? Manipulator.getDataAttribute(element, 'config') : {} // try to parse\n\n    return {\n      ...this.constructor.Default,\n      ...(typeof jsonConfig === 'object' ? jsonConfig : {}),\n      ...(isElement(element) ? Manipulator.getDataAttributes(element) : {}),\n      ...(typeof config === 'object' ? config : {})\n    }\n  }\n\n  _typeCheckConfig(config, configTypes = this.constructor.DefaultType) {\n    for (const [property, expectedTypes] of Object.entries(configTypes)) {\n      const value = config[property]\n      const valueType = isElement(value) ? 'element' : toType(value)\n\n      if (!new RegExp(expectedTypes).test(valueType)) {\n        throw new TypeError(\n          `${this.constructor.NAME.toUpperCase()}: Option \"${property}\" provided type \"${valueType}\" but expected type \"${expectedTypes}\".`\n        )\n      }\n    }\n  }\n}\n\nexport default Config\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap base-component.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Data from './dom/data.js'\nimport EventHandler from './dom/event-handler.js'\nimport Config from './util/config.js'\nimport { executeAfterTransition, getElement } from './util/index.js'\n\n/**\n * Constants\n */\n\nconst VERSION = '5.3.7'\n\n/**\n * Class definition\n */\n\nclass BaseComponent extends Config {\n  constructor(element, config) {\n    super()\n\n    element = getElement(element)\n    if (!element) {\n      return\n    }\n\n    this._element = element\n    this._config = this._getConfig(config)\n\n    Data.set(this._element, this.constructor.DATA_KEY, this)\n  }\n\n  // Public\n  dispose() {\n    Data.remove(this._element, this.constructor.DATA_KEY)\n    EventHandler.off(this._element, this.constructor.EVENT_KEY)\n\n    for (const propertyName of Object.getOwnPropertyNames(this)) {\n      this[propertyName] = null\n    }\n  }\n\n  // Private\n  _queueCallback(callback, element, isAnimated = true) {\n    executeAfterTransition(callback, element, isAnimated)\n  }\n\n  _getConfig(config) {\n    config = this._mergeConfigObj(config, this._element)\n    config = this._configAfterMerge(config)\n    this._typeCheckConfig(config)\n    return config\n  }\n\n  // Static\n  static getInstance(element) {\n    return Data.get(getElement(element), this.DATA_KEY)\n  }\n\n  static getOrCreateInstance(element, config = {}) {\n    return this.getInstance(element) || new this(element, typeof config === 'object' ? config : null)\n  }\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get DATA_KEY() {\n    return `bs.${this.NAME}`\n  }\n\n  static get EVENT_KEY() {\n    return `.${this.DATA_KEY}`\n  }\n\n  static eventName(name) {\n    return `${name}${this.EVENT_KEY}`\n  }\n}\n\nexport default BaseComponent\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap dom/selector-engine.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { isDisabled, isVisible, parseSelector } from '../util/index.js'\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-bs-target')\n\n  if (!selector || selector === '#') {\n    let hrefAttribute = element.getAttribute('href')\n\n    // The only valid content that could double as a selector are IDs or classes,\n    // so everything starting with `#` or `.`. If a \"real\" URL is used as the selector,\n    // `document.querySelector` will rightfully complain it is invalid.\n    // See https://github.com/twbs/bootstrap/issues/32273\n    if (!hrefAttribute || (!hrefAttribute.includes('#') && !hrefAttribute.startsWith('.'))) {\n      return null\n    }\n\n    // Just in case some CMS puts out a full URL with the anchor appended\n    if (hrefAttribute.includes('#') && !hrefAttribute.startsWith('#')) {\n      hrefAttribute = `#${hrefAttribute.split('#')[1]}`\n    }\n\n    selector = hrefAttribute && hrefAttribute !== '#' ? hrefAttribute.trim() : null\n  }\n\n  return selector ? selector.split(',').map(sel => parseSelector(sel)).join(',') : null\n}\n\nconst SelectorEngine = {\n  find(selector, element = document.documentElement) {\n    return [].concat(...Element.prototype.querySelectorAll.call(element, selector))\n  },\n\n  findOne(selector, element = document.documentElement) {\n    return Element.prototype.querySelector.call(element, selector)\n  },\n\n  children(element, selector) {\n    return [].concat(...element.children).filter(child => child.matches(selector))\n  },\n\n  parents(element, selector) {\n    const parents = []\n    let ancestor = element.parentNode.closest(selector)\n\n    while (ancestor) {\n      parents.push(ancestor)\n      ancestor = ancestor.parentNode.closest(selector)\n    }\n\n    return parents\n  },\n\n  prev(element, selector) {\n    let previous = element.previousElementSibling\n\n    while (previous) {\n      if (previous.matches(selector)) {\n        return [previous]\n      }\n\n      previous = previous.previousElementSibling\n    }\n\n    return []\n  },\n  // TODO: this is now unused; remove later along with prev()\n  next(element, selector) {\n    let next = element.nextElementSibling\n\n    while (next) {\n      if (next.matches(selector)) {\n        return [next]\n      }\n\n      next = next.nextElementSibling\n    }\n\n    return []\n  },\n\n  focusableChildren(element) {\n    const focusables = [\n      'a',\n      'button',\n      'input',\n      'textarea',\n      'select',\n      'details',\n      '[tabindex]',\n      '[contenteditable=\"true\"]'\n    ].map(selector => `${selector}:not([tabindex^=\"-\"])`).join(',')\n\n    return this.find(focusables, element).filter(el => !isDisabled(el) && isVisible(el))\n  },\n\n  getSelectorFromElement(element) {\n    const selector = getSelector(element)\n\n    if (selector) {\n      return SelectorEngine.findOne(selector) ? selector : null\n    }\n\n    return null\n  },\n\n  getElementFromSelector(element) {\n    const selector = getSelector(element)\n\n    return selector ? SelectorEngine.findOne(selector) : null\n  },\n\n  getMultipleElementsFromSelector(element) {\n    const selector = getSelector(element)\n\n    return selector ? SelectorEngine.find(selector) : []\n  }\n}\n\nexport default SelectorEngine\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap util/component-functions.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler.js'\nimport SelectorEngine from '../dom/selector-engine.js'\nimport { isDisabled } from './index.js'\n\nconst enableDismissTrigger = (component, method = 'hide') => {\n  const clickEvent = `click.dismiss${component.EVENT_KEY}`\n  const name = component.NAME\n\n  EventHandler.on(document, clickEvent, `[data-bs-dismiss=\"${name}\"]`, function (event) {\n    if (['A', 'AREA'].includes(this.tagName)) {\n      event.preventDefault()\n    }\n\n    if (isDisabled(this)) {\n      return\n    }\n\n    const target = SelectorEngine.getElementFromSelector(this) || this.closest(`.${name}`)\n    const instance = component.getOrCreateInstance(target)\n\n    // Method argument is left, for Alert and only, as it doesn't implement the 'hide' method\n    instance[method]()\n  })\n}\n\nexport {\n  enableDismissTrigger\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap alert.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport { enableDismissTrigger } from './util/component-functions.js'\nimport { defineJQueryPlugin } from './util/index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'alert'\nconst DATA_KEY = 'bs.alert'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_CLOSE = `close${EVENT_KEY}`\nconst EVENT_CLOSED = `closed${EVENT_KEY}`\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\n/**\n * Class definition\n */\n\nclass Alert extends BaseComponent {\n  // Getters\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  close() {\n    const closeEvent = EventHandler.trigger(this._element, EVENT_CLOSE)\n\n    if (closeEvent.defaultPrevented) {\n      return\n    }\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    const isAnimated = this._element.classList.contains(CLASS_NAME_FADE)\n    this._queueCallback(() => this._destroyElement(), this._element, isAnimated)\n  }\n\n  // Private\n  _destroyElement() {\n    this._element.remove()\n    EventHandler.trigger(this._element, EVENT_CLOSED)\n    this.dispose()\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Alert.getOrCreateInstance(this)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](this)\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nenableDismissTrigger(Alert, 'close')\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Alert)\n\nexport default Alert\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport { defineJQueryPlugin } from './util/index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'button'\nconst DATA_KEY = 'bs.button'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst CLASS_NAME_ACTIVE = 'active'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"button\"]'\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\n/**\n * Class definition\n */\n\nclass Button extends BaseComponent {\n  // Getters\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle() {\n    // Toggle class and sync the `aria-pressed` attribute with the return value of the `.toggle()` method\n    this._element.setAttribute('aria-pressed', this._element.classList.toggle(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Button.getOrCreateInstance(this)\n\n      if (config === 'toggle') {\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, event => {\n  event.preventDefault()\n\n  const button = event.target.closest(SELECTOR_DATA_TOGGLE)\n  const data = Button.getOrCreateInstance(button)\n\n  data.toggle()\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Button)\n\nexport default Button\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap util/swipe.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler.js'\nimport Config from './config.js'\nimport { execute } from './index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'swipe'\nconst EVENT_KEY = '.bs.swipe'\nconst EVENT_TOUCHSTART = `touchstart${EVENT_KEY}`\nconst EVENT_TOUCHMOVE = `touchmove${EVENT_KEY}`\nconst EVENT_TOUCHEND = `touchend${EVENT_KEY}`\nconst EVENT_POINTERDOWN = `pointerdown${EVENT_KEY}`\nconst EVENT_POINTERUP = `pointerup${EVENT_KEY}`\nconst POINTER_TYPE_TOUCH = 'touch'\nconst POINTER_TYPE_PEN = 'pen'\nconst CLASS_NAME_POINTER_EVENT = 'pointer-event'\nconst SWIPE_THRESHOLD = 40\n\nconst Default = {\n  endCallback: null,\n  leftCallback: null,\n  rightCallback: null\n}\n\nconst DefaultType = {\n  endCallback: '(function|null)',\n  leftCallback: '(function|null)',\n  rightCallback: '(function|null)'\n}\n\n/**\n * Class definition\n */\n\nclass Swipe extends Config {\n  constructor(element, config) {\n    super()\n    this._element = element\n\n    if (!element || !Swipe.isSupported()) {\n      return\n    }\n\n    this._config = this._getConfig(config)\n    this._deltaX = 0\n    this._supportPointerEvents = Boolean(window.PointerEvent)\n    this._initEvents()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  dispose() {\n    EventHandler.off(this._element, EVENT_KEY)\n  }\n\n  // Private\n  _start(event) {\n    if (!this._supportPointerEvents) {\n      this._deltaX = event.touches[0].clientX\n\n      return\n    }\n\n    if (this._eventIsPointerPenTouch(event)) {\n      this._deltaX = event.clientX\n    }\n  }\n\n  _end(event) {\n    if (this._eventIsPointerPenTouch(event)) {\n      this._deltaX = event.clientX - this._deltaX\n    }\n\n    this._handleSwipe()\n    execute(this._config.endCallback)\n  }\n\n  _move(event) {\n    this._deltaX = event.touches && event.touches.length > 1 ?\n      0 :\n      event.touches[0].clientX - this._deltaX\n  }\n\n  _handleSwipe() {\n    const absDeltaX = Math.abs(this._deltaX)\n\n    if (absDeltaX <= SWIPE_THRESHOLD) {\n      return\n    }\n\n    const direction = absDeltaX / this._deltaX\n\n    this._deltaX = 0\n\n    if (!direction) {\n      return\n    }\n\n    execute(direction > 0 ? this._config.rightCallback : this._config.leftCallback)\n  }\n\n  _initEvents() {\n    if (this._supportPointerEvents) {\n      EventHandler.on(this._element, EVENT_POINTERDOWN, event => this._start(event))\n      EventHandler.on(this._element, EVENT_POINTERUP, event => this._end(event))\n\n      this._element.classList.add(CLASS_NAME_POINTER_EVENT)\n    } else {\n      EventHandler.on(this._element, EVENT_TOUCHSTART, event => this._start(event))\n      EventHandler.on(this._element, EVENT_TOUCHMOVE, event => this._move(event))\n      EventHandler.on(this._element, EVENT_TOUCHEND, event => this._end(event))\n    }\n  }\n\n  _eventIsPointerPenTouch(event) {\n    return this._supportPointerEvents && (event.pointerType === POINTER_TYPE_PEN || event.pointerType === POINTER_TYPE_TOUCH)\n  }\n\n  // Static\n  static isSupported() {\n    return 'ontouchstart' in document.documentElement || navigator.maxTouchPoints > 0\n  }\n}\n\nexport default Swipe\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap carousel.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport Manipulator from './dom/manipulator.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport {\n  defineJQueryPlugin,\n  getNextActiveElement,\n  isRTL,\n  isVisible,\n  reflow,\n  triggerTransitionEnd\n} from './util/index.js'\nimport Swipe from './util/swipe.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'carousel'\nconst DATA_KEY = 'bs.carousel'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst TOUCHEVENT_COMPAT_WAIT = 500 // Time for mouse compat events to fire after touch\n\nconst ORDER_NEXT = 'next'\nconst ORDER_PREV = 'prev'\nconst DIRECTION_LEFT = 'left'\nconst DIRECTION_RIGHT = 'right'\n\nconst EVENT_SLIDE = `slide${EVENT_KEY}`\nconst EVENT_SLID = `slid${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_MOUSEENTER = `mouseenter${EVENT_KEY}`\nconst EVENT_MOUSELEAVE = `mouseleave${EVENT_KEY}`\nconst EVENT_DRAG_START = `dragstart${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_CAROUSEL = 'carousel'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_SLIDE = 'slide'\nconst CLASS_NAME_END = 'carousel-item-end'\nconst CLASS_NAME_START = 'carousel-item-start'\nconst CLASS_NAME_NEXT = 'carousel-item-next'\nconst CLASS_NAME_PREV = 'carousel-item-prev'\nconst CLASS_NAME_PAUSED = 'is-paused' // Boosted mod: used for progress indicators\nconst CLASS_NAME_DONE = 'is-done' // Boosted mod: used for progress indicators\nconst CLASS_NAME_PAUSE = 'pause' // Boosted mod: used for pause button\nconst CLASS_NAME_PLAY = 'play' // Boosted mod: used for play button\n\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ITEM = '.carousel-item'\nconst SELECTOR_ACTIVE_ITEM = SELECTOR_ACTIVE + SELECTOR_ITEM\nconst SELECTOR_ITEM_IMG = '.carousel-item img'\nconst SELECTOR_INDICATORS = '.carousel-indicators'\nconst SELECTOR_DATA_SLIDE = '[data-bs-slide], [data-bs-slide-to]'\nconst SELECTOR_DATA_RIDE = '[data-bs-ride=\"carousel\"]'\nconst SELECTOR_CONTROL_PREV = '.carousel-control-prev' // Boosted mod\nconst SELECTOR_CONTROL_NEXT = '.carousel-control-next' // Boosted mod\nconst SELECTOR_CONTROL_PAUSE = '.carousel-control-play-pause' // Boosted mod\nconst SELECTOR_CAROUSEL_TO_PAUSE = 'data-bs-target' // Boosted mod\nconst SELECTOR_CAROUSEL_PLAY_TEXT = 'data-bs-play-text' // Boosted mod\nconst SELECTOR_CAROUSEL_PAUSE_TEXT = 'data-bs-pause-text' // Boosted mod\nconst SELECTOR_CAROUSEL_DEFAULT_PLAY_TEXT = 'Play Carousel' // Boosted mod\nconst SELECTOR_CAROUSEL_DEFAULT_PAUSE_TEXT = 'Pause Carousel' // Boosted mod\n\nconst PREFIX_CUSTOM_PROPS = 'bs-' // Boosted mod: should match `$prefix` in scss/_variables.scss\n\nconst KEY_TO_DIRECTION = {\n  [ARROW_LEFT_KEY]: DIRECTION_RIGHT,\n  [ARROW_RIGHT_KEY]: DIRECTION_LEFT\n}\n\nconst Default = {\n  interval: 5000,\n  keyboard: true,\n  pause: 'hover',\n  ride: false,\n  touch: true,\n  wrap: true\n}\n\nconst DefaultType = {\n  interval: '(number|boolean)', // TODO:v6 remove boolean support\n  keyboard: 'boolean',\n  pause: '(string|boolean)',\n  ride: '(boolean|string)',\n  touch: 'boolean',\n  wrap: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Carousel extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._interval = null\n    this._activeElement = null\n    this._isSliding = false\n    this.touchTimeout = null\n    this._swipeHelper = null\n\n    this._indicatorsElement = SelectorEngine.findOne(SELECTOR_INDICATORS, this._element)\n\n    this._playPauseButton = SelectorEngine.findOne(`${SELECTOR_CONTROL_PAUSE}[${SELECTOR_CAROUSEL_TO_PAUSE}=\"#${this._element.id}\"]`) // Boosted mod\n\n    this._addEventListeners()\n\n    if (this._config.ride === CLASS_NAME_CAROUSEL) {\n      this.cycle()\n    } else if (this._indicatorsElement) { // Boosted mod: set the animation properly on progress indicator\n      this._element.classList.add(CLASS_NAME_PAUSED)\n    }\n    // End mod\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  next() {\n    this._slide(ORDER_NEXT)\n  }\n\n  nextWhenVisible() {\n    // FIXME TODO use `document.visibilityState`\n    // Don't call next when the page isn't visible\n    // or the carousel or its parent isn't visible\n    if (!document.hidden && isVisible(this._element)) {\n      this.next()\n    }\n  }\n\n  prev() {\n    this._slide(ORDER_PREV)\n  }\n\n  pause() {\n    // Boosted mod: reset the animation on progress indicator\n    if (this._indicatorsElement) {\n      this._element.classList.add(CLASS_NAME_PAUSED)\n    }\n    // End mod\n\n    // Boosted mod: if a play-pause button is present, set the button to play\n    if (this._playPauseButton !== null && this._playPauseButton.classList.contains(CLASS_NAME_PAUSE)) {\n      this._playPauseButton.classList.remove(CLASS_NAME_PAUSE)\n      this._playPauseButton.classList.add(CLASS_NAME_PLAY)\n\n      if (this._playPauseButton.getAttribute(SELECTOR_CAROUSEL_PLAY_TEXT)) {\n        this._playPauseButton.setAttribute('title', this._playPauseButton.getAttribute(SELECTOR_CAROUSEL_PLAY_TEXT))\n        this._playPauseButton.querySelector('span.visually-hidden').innerHTML = this._playPauseButton.getAttribute(SELECTOR_CAROUSEL_PLAY_TEXT)\n      } else {\n        this._playPauseButton.setAttribute('title', SELECTOR_CAROUSEL_DEFAULT_PLAY_TEXT)\n        this._playPauseButton.querySelector('span.visually-hidden').innerHTML = SELECTOR_CAROUSEL_DEFAULT_PLAY_TEXT\n      }\n\n      this._stayPaused = true\n    }\n    // End mod\n\n    if (this._isSliding) {\n      triggerTransitionEnd(this._element)\n    }\n\n    this._clearInterval()\n  }\n\n  cycle() {\n    // Boosted mod: restart the animation on progress indicator\n    if (this._indicatorsElement) {\n      this._element.classList.remove(CLASS_NAME_PAUSED)\n    }\n    // End mod\n\n    // Boosted mod: if a play-pause button is present, reset the button to pause\n    if (this._playPauseButton !== null && this._playPauseButton.classList.contains(CLASS_NAME_PLAY)) {\n      this._playPauseButton.classList.remove(CLASS_NAME_PLAY)\n      this._playPauseButton.classList.add(CLASS_NAME_PAUSE)\n\n      if (this._playPauseButton.getAttribute(SELECTOR_CAROUSEL_PAUSE_TEXT)) {\n        this._playPauseButton.setAttribute('title', this._playPauseButton.getAttribute(SELECTOR_CAROUSEL_PAUSE_TEXT))\n        this._playPauseButton.querySelector('span.visually-hidden').innerHTML = this._playPauseButton.getAttribute(SELECTOR_CAROUSEL_PAUSE_TEXT)\n      } else {\n        this._playPauseButton.setAttribute('title', SELECTOR_CAROUSEL_DEFAULT_PAUSE_TEXT)\n        this._playPauseButton.querySelector('span.visually-hidden').innerHTML = SELECTOR_CAROUSEL_DEFAULT_PAUSE_TEXT\n      }\n\n      this._stayPaused = false\n    }\n    // End mod\n\n    this._clearInterval()\n    this._updateInterval()\n\n    this._interval = setInterval(() => this.nextWhenVisible(), this._config.interval)\n  }\n\n  _maybeEnableCycle() {\n    if (!this._config.ride) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.cycle())\n      return\n    }\n\n    this.cycle()\n  }\n\n  to(index) {\n    // Boosted mod: restart the animation on progress indicator\n    if (this._indicatorsElement) {\n      this._element.classList.remove(CLASS_NAME_DONE)\n    }\n    // End mod\n\n    const items = this._getItems()\n    if (index > items.length - 1 || index < 0) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.to(index))\n      return\n    }\n\n    const activeIndex = this._getItemIndex(this._getActive())\n    if (activeIndex === index) {\n      return\n    }\n\n    const order = index > activeIndex ? ORDER_NEXT : ORDER_PREV\n\n    this._slide(order, items[index])\n  }\n\n  dispose() {\n    if (this._swipeHelper) {\n      this._swipeHelper.dispose()\n    }\n\n    super.dispose()\n  }\n\n  // Private\n  _configAfterMerge(config) {\n    config.defaultInterval = config.interval\n    return config\n  }\n\n  _addEventListeners() {\n    if (this._config.keyboard) {\n      EventHandler.on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n    }\n\n    if (this._config.pause === 'hover') {\n      EventHandler.on(this._element, EVENT_MOUSEENTER, () => this.pause())\n      EventHandler.on(this._element, EVENT_MOUSELEAVE, () => this._maybeEnableCycle())\n    }\n\n    if (this._config.touch && Swipe.isSupported()) {\n      this._addTouchEventListeners()\n    }\n  }\n\n  _addTouchEventListeners() {\n    for (const img of SelectorEngine.find(SELECTOR_ITEM_IMG, this._element)) {\n      EventHandler.on(img, EVENT_DRAG_START, event => event.preventDefault())\n    }\n\n    const endCallBack = () => {\n      if (this._config.pause !== 'hover') {\n        return\n      }\n\n      // If it's a touch-enabled device, mouseenter/leave are fired as\n      // part of the mouse compatibility events on first tap - the carousel\n      // would stop cycling until user tapped out of it;\n      // here, we listen for touchend, explicitly pause the carousel\n      // (as if it's the second time we tap on it, mouseenter compat event\n      // is NOT fired) and after a timeout (to allow for mouse compatibility\n      // events to fire) we explicitly restart cycling\n\n      this.pause()\n      if (this.touchTimeout) {\n        clearTimeout(this.touchTimeout)\n      }\n\n      this.touchTimeout = setTimeout(() => this._maybeEnableCycle(), TOUCHEVENT_COMPAT_WAIT + this._config.interval)\n    }\n\n    const swipeConfig = {\n      leftCallback: () => this._slide(this._directionToOrder(DIRECTION_LEFT)),\n      rightCallback: () => this._slide(this._directionToOrder(DIRECTION_RIGHT)),\n      endCallback: endCallBack\n    }\n\n    this._swipeHelper = new Swipe(this._element, swipeConfig)\n  }\n\n  _keydown(event) {\n    if (/input|textarea/i.test(event.target.tagName)) {\n      return\n    }\n\n    const direction = KEY_TO_DIRECTION[event.key]\n    if (direction) {\n      event.preventDefault()\n      this._slide(this._directionToOrder(direction))\n    }\n  }\n\n  // Boosted mod: handle prev/next controls states\n  _disableControl(element) {\n    if (element.nodeName === 'BUTTON') {\n      element.disabled = true\n    } else {\n      element.setAttribute('aria-disabled', true)\n      element.setAttribute('tabindex', '-1')\n    }\n  }\n\n  _enableControl(element) {\n    if (element.nodeName === 'BUTTON') {\n      element.disabled = false\n    } else {\n      element.removeAttribute('aria-disabled')\n      element.removeAttribute('tabindex')\n    }\n  }\n  // End mod\n\n  _getItemIndex(element) {\n    return this._getItems().indexOf(element)\n  }\n\n  _setActiveIndicatorElement(index) {\n    if (!this._indicatorsElement) {\n      return\n    }\n\n    const activeIndicator = SelectorEngine.findOne(SELECTOR_ACTIVE, this._indicatorsElement)\n\n    activeIndicator.classList.remove(CLASS_NAME_ACTIVE)\n    activeIndicator.removeAttribute('aria-current')\n\n    const newActiveIndicator = SelectorEngine.findOne(`[data-bs-slide-to=\"${index}\"]`, this._indicatorsElement)\n\n    if (newActiveIndicator) {\n      newActiveIndicator.classList.add(CLASS_NAME_ACTIVE)\n      newActiveIndicator.setAttribute('aria-current', 'true')\n    }\n  }\n\n  _updateInterval() {\n    const element = this._activeElement || this._getActive()\n\n    if (!element) {\n      return\n    }\n\n    const elementInterval = Number.parseInt(element.getAttribute('data-bs-interval'), 10)\n\n    this._config.interval = elementInterval || this._config.defaultInterval\n\n    // Boosted mod: set progress indicator's interval as custom property\n    if (this._indicatorsElement && this._config.interval !== Default.interval) {\n      const currentIndex = this._getItemIndex(element)\n      const currentIndicator = SelectorEngine.findOne(`:nth-child(${currentIndex + 1})`, this._indicatorsElement)\n      currentIndicator.style.setProperty(`--${PREFIX_CUSTOM_PROPS}carousel-interval`, `${this._config.interval}ms`)\n    }\n    // End mod\n  }\n\n  _slide(order, element = null) {\n    if (this._isSliding) {\n      return\n    }\n\n    const activeElement = this._getActive()\n    const isNext = order === ORDER_NEXT\n\n    // Boosted mod: progress indicators animation when wrapping is disabled\n    if (!this._config.wrap) {\n      const isPrev = order === ORDER_PREV\n      const activeIndex = this._getItemIndex(activeElement)\n      const lastItemIndex = this._getItems().length - 1\n      const isGoingToWrap = (isPrev && activeIndex === 0) || (isNext && activeIndex === lastItemIndex)\n\n      if (isGoingToWrap) {\n        // Reset the animation on last progress indicator when last slide is active\n        if (isNext && this._indicatorsElement && !this._element.hasAttribute('data-bs-slide')) {\n          this._element.classList.add(CLASS_NAME_DONE)\n        }\n\n        return activeElement\n      }\n\n      // Restart animation otherwise\n      if (this._indicatorsElement) {\n        this._element.classList.remove(CLASS_NAME_DONE)\n      }\n    }\n    // End mod\n\n    const nextElement = element || getNextActiveElement(this._getItems(), activeElement, isNext, this._config.wrap)\n\n    if (nextElement === activeElement) {\n      return\n    }\n\n    const nextElementIndex = this._getItemIndex(nextElement)\n\n    const triggerEvent = eventName => {\n      return EventHandler.trigger(this._element, eventName, {\n        relatedTarget: nextElement,\n        direction: this._orderToDirection(order),\n        from: this._getItemIndex(activeElement),\n        to: nextElementIndex\n      })\n    }\n\n    const slideEvent = triggerEvent(EVENT_SLIDE)\n\n    if (slideEvent.defaultPrevented) {\n      return\n    }\n\n    if (!activeElement || !nextElement) {\n      // Some weirdness is happening, so we bail\n      // TODO: change tests that use empty divs to avoid this check\n      return\n    }\n\n    const isCycling = Boolean(this._interval)\n    this.pause()\n\n    this._isSliding = true\n\n    this._setActiveIndicatorElement(nextElementIndex)\n    this._activeElement = nextElement\n\n    // Boosted mod: enable/disable prev/next controls when wrap=false\n    if (!this._config.wrap) {\n      const prevControl = SelectorEngine.findOne(SELECTOR_CONTROL_PREV, this._element)\n      const nextControl = SelectorEngine.findOne(SELECTOR_CONTROL_NEXT, this._element)\n\n      this._enableControl(prevControl)\n      this._enableControl(nextControl)\n\n      if (nextElementIndex === 0) {\n        this._disableControl(prevControl)\n      } else if (nextElementIndex === (this._getItems().length - 1)) {\n        this._disableControl(nextControl)\n      }\n    }\n    // End mod\n\n    const directionalClassName = isNext ? CLASS_NAME_START : CLASS_NAME_END\n    const orderClassName = isNext ? CLASS_NAME_NEXT : CLASS_NAME_PREV\n\n    nextElement.classList.add(orderClassName)\n\n    reflow(nextElement)\n\n    activeElement.classList.add(directionalClassName)\n    nextElement.classList.add(directionalClassName)\n\n    const completeCallBack = () => {\n      nextElement.classList.remove(directionalClassName, orderClassName)\n      nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n      activeElement.classList.remove(CLASS_NAME_ACTIVE, orderClassName, directionalClassName)\n\n      this._isSliding = false\n\n      triggerEvent(EVENT_SLID)\n    }\n\n    this._queueCallback(completeCallBack, activeElement, this._isAnimated())\n\n    if (isCycling) {\n      this.cycle()\n    }\n  }\n\n  _isAnimated() {\n    return this._element.classList.contains(CLASS_NAME_SLIDE)\n  }\n\n  _getActive() {\n    return SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n  }\n\n  _getItems() {\n    return SelectorEngine.find(SELECTOR_ITEM, this._element)\n  }\n\n  _clearInterval() {\n    if (this._interval) {\n      clearInterval(this._interval)\n      this._interval = null\n    }\n  }\n\n  _directionToOrder(direction) {\n    if (isRTL()) {\n      return direction === DIRECTION_LEFT ? ORDER_PREV : ORDER_NEXT\n    }\n\n    return direction === DIRECTION_LEFT ? ORDER_NEXT : ORDER_PREV\n  }\n\n  _orderToDirection(order) {\n    if (isRTL()) {\n      return order === ORDER_PREV ? DIRECTION_LEFT : DIRECTION_RIGHT\n    }\n\n    return order === ORDER_PREV ? DIRECTION_RIGHT : DIRECTION_LEFT\n  }\n\n  // Static\n  // Boosted mod: add pause button\n  static PauseCarousel(event) {\n    const pauseButton = event.target\n    const pauseButtonAttribute = pauseButton.getAttribute(SELECTOR_CAROUSEL_TO_PAUSE)\n    const carouselToPause = Carousel.getOrCreateInstance(document.querySelector(pauseButtonAttribute))\n    if (pauseButton.classList.contains(CLASS_NAME_PAUSE)) {\n      carouselToPause.pause()\n    } else {\n      carouselToPause.cycle()\n    }\n  }\n  // End mod\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Carousel.getOrCreateInstance(this, config)\n\n      if (typeof config === 'number') {\n        data.to(config)\n        return\n      }\n\n      if (typeof config === 'string') {\n        if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_SLIDE, function (event) {\n  const target = SelectorEngine.getElementFromSelector(this)\n\n  if (!target || !target.classList.contains(CLASS_NAME_CAROUSEL)) {\n    return\n  }\n\n  event.preventDefault()\n\n  const carousel = Carousel.getOrCreateInstance(target)\n  const slideIndex = this.getAttribute('data-bs-slide-to')\n\n  if (slideIndex) {\n    carousel.to(slideIndex)\n    carousel._maybeEnableCycle()\n    return\n  }\n\n  if (Manipulator.getDataAttribute(this, 'slide') === 'next') {\n    carousel.next()\n    carousel._maybeEnableCycle()\n    return\n  }\n\n  carousel.prev()\n  carousel._maybeEnableCycle()\n})\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_CONTROL_PAUSE, Carousel.PauseCarousel) // Boosted mod\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  const carousels = SelectorEngine.find(SELECTOR_DATA_RIDE)\n\n  for (const carousel of carousels) {\n    Carousel.getOrCreateInstance(carousel)\n  }\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Carousel)\n\nexport default Carousel\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport {\n  defineJQueryPlugin,\n  getElement,\n  reflow\n} from './util/index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'collapse'\nconst DATA_KEY = 'bs.collapse'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_COLLAPSE = 'collapse'\nconst CLASS_NAME_COLLAPSING = 'collapsing'\nconst CLASS_NAME_COLLAPSED = 'collapsed'\nconst CLASS_NAME_DEEPER_CHILDREN = `:scope .${CLASS_NAME_COLLAPSE} .${CLASS_NAME_COLLAPSE}`\nconst CLASS_NAME_HORIZONTAL = 'collapse-horizontal'\n\nconst WIDTH = 'width'\nconst HEIGHT = 'height'\n\nconst SELECTOR_ACTIVES = '.collapse.show, .collapse.collapsing'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"collapse\"]'\n\nconst Default = {\n  parent: null,\n  toggle: true\n}\n\nconst DefaultType = {\n  parent: '(null|element)',\n  toggle: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Collapse extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._isTransitioning = false\n    this._triggerArray = []\n\n    const toggleList = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (const elem of toggleList) {\n      const selector = SelectorEngine.getSelectorFromElement(elem)\n      const filterElement = SelectorEngine.find(selector)\n        .filter(foundElement => foundElement === this._element)\n\n      if (selector !== null && filterElement.length) {\n        this._triggerArray.push(elem)\n      }\n    }\n\n    this._initializeChildren()\n\n    if (!this._config.parent) {\n      this._addAriaAndCollapsedClass(this._triggerArray, this._isShown())\n    }\n\n    if (this._config.toggle) {\n      this.toggle()\n    }\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle() {\n    if (this._isShown()) {\n      this.hide()\n    } else {\n      this.show()\n    }\n  }\n\n  show() {\n    if (this._isTransitioning || this._isShown()) {\n      return\n    }\n\n    let activeChildren = []\n\n    // find active children\n    if (this._config.parent) {\n      activeChildren = this._getFirstLevelChildren(SELECTOR_ACTIVES)\n        .filter(element => element !== this._element)\n        .map(element => Collapse.getOrCreateInstance(element, { toggle: false }))\n    }\n\n    if (activeChildren.length && activeChildren[0]._isTransitioning) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    for (const activeInstance of activeChildren) {\n      activeInstance.hide()\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.classList.remove(CLASS_NAME_COLLAPSE)\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n\n    this._element.style[dimension] = 0\n\n    this._addAriaAndCollapsedClass(this._triggerArray, true)\n    this._isTransitioning = true\n\n    const complete = () => {\n      this._isTransitioning = false\n\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n      this._element.style[dimension] = ''\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n    }\n\n    const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n    const scrollSize = `scroll${capitalizedDimension}`\n\n    this._queueCallback(complete, this._element, true)\n    this._element.style[dimension] = `${this._element[scrollSize]}px`\n  }\n\n  hide() {\n    if (this._isTransitioning || !this._isShown()) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n    this._element.classList.remove(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n    this._isTransitioning = true\n\n    const complete = () => {\n      this._isTransitioning = false\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE)\n\n      // Boosted mod: Change the moment of the appliance of .collapsed\n      for (const trigger of this._triggerArray) {\n        const element = SelectorEngine.getElementFromSelector(trigger)\n\n        if (element && !this._isShown(element)) {\n          this._addAriaAndCollapsedClass([trigger], false)\n        }\n      }\n      // End mod\n\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.style[dimension] = ''\n\n    this._queueCallback(complete, this._element, true)\n  }\n\n  // Private\n  _isShown(element = this._element) {\n    return element.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  _configAfterMerge(config) {\n    config.toggle = Boolean(config.toggle) // Coerce string values\n    config.parent = getElement(config.parent)\n    return config\n  }\n\n  _getDimension() {\n    return this._element.classList.contains(CLASS_NAME_HORIZONTAL) ? WIDTH : HEIGHT\n  }\n\n  _initializeChildren() {\n    if (!this._config.parent) {\n      return\n    }\n\n    const children = this._getFirstLevelChildren(SELECTOR_DATA_TOGGLE)\n\n    for (const element of children) {\n      const selected = SelectorEngine.getElementFromSelector(element)\n\n      if (selected) {\n        this._addAriaAndCollapsedClass([element], this._isShown(selected))\n      }\n    }\n  }\n\n  _getFirstLevelChildren(selector) {\n    const children = SelectorEngine.find(CLASS_NAME_DEEPER_CHILDREN, this._config.parent)\n    // remove children if greater depth\n    return SelectorEngine.find(selector, this._config.parent).filter(element => !children.includes(element))\n  }\n\n  _addAriaAndCollapsedClass(triggerArray, isOpen) {\n    if (!triggerArray.length) {\n      return\n    }\n\n    for (const element of triggerArray) {\n      element.classList.toggle(CLASS_NAME_COLLAPSED, !isOpen)\n      element.setAttribute('aria-expanded', isOpen)\n    }\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    const _config = {}\n    if (typeof config === 'string' && /show|hide/.test(config)) {\n      _config.toggle = false\n    }\n\n    return this.each(function () {\n      const data = Collapse.getOrCreateInstance(this, _config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n  if (event.target.tagName === 'A' || (event.delegateTarget && event.delegateTarget.tagName === 'A')) {\n    event.preventDefault()\n  }\n\n  for (const element of SelectorEngine.getMultipleElementsFromSelector(this)) {\n    Collapse.getOrCreateInstance(element, { toggle: false }).toggle()\n  }\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Collapse)\n\nexport default Collapse\n", "export var top = 'top';\nexport var bottom = 'bottom';\nexport var right = 'right';\nexport var left = 'left';\nexport var auto = 'auto';\nexport var basePlacements = [top, bottom, right, left];\nexport var start = 'start';\nexport var end = 'end';\nexport var clippingParents = 'clippingParents';\nexport var viewport = 'viewport';\nexport var popper = 'popper';\nexport var reference = 'reference';\nexport var variationPlacements = /*#__PURE__*/basePlacements.reduce(function (acc, placement) {\n  return acc.concat([placement + \"-\" + start, placement + \"-\" + end]);\n}, []);\nexport var placements = /*#__PURE__*/[].concat(basePlacements, [auto]).reduce(function (acc, placement) {\n  return acc.concat([placement, placement + \"-\" + start, placement + \"-\" + end]);\n}, []); // modifiers that need to read the DOM\n\nexport var beforeRead = 'beforeRead';\nexport var read = 'read';\nexport var afterRead = 'afterRead'; // pure-logic modifiers\n\nexport var beforeMain = 'beforeMain';\nexport var main = 'main';\nexport var afterMain = 'afterMain'; // modifier with the purpose to write to the DOM (or write into a framework state)\n\nexport var beforeWrite = 'beforeWrite';\nexport var write = 'write';\nexport var afterWrite = 'afterWrite';\nexport var modifierPhases = [beforeRead, read, afterRead, beforeMain, main, afterMain, beforeWrite, write, afterWrite];", "export default function getNodeName(element) {\n  return element ? (element.nodeName || '').toLowerCase() : null;\n}", "export default function getWindow(node) {\n  if (node == null) {\n    return window;\n  }\n\n  if (node.toString() !== '[object Window]') {\n    var ownerDocument = node.ownerDocument;\n    return ownerDocument ? ownerDocument.defaultView || window : window;\n  }\n\n  return node;\n}", "import getWindow from \"./getWindow.js\";\n\nfunction isElement(node) {\n  var OwnElement = getWindow(node).Element;\n  return node instanceof OwnElement || node instanceof Element;\n}\n\nfunction isHTMLElement(node) {\n  var OwnElement = getWindow(node).HTMLElement;\n  return node instanceof OwnElement || node instanceof HTMLElement;\n}\n\nfunction isShadowRoot(node) {\n  // IE 11 has no ShadowRoot\n  if (typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n\n  var OwnElement = getWindow(node).ShadowRoot;\n  return node instanceof OwnElement || node instanceof ShadowRoot;\n}\n\nexport { isElement, isHTMLElement, isShadowRoot };", "import getNodeName from \"../dom-utils/getNodeName.js\";\nimport { isHTMLElement } from \"../dom-utils/instanceOf.js\"; // This modifier takes the styles prepared by the `computeStyles` modifier\n// and applies them to the HTMLElements such as popper and arrow\n\nfunction applyStyles(_ref) {\n  var state = _ref.state;\n  Object.keys(state.elements).forEach(function (name) {\n    var style = state.styles[name] || {};\n    var attributes = state.attributes[name] || {};\n    var element = state.elements[name]; // arrow is optional + virtual elements\n\n    if (!isHTMLElement(element) || !getNodeName(element)) {\n      return;\n    } // Flow doesn't support to extend this property, but it's the most\n    // effective way to apply styles to an HTMLElement\n    // $FlowFixMe[cannot-write]\n\n\n    Object.assign(element.style, style);\n    Object.keys(attributes).forEach(function (name) {\n      var value = attributes[name];\n\n      if (value === false) {\n        element.removeAttribute(name);\n      } else {\n        element.setAttribute(name, value === true ? '' : value);\n      }\n    });\n  });\n}\n\nfunction effect(_ref2) {\n  var state = _ref2.state;\n  var initialStyles = {\n    popper: {\n      position: state.options.strategy,\n      left: '0',\n      top: '0',\n      margin: '0'\n    },\n    arrow: {\n      position: 'absolute'\n    },\n    reference: {}\n  };\n  Object.assign(state.elements.popper.style, initialStyles.popper);\n  state.styles = initialStyles;\n\n  if (state.elements.arrow) {\n    Object.assign(state.elements.arrow.style, initialStyles.arrow);\n  }\n\n  return function () {\n    Object.keys(state.elements).forEach(function (name) {\n      var element = state.elements[name];\n      var attributes = state.attributes[name] || {};\n      var styleProperties = Object.keys(state.styles.hasOwnProperty(name) ? state.styles[name] : initialStyles[name]); // Set all values to an empty string to unset them\n\n      var style = styleProperties.reduce(function (style, property) {\n        style[property] = '';\n        return style;\n      }, {}); // arrow is optional + virtual elements\n\n      if (!isHTMLElement(element) || !getNodeName(element)) {\n        return;\n      }\n\n      Object.assign(element.style, style);\n      Object.keys(attributes).forEach(function (attribute) {\n        element.removeAttribute(attribute);\n      });\n    });\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'applyStyles',\n  enabled: true,\n  phase: 'write',\n  fn: applyStyles,\n  effect: effect,\n  requires: ['computeStyles']\n};", "import { auto } from \"../enums.js\";\nexport default function getBasePlacement(placement) {\n  return placement.split('-')[0];\n}", "export var max = Math.max;\nexport var min = Math.min;\nexport var round = Math.round;", "export default function getUAString() {\n  var uaData = navigator.userAgentData;\n\n  if (uaData != null && uaData.brands && Array.isArray(uaData.brands)) {\n    return uaData.brands.map(function (item) {\n      return item.brand + \"/\" + item.version;\n    }).join(' ');\n  }\n\n  return navigator.userAgent;\n}", "import getUAString from \"../utils/userAgent.js\";\nexport default function isLayoutViewport() {\n  return !/^((?!chrome|android).)*safari/i.test(getUAString());\n}", "import { isElement, isHTMLElement } from \"./instanceOf.js\";\nimport { round } from \"../utils/math.js\";\nimport getWindow from \"./getWindow.js\";\nimport isLayoutViewport from \"./isLayoutViewport.js\";\nexport default function getBoundingClientRect(element, includeScale, isFixedStrategy) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n\n  if (isFixedStrategy === void 0) {\n    isFixedStrategy = false;\n  }\n\n  var clientRect = element.getBoundingClientRect();\n  var scaleX = 1;\n  var scaleY = 1;\n\n  if (includeScale && isHTMLElement(element)) {\n    scaleX = element.offsetWidth > 0 ? round(clientRect.width) / element.offsetWidth || 1 : 1;\n    scaleY = element.offsetHeight > 0 ? round(clientRect.height) / element.offsetHeight || 1 : 1;\n  }\n\n  var _ref = isElement(element) ? getWindow(element) : window,\n      visualViewport = _ref.visualViewport;\n\n  var addVisualOffsets = !isLayoutViewport() && isFixedStrategy;\n  var x = (clientRect.left + (addVisualOffsets && visualViewport ? visualViewport.offsetLeft : 0)) / scaleX;\n  var y = (clientRect.top + (addVisualOffsets && visualViewport ? visualViewport.offsetTop : 0)) / scaleY;\n  var width = clientRect.width / scaleX;\n  var height = clientRect.height / scaleY;\n  return {\n    width: width,\n    height: height,\n    top: y,\n    right: x + width,\n    bottom: y + height,\n    left: x,\n    x: x,\n    y: y\n  };\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\"; // Returns the layout rect of an element relative to its offsetParent. Layout\n// means it doesn't take into account transforms.\n\nexport default function getLayoutRect(element) {\n  var clientRect = getBoundingClientRect(element); // Use the clientRect sizes if it's not been transformed.\n  // Fixes https://github.com/popperjs/popper-core/issues/1223\n\n  var width = element.offsetWidth;\n  var height = element.offsetHeight;\n\n  if (Math.abs(clientRect.width - width) <= 1) {\n    width = clientRect.width;\n  }\n\n  if (Math.abs(clientRect.height - height) <= 1) {\n    height = clientRect.height;\n  }\n\n  return {\n    x: element.offsetLeft,\n    y: element.offsetTop,\n    width: width,\n    height: height\n  };\n}", "import { isShadowRoot } from \"./instanceOf.js\";\nexport default function contains(parent, child) {\n  var rootNode = child.getRootNode && child.getRootNode(); // First, attempt with faster native method\n\n  if (parent.contains(child)) {\n    return true;\n  } // then fallback to custom implementation with Shadow DOM support\n  else if (rootNode && isShadowRoot(rootNode)) {\n      var next = child;\n\n      do {\n        if (next && parent.isSameNode(next)) {\n          return true;\n        } // $FlowFixMe[prop-missing]: need a better way to handle this...\n\n\n        next = next.parentNode || next.host;\n      } while (next);\n    } // Give up, the result is false\n\n\n  return false;\n}", "import getWindow from \"./getWindow.js\";\nexport default function getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}", "import getNodeName from \"./getNodeName.js\";\nexport default function isTableElement(element) {\n  return ['table', 'td', 'th'].indexOf(getNodeName(element)) >= 0;\n}", "import { isElement } from \"./instanceOf.js\";\nexport default function getDocumentElement(element) {\n  // $FlowFixMe[incompatible-return]: assume body is always available\n  return ((isElement(element) ? element.ownerDocument : // $FlowFixMe[prop-missing]\n  element.document) || window.document).documentElement;\n}", "import getNodeName from \"./getNodeName.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport { isShadowRoot } from \"./instanceOf.js\";\nexport default function getParentNode(element) {\n  if (getNodeName(element) === 'html') {\n    return element;\n  }\n\n  return (// this is a quicker (but less type safe) way to save quite some bytes from the bundle\n    // $FlowFixMe[incompatible-return]\n    // $FlowFixMe[prop-missing]\n    element.assignedSlot || // step into the shadow DOM of the parent of a slotted node\n    element.parentNode || ( // DOM Element detected\n    isShadowRoot(element) ? element.host : null) || // ShadowRoot detected\n    // $FlowFixMe[incompatible-call]: HTMLElement is a Node\n    getDocumentElement(element) // fallback\n\n  );\n}", "import getWindow from \"./getWindow.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport { isHTMLElement, isShadowRoot } from \"./instanceOf.js\";\nimport isTableElement from \"./isTableElement.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport getUAString from \"../utils/userAgent.js\";\n\nfunction getTrueOffsetParent(element) {\n  if (!isHTMLElement(element) || // https://github.com/popperjs/popper-core/issues/837\n  getComputedStyle(element).position === 'fixed') {\n    return null;\n  }\n\n  return element.offsetParent;\n} // `.offsetParent` reports `null` for fixed elements, while absolute elements\n// return the containing block\n\n\nfunction getContainingBlock(element) {\n  var isFirefox = /firefox/i.test(getUAString());\n  var isIE = /Trident/i.test(getUAString());\n\n  if (isIE && isHTMLElement(element)) {\n    // In IE 9, 10 and 11 fixed elements containing block is always established by the viewport\n    var elementCss = getComputedStyle(element);\n\n    if (elementCss.position === 'fixed') {\n      return null;\n    }\n  }\n\n  var currentNode = getParentNode(element);\n\n  if (isShadowRoot(currentNode)) {\n    currentNode = currentNode.host;\n  }\n\n  while (isHTMLElement(currentNode) && ['html', 'body'].indexOf(getNodeName(currentNode)) < 0) {\n    var css = getComputedStyle(currentNode); // This is non-exhaustive but covers the most common CSS properties that\n    // create a containing block.\n    // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n\n    if (css.transform !== 'none' || css.perspective !== 'none' || css.contain === 'paint' || ['transform', 'perspective'].indexOf(css.willChange) !== -1 || isFirefox && css.willChange === 'filter' || isFirefox && css.filter && css.filter !== 'none') {\n      return currentNode;\n    } else {\n      currentNode = currentNode.parentNode;\n    }\n  }\n\n  return null;\n} // Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\n\n\nexport default function getOffsetParent(element) {\n  var window = getWindow(element);\n  var offsetParent = getTrueOffsetParent(element);\n\n  while (offsetParent && isTableElement(offsetParent) && getComputedStyle(offsetParent).position === 'static') {\n    offsetParent = getTrueOffsetParent(offsetParent);\n  }\n\n  if (offsetParent && (getNodeName(offsetParent) === 'html' || getNodeName(offsetParent) === 'body' && getComputedStyle(offsetParent).position === 'static')) {\n    return window;\n  }\n\n  return offsetParent || getContainingBlock(element) || window;\n}", "export default function getMainAxisFromPlacement(placement) {\n  return ['top', 'bottom'].indexOf(placement) >= 0 ? 'x' : 'y';\n}", "import { max as mathMax, min as mathMin } from \"./math.js\";\nexport function within(min, value, max) {\n  return mathMax(min, mathMin(value, max));\n}\nexport function withinMaxClamp(min, value, max) {\n  var v = within(min, value, max);\n  return v > max ? max : v;\n}", "import getFreshSideObject from \"./getFreshSideObject.js\";\nexport default function mergePaddingObject(paddingObject) {\n  return Object.assign({}, getFreshSideObject(), paddingObject);\n}", "export default function getFreshSideObject() {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0\n  };\n}", "export default function expandToHashMap(value, keys) {\n  return keys.reduce(function (hashMap, key) {\n    hashMap[key] = value;\n    return hashMap;\n  }, {});\n}", "import getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getLayoutRect from \"../dom-utils/getLayoutRect.js\";\nimport contains from \"../dom-utils/contains.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport getMainAxisFromPlacement from \"../utils/getMainAxisFromPlacement.js\";\nimport { within } from \"../utils/within.js\";\nimport mergePaddingObject from \"../utils/mergePaddingObject.js\";\nimport expandToHashMap from \"../utils/expandToHashMap.js\";\nimport { left, right, basePlacements, top, bottom } from \"../enums.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar toPaddingObject = function toPaddingObject(padding, state) {\n  padding = typeof padding === 'function' ? padding(Object.assign({}, state.rects, {\n    placement: state.placement\n  })) : padding;\n  return mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));\n};\n\nfunction arrow(_ref) {\n  var _state$modifiersData$;\n\n  var state = _ref.state,\n      name = _ref.name,\n      options = _ref.options;\n  var arrowElement = state.elements.arrow;\n  var popperOffsets = state.modifiersData.popperOffsets;\n  var basePlacement = getBasePlacement(state.placement);\n  var axis = getMainAxisFromPlacement(basePlacement);\n  var isVertical = [left, right].indexOf(basePlacement) >= 0;\n  var len = isVertical ? 'height' : 'width';\n\n  if (!arrowElement || !popperOffsets) {\n    return;\n  }\n\n  var paddingObject = toPaddingObject(options.padding, state);\n  var arrowRect = getLayoutRect(arrowElement);\n  var minProp = axis === 'y' ? top : left;\n  var maxProp = axis === 'y' ? bottom : right;\n  var endDiff = state.rects.reference[len] + state.rects.reference[axis] - popperOffsets[axis] - state.rects.popper[len];\n  var startDiff = popperOffsets[axis] - state.rects.reference[axis];\n  var arrowOffsetParent = getOffsetParent(arrowElement);\n  var clientSize = arrowOffsetParent ? axis === 'y' ? arrowOffsetParent.clientHeight || 0 : arrowOffsetParent.clientWidth || 0 : 0;\n  var centerToReference = endDiff / 2 - startDiff / 2; // Make sure the arrow doesn't overflow the popper if the center point is\n  // outside of the popper bounds\n\n  var min = paddingObject[minProp];\n  var max = clientSize - arrowRect[len] - paddingObject[maxProp];\n  var center = clientSize / 2 - arrowRect[len] / 2 + centerToReference;\n  var offset = within(min, center, max); // Prevents breaking syntax highlighting...\n\n  var axisProp = axis;\n  state.modifiersData[name] = (_state$modifiersData$ = {}, _state$modifiersData$[axisProp] = offset, _state$modifiersData$.centerOffset = offset - center, _state$modifiersData$);\n}\n\nfunction effect(_ref2) {\n  var state = _ref2.state,\n      options = _ref2.options;\n  var _options$element = options.element,\n      arrowElement = _options$element === void 0 ? '[data-popper-arrow]' : _options$element;\n\n  if (arrowElement == null) {\n    return;\n  } // CSS selector\n\n\n  if (typeof arrowElement === 'string') {\n    arrowElement = state.elements.popper.querySelector(arrowElement);\n\n    if (!arrowElement) {\n      return;\n    }\n  }\n\n  if (!contains(state.elements.popper, arrowElement)) {\n    return;\n  }\n\n  state.elements.arrow = arrowElement;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'arrow',\n  enabled: true,\n  phase: 'main',\n  fn: arrow,\n  effect: effect,\n  requires: ['popperOffsets'],\n  requiresIfExists: ['preventOverflow']\n};", "export default function getVariation(placement) {\n  return placement.split('-')[1];\n}", "import { top, left, right, bottom, end } from \"../enums.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport getWindow from \"../dom-utils/getWindow.js\";\nimport getDocumentElement from \"../dom-utils/getDocumentElement.js\";\nimport getComputedStyle from \"../dom-utils/getComputedStyle.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getVariation from \"../utils/getVariation.js\";\nimport { round } from \"../utils/math.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar unsetSides = {\n  top: 'auto',\n  right: 'auto',\n  bottom: 'auto',\n  left: 'auto'\n}; // Round the offsets to the nearest suitable subpixel based on the DPR.\n// Zooming can change the DPR, but it seems to report a value that will\n// cleanly divide the values into the appropriate subpixels.\n\nfunction roundOffsetsByDPR(_ref, win) {\n  var x = _ref.x,\n      y = _ref.y;\n  var dpr = win.devicePixelRatio || 1;\n  return {\n    x: round(x * dpr) / dpr || 0,\n    y: round(y * dpr) / dpr || 0\n  };\n}\n\nexport function mapToStyles(_ref2) {\n  var _Object$assign2;\n\n  var popper = _ref2.popper,\n      popperRect = _ref2.popperRect,\n      placement = _ref2.placement,\n      variation = _ref2.variation,\n      offsets = _ref2.offsets,\n      position = _ref2.position,\n      gpuAcceleration = _ref2.gpuAcceleration,\n      adaptive = _ref2.adaptive,\n      roundOffsets = _ref2.roundOffsets,\n      isFixed = _ref2.isFixed;\n  var _offsets$x = offsets.x,\n      x = _offsets$x === void 0 ? 0 : _offsets$x,\n      _offsets$y = offsets.y,\n      y = _offsets$y === void 0 ? 0 : _offsets$y;\n\n  var _ref3 = typeof roundOffsets === 'function' ? roundOffsets({\n    x: x,\n    y: y\n  }) : {\n    x: x,\n    y: y\n  };\n\n  x = _ref3.x;\n  y = _ref3.y;\n  var hasX = offsets.hasOwnProperty('x');\n  var hasY = offsets.hasOwnProperty('y');\n  var sideX = left;\n  var sideY = top;\n  var win = window;\n\n  if (adaptive) {\n    var offsetParent = getOffsetParent(popper);\n    var heightProp = 'clientHeight';\n    var widthProp = 'clientWidth';\n\n    if (offsetParent === getWindow(popper)) {\n      offsetParent = getDocumentElement(popper);\n\n      if (getComputedStyle(offsetParent).position !== 'static' && position === 'absolute') {\n        heightProp = 'scrollHeight';\n        widthProp = 'scrollWidth';\n      }\n    } // $FlowFixMe[incompatible-cast]: force type refinement, we compare offsetParent with window above, but Flow doesn't detect it\n\n\n    offsetParent = offsetParent;\n\n    if (placement === top || (placement === left || placement === right) && variation === end) {\n      sideY = bottom;\n      var offsetY = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.height : // $FlowFixMe[prop-missing]\n      offsetParent[heightProp];\n      y -= offsetY - popperRect.height;\n      y *= gpuAcceleration ? 1 : -1;\n    }\n\n    if (placement === left || (placement === top || placement === bottom) && variation === end) {\n      sideX = right;\n      var offsetX = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.width : // $FlowFixMe[prop-missing]\n      offsetParent[widthProp];\n      x -= offsetX - popperRect.width;\n      x *= gpuAcceleration ? 1 : -1;\n    }\n  }\n\n  var commonStyles = Object.assign({\n    position: position\n  }, adaptive && unsetSides);\n\n  var _ref4 = roundOffsets === true ? roundOffsetsByDPR({\n    x: x,\n    y: y\n  }, getWindow(popper)) : {\n    x: x,\n    y: y\n  };\n\n  x = _ref4.x;\n  y = _ref4.y;\n\n  if (gpuAcceleration) {\n    var _Object$assign;\n\n    return Object.assign({}, commonStyles, (_Object$assign = {}, _Object$assign[sideY] = hasY ? '0' : '', _Object$assign[sideX] = hasX ? '0' : '', _Object$assign.transform = (win.devicePixelRatio || 1) <= 1 ? \"translate(\" + x + \"px, \" + y + \"px)\" : \"translate3d(\" + x + \"px, \" + y + \"px, 0)\", _Object$assign));\n  }\n\n  return Object.assign({}, commonStyles, (_Object$assign2 = {}, _Object$assign2[sideY] = hasY ? y + \"px\" : '', _Object$assign2[sideX] = hasX ? x + \"px\" : '', _Object$assign2.transform = '', _Object$assign2));\n}\n\nfunction computeStyles(_ref5) {\n  var state = _ref5.state,\n      options = _ref5.options;\n  var _options$gpuAccelerat = options.gpuAcceleration,\n      gpuAcceleration = _options$gpuAccelerat === void 0 ? true : _options$gpuAccelerat,\n      _options$adaptive = options.adaptive,\n      adaptive = _options$adaptive === void 0 ? true : _options$adaptive,\n      _options$roundOffsets = options.roundOffsets,\n      roundOffsets = _options$roundOffsets === void 0 ? true : _options$roundOffsets;\n  var commonStyles = {\n    placement: getBasePlacement(state.placement),\n    variation: getVariation(state.placement),\n    popper: state.elements.popper,\n    popperRect: state.rects.popper,\n    gpuAcceleration: gpuAcceleration,\n    isFixed: state.options.strategy === 'fixed'\n  };\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.styles.popper = Object.assign({}, state.styles.popper, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.popperOffsets,\n      position: state.options.strategy,\n      adaptive: adaptive,\n      roundOffsets: roundOffsets\n    })));\n  }\n\n  if (state.modifiersData.arrow != null) {\n    state.styles.arrow = Object.assign({}, state.styles.arrow, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.arrow,\n      position: 'absolute',\n      adaptive: false,\n      roundOffsets: roundOffsets\n    })));\n  }\n\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-placement': state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'computeStyles',\n  enabled: true,\n  phase: 'beforeWrite',\n  fn: computeStyles,\n  data: {}\n};", "import getWindow from \"../dom-utils/getWindow.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar passive = {\n  passive: true\n};\n\nfunction effect(_ref) {\n  var state = _ref.state,\n      instance = _ref.instance,\n      options = _ref.options;\n  var _options$scroll = options.scroll,\n      scroll = _options$scroll === void 0 ? true : _options$scroll,\n      _options$resize = options.resize,\n      resize = _options$resize === void 0 ? true : _options$resize;\n  var window = getWindow(state.elements.popper);\n  var scrollParents = [].concat(state.scrollParents.reference, state.scrollParents.popper);\n\n  if (scroll) {\n    scrollParents.forEach(function (scrollParent) {\n      scrollParent.addEventListener('scroll', instance.update, passive);\n    });\n  }\n\n  if (resize) {\n    window.addEventListener('resize', instance.update, passive);\n  }\n\n  return function () {\n    if (scroll) {\n      scrollParents.forEach(function (scrollParent) {\n        scrollParent.removeEventListener('scroll', instance.update, passive);\n      });\n    }\n\n    if (resize) {\n      window.removeEventListener('resize', instance.update, passive);\n    }\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'eventListeners',\n  enabled: true,\n  phase: 'write',\n  fn: function fn() {},\n  effect: effect,\n  data: {}\n};", "var hash = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nexport default function getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, function (matched) {\n    return hash[matched];\n  });\n}", "var hash = {\n  start: 'end',\n  end: 'start'\n};\nexport default function getOppositeVariationPlacement(placement) {\n  return placement.replace(/start|end/g, function (matched) {\n    return hash[matched];\n  });\n}", "import getWindow from \"./getWindow.js\";\nexport default function getWindowScroll(node) {\n  var win = getWindow(node);\n  var scrollLeft = win.pageXOffset;\n  var scrollTop = win.pageYOffset;\n  return {\n    scrollLeft: scrollLeft,\n    scrollTop: scrollTop\n  };\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getWindowScroll from \"./getWindowScroll.js\";\nexport default function getWindowScrollBarX(element) {\n  // If <html> has a CSS width greater than the viewport, then this will be\n  // incorrect for RTL.\n  // Popper 1 is broken in this case and never had a bug report so let's assume\n  // it's not an issue. I don't think anyone ever specifies width on <html>\n  // anyway.\n  // Browsers where the left scrollbar doesn't cause an issue report `0` for\n  // this (e.g. Edge 2019, IE11, Safari)\n  return getBoundingClientRect(getDocumentElement(element)).left + getWindowScroll(element).scrollLeft;\n}", "import getComputedStyle from \"./getComputedStyle.js\";\nexport default function isScrollParent(element) {\n  // Firefox wants us to check `-x` and `-y` variations as well\n  var _getComputedStyle = getComputedStyle(element),\n      overflow = _getComputedStyle.overflow,\n      overflowX = _getComputedStyle.overflowX,\n      overflowY = _getComputedStyle.overflowY;\n\n  return /auto|scroll|overlay|hidden/.test(overflow + overflowY + overflowX);\n}", "import getParentNode from \"./getParentNode.js\";\nimport isScrollParent from \"./isScrollParent.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nexport default function getScrollParent(node) {\n  if (['html', 'body', '#document'].indexOf(getNodeName(node)) >= 0) {\n    // $FlowFixMe[incompatible-return]: assume body is always available\n    return node.ownerDocument.body;\n  }\n\n  if (isHTMLElement(node) && isScrollParent(node)) {\n    return node;\n  }\n\n  return getScrollParent(getParentNode(node));\n}", "import getScrollParent from \"./getScrollParent.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport getWindow from \"./getWindow.js\";\nimport isScrollParent from \"./isScrollParent.js\";\n/*\ngiven a DOM element, return the list of all scroll parents, up the list of ancesors\nuntil we get to the top window object. This list is what we attach scroll listeners\nto, because if any of these parent elements scroll, we'll need to re-calculate the\nreference element's position.\n*/\n\nexport default function listScrollParents(element, list) {\n  var _element$ownerDocumen;\n\n  if (list === void 0) {\n    list = [];\n  }\n\n  var scrollParent = getScrollParent(element);\n  var isBody = scrollParent === ((_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body);\n  var win = getWindow(scrollParent);\n  var target = isBody ? [win].concat(win.visualViewport || [], isScrollParent(scrollParent) ? scrollParent : []) : scrollParent;\n  var updatedList = list.concat(target);\n  return isBody ? updatedList : // $FlowFixMe[incompatible-call]: isBody tells us target will be an HTMLElement here\n  updatedList.concat(listScrollParents(getParentNode(target)));\n}", "export default function rectToClientRect(rect) {\n  return Object.assign({}, rect, {\n    left: rect.x,\n    top: rect.y,\n    right: rect.x + rect.width,\n    bottom: rect.y + rect.height\n  });\n}", "import { viewport } from \"../enums.js\";\nimport getViewportRect from \"./getViewportRect.js\";\nimport getDocumentRect from \"./getDocumentRect.js\";\nimport listScrollParents from \"./listScrollParents.js\";\nimport getOffsetParent from \"./getOffsetParent.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport { isElement, isHTMLElement } from \"./instanceOf.js\";\nimport getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport contains from \"./contains.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport rectToClientRect from \"../utils/rectToClientRect.js\";\nimport { max, min } from \"../utils/math.js\";\n\nfunction getInnerBoundingClientRect(element, strategy) {\n  var rect = getBoundingClientRect(element, false, strategy === 'fixed');\n  rect.top = rect.top + element.clientTop;\n  rect.left = rect.left + element.clientLeft;\n  rect.bottom = rect.top + element.clientHeight;\n  rect.right = rect.left + element.clientWidth;\n  rect.width = element.clientWidth;\n  rect.height = element.clientHeight;\n  rect.x = rect.left;\n  rect.y = rect.top;\n  return rect;\n}\n\nfunction getClientRectFromMixedType(element, clippingParent, strategy) {\n  return clippingParent === viewport ? rectToClientRect(getViewportRect(element, strategy)) : isElement(clippingParent) ? getInnerBoundingClientRect(clippingParent, strategy) : rectToClientRect(getDocumentRect(getDocumentElement(element)));\n} // A \"clipping parent\" is an overflowable container with the characteristic of\n// clipping (or hiding) overflowing elements with a position different from\n// `initial`\n\n\nfunction getClippingParents(element) {\n  var clippingParents = listScrollParents(getParentNode(element));\n  var canEscapeClipping = ['absolute', 'fixed'].indexOf(getComputedStyle(element).position) >= 0;\n  var clipperElement = canEscapeClipping && isHTMLElement(element) ? getOffsetParent(element) : element;\n\n  if (!isElement(clipperElement)) {\n    return [];\n  } // $FlowFixMe[incompatible-return]: https://github.com/facebook/flow/issues/1414\n\n\n  return clippingParents.filter(function (clippingParent) {\n    return isElement(clippingParent) && contains(clippingParent, clipperElement) && getNodeName(clippingParent) !== 'body';\n  });\n} // Gets the maximum area that the element is visible in due to any number of\n// clipping parents\n\n\nexport default function getClippingRect(element, boundary, rootBoundary, strategy) {\n  var mainClippingParents = boundary === 'clippingParents' ? getClippingParents(element) : [].concat(boundary);\n  var clippingParents = [].concat(mainClippingParents, [rootBoundary]);\n  var firstClippingParent = clippingParents[0];\n  var clippingRect = clippingParents.reduce(function (accRect, clippingParent) {\n    var rect = getClientRectFromMixedType(element, clippingParent, strategy);\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromMixedType(element, firstClippingParent, strategy));\n  clippingRect.width = clippingRect.right - clippingRect.left;\n  clippingRect.height = clippingRect.bottom - clippingRect.top;\n  clippingRect.x = clippingRect.left;\n  clippingRect.y = clippingRect.top;\n  return clippingRect;\n}", "import getWindow from \"./getWindow.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport isLayoutViewport from \"./isLayoutViewport.js\";\nexport default function getViewportRect(element, strategy) {\n  var win = getWindow(element);\n  var html = getDocumentElement(element);\n  var visualViewport = win.visualViewport;\n  var width = html.clientWidth;\n  var height = html.clientHeight;\n  var x = 0;\n  var y = 0;\n\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n    var layoutViewport = isLayoutViewport();\n\n    if (layoutViewport || !layoutViewport && strategy === 'fixed') {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x + getWindowScrollBarX(element),\n    y: y\n  };\n}", "import getDocumentElement from \"./getDocumentElement.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport getWindowScroll from \"./getWindowScroll.js\";\nimport { max } from \"../utils/math.js\"; // Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable\n\nexport default function getDocumentRect(element) {\n  var _element$ownerDocumen;\n\n  var html = getDocumentElement(element);\n  var winScroll = getWindowScroll(element);\n  var body = (_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body;\n  var width = max(html.scrollWidth, html.clientWidth, body ? body.scrollWidth : 0, body ? body.clientWidth : 0);\n  var height = max(html.scrollHeight, html.clientHeight, body ? body.scrollHeight : 0, body ? body.clientHeight : 0);\n  var x = -winScroll.scrollLeft + getWindowScrollBarX(element);\n  var y = -winScroll.scrollTop;\n\n  if (getComputedStyle(body || html).direction === 'rtl') {\n    x += max(html.clientWidth, body ? body.clientWidth : 0) - width;\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x,\n    y: y\n  };\n}", "import getBasePlacement from \"./getBasePlacement.js\";\nimport getVariation from \"./getVariation.js\";\nimport getMainAxisFromPlacement from \"./getMainAxisFromPlacement.js\";\nimport { top, right, bottom, left, start, end } from \"../enums.js\";\nexport default function computeOffsets(_ref) {\n  var reference = _ref.reference,\n      element = _ref.element,\n      placement = _ref.placement;\n  var basePlacement = placement ? getBasePlacement(placement) : null;\n  var variation = placement ? getVariation(placement) : null;\n  var commonX = reference.x + reference.width / 2 - element.width / 2;\n  var commonY = reference.y + reference.height / 2 - element.height / 2;\n  var offsets;\n\n  switch (basePlacement) {\n    case top:\n      offsets = {\n        x: commonX,\n        y: reference.y - element.height\n      };\n      break;\n\n    case bottom:\n      offsets = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n\n    case right:\n      offsets = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n\n    case left:\n      offsets = {\n        x: reference.x - element.width,\n        y: commonY\n      };\n      break;\n\n    default:\n      offsets = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n\n  var mainAxis = basePlacement ? getMainAxisFromPlacement(basePlacement) : null;\n\n  if (mainAxis != null) {\n    var len = mainAxis === 'y' ? 'height' : 'width';\n\n    switch (variation) {\n      case start:\n        offsets[mainAxis] = offsets[mainAxis] - (reference[len] / 2 - element[len] / 2);\n        break;\n\n      case end:\n        offsets[mainAxis] = offsets[mainAxis] + (reference[len] / 2 - element[len] / 2);\n        break;\n\n      default:\n    }\n  }\n\n  return offsets;\n}", "import getClippingRect from \"../dom-utils/getClippingRect.js\";\nimport getDocumentElement from \"../dom-utils/getDocumentElement.js\";\nimport getBoundingClientRect from \"../dom-utils/getBoundingClientRect.js\";\nimport computeOffsets from \"./computeOffsets.js\";\nimport rectToClientRect from \"./rectToClientRect.js\";\nimport { clippingParents, reference, popper, bottom, top, right, basePlacements, viewport } from \"../enums.js\";\nimport { isElement } from \"../dom-utils/instanceOf.js\";\nimport mergePaddingObject from \"./mergePaddingObject.js\";\nimport expandToHashMap from \"./expandToHashMap.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport default function detectOverflow(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      _options$placement = _options.placement,\n      placement = _options$placement === void 0 ? state.placement : _options$placement,\n      _options$strategy = _options.strategy,\n      strategy = _options$strategy === void 0 ? state.strategy : _options$strategy,\n      _options$boundary = _options.boundary,\n      boundary = _options$boundary === void 0 ? clippingParents : _options$boundary,\n      _options$rootBoundary = _options.rootBoundary,\n      rootBoundary = _options$rootBoundary === void 0 ? viewport : _options$rootBoundary,\n      _options$elementConte = _options.elementContext,\n      elementContext = _options$elementConte === void 0 ? popper : _options$elementConte,\n      _options$altBoundary = _options.altBoundary,\n      altBoundary = _options$altBoundary === void 0 ? false : _options$altBoundary,\n      _options$padding = _options.padding,\n      padding = _options$padding === void 0 ? 0 : _options$padding;\n  var paddingObject = mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));\n  var altContext = elementContext === popper ? reference : popper;\n  var popperRect = state.rects.popper;\n  var element = state.elements[altBoundary ? altContext : elementContext];\n  var clippingClientRect = getClippingRect(isElement(element) ? element : element.contextElement || getDocumentElement(state.elements.popper), boundary, rootBoundary, strategy);\n  var referenceClientRect = getBoundingClientRect(state.elements.reference);\n  var popperOffsets = computeOffsets({\n    reference: referenceClientRect,\n    element: popperRect,\n    strategy: 'absolute',\n    placement: placement\n  });\n  var popperClientRect = rectToClientRect(Object.assign({}, popperRect, popperOffsets));\n  var elementClientRect = elementContext === popper ? popperClientRect : referenceClientRect; // positive = overflowing the clipping rect\n  // 0 or negative = within the clipping rect\n\n  var overflowOffsets = {\n    top: clippingClientRect.top - elementClientRect.top + paddingObject.top,\n    bottom: elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom,\n    left: clippingClientRect.left - elementClientRect.left + paddingObject.left,\n    right: elementClientRect.right - clippingClientRect.right + paddingObject.right\n  };\n  var offsetData = state.modifiersData.offset; // Offsets can be applied only to the popper element\n\n  if (elementContext === popper && offsetData) {\n    var offset = offsetData[placement];\n    Object.keys(overflowOffsets).forEach(function (key) {\n      var multiply = [right, bottom].indexOf(key) >= 0 ? 1 : -1;\n      var axis = [top, bottom].indexOf(key) >= 0 ? 'y' : 'x';\n      overflowOffsets[key] += offset[axis] * multiply;\n    });\n  }\n\n  return overflowOffsets;\n}", "import getVariation from \"./getVariation.js\";\nimport { variationPlacements, basePlacements, placements as allPlacements } from \"../enums.js\";\nimport detectOverflow from \"./detectOverflow.js\";\nimport getBasePlacement from \"./getBasePlacement.js\";\nexport default function computeAutoPlacement(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      placement = _options.placement,\n      boundary = _options.boundary,\n      rootBoundary = _options.rootBoundary,\n      padding = _options.padding,\n      flipVariations = _options.flipVariations,\n      _options$allowedAutoP = _options.allowedAutoPlacements,\n      allowedAutoPlacements = _options$allowedAutoP === void 0 ? allPlacements : _options$allowedAutoP;\n  var variation = getVariation(placement);\n  var placements = variation ? flipVariations ? variationPlacements : variationPlacements.filter(function (placement) {\n    return getVariation(placement) === variation;\n  }) : basePlacements;\n  var allowedPlacements = placements.filter(function (placement) {\n    return allowedAutoPlacements.indexOf(placement) >= 0;\n  });\n\n  if (allowedPlacements.length === 0) {\n    allowedPlacements = placements;\n  } // $FlowFixMe[incompatible-type]: Flow seems to have problems with two array unions...\n\n\n  var overflows = allowedPlacements.reduce(function (acc, placement) {\n    acc[placement] = detectOverflow(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding\n    })[getBasePlacement(placement)];\n    return acc;\n  }, {});\n  return Object.keys(overflows).sort(function (a, b) {\n    return overflows[a] - overflows[b];\n  });\n}", "import getOppositePlacement from \"../utils/getOppositePlacement.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getOppositeVariationPlacement from \"../utils/getOppositeVariationPlacement.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\nimport computeAutoPlacement from \"../utils/computeAutoPlacement.js\";\nimport { bottom, top, start, right, left, auto } from \"../enums.js\";\nimport getVariation from \"../utils/getVariation.js\"; // eslint-disable-next-line import/no-unused-modules\n\nfunction getExpandedFallbackPlacements(placement) {\n  if (getBasePlacement(placement) === auto) {\n    return [];\n  }\n\n  var oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeVariationPlacement(placement), oppositePlacement, getOppositeVariationPlacement(oppositePlacement)];\n}\n\nfunction flip(_ref) {\n  var state = _ref.state,\n      options = _ref.options,\n      name = _ref.name;\n\n  if (state.modifiersData[name]._skip) {\n    return;\n  }\n\n  var _options$mainAxis = options.mainAxis,\n      checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,\n      _options$altAxis = options.altAxis,\n      checkAltAxis = _options$altAxis === void 0 ? true : _options$altAxis,\n      specifiedFallbackPlacements = options.fallbackPlacements,\n      padding = options.padding,\n      boundary = options.boundary,\n      rootBoundary = options.rootBoundary,\n      altBoundary = options.altBoundary,\n      _options$flipVariatio = options.flipVariations,\n      flipVariations = _options$flipVariatio === void 0 ? true : _options$flipVariatio,\n      allowedAutoPlacements = options.allowedAutoPlacements;\n  var preferredPlacement = state.options.placement;\n  var basePlacement = getBasePlacement(preferredPlacement);\n  var isBasePlacement = basePlacement === preferredPlacement;\n  var fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipVariations ? [getOppositePlacement(preferredPlacement)] : getExpandedFallbackPlacements(preferredPlacement));\n  var placements = [preferredPlacement].concat(fallbackPlacements).reduce(function (acc, placement) {\n    return acc.concat(getBasePlacement(placement) === auto ? computeAutoPlacement(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding,\n      flipVariations: flipVariations,\n      allowedAutoPlacements: allowedAutoPlacements\n    }) : placement);\n  }, []);\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var checksMap = new Map();\n  var makeFallbackChecks = true;\n  var firstFittingPlacement = placements[0];\n\n  for (var i = 0; i < placements.length; i++) {\n    var placement = placements[i];\n\n    var _basePlacement = getBasePlacement(placement);\n\n    var isStartVariation = getVariation(placement) === start;\n    var isVertical = [top, bottom].indexOf(_basePlacement) >= 0;\n    var len = isVertical ? 'width' : 'height';\n    var overflow = detectOverflow(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      altBoundary: altBoundary,\n      padding: padding\n    });\n    var mainVariationSide = isVertical ? isStartVariation ? right : left : isStartVariation ? bottom : top;\n\n    if (referenceRect[len] > popperRect[len]) {\n      mainVariationSide = getOppositePlacement(mainVariationSide);\n    }\n\n    var altVariationSide = getOppositePlacement(mainVariationSide);\n    var checks = [];\n\n    if (checkMainAxis) {\n      checks.push(overflow[_basePlacement] <= 0);\n    }\n\n    if (checkAltAxis) {\n      checks.push(overflow[mainVariationSide] <= 0, overflow[altVariationSide] <= 0);\n    }\n\n    if (checks.every(function (check) {\n      return check;\n    })) {\n      firstFittingPlacement = placement;\n      makeFallbackChecks = false;\n      break;\n    }\n\n    checksMap.set(placement, checks);\n  }\n\n  if (makeFallbackChecks) {\n    // `2` may be desired in some cases – research later\n    var numberOfChecks = flipVariations ? 3 : 1;\n\n    var _loop = function _loop(_i) {\n      var fittingPlacement = placements.find(function (placement) {\n        var checks = checksMap.get(placement);\n\n        if (checks) {\n          return checks.slice(0, _i).every(function (check) {\n            return check;\n          });\n        }\n      });\n\n      if (fittingPlacement) {\n        firstFittingPlacement = fittingPlacement;\n        return \"break\";\n      }\n    };\n\n    for (var _i = numberOfChecks; _i > 0; _i--) {\n      var _ret = _loop(_i);\n\n      if (_ret === \"break\") break;\n    }\n  }\n\n  if (state.placement !== firstFittingPlacement) {\n    state.modifiersData[name]._skip = true;\n    state.placement = firstFittingPlacement;\n    state.reset = true;\n  }\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'flip',\n  enabled: true,\n  phase: 'main',\n  fn: flip,\n  requiresIfExists: ['offset'],\n  data: {\n    _skip: false\n  }\n};", "import { top, bottom, left, right } from \"../enums.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\n\nfunction getSideOffsets(overflow, rect, preventedOffsets) {\n  if (preventedOffsets === void 0) {\n    preventedOffsets = {\n      x: 0,\n      y: 0\n    };\n  }\n\n  return {\n    top: overflow.top - rect.height - preventedOffsets.y,\n    right: overflow.right - rect.width + preventedOffsets.x,\n    bottom: overflow.bottom - rect.height + preventedOffsets.y,\n    left: overflow.left - rect.width - preventedOffsets.x\n  };\n}\n\nfunction isAnySideFullyClipped(overflow) {\n  return [top, right, bottom, left].some(function (side) {\n    return overflow[side] >= 0;\n  });\n}\n\nfunction hide(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var preventedOffsets = state.modifiersData.preventOverflow;\n  var referenceOverflow = detectOverflow(state, {\n    elementContext: 'reference'\n  });\n  var popperAltOverflow = detectOverflow(state, {\n    altBoundary: true\n  });\n  var referenceClippingOffsets = getSideOffsets(referenceOverflow, referenceRect);\n  var popperEscapeOffsets = getSideOffsets(popperAltOverflow, popperRect, preventedOffsets);\n  var isReferenceHidden = isAnySideFullyClipped(referenceClippingOffsets);\n  var hasPopperEscaped = isAnySideFullyClipped(popperEscapeOffsets);\n  state.modifiersData[name] = {\n    referenceClippingOffsets: referenceClippingOffsets,\n    popperEscapeOffsets: popperEscapeOffsets,\n    isReferenceHidden: isReferenceHidden,\n    hasPopperEscaped: hasPopperEscaped\n  };\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-reference-hidden': isReferenceHidden,\n    'data-popper-escaped': hasPopperEscaped\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'hide',\n  enabled: true,\n  phase: 'main',\n  requiresIfExists: ['preventOverflow'],\n  fn: hide\n};", "import getBasePlacement from \"../utils/getBasePlacement.js\";\nimport { top, left, right, placements } from \"../enums.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport function distanceAndSkiddingToXY(placement, rects, offset) {\n  var basePlacement = getBasePlacement(placement);\n  var invertDistance = [left, top].indexOf(basePlacement) >= 0 ? -1 : 1;\n\n  var _ref = typeof offset === 'function' ? offset(Object.assign({}, rects, {\n    placement: placement\n  })) : offset,\n      skidding = _ref[0],\n      distance = _ref[1];\n\n  skidding = skidding || 0;\n  distance = (distance || 0) * invertDistance;\n  return [left, right].indexOf(basePlacement) >= 0 ? {\n    x: distance,\n    y: skidding\n  } : {\n    x: skidding,\n    y: distance\n  };\n}\n\nfunction offset(_ref2) {\n  var state = _ref2.state,\n      options = _ref2.options,\n      name = _ref2.name;\n  var _options$offset = options.offset,\n      offset = _options$offset === void 0 ? [0, 0] : _options$offset;\n  var data = placements.reduce(function (acc, placement) {\n    acc[placement] = distanceAndSkiddingToXY(placement, state.rects, offset);\n    return acc;\n  }, {});\n  var _data$state$placement = data[state.placement],\n      x = _data$state$placement.x,\n      y = _data$state$placement.y;\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.modifiersData.popperOffsets.x += x;\n    state.modifiersData.popperOffsets.y += y;\n  }\n\n  state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'offset',\n  enabled: true,\n  phase: 'main',\n  requires: ['popperOffsets'],\n  fn: offset\n};", "import computeOffsets from \"../utils/computeOffsets.js\";\n\nfunction popperOffsets(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  // Offsets are the actual position the popper needs to have to be\n  // properly positioned near its reference element\n  // This is the most basic placement, and will be adjusted by\n  // the modifiers in the next step\n  state.modifiersData[name] = computeOffsets({\n    reference: state.rects.reference,\n    element: state.rects.popper,\n    strategy: 'absolute',\n    placement: state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'popperOffsets',\n  enabled: true,\n  phase: 'read',\n  fn: popperOffsets,\n  data: {}\n};", "import { top, left, right, bottom, start } from \"../enums.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getMainAxisFromPlacement from \"../utils/getMainAxisFromPlacement.js\";\nimport getAltAxis from \"../utils/getAltAxis.js\";\nimport { within, withinMaxClamp } from \"../utils/within.js\";\nimport getLayoutRect from \"../dom-utils/getLayoutRect.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\nimport getVariation from \"../utils/getVariation.js\";\nimport getFreshSideObject from \"../utils/getFreshSideObject.js\";\nimport { min as mathMin, max as mathMax } from \"../utils/math.js\";\n\nfunction preventOverflow(_ref) {\n  var state = _ref.state,\n      options = _ref.options,\n      name = _ref.name;\n  var _options$mainAxis = options.mainAxis,\n      checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,\n      _options$altAxis = options.altAxis,\n      checkAltAxis = _options$altAxis === void 0 ? false : _options$altAxis,\n      boundary = options.boundary,\n      rootBoundary = options.rootBoundary,\n      altBoundary = options.altBoundary,\n      padding = options.padding,\n      _options$tether = options.tether,\n      tether = _options$tether === void 0 ? true : _options$tether,\n      _options$tetherOffset = options.tetherOffset,\n      tetherOffset = _options$tetherOffset === void 0 ? 0 : _options$tetherOffset;\n  var overflow = detectOverflow(state, {\n    boundary: boundary,\n    rootBoundary: rootBoundary,\n    padding: padding,\n    altBoundary: altBoundary\n  });\n  var basePlacement = getBasePlacement(state.placement);\n  var variation = getVariation(state.placement);\n  var isBasePlacement = !variation;\n  var mainAxis = getMainAxisFromPlacement(basePlacement);\n  var altAxis = getAltAxis(mainAxis);\n  var popperOffsets = state.modifiersData.popperOffsets;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var tetherOffsetValue = typeof tetherOffset === 'function' ? tetherOffset(Object.assign({}, state.rects, {\n    placement: state.placement\n  })) : tetherOffset;\n  var normalizedTetherOffsetValue = typeof tetherOffsetValue === 'number' ? {\n    mainAxis: tetherOffsetValue,\n    altAxis: tetherOffsetValue\n  } : Object.assign({\n    mainAxis: 0,\n    altAxis: 0\n  }, tetherOffsetValue);\n  var offsetModifierState = state.modifiersData.offset ? state.modifiersData.offset[state.placement] : null;\n  var data = {\n    x: 0,\n    y: 0\n  };\n\n  if (!popperOffsets) {\n    return;\n  }\n\n  if (checkMainAxis) {\n    var _offsetModifierState$;\n\n    var mainSide = mainAxis === 'y' ? top : left;\n    var altSide = mainAxis === 'y' ? bottom : right;\n    var len = mainAxis === 'y' ? 'height' : 'width';\n    var offset = popperOffsets[mainAxis];\n    var min = offset + overflow[mainSide];\n    var max = offset - overflow[altSide];\n    var additive = tether ? -popperRect[len] / 2 : 0;\n    var minLen = variation === start ? referenceRect[len] : popperRect[len];\n    var maxLen = variation === start ? -popperRect[len] : -referenceRect[len]; // We need to include the arrow in the calculation so the arrow doesn't go\n    // outside the reference bounds\n\n    var arrowElement = state.elements.arrow;\n    var arrowRect = tether && arrowElement ? getLayoutRect(arrowElement) : {\n      width: 0,\n      height: 0\n    };\n    var arrowPaddingObject = state.modifiersData['arrow#persistent'] ? state.modifiersData['arrow#persistent'].padding : getFreshSideObject();\n    var arrowPaddingMin = arrowPaddingObject[mainSide];\n    var arrowPaddingMax = arrowPaddingObject[altSide]; // If the reference length is smaller than the arrow length, we don't want\n    // to include its full size in the calculation. If the reference is small\n    // and near the edge of a boundary, the popper can overflow even if the\n    // reference is not overflowing as well (e.g. virtual elements with no\n    // width or height)\n\n    var arrowLen = within(0, referenceRect[len], arrowRect[len]);\n    var minOffset = isBasePlacement ? referenceRect[len] / 2 - additive - arrowLen - arrowPaddingMin - normalizedTetherOffsetValue.mainAxis : minLen - arrowLen - arrowPaddingMin - normalizedTetherOffsetValue.mainAxis;\n    var maxOffset = isBasePlacement ? -referenceRect[len] / 2 + additive + arrowLen + arrowPaddingMax + normalizedTetherOffsetValue.mainAxis : maxLen + arrowLen + arrowPaddingMax + normalizedTetherOffsetValue.mainAxis;\n    var arrowOffsetParent = state.elements.arrow && getOffsetParent(state.elements.arrow);\n    var clientOffset = arrowOffsetParent ? mainAxis === 'y' ? arrowOffsetParent.clientTop || 0 : arrowOffsetParent.clientLeft || 0 : 0;\n    var offsetModifierValue = (_offsetModifierState$ = offsetModifierState == null ? void 0 : offsetModifierState[mainAxis]) != null ? _offsetModifierState$ : 0;\n    var tetherMin = offset + minOffset - offsetModifierValue - clientOffset;\n    var tetherMax = offset + maxOffset - offsetModifierValue;\n    var preventedOffset = within(tether ? mathMin(min, tetherMin) : min, offset, tether ? mathMax(max, tetherMax) : max);\n    popperOffsets[mainAxis] = preventedOffset;\n    data[mainAxis] = preventedOffset - offset;\n  }\n\n  if (checkAltAxis) {\n    var _offsetModifierState$2;\n\n    var _mainSide = mainAxis === 'x' ? top : left;\n\n    var _altSide = mainAxis === 'x' ? bottom : right;\n\n    var _offset = popperOffsets[altAxis];\n\n    var _len = altAxis === 'y' ? 'height' : 'width';\n\n    var _min = _offset + overflow[_mainSide];\n\n    var _max = _offset - overflow[_altSide];\n\n    var isOriginSide = [top, left].indexOf(basePlacement) !== -1;\n\n    var _offsetModifierValue = (_offsetModifierState$2 = offsetModifierState == null ? void 0 : offsetModifierState[altAxis]) != null ? _offsetModifierState$2 : 0;\n\n    var _tetherMin = isOriginSide ? _min : _offset - referenceRect[_len] - popperRect[_len] - _offsetModifierValue + normalizedTetherOffsetValue.altAxis;\n\n    var _tetherMax = isOriginSide ? _offset + referenceRect[_len] + popperRect[_len] - _offsetModifierValue - normalizedTetherOffsetValue.altAxis : _max;\n\n    var _preventedOffset = tether && isOriginSide ? withinMaxClamp(_tetherMin, _offset, _tetherMax) : within(tether ? _tetherMin : _min, _offset, tether ? _tetherMax : _max);\n\n    popperOffsets[altAxis] = _preventedOffset;\n    data[altAxis] = _preventedOffset - _offset;\n  }\n\n  state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'preventOverflow',\n  enabled: true,\n  phase: 'main',\n  fn: preventOverflow,\n  requiresIfExists: ['offset']\n};", "export default function getAltAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getNodeScroll from \"./getNodeScroll.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport isScrollParent from \"./isScrollParent.js\";\nimport { round } from \"../utils/math.js\";\n\nfunction isElementScaled(element) {\n  var rect = element.getBoundingClientRect();\n  var scaleX = round(rect.width) / element.offsetWidth || 1;\n  var scaleY = round(rect.height) / element.offsetHeight || 1;\n  return scaleX !== 1 || scaleY !== 1;\n} // Returns the composite rect of an element relative to its offsetParent.\n// Composite means it takes into account transforms as well as layout.\n\n\nexport default function getCompositeRect(elementOrVirtualElement, offsetParent, isFixed) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n\n  var isOffsetParentAnElement = isHTMLElement(offsetParent);\n  var offsetParentIsScaled = isHTMLElement(offsetParent) && isElementScaled(offsetParent);\n  var documentElement = getDocumentElement(offsetParent);\n  var rect = getBoundingClientRect(elementOrVirtualElement, offsetParentIsScaled, isFixed);\n  var scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  var offsets = {\n    x: 0,\n    y: 0\n  };\n\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || // https://github.com/popperjs/popper-core/issues/1078\n    isScrollParent(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n\n    if (isHTMLElement(offsetParent)) {\n      offsets = getBoundingClientRect(offsetParent, true);\n      offsets.x += offsetParent.clientLeft;\n      offsets.y += offsetParent.clientTop;\n    } else if (documentElement) {\n      offsets.x = getWindowScrollBarX(documentElement);\n    }\n  }\n\n  return {\n    x: rect.left + scroll.scrollLeft - offsets.x,\n    y: rect.top + scroll.scrollTop - offsets.y,\n    width: rect.width,\n    height: rect.height\n  };\n}", "import getWindowScroll from \"./getWindowScroll.js\";\nimport getWindow from \"./getWindow.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport getHTMLElementScroll from \"./getHTMLElementScroll.js\";\nexport default function getNodeScroll(node) {\n  if (node === getWindow(node) || !isHTMLElement(node)) {\n    return getWindowScroll(node);\n  } else {\n    return getHTMLElementScroll(node);\n  }\n}", "export default function getHTMLElementScroll(element) {\n  return {\n    scrollLeft: element.scrollLeft,\n    scrollTop: element.scrollTop\n  };\n}", "import { modifierPhases } from \"../enums.js\"; // source: https://stackoverflow.com/questions/49875255\n\nfunction order(modifiers) {\n  var map = new Map();\n  var visited = new Set();\n  var result = [];\n  modifiers.forEach(function (modifier) {\n    map.set(modifier.name, modifier);\n  }); // On visiting object, check for its dependencies and visit them recursively\n\n  function sort(modifier) {\n    visited.add(modifier.name);\n    var requires = [].concat(modifier.requires || [], modifier.requiresIfExists || []);\n    requires.forEach(function (dep) {\n      if (!visited.has(dep)) {\n        var depModifier = map.get(dep);\n\n        if (depModifier) {\n          sort(depModifier);\n        }\n      }\n    });\n    result.push(modifier);\n  }\n\n  modifiers.forEach(function (modifier) {\n    if (!visited.has(modifier.name)) {\n      // check for visited object\n      sort(modifier);\n    }\n  });\n  return result;\n}\n\nexport default function orderModifiers(modifiers) {\n  // order based on dependencies\n  var orderedModifiers = order(modifiers); // order based on phase\n\n  return modifierPhases.reduce(function (acc, phase) {\n    return acc.concat(orderedModifiers.filter(function (modifier) {\n      return modifier.phase === phase;\n    }));\n  }, []);\n}", "import getCompositeRect from \"./dom-utils/getCompositeRect.js\";\nimport getLayoutRect from \"./dom-utils/getLayoutRect.js\";\nimport listScrollParents from \"./dom-utils/listScrollParents.js\";\nimport getOffsetParent from \"./dom-utils/getOffsetParent.js\";\nimport orderModifiers from \"./utils/orderModifiers.js\";\nimport debounce from \"./utils/debounce.js\";\nimport mergeByName from \"./utils/mergeByName.js\";\nimport detectOverflow from \"./utils/detectOverflow.js\";\nimport { isElement } from \"./dom-utils/instanceOf.js\";\nvar DEFAULT_OPTIONS = {\n  placement: 'bottom',\n  modifiers: [],\n  strategy: 'absolute'\n};\n\nfunction areValidElements() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n\n  return !args.some(function (element) {\n    return !(element && typeof element.getBoundingClientRect === 'function');\n  });\n}\n\nexport function popperGenerator(generatorOptions) {\n  if (generatorOptions === void 0) {\n    generatorOptions = {};\n  }\n\n  var _generatorOptions = generatorOptions,\n      _generatorOptions$def = _generatorOptions.defaultModifiers,\n      defaultModifiers = _generatorOptions$def === void 0 ? [] : _generatorOptions$def,\n      _generatorOptions$def2 = _generatorOptions.defaultOptions,\n      defaultOptions = _generatorOptions$def2 === void 0 ? DEFAULT_OPTIONS : _generatorOptions$def2;\n  return function createPopper(reference, popper, options) {\n    if (options === void 0) {\n      options = defaultOptions;\n    }\n\n    var state = {\n      placement: 'bottom',\n      orderedModifiers: [],\n      options: Object.assign({}, DEFAULT_OPTIONS, defaultOptions),\n      modifiersData: {},\n      elements: {\n        reference: reference,\n        popper: popper\n      },\n      attributes: {},\n      styles: {}\n    };\n    var effectCleanupFns = [];\n    var isDestroyed = false;\n    var instance = {\n      state: state,\n      setOptions: function setOptions(setOptionsAction) {\n        var options = typeof setOptionsAction === 'function' ? setOptionsAction(state.options) : setOptionsAction;\n        cleanupModifierEffects();\n        state.options = Object.assign({}, defaultOptions, state.options, options);\n        state.scrollParents = {\n          reference: isElement(reference) ? listScrollParents(reference) : reference.contextElement ? listScrollParents(reference.contextElement) : [],\n          popper: listScrollParents(popper)\n        }; // Orders the modifiers based on their dependencies and `phase`\n        // properties\n\n        var orderedModifiers = orderModifiers(mergeByName([].concat(defaultModifiers, state.options.modifiers))); // Strip out disabled modifiers\n\n        state.orderedModifiers = orderedModifiers.filter(function (m) {\n          return m.enabled;\n        });\n        runModifierEffects();\n        return instance.update();\n      },\n      // Sync update – it will always be executed, even if not necessary. This\n      // is useful for low frequency updates where sync behavior simplifies the\n      // logic.\n      // For high frequency updates (e.g. `resize` and `scroll` events), always\n      // prefer the async Popper#update method\n      forceUpdate: function forceUpdate() {\n        if (isDestroyed) {\n          return;\n        }\n\n        var _state$elements = state.elements,\n            reference = _state$elements.reference,\n            popper = _state$elements.popper; // Don't proceed if `reference` or `popper` are not valid elements\n        // anymore\n\n        if (!areValidElements(reference, popper)) {\n          return;\n        } // Store the reference and popper rects to be read by modifiers\n\n\n        state.rects = {\n          reference: getCompositeRect(reference, getOffsetParent(popper), state.options.strategy === 'fixed'),\n          popper: getLayoutRect(popper)\n        }; // Modifiers have the ability to reset the current update cycle. The\n        // most common use case for this is the `flip` modifier changing the\n        // placement, which then needs to re-run all the modifiers, because the\n        // logic was previously ran for the previous placement and is therefore\n        // stale/incorrect\n\n        state.reset = false;\n        state.placement = state.options.placement; // On each update cycle, the `modifiersData` property for each modifier\n        // is filled with the initial data specified by the modifier. This means\n        // it doesn't persist and is fresh on each update.\n        // To ensure persistent data, use `${name}#persistent`\n\n        state.orderedModifiers.forEach(function (modifier) {\n          return state.modifiersData[modifier.name] = Object.assign({}, modifier.data);\n        });\n\n        for (var index = 0; index < state.orderedModifiers.length; index++) {\n          if (state.reset === true) {\n            state.reset = false;\n            index = -1;\n            continue;\n          }\n\n          var _state$orderedModifie = state.orderedModifiers[index],\n              fn = _state$orderedModifie.fn,\n              _state$orderedModifie2 = _state$orderedModifie.options,\n              _options = _state$orderedModifie2 === void 0 ? {} : _state$orderedModifie2,\n              name = _state$orderedModifie.name;\n\n          if (typeof fn === 'function') {\n            state = fn({\n              state: state,\n              options: _options,\n              name: name,\n              instance: instance\n            }) || state;\n          }\n        }\n      },\n      // Async and optimistically optimized update – it will not be executed if\n      // not necessary (debounced to run at most once-per-tick)\n      update: debounce(function () {\n        return new Promise(function (resolve) {\n          instance.forceUpdate();\n          resolve(state);\n        });\n      }),\n      destroy: function destroy() {\n        cleanupModifierEffects();\n        isDestroyed = true;\n      }\n    };\n\n    if (!areValidElements(reference, popper)) {\n      return instance;\n    }\n\n    instance.setOptions(options).then(function (state) {\n      if (!isDestroyed && options.onFirstUpdate) {\n        options.onFirstUpdate(state);\n      }\n    }); // Modifiers have the ability to execute arbitrary code before the first\n    // update cycle runs. They will be executed in the same order as the update\n    // cycle. This is useful when a modifier adds some persistent data that\n    // other modifiers need to use, but the modifier is run after the dependent\n    // one.\n\n    function runModifierEffects() {\n      state.orderedModifiers.forEach(function (_ref) {\n        var name = _ref.name,\n            _ref$options = _ref.options,\n            options = _ref$options === void 0 ? {} : _ref$options,\n            effect = _ref.effect;\n\n        if (typeof effect === 'function') {\n          var cleanupFn = effect({\n            state: state,\n            name: name,\n            instance: instance,\n            options: options\n          });\n\n          var noopFn = function noopFn() {};\n\n          effectCleanupFns.push(cleanupFn || noopFn);\n        }\n      });\n    }\n\n    function cleanupModifierEffects() {\n      effectCleanupFns.forEach(function (fn) {\n        return fn();\n      });\n      effectCleanupFns = [];\n    }\n\n    return instance;\n  };\n}\nexport var createPopper = /*#__PURE__*/popperGenerator(); // eslint-disable-next-line import/no-unused-modules\n\nexport { detectOverflow };", "export default function debounce(fn) {\n  var pending;\n  return function () {\n    if (!pending) {\n      pending = new Promise(function (resolve) {\n        Promise.resolve().then(function () {\n          pending = undefined;\n          resolve(fn());\n        });\n      });\n    }\n\n    return pending;\n  };\n}", "export default function mergeByName(modifiers) {\n  var merged = modifiers.reduce(function (merged, current) {\n    var existing = merged[current.name];\n    merged[current.name] = existing ? Object.assign({}, existing, current, {\n      options: Object.assign({}, existing.options, current.options),\n      data: Object.assign({}, existing.data, current.data)\n    }) : current;\n    return merged;\n  }, {}); // IE11 does not support Object.values\n\n  return Object.keys(merged).map(function (key) {\n    return merged[key];\n  });\n}", "import { popperGenerator, detectOverflow } from \"./createPopper.js\";\nimport eventListeners from \"./modifiers/eventListeners.js\";\nimport popperOffsets from \"./modifiers/popperOffsets.js\";\nimport computeStyles from \"./modifiers/computeStyles.js\";\nimport applyStyles from \"./modifiers/applyStyles.js\";\nvar defaultModifiers = [eventListeners, popperOffsets, computeStyles, applyStyles];\nvar createPopper = /*#__PURE__*/popperGenerator({\n  defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow };", "import { popperGenerator, detectOverflow } from \"./createPopper.js\";\nimport eventListeners from \"./modifiers/eventListeners.js\";\nimport popperOffsets from \"./modifiers/popperOffsets.js\";\nimport computeStyles from \"./modifiers/computeStyles.js\";\nimport applyStyles from \"./modifiers/applyStyles.js\";\nimport offset from \"./modifiers/offset.js\";\nimport flip from \"./modifiers/flip.js\";\nimport preventOverflow from \"./modifiers/preventOverflow.js\";\nimport arrow from \"./modifiers/arrow.js\";\nimport hide from \"./modifiers/hide.js\";\nvar defaultModifiers = [eventListeners, popperOffsets, computeStyles, applyStyles, offset, flip, preventOverflow, arrow, hide];\nvar createPopper = /*#__PURE__*/popperGenerator({\n  defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow }; // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper as createPopperLite } from \"./popper-lite.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport * from \"./modifiers/index.js\";", "/**\n * --------------------------------------------------------------------------\n * Bootstrap dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport Manipulator from './dom/manipulator.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport {\n  defineJQueryPlugin,\n  execute,\n  getElement,\n  getNextActiveElement,\n  isDisabled,\n  isElement,\n  isRTL,\n  isVisible,\n  noop\n} from './util/index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'dropdown'\nconst DATA_KEY = 'bs.dropdown'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ESCAPE_KEY = 'Escape'\nconst TAB_KEY = 'Tab'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\nconst RIGHT_MOUSE_BUTTON = 2 // MouseEvent.button value for the secondary button, usually the right button\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DATA_API = `keydown${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYUP_DATA_API = `keyup${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_DROPUP = 'dropup'\nconst CLASS_NAME_DROPEND = 'dropend'\nconst CLASS_NAME_DROPSTART = 'dropstart'\nconst CLASS_NAME_DROPUP_CENTER = 'dropup-center'\nconst CLASS_NAME_DROPDOWN_CENTER = 'dropdown-center'\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"dropdown\"]:not(.disabled):not(:disabled)'\nconst SELECTOR_DATA_TOGGLE_SHOWN = `${SELECTOR_DATA_TOGGLE}.${CLASS_NAME_SHOW}`\nconst SELECTOR_MENU = '.dropdown-menu'\nconst SELECTOR_NAVBAR = '.navbar'\nconst SELECTOR_NAVBAR_NAV = '.navbar-nav'\nconst SELECTOR_VISIBLE_ITEMS = '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)'\n\nconst PLACEMENT_TOP = isRTL() ? 'top-end' : 'top-start'\nconst PLACEMENT_TOPEND = isRTL() ? 'top-start' : 'top-end'\nconst PLACEMENT_BOTTOM = isRTL() ? 'bottom-end' : 'bottom-start'\nconst PLACEMENT_BOTTOMEND = isRTL() ? 'bottom-start' : 'bottom-end'\nconst PLACEMENT_RIGHT = isRTL() ? 'left-start' : 'right-start'\nconst PLACEMENT_LEFT = isRTL() ? 'right-start' : 'left-start'\nconst PLACEMENT_TOPCENTER = 'top'\nconst PLACEMENT_BOTTOMCENTER = 'bottom'\n\nconst Default = {\n  autoClose: true,\n  boundary: 'clippingParents',\n  display: 'dynamic',\n  offset: [0, 0], // Boosted mod\n  popperConfig: null,\n  reference: 'toggle'\n}\n\nconst DefaultType = {\n  autoClose: '(boolean|string)',\n  boundary: '(string|element)',\n  display: 'string',\n  offset: '(array|string|function)',\n  popperConfig: '(null|object|function)',\n  reference: '(string|element|object)'\n}\n\n/**\n * Class definition\n */\n\nclass Dropdown extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._popper = null\n    this._parent = this._element.parentNode // dropdown wrapper\n    // TODO: v6 revert #37011 & change markup https://getbootstrap.com/docs/5.3/forms/input-group/\n    this._menu = SelectorEngine.next(this._element, SELECTOR_MENU)[0] ||\n      SelectorEngine.prev(this._element, SELECTOR_MENU)[0] ||\n      SelectorEngine.findOne(SELECTOR_MENU, this._parent)\n    this._inNavbar = this._detectNavbar()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle() {\n    return this._isShown() ? this.hide() : this.show()\n  }\n\n  show() {\n    if (isDisabled(this._element) || this._isShown()) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, relatedTarget)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._createPopper()\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement && !this._parent.closest(SELECTOR_NAVBAR_NAV)) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.on(element, 'mouseover', noop)\n      }\n    }\n\n    this._element.focus()\n    this._element.setAttribute('aria-expanded', true)\n\n    this._menu.classList.add(CLASS_NAME_SHOW)\n    this._element.classList.add(CLASS_NAME_SHOW)\n    EventHandler.trigger(this._element, EVENT_SHOWN, relatedTarget)\n  }\n\n  hide() {\n    if (isDisabled(this._element) || !this._isShown()) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    this._completeHide(relatedTarget)\n  }\n\n  dispose() {\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    super.dispose()\n  }\n\n  update() {\n    this._inNavbar = this._detectNavbar()\n    if (this._popper) {\n      this._popper.update()\n    }\n  }\n\n  // Private\n  _completeHide(relatedTarget) {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE, relatedTarget)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.off(element, 'mouseover', noop)\n      }\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._menu.classList.remove(CLASS_NAME_SHOW)\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    this._element.setAttribute('aria-expanded', 'false')\n    Manipulator.removeDataAttribute(this._menu, 'popper')\n    EventHandler.trigger(this._element, EVENT_HIDDEN, relatedTarget)\n\n    // Explicitly return focus to the trigger element\n    this._element.focus()\n  }\n\n  _getConfig(config) {\n    config = super._getConfig(config)\n\n    if (typeof config.reference === 'object' && !isElement(config.reference) &&\n      typeof config.reference.getBoundingClientRect !== 'function'\n    ) {\n      // Popper virtual elements require a getBoundingClientRect method\n      throw new TypeError(`${NAME.toUpperCase()}: Option \"reference\" provided type \"object\" without a required \"getBoundingClientRect\" method.`)\n    }\n\n    return config\n  }\n\n  _createPopper() {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s dropdowns require Popper (https://popper.js.org/docs/v2/)')\n    }\n\n    let referenceElement = this._element\n\n    if (this._config.reference === 'parent') {\n      referenceElement = this._parent\n    } else if (isElement(this._config.reference)) {\n      referenceElement = getElement(this._config.reference)\n    } else if (typeof this._config.reference === 'object') {\n      referenceElement = this._config.reference\n    }\n\n    const popperConfig = this._getPopperConfig()\n    this._popper = Popper.createPopper(referenceElement, this._menu, popperConfig)\n  }\n\n  _isShown() {\n    return this._menu.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  _getPlacement() {\n    const parentDropdown = this._parent\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPEND)) {\n      return PLACEMENT_RIGHT\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPSTART)) {\n      return PLACEMENT_LEFT\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP_CENTER)) {\n      return PLACEMENT_TOPCENTER\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPDOWN_CENTER)) {\n      return PLACEMENT_BOTTOMCENTER\n    }\n\n    // We need to trim the value because custom properties can also include spaces\n    const isEnd = getComputedStyle(this._menu).getPropertyValue('--bs-position').trim() === 'end'\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP)) {\n      return isEnd ? PLACEMENT_TOPEND : PLACEMENT_TOP\n    }\n\n    return isEnd ? PLACEMENT_BOTTOMEND : PLACEMENT_BOTTOM\n  }\n\n  _detectNavbar() {\n    return this._element.closest(SELECTOR_NAVBAR) !== null\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(value => Number.parseInt(value, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _getPopperConfig() {\n    const defaultBsPopperConfig = {\n      placement: this._getPlacement(),\n      modifiers: [{\n        name: 'preventOverflow',\n        options: {\n          boundary: this._config.boundary\n        }\n      },\n      {\n        name: 'offset',\n        options: {\n          offset: this._getOffset()\n        }\n      }]\n    }\n\n    // Disable Popper if we have a static display or Dropdown is in Navbar\n    if (this._inNavbar || this._config.display === 'static') {\n      Manipulator.setDataAttribute(this._menu, 'popper', 'static') // TODO: v6 remove\n      defaultBsPopperConfig.modifiers = [{\n        name: 'applyStyles',\n        enabled: false\n      }]\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...execute(this._config.popperConfig, [undefined, defaultBsPopperConfig])\n    }\n  }\n\n  _selectMenuItem({ key, target }) {\n    const items = SelectorEngine.find(SELECTOR_VISIBLE_ITEMS, this._menu).filter(element => isVisible(element))\n\n    if (!items.length) {\n      return\n    }\n\n    // if target isn't included in items (e.g. when expanding the dropdown)\n    // allow cycling to get the last item in case key equals ARROW_UP_KEY\n    getNextActiveElement(items, target, key === ARROW_DOWN_KEY, !items.includes(target)).focus()\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Dropdown.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n\n  static clearMenus(event) {\n    if (event.button === RIGHT_MOUSE_BUTTON || (event.type === 'keyup' && event.key !== TAB_KEY)) {\n      return\n    }\n\n    const openToggles = SelectorEngine.find(SELECTOR_DATA_TOGGLE_SHOWN)\n\n    for (const toggle of openToggles) {\n      const context = Dropdown.getInstance(toggle)\n      if (!context || context._config.autoClose === false) {\n        continue\n      }\n\n      const composedPath = event.composedPath()\n      const isMenuTarget = composedPath.includes(context._menu)\n      if (\n        composedPath.includes(context._element) ||\n        (context._config.autoClose === 'inside' && !isMenuTarget) ||\n        (context._config.autoClose === 'outside' && isMenuTarget)\n      ) {\n        continue\n      }\n\n      // Tab navigation through the dropdown menu or events from contained inputs shouldn't close the menu\n      if (context._menu.contains(event.target) && ((event.type === 'keyup' && event.key === TAB_KEY) || /input|select|option|textarea|form/i.test(event.target.tagName))) {\n        continue\n      }\n\n      const relatedTarget = { relatedTarget: context._element }\n\n      if (event.type === 'click') {\n        relatedTarget.clickEvent = event\n      }\n\n      context._completeHide(relatedTarget)\n    }\n  }\n\n  static dataApiKeydownHandler(event) {\n    // If not an UP | DOWN | ESCAPE key => not a dropdown command\n    // If input/textarea && if key is other than ESCAPE => not a dropdown command\n\n    const isInput = /input|textarea/i.test(event.target.tagName)\n    const isEscapeEvent = event.key === ESCAPE_KEY\n    const isUpOrDownEvent = [ARROW_UP_KEY, ARROW_DOWN_KEY].includes(event.key)\n\n    if (!isUpOrDownEvent && !isEscapeEvent) {\n      return\n    }\n\n    if (isInput && !isEscapeEvent) {\n      return\n    }\n\n    event.preventDefault()\n\n    // TODO: v6 revert #37011 & change markup https://getbootstrap.com/docs/5.3/forms/input-group/\n    const getToggleButton = this.matches(SELECTOR_DATA_TOGGLE) ?\n      this :\n      (SelectorEngine.prev(this, SELECTOR_DATA_TOGGLE)[0] ||\n        SelectorEngine.next(this, SELECTOR_DATA_TOGGLE)[0] ||\n        SelectorEngine.findOne(SELECTOR_DATA_TOGGLE, event.delegateTarget.parentNode))\n\n    const instance = Dropdown.getOrCreateInstance(getToggleButton)\n\n    if (isUpOrDownEvent) {\n      event.stopPropagation()\n      instance.show()\n      instance._selectMenuItem(event)\n      return\n    }\n\n    if (instance._isShown()) { // else is escape and we check if it is shown\n      event.stopPropagation()\n      instance.hide()\n      getToggleButton.focus()\n    }\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_DATA_TOGGLE, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_MENU, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_CLICK_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_KEYUP_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n  Dropdown.getOrCreateInstance(this).toggle()\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Dropdown)\n\nexport default Dropdown\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap util/backdrop.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler.js'\nimport Config from './config.js'\nimport {\n  execute, executeAfterTransition, getElement, reflow\n} from './index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'backdrop'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst EVENT_MOUSEDOWN = `mousedown.bs.${NAME}`\n\nconst Default = {\n  className: 'modal-backdrop',\n  clickCallback: null,\n  isAnimated: false,\n  isVisible: true, // if false, we use the backdrop helper without adding any element to the dom\n  rootElement: 'body' // give the choice to place backdrop under different elements\n}\n\nconst DefaultType = {\n  className: 'string',\n  clickCallback: '(function|null)',\n  isAnimated: 'boolean',\n  isVisible: 'boolean',\n  rootElement: '(element|string)'\n}\n\n/**\n * Class definition\n */\n\nclass Backdrop extends Config {\n  constructor(config) {\n    super()\n    this._config = this._getConfig(config)\n    this._isAppended = false\n    this._element = null\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  show(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._append()\n\n    const element = this._getElement()\n    if (this._config.isAnimated) {\n      reflow(element)\n    }\n\n    element.classList.add(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      execute(callback)\n    })\n  }\n\n  hide(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._getElement().classList.remove(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      this.dispose()\n      execute(callback)\n    })\n  }\n\n  dispose() {\n    if (!this._isAppended) {\n      return\n    }\n\n    EventHandler.off(this._element, EVENT_MOUSEDOWN)\n\n    this._element.remove()\n    this._isAppended = false\n  }\n\n  // Private\n  _getElement() {\n    if (!this._element) {\n      const backdrop = document.createElement('div')\n      backdrop.className = this._config.className\n      if (this._config.isAnimated) {\n        backdrop.classList.add(CLASS_NAME_FADE)\n      }\n\n      this._element = backdrop\n    }\n\n    return this._element\n  }\n\n  _configAfterMerge(config) {\n    // use getElement() with the default \"body\" to get a fresh Element on each instantiation\n    config.rootElement = getElement(config.rootElement)\n    return config\n  }\n\n  _append() {\n    if (this._isAppended) {\n      return\n    }\n\n    const element = this._getElement()\n    this._config.rootElement.append(element)\n\n    EventHandler.on(element, EVENT_MOUSEDOWN, () => {\n      execute(this._config.clickCallback)\n    })\n\n    this._isAppended = true\n  }\n\n  _emulateAnimation(callback) {\n    executeAfterTransition(callback, this._getElement(), this._config.isAnimated)\n  }\n}\n\nexport default Backdrop\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap util/focustrap.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler.js'\nimport SelectorEngine from '../dom/selector-engine.js'\nimport Config from './config.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'focustrap'\nconst DATA_KEY = 'bs.focustrap'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_KEYDOWN_TAB = `keydown.tab${EVENT_KEY}`\n\nconst TAB_KEY = 'Tab'\nconst TAB_NAV_FORWARD = 'forward'\nconst TAB_NAV_BACKWARD = 'backward'\n\nconst Default = {\n  autofocus: true,\n  trapElement: null // The element to trap focus inside of\n}\n\nconst DefaultType = {\n  autofocus: 'boolean',\n  trapElement: 'element'\n}\n\n/**\n * Class definition\n */\n\nclass FocusTrap extends Config {\n  constructor(config) {\n    super()\n    this._config = this._getConfig(config)\n    this._isActive = false\n    this._lastTabNavDirection = null\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  activate() {\n    if (this._isActive) {\n      return\n    }\n\n    if (this._config.autofocus) {\n      this._config.trapElement.focus()\n    }\n\n    EventHandler.off(document, EVENT_KEY) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => this._handleFocusin(event))\n    EventHandler.on(document, EVENT_KEYDOWN_TAB, event => this._handleKeydown(event))\n\n    this._isActive = true\n  }\n\n  deactivate() {\n    if (!this._isActive) {\n      return\n    }\n\n    this._isActive = false\n    EventHandler.off(document, EVENT_KEY)\n  }\n\n  // Private\n  _handleFocusin(event) {\n    const { trapElement } = this._config\n\n    if (event.target === document || event.target === trapElement || trapElement.contains(event.target)) {\n      return\n    }\n\n    const elements = SelectorEngine.focusableChildren(trapElement)\n\n    if (elements.length === 0) {\n      trapElement.focus()\n    } else if (this._lastTabNavDirection === TAB_NAV_BACKWARD) {\n      elements[elements.length - 1].focus()\n    } else {\n      elements[0].focus()\n    }\n  }\n\n  _handleKeydown(event) {\n    if (event.key !== TAB_KEY) {\n      return\n    }\n\n    this._lastTabNavDirection = event.shiftKey ? TAB_NAV_BACKWARD : TAB_NAV_FORWARD\n  }\n}\n\nexport default FocusTrap\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap util/scrollBar.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Manipulator from '../dom/manipulator.js'\nimport SelectorEngine from '../dom/selector-engine.js'\nimport { isElement } from './index.js'\n\n/**\n * Constants\n */\n\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\nconst PROPERTY_PADDING = 'padding-right'\nconst PROPERTY_MARGIN = 'margin-right'\n\n/**\n * Class definition\n */\n\nclass ScrollBarHelper {\n  constructor() {\n    this._element = document.body\n  }\n\n  // Public\n  getWidth() {\n    // https://developer.mozilla.org/en-US/docs/Web/API/Window/innerWidth#usage_notes\n    const documentWidth = document.documentElement.clientWidth\n    return Math.abs(window.innerWidth - documentWidth)\n  }\n\n  hide() {\n    const width = this.getWidth()\n    this._disableOverFlow()\n    // give padding to element to balance the hidden scrollbar width\n    this._setElementAttributes(this._element, PROPERTY_PADDING, calculatedValue => calculatedValue + width)\n    // trick: We adjust positive paddingRight and negative marginRight to sticky-top elements to keep showing fullwidth\n    this._setElementAttributes(SELECTOR_FIXED_CONTENT, PROPERTY_PADDING, calculatedValue => calculatedValue + width)\n    this._setElementAttributes(SELECTOR_STICKY_CONTENT, PROPERTY_MARGIN, calculatedValue => calculatedValue - width)\n  }\n\n  reset() {\n    this._resetElementAttributes(this._element, 'overflow')\n    this._resetElementAttributes(this._element, PROPERTY_PADDING)\n    this._resetElementAttributes(SELECTOR_FIXED_CONTENT, PROPERTY_PADDING)\n    this._resetElementAttributes(SELECTOR_STICKY_CONTENT, PROPERTY_MARGIN)\n  }\n\n  isOverflowing() {\n    return this.getWidth() > 0\n  }\n\n  // Private\n  _disableOverFlow() {\n    this._saveInitialAttribute(this._element, 'overflow')\n    this._element.style.overflow = 'hidden'\n  }\n\n  _setElementAttributes(selector, styleProperty, callback) {\n    const scrollbarWidth = this.getWidth()\n    const manipulationCallBack = element => {\n      if (element !== this._element && window.innerWidth > element.clientWidth + scrollbarWidth) {\n        return\n      }\n\n      this._saveInitialAttribute(element, styleProperty)\n      const calculatedValue = window.getComputedStyle(element).getPropertyValue(styleProperty)\n      element.style.setProperty(styleProperty, `${callback(Number.parseFloat(calculatedValue))}px`)\n    }\n\n    this._applyManipulationCallback(selector, manipulationCallBack)\n  }\n\n  _saveInitialAttribute(element, styleProperty) {\n    const actualValue = element.style.getPropertyValue(styleProperty)\n    if (actualValue) {\n      Manipulator.setDataAttribute(element, styleProperty, actualValue)\n    }\n  }\n\n  _resetElementAttributes(selector, styleProperty) {\n    const manipulationCallBack = element => {\n      const value = Manipulator.getDataAttribute(element, styleProperty)\n      // We only want to remove the property if the value is `null`; the value can also be zero\n      if (value === null) {\n        element.style.removeProperty(styleProperty)\n        return\n      }\n\n      Manipulator.removeDataAttribute(element, styleProperty)\n      element.style.setProperty(styleProperty, value)\n    }\n\n    this._applyManipulationCallback(selector, manipulationCallBack)\n  }\n\n  _applyManipulationCallback(selector, callBack) {\n    if (isElement(selector)) {\n      callBack(selector)\n      return\n    }\n\n    for (const sel of SelectorEngine.find(selector, this._element)) {\n      callBack(sel)\n    }\n  }\n}\n\nexport default ScrollBarHelper\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport Backdrop from './util/backdrop.js'\nimport { enableDismissTrigger } from './util/component-functions.js'\nimport FocusTrap from './util/focustrap.js'\nimport {\n  defineJQueryPlugin, isRTL, isVisible, reflow\n} from './util/index.js'\nimport ScrollBarHelper from './util/scrollbar.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'modal'\nconst DATA_KEY = 'bs.modal'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst ESCAPE_KEY = 'Escape'\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEDOWN_DISMISS = `mousedown.dismiss${EVENT_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_OPEN = 'modal-open'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_STATIC = 'modal-static'\n\nconst OPEN_SELECTOR = '.modal.show'\nconst SELECTOR_DIALOG = '.modal-dialog'\nconst SELECTOR_MODAL_BODY = '.modal-body'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"modal\"]'\n\nconst Default = {\n  backdrop: true,\n  focus: true,\n  keyboard: true\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  focus: 'boolean',\n  keyboard: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Modal extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._dialog = SelectorEngine.findOne(SELECTOR_DIALOG, this._element)\n    this._backdrop = this._initializeBackDrop()\n    this._focustrap = this._initializeFocusTrap()\n    this._isShown = false\n    this._isTransitioning = false\n    this._scrollBar = new ScrollBarHelper()\n\n    this._addEventListeners()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget\n    })\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n    this._isTransitioning = true\n\n    this._scrollBar.hide()\n\n    document.body.classList.add(CLASS_NAME_OPEN)\n\n    this._adjustDialog()\n\n    this._backdrop.show(() => this._showElement(relatedTarget))\n  }\n\n  hide() {\n    if (!this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = false\n    this._isTransitioning = true\n    this._focustrap.deactivate()\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    this._queueCallback(() => this._hideModal(), this._element, this._isAnimated())\n  }\n\n  dispose() {\n    EventHandler.off(window, EVENT_KEY)\n    EventHandler.off(this._dialog, EVENT_KEY)\n\n    this._backdrop.dispose()\n    this._focustrap.deactivate()\n\n    super.dispose()\n  }\n\n  handleUpdate() {\n    this._adjustDialog()\n  }\n\n  // Private\n  _initializeBackDrop() {\n    return new Backdrop({\n      isVisible: Boolean(this._config.backdrop), // 'static' option will be translated to true, and booleans will keep their value,\n      isAnimated: this._isAnimated()\n    })\n  }\n\n  _initializeFocusTrap() {\n    return new FocusTrap({\n      trapElement: this._element\n    })\n  }\n\n  _showElement(relatedTarget) {\n    // try to append dynamic modal\n    if (!document.body.contains(this._element)) {\n      document.body.append(this._element)\n    }\n\n    this._element.style.display = 'block'\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.scrollTop = 0\n\n    const modalBody = SelectorEngine.findOne(SELECTOR_MODAL_BODY, this._dialog)\n    if (modalBody) {\n      modalBody.scrollTop = 0\n    }\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    const transitionComplete = () => {\n      if (this._config.focus) {\n        this._focustrap.activate()\n      }\n\n      this._isTransitioning = false\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget\n      })\n    }\n\n    this._queueCallback(transitionComplete, this._dialog, this._isAnimated())\n  }\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n      if (event.key !== ESCAPE_KEY) {\n        return\n      }\n\n      if (this._config.keyboard) {\n        this.hide()\n        return\n      }\n\n      this._triggerBackdropTransition()\n    })\n\n    EventHandler.on(window, EVENT_RESIZE, () => {\n      if (this._isShown && !this._isTransitioning) {\n        this._adjustDialog()\n      }\n    })\n\n    EventHandler.on(this._element, EVENT_MOUSEDOWN_DISMISS, event => {\n      // a bad trick to segregate clicks that may start inside dialog but end outside, and avoid listen to scrollbar clicks\n      EventHandler.one(this._element, EVENT_CLICK_DISMISS, event2 => {\n        if (this._element !== event.target || this._element !== event2.target) {\n          return\n        }\n\n        if (this._config.backdrop === 'static') {\n          this._triggerBackdropTransition()\n          return\n        }\n\n        if (this._config.backdrop) {\n          this.hide()\n        }\n      })\n    })\n  }\n\n  _hideModal() {\n    this._element.style.display = 'none'\n    this._element.setAttribute('aria-hidden', true)\n    this._element.removeAttribute('aria-modal')\n    this._element.removeAttribute('role')\n    this._isTransitioning = false\n\n    this._backdrop.hide(() => {\n      document.body.classList.remove(CLASS_NAME_OPEN)\n      this._resetAdjustments()\n      this._scrollBar.reset()\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    })\n  }\n\n  _isAnimated() {\n    return this._element.classList.contains(CLASS_NAME_FADE)\n  }\n\n  _triggerBackdropTransition() {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n    const initialOverflowY = this._element.style.overflowY\n    // return if the following background transition hasn't yet completed\n    if (initialOverflowY === 'hidden' || this._element.classList.contains(CLASS_NAME_STATIC)) {\n      return\n    }\n\n    if (!isModalOverflowing) {\n      this._element.style.overflowY = 'hidden'\n    }\n\n    this._element.classList.add(CLASS_NAME_STATIC)\n    this._queueCallback(() => {\n      this._element.classList.remove(CLASS_NAME_STATIC)\n      this._queueCallback(() => {\n        this._element.style.overflowY = initialOverflowY\n      }, this._dialog)\n    }, this._dialog)\n\n    this._element.focus()\n  }\n\n  /**\n   * The following methods are used to handle overflowing modals\n   */\n\n  _adjustDialog() {\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n    const scrollbarWidth = this._scrollBar.getWidth()\n    const isBodyOverflowing = scrollbarWidth > 0\n\n    if (isBodyOverflowing && !isModalOverflowing) {\n      const property = isRTL() ? 'paddingLeft' : 'paddingRight'\n      this._element.style[property] = `${scrollbarWidth}px`\n    }\n\n    if (!isBodyOverflowing && isModalOverflowing) {\n      const property = isRTL() ? 'paddingRight' : 'paddingLeft'\n      this._element.style[property] = `${scrollbarWidth}px`\n    }\n  }\n\n  _resetAdjustments() {\n    this._element.style.paddingLeft = ''\n    this._element.style.paddingRight = ''\n  }\n\n  // Static\n  static jQueryInterface(config, relatedTarget) {\n    return this.each(function () {\n      const data = Modal.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](relatedTarget)\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = SelectorEngine.getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  EventHandler.one(target, EVENT_SHOW, showEvent => {\n    if (showEvent.defaultPrevented) {\n      // only register focus restorer if modal will actually get shown\n      return\n    }\n\n    EventHandler.one(target, EVENT_HIDDEN, () => {\n      if (isVisible(this)) {\n        this.focus()\n      }\n    })\n  })\n\n  // avoid conflict when clicking modal toggler while another one is open\n  const alreadyOpen = SelectorEngine.findOne(OPEN_SELECTOR)\n  if (alreadyOpen) {\n    Modal.getInstance(alreadyOpen).hide()\n  }\n\n  const data = Modal.getOrCreateInstance(target)\n\n  data.toggle(this)\n})\n\nenableDismissTrigger(Modal)\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Modal)\n\nexport default Modal\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap offcanvas.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport Backdrop from './util/backdrop.js'\nimport { enableDismissTrigger } from './util/component-functions.js'\nimport FocusTrap from './util/focustrap.js'\nimport {\n  defineJQueryPlugin,\n  isDisabled,\n  isVisible\n} from './util/index.js'\nimport ScrollBarHelper from './util/scrollbar.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'offcanvas'\nconst DATA_KEY = 'bs.offcanvas'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst ESCAPE_KEY = 'Escape'\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\nconst CLASS_NAME_HIDING = 'hiding'\nconst CLASS_NAME_BACKDROP = 'offcanvas-backdrop'\nconst OPEN_SELECTOR = '.offcanvas.show'\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"offcanvas\"]'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  scroll: false\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  keyboard: 'boolean',\n  scroll: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Offcanvas extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._isShown = false\n    this._backdrop = this._initializeBackDrop()\n    this._focustrap = this._initializeFocusTrap()\n    this._addEventListeners()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, { relatedTarget })\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n    this._backdrop.show()\n\n    if (!this._config.scroll) {\n      new ScrollBarHelper().hide()\n    }\n\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.classList.add(CLASS_NAME_SHOWING)\n\n    const completeCallBack = () => {\n      if (!this._config.scroll || this._config.backdrop) {\n        this._focustrap.activate()\n      }\n\n      this._element.classList.add(CLASS_NAME_SHOW)\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      EventHandler.trigger(this._element, EVENT_SHOWN, { relatedTarget })\n    }\n\n    this._queueCallback(completeCallBack, this._element, true)\n  }\n\n  hide() {\n    if (!this._isShown) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._focustrap.deactivate()\n    this._element.blur()\n    this._isShown = false\n    this._element.classList.add(CLASS_NAME_HIDING)\n    this._backdrop.hide()\n\n    const completeCallback = () => {\n      this._element.classList.remove(CLASS_NAME_SHOW, CLASS_NAME_HIDING)\n      this._element.removeAttribute('aria-modal')\n      this._element.removeAttribute('role')\n\n      if (!this._config.scroll) {\n        new ScrollBarHelper().reset()\n      }\n\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._queueCallback(completeCallback, this._element, true)\n  }\n\n  dispose() {\n    this._backdrop.dispose()\n    this._focustrap.deactivate()\n    super.dispose()\n  }\n\n  // Private\n  _initializeBackDrop() {\n    const clickCallback = () => {\n      if (this._config.backdrop === 'static') {\n        EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n        return\n      }\n\n      this.hide()\n    }\n\n    // 'static' option will be translated to true, and booleans will keep their value\n    const isVisible = Boolean(this._config.backdrop)\n\n    return new Backdrop({\n      className: CLASS_NAME_BACKDROP,\n      isVisible,\n      isAnimated: true,\n      rootElement: this._element.parentNode,\n      clickCallback: isVisible ? clickCallback : null\n    })\n  }\n\n  _initializeFocusTrap() {\n    return new FocusTrap({\n      trapElement: this._element\n    })\n  }\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n      if (event.key !== ESCAPE_KEY) {\n        return\n      }\n\n      if (this._config.keyboard) {\n        this.hide()\n        return\n      }\n\n      EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n    })\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Offcanvas.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](this)\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = SelectorEngine.getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  if (isDisabled(this)) {\n    return\n  }\n\n  EventHandler.one(target, EVENT_HIDDEN, () => {\n    // focus on trigger when it is closed\n    if (isVisible(this)) {\n      this.focus()\n    }\n  })\n\n  // avoid conflict when clicking a toggler of an offcanvas, while another is open\n  const alreadyOpen = SelectorEngine.findOne(OPEN_SELECTOR)\n  if (alreadyOpen && alreadyOpen !== target) {\n    Offcanvas.getInstance(alreadyOpen).hide()\n  }\n\n  const data = Offcanvas.getOrCreateInstance(target)\n  data.toggle(this)\n})\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  for (const selector of SelectorEngine.find(OPEN_SELECTOR)) {\n    Offcanvas.getOrCreateInstance(selector).show()\n  }\n})\n\nEventHandler.on(window, EVENT_RESIZE, () => {\n  for (const element of SelectorEngine.find('[aria-modal][class*=show][class*=offcanvas-]')) {\n    if (getComputedStyle(element).position !== 'fixed') {\n      Offcanvas.getOrCreateInstance(element).hide()\n    }\n  }\n})\n\nenableDismissTrigger(Offcanvas)\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Offcanvas)\n\nexport default Offcanvas\n", "/**\n * --------------------------------------------------------------------------\n * Boosted orange-navbar.js\n * Licensed under MIT (https://github.com/Orange-OpenSource/Orange-Boosted-Bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport { defineJQueryPlugin } from './util/index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'orangenavbar'\nconst DATA_KEY = 'bs.orangenavbar'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst EVENT_SCROLL_DATA_API = `scroll${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst SELECTOR_STICKY_TOP = 'header.sticky-top'\n\n/**\n * Class definition\n */\n\nclass OrangeNavbar extends BaseComponent {\n  // Getters\n  static get NAME() {\n    return NAME\n  }\n\n  // Static\n  static enableMinimizing(el) {\n    // The minimized behavior works only if your header has .sticky-top (fixed-top will be sticky without minimizing)\n    if (window.scrollY > 0) {\n      el.classList.add('header-minimized')\n    } else {\n      el.classList.remove('header-minimized')\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = OrangeNavbar.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(window, EVENT_SCROLL_DATA_API, () => {\n  for (const el of SelectorEngine.find(SELECTOR_STICKY_TOP)) {\n    OrangeNavbar.enableMinimizing(el)\n  }\n})\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  for (const el of SelectorEngine.find(SELECTOR_STICKY_TOP)) {\n    OrangeNavbar.enableMinimizing(el)\n  }\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(OrangeNavbar)\n\nexport default OrangeNavbar\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap util/sanitizer.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n// js-docs-start allow-list\nconst ARIA_ATTRIBUTE_PATTERN = /^aria-[\\w-]*$/i\n\nexport const DefaultAllowlist = {\n  // Global attributes allowed on any supplied element below.\n  '*': ['class', 'dir', 'id', 'lang', 'role', ARIA_ATTRIBUTE_PATTERN],\n  a: ['target', 'href', 'title', 'rel'],\n  area: [],\n  b: [],\n  br: [],\n  col: [],\n  code: [],\n  dd: [],\n  div: [],\n  dl: [],\n  dt: [],\n  em: [],\n  hr: [],\n  h1: [],\n  h2: [],\n  h3: [],\n  h4: [],\n  h5: [],\n  h6: [],\n  i: [],\n  img: ['src', 'srcset', 'alt', 'title', 'width', 'height'],\n  li: [],\n  ol: [],\n  p: [],\n  pre: [],\n  s: [],\n  small: [],\n  span: [],\n  sub: [],\n  sup: [],\n  strong: [],\n  u: [],\n  ul: []\n}\n// js-docs-end allow-list\n\nconst uriAttributes = new Set([\n  'background',\n  'cite',\n  'href',\n  'itemtype',\n  'longdesc',\n  'poster',\n  'src',\n  'xlink:href'\n])\n\n/**\n * A pattern that recognizes URLs that are safe wrt. XSS in URL navigation\n * contexts.\n *\n * Shout-out to Angular https://github.com/angular/angular/blob/15.2.8/packages/core/src/sanitization/url_sanitizer.ts#L38\n */\nconst SAFE_URL_PATTERN = /^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i\n\nconst allowedAttribute = (attribute, allowedAttributeList) => {\n  const attributeName = attribute.nodeName.toLowerCase()\n\n  if (allowedAttributeList.includes(attributeName)) {\n    if (uriAttributes.has(attributeName)) {\n      return Boolean(SAFE_URL_PATTERN.test(attribute.nodeValue))\n    }\n\n    return true\n  }\n\n  // Check if a regular expression validates the attribute.\n  return allowedAttributeList.filter(attributeRegex => attributeRegex instanceof RegExp)\n    .some(regex => regex.test(attributeName))\n}\n\nexport function sanitizeHtml(unsafeHtml, allowList, sanitizeFunction) {\n  if (!unsafeHtml.length) {\n    return unsafeHtml\n  }\n\n  if (sanitizeFunction && typeof sanitizeFunction === 'function') {\n    return sanitizeFunction(unsafeHtml)\n  }\n\n  const domParser = new window.DOMParser()\n  const createdDocument = domParser.parseFromString(unsafeHtml, 'text/html')\n  const elements = [].concat(...createdDocument.body.querySelectorAll('*'))\n\n  for (const element of elements) {\n    const elementName = element.nodeName.toLowerCase()\n\n    if (!Object.keys(allowList).includes(elementName)) {\n      element.remove()\n      continue\n    }\n\n    const attributeList = [].concat(...element.attributes)\n    const allowedAttributes = [].concat(allowList['*'] || [], allowList[elementName] || [])\n\n    for (const attribute of attributeList) {\n      if (!allowedAttribute(attribute, allowedAttributes)) {\n        element.removeAttribute(attribute.nodeName)\n      }\n    }\n  }\n\n  return createdDocument.body.innerHTML\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap util/template-factory.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport SelectorEngine from '../dom/selector-engine.js'\nimport Config from './config.js'\nimport { DefaultAllowlist, sanitizeHtml } from './sanitizer.js'\nimport { execute, getElement, isElement } from './index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'TemplateFactory'\n\nconst Default = {\n  allowList: DefaultAllowlist,\n  content: {}, // { selector : text ,  selector2 : text2 , }\n  extraClass: '',\n  html: false,\n  sanitize: true,\n  sanitizeFn: null,\n  template: '<div></div>'\n}\n\nconst DefaultType = {\n  allowList: 'object',\n  content: 'object',\n  extraClass: '(string|function)',\n  html: 'boolean',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  template: 'string'\n}\n\nconst DefaultContentType = {\n  selector: '(string|element)',\n  entry: '(string|element|function|null)'\n}\n\n/**\n * Class definition\n */\n\nclass TemplateFactory extends Config {\n  constructor(config) {\n    super()\n    this._config = this._getConfig(config)\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  getContent() {\n    return Object.values(this._config.content)\n      .map(config => this._resolvePossibleFunction(config))\n      .filter(Boolean)\n  }\n\n  hasContent() {\n    return this.getContent().length > 0\n  }\n\n  changeContent(content) {\n    this._checkContent(content)\n    this._config.content = { ...this._config.content, ...content }\n    return this\n  }\n\n  toHtml() {\n    const templateWrapper = document.createElement('div')\n    templateWrapper.innerHTML = this._maybeSanitize(this._config.template)\n\n    for (const [selector, text] of Object.entries(this._config.content)) {\n      this._setContent(templateWrapper, text, selector)\n    }\n\n    const template = templateWrapper.children[0]\n    const extraClass = this._resolvePossibleFunction(this._config.extraClass)\n\n    if (extraClass) {\n      template.classList.add(...extraClass.split(' '))\n    }\n\n    return template\n  }\n\n  // Private\n  _typeCheckConfig(config) {\n    super._typeCheckConfig(config)\n    this._checkContent(config.content)\n  }\n\n  _checkContent(arg) {\n    for (const [selector, content] of Object.entries(arg)) {\n      super._typeCheckConfig({ selector, entry: content }, DefaultContentType)\n    }\n  }\n\n  _setContent(template, content, selector) {\n    const templateElement = SelectorEngine.findOne(selector, template)\n\n    if (!templateElement) {\n      return\n    }\n\n    content = this._resolvePossibleFunction(content)\n\n    if (!content) {\n      templateElement.remove()\n      return\n    }\n\n    if (isElement(content)) {\n      this._putElementInTemplate(getElement(content), templateElement)\n      return\n    }\n\n    if (this._config.html) {\n      templateElement.innerHTML = this._maybeSanitize(content)\n      return\n    }\n\n    templateElement.textContent = content\n  }\n\n  _maybeSanitize(arg) {\n    return this._config.sanitize ? sanitizeHtml(arg, this._config.allowList, this._config.sanitizeFn) : arg\n  }\n\n  _resolvePossibleFunction(arg) {\n    return execute(arg, [undefined, this])\n  }\n\n  _putElementInTemplate(element, templateElement) {\n    if (this._config.html) {\n      templateElement.innerHTML = ''\n      templateElement.append(element)\n      return\n    }\n\n    templateElement.textContent = element.textContent\n  }\n}\n\nexport default TemplateFactory\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport Manipulator from './dom/manipulator.js'\nimport {\n  defineJQueryPlugin, execute, findShadowRoot, getElement, getUID, isRTL, noop\n} from './util/index.js'\nimport { DefaultAllowlist } from './util/sanitizer.js'\nimport TemplateFactory from './util/template-factory.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'tooltip'\nconst DISALLOWED_ATTRIBUTES = new Set(['sanitize', 'allowList', 'sanitizeFn'])\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_MODAL = 'modal'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_TOOLTIP_INNER = '.tooltip-inner'\nconst SELECTOR_MODAL = `.${CLASS_NAME_MODAL}`\n\nconst EVENT_MODAL_HIDE = 'hide.bs.modal'\n\nconst TRIGGER_HOVER = 'hover'\nconst TRIGGER_FOCUS = 'focus'\nconst TRIGGER_CLICK = 'click'\nconst TRIGGER_MANUAL = 'manual'\n\nconst EVENT_HIDE = 'hide'\nconst EVENT_HIDDEN = 'hidden'\nconst EVENT_SHOW = 'show'\nconst EVENT_SHOWN = 'shown'\nconst EVENT_INSERTED = 'inserted'\nconst EVENT_CLICK = 'click'\nconst EVENT_FOCUSIN = 'focusin'\nconst EVENT_FOCUSOUT = 'focusout'\nconst EVENT_MOUSEENTER = 'mouseenter'\nconst EVENT_MOUSELEAVE = 'mouseleave'\n\nconst AttachmentMap = {\n  AUTO: 'auto',\n  TOP: 'top',\n  RIGHT: isRTL() ? 'left' : 'right',\n  BOTTOM: 'bottom',\n  LEFT: isRTL() ? 'right' : 'left'\n}\n\nconst Default = {\n  allowList: DefaultAllowlist,\n  animation: true,\n  boundary: 'clippingParents',\n  container: false,\n  customClass: '',\n  delay: 0,\n  fallbackPlacements: ['top', 'right', 'bottom', 'left'],\n  html: false,\n  offset: [0, 10], // Boosted mod: instead of `offset: [0, 6],`\n  placement: 'top',\n  popperConfig: null,\n  sanitize: true,\n  sanitizeFn: null,\n  selector: false,\n  template: '<div class=\"tooltip\" role=\"tooltip\">' +\n            '<div class=\"tooltip-arrow\"></div>' +\n            '<div class=\"tooltip-inner\"></div>' +\n            '</div>',\n  title: '',\n  trigger: 'hover focus'\n}\n\nconst DefaultType = {\n  allowList: 'object',\n  animation: 'boolean',\n  boundary: '(string|element)',\n  container: '(string|element|boolean)',\n  customClass: '(string|function)',\n  delay: '(number|object)',\n  fallbackPlacements: 'array',\n  html: 'boolean',\n  offset: '(array|string|function)',\n  placement: '(string|function)',\n  popperConfig: '(null|object|function)',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  selector: '(string|boolean)',\n  template: 'string',\n  title: '(string|element|function)',\n  trigger: 'string'\n}\n\n/**\n * Class definition\n */\n\nclass Tooltip extends BaseComponent {\n  constructor(element, config) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s tooltips require Popper (https://popper.js.org/docs/v2/)')\n    }\n\n    super(element, config)\n\n    // Private\n    this._isEnabled = true\n    this._timeout = 0\n    this._isHovered = null\n    this._activeTrigger = {}\n    this._popper = null\n    this._templateFactory = null\n    this._newContent = null\n\n    // Protected\n    this.tip = null\n\n    this._setListeners()\n\n    if (!this._config.selector) {\n      this._fixTitle()\n    }\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  enable() {\n    this._isEnabled = true\n  }\n\n  disable() {\n    this._isEnabled = false\n  }\n\n  toggleEnabled() {\n    this._isEnabled = !this._isEnabled\n  }\n\n  toggle() {\n    if (!this._isEnabled) {\n      return\n    }\n\n    if (this._isShown()) {\n      this._leave()\n      return\n    }\n\n    this._enter()\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n\n    EventHandler.off(this._element.closest(SELECTOR_MODAL), EVENT_MODAL_HIDE, this._hideModalHandler)\n\n    if (this._element.getAttribute('data-bs-original-title')) {\n      this._element.setAttribute('title', this._element.getAttribute('data-bs-original-title'))\n    }\n\n    this._disposePopper()\n    super.dispose()\n  }\n\n  show() {\n    if (this._element.style.display === 'none') {\n      throw new Error('Please use show on visible elements')\n    }\n\n    if (!(this._isWithContent() && this._isEnabled)) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, this.constructor.eventName(EVENT_SHOW))\n    const shadowRoot = findShadowRoot(this._element)\n    const isInTheDom = (shadowRoot || this._element.ownerDocument.documentElement).contains(this._element)\n\n    if (showEvent.defaultPrevented || !isInTheDom) {\n      return\n    }\n\n    // TODO: v6 remove this or make it optional\n    this._disposePopper()\n\n    const tip = this._getTipElement()\n\n    this._element.setAttribute('aria-describedby', tip.getAttribute('id'))\n\n    const { container } = this._config\n\n    if (!this._element.ownerDocument.documentElement.contains(this.tip)) {\n      container.append(tip)\n      EventHandler.trigger(this._element, this.constructor.eventName(EVENT_INSERTED))\n    }\n\n    this._popper = this._createPopper(tip)\n\n    tip.classList.add(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.on(element, 'mouseover', noop)\n      }\n    }\n\n    const complete = () => {\n      EventHandler.trigger(this._element, this.constructor.eventName(EVENT_SHOWN))\n\n      if (this._isHovered === false) {\n        this._leave()\n      }\n\n      this._isHovered = false\n    }\n\n    this._queueCallback(complete, this.tip, this._isAnimated())\n  }\n\n  hide() {\n    if (!this._isShown()) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, this.constructor.eventName(EVENT_HIDE))\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const tip = this._getTipElement()\n    tip.classList.remove(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.off(element, 'mouseover', noop)\n      }\n    }\n\n    this._activeTrigger[TRIGGER_CLICK] = false\n    this._activeTrigger[TRIGGER_FOCUS] = false\n    this._activeTrigger[TRIGGER_HOVER] = false\n    this._isHovered = null // it is a trick to support manual triggering\n\n    const complete = () => {\n      if (this._isWithActiveTrigger()) {\n        return\n      }\n\n      if (!this._isHovered) {\n        this._disposePopper()\n      }\n\n      this._element.removeAttribute('aria-describedby')\n      EventHandler.trigger(this._element, this.constructor.eventName(EVENT_HIDDEN))\n    }\n\n    this._queueCallback(complete, this.tip, this._isAnimated())\n  }\n\n  update() {\n    if (this._popper) {\n      this._popper.update()\n    }\n  }\n\n  // Protected\n  _isWithContent() {\n    return Boolean(this._getTitle())\n  }\n\n  _getTipElement() {\n    if (!this.tip) {\n      this.tip = this._createTipElement(this._newContent || this._getContentForTemplate())\n    }\n\n    return this.tip\n  }\n\n  _createTipElement(content) {\n    const tip = this._getTemplateFactory(content).toHtml()\n\n    // TODO: remove this check in v6\n    if (!tip) {\n      return null\n    }\n\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n    // TODO: v6 the following can be achieved with CSS only\n    tip.classList.add(`bs-${this.constructor.NAME}-auto`)\n\n    const tipId = getUID(this.constructor.NAME).toString()\n\n    tip.setAttribute('id', tipId)\n\n    if (this._isAnimated()) {\n      tip.classList.add(CLASS_NAME_FADE)\n    }\n\n    return tip\n  }\n\n  setContent(content) {\n    this._newContent = content\n    if (this._isShown()) {\n      this._disposePopper()\n      this.show()\n    }\n  }\n\n  _getTemplateFactory(content) {\n    if (this._templateFactory) {\n      this._templateFactory.changeContent(content)\n    } else {\n      this._templateFactory = new TemplateFactory({\n        ...this._config,\n        // the `content` var has to be after `this._config`\n        // to override config.content in case of popover\n        content,\n        extraClass: this._resolvePossibleFunction(this._config.customClass)\n      })\n    }\n\n    return this._templateFactory\n  }\n\n  _getContentForTemplate() {\n    return {\n      [SELECTOR_TOOLTIP_INNER]: this._getTitle()\n    }\n  }\n\n  _getTitle() {\n    return this._resolvePossibleFunction(this._config.title) || this._element.getAttribute('data-bs-original-title')\n  }\n\n  // Private\n  _initializeOnDelegatedTarget(event) {\n    return this.constructor.getOrCreateInstance(event.delegateTarget, this._getDelegateConfig())\n  }\n\n  _isAnimated() {\n    return this._config.animation || (this.tip && this.tip.classList.contains(CLASS_NAME_FADE))\n  }\n\n  _isShown() {\n    return this.tip && this.tip.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  _createPopper(tip) {\n    const placement = execute(this._config.placement, [this, tip, this._element])\n    const attachment = AttachmentMap[placement.toUpperCase()]\n    return Popper.createPopper(this._element, tip, this._getPopperConfig(attachment))\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(value => Number.parseInt(value, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _resolvePossibleFunction(arg) {\n    return execute(arg, [this._element, this._element])\n  }\n\n  _getPopperConfig(attachment) {\n    const defaultBsPopperConfig = {\n      placement: attachment,\n      modifiers: [\n        {\n          name: 'flip',\n          options: {\n            fallbackPlacements: this._config.fallbackPlacements\n          }\n        },\n        {\n          name: 'offset',\n          options: {\n            offset: this._getOffset()\n          }\n        },\n        {\n          name: 'preventOverflow',\n          options: {\n            boundary: this._config.boundary\n          }\n        },\n        {\n          name: 'arrow',\n          options: {\n            element: `.${this.constructor.NAME}-arrow`\n          }\n        },\n        {\n          name: 'preSetPlacement',\n          enabled: true,\n          phase: 'beforeMain',\n          fn: data => {\n            // Pre-set Popper's placement attribute in order to read the arrow sizes properly.\n            // Otherwise, Popper mixes up the width and height dimensions since the initial arrow style is for top placement\n            this._getTipElement().setAttribute('data-popper-placement', data.state.placement)\n          }\n        }\n      ]\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...execute(this._config.popperConfig, [undefined, defaultBsPopperConfig])\n    }\n  }\n\n  _setListeners() {\n    const triggers = this._config.trigger.split(' ')\n\n    for (const trigger of triggers) {\n      if (trigger === 'click') {\n        EventHandler.on(this._element, this.constructor.eventName(EVENT_CLICK), this._config.selector, event => {\n          const context = this._initializeOnDelegatedTarget(event)\n          context._activeTrigger[TRIGGER_CLICK] = !(context._isShown() && context._activeTrigger[TRIGGER_CLICK])\n          context.toggle()\n        })\n      } else if (trigger !== TRIGGER_MANUAL) {\n        const eventIn = trigger === TRIGGER_HOVER ?\n          this.constructor.eventName(EVENT_MOUSEENTER) :\n          this.constructor.eventName(EVENT_FOCUSIN)\n        const eventOut = trigger === TRIGGER_HOVER ?\n          this.constructor.eventName(EVENT_MOUSELEAVE) :\n          this.constructor.eventName(EVENT_FOCUSOUT)\n\n        EventHandler.on(this._element, eventIn, this._config.selector, event => {\n          const context = this._initializeOnDelegatedTarget(event)\n          context._activeTrigger[event.type === 'focusin' ? TRIGGER_FOCUS : TRIGGER_HOVER] = true\n          context._enter()\n        })\n        EventHandler.on(this._element, eventOut, this._config.selector, event => {\n          const context = this._initializeOnDelegatedTarget(event)\n          context._activeTrigger[event.type === 'focusout' ? TRIGGER_FOCUS : TRIGGER_HOVER] =\n            context._element.contains(event.relatedTarget)\n\n          context._leave()\n        })\n      }\n    }\n\n    this._hideModalHandler = () => {\n      if (this._element) {\n        this.hide()\n      }\n    }\n\n    EventHandler.on(this._element.closest(SELECTOR_MODAL), EVENT_MODAL_HIDE, this._hideModalHandler)\n  }\n\n  _fixTitle() {\n    const title = this._element.getAttribute('title')\n\n    if (!title) {\n      return\n    }\n\n    if (!this._element.getAttribute('aria-label') && !this._element.textContent.trim()) {\n      this._element.setAttribute('aria-label', title)\n    }\n\n    this._element.setAttribute('data-bs-original-title', title) // DO NOT USE IT. Is only for backwards compatibility\n    this._element.removeAttribute('title')\n  }\n\n  _enter() {\n    if (this._isShown() || this._isHovered) {\n      this._isHovered = true\n      return\n    }\n\n    this._isHovered = true\n\n    this._setTimeout(() => {\n      if (this._isHovered) {\n        this.show()\n      }\n    }, this._config.delay.show)\n  }\n\n  _leave() {\n    if (this._isWithActiveTrigger()) {\n      return\n    }\n\n    this._isHovered = false\n\n    this._setTimeout(() => {\n      if (!this._isHovered) {\n        this.hide()\n      }\n    }, this._config.delay.hide)\n  }\n\n  _setTimeout(handler, timeout) {\n    clearTimeout(this._timeout)\n    this._timeout = setTimeout(handler, timeout)\n  }\n\n  _isWithActiveTrigger() {\n    return Object.values(this._activeTrigger).includes(true)\n  }\n\n  _getConfig(config) {\n    const dataAttributes = Manipulator.getDataAttributes(this._element)\n\n    for (const dataAttribute of Object.keys(dataAttributes)) {\n      if (DISALLOWED_ATTRIBUTES.has(dataAttribute)) {\n        delete dataAttributes[dataAttribute]\n      }\n    }\n\n    config = {\n      ...dataAttributes,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n    config = this._mergeConfigObj(config)\n    config = this._configAfterMerge(config)\n    this._typeCheckConfig(config)\n    return config\n  }\n\n  _configAfterMerge(config) {\n    config.container = config.container === false ? document.body : getElement(config.container)\n\n    if (typeof config.delay === 'number') {\n      config.delay = {\n        show: config.delay,\n        hide: config.delay\n      }\n    }\n\n    if (typeof config.title === 'number') {\n      config.title = config.title.toString()\n    }\n\n    if (typeof config.content === 'number') {\n      config.content = config.content.toString()\n    }\n\n    return config\n  }\n\n  _getDelegateConfig() {\n    const config = {}\n\n    for (const [key, value] of Object.entries(this._config)) {\n      if (this.constructor.Default[key] !== value) {\n        config[key] = value\n      }\n    }\n\n    config.selector = false\n    config.trigger = 'manual'\n\n    // In the future can be replaced with:\n    // const keysWithDifferentValues = Object.entries(this._config).filter(entry => this.constructor.Default[entry[0]] !== this._config[entry[0]])\n    // `Object.fromEntries(keysWithDifferentValues)`\n    return config\n  }\n\n  _disposePopper() {\n    if (this._popper) {\n      this._popper.destroy()\n      this._popper = null\n    }\n\n    if (this.tip) {\n      this.tip.remove()\n      this.tip = null\n    }\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Tooltip.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Tooltip)\n\nexport default Tooltip\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Tooltip from './tooltip.js'\nimport { defineJQueryPlugin } from './util/index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'popover'\n\nconst SELECTOR_TITLE = '.popover-header'\nconst SELECTOR_CONTENT = '.popover-body'\n\nconst Default = {\n  ...Tooltip.Default,\n  content: '',\n  offset: [0, 15], // Boosted mod: instead of `offset: [0, 8],`\n  placement: 'right',\n  template: '<div class=\"popover\" role=\"tooltip\">' +\n              '<div class=\"popover-arrow\"></div>' +\n              '<h3 class=\"popover-header\"></h3>' +\n              '<div class=\"popover-body\"></div>' +\n            '</div>',\n  trigger: 'click'\n}\n\nconst DefaultType = {\n  ...Tooltip.DefaultType,\n  content: '(null|string|element|function)'\n}\n\n/**\n * Class definition\n */\n\nclass Popover extends Tooltip {\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Overrides\n  _isWithContent() {\n    return this._getTitle() || this._getContent()\n  }\n\n  // Private\n  _getContentForTemplate() {\n    return {\n      [SELECTOR_TITLE]: this._getTitle(),\n      [SELECTOR_CONTENT]: this._getContent()\n    }\n  }\n\n  _getContent() {\n    return this._resolvePossibleFunction(this._config.content)\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Popover.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Popover)\n\nexport default Popover\n", "/**\n * --------------------------------------------------------------------------\n * Boosted quantity-selector.js\n * Licensed under MIT (https://github.com/Orange-OpenSource/Orange-Boosted-Bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport { defineJQueryPlugin } from './util/index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'quantityselector'\nconst DATA_KEY = 'bs.quantityselector'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CHANGE_DATA_API = `change${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst SELECTOR_STEP_UP_BUTTON = '[data-bs-step=\"up\"]'\nconst SELECTOR_STEP_DOWN_BUTTON = '[data-bs-step=\"down\"]'\nconst SELECTOR_COUNTER_INPUT = '[data-bs-step=\"counter\"]'\nconst SELECTOR_QUANTITY_SELECTOR = '.quantity-selector'\n\n/**\n * Class definition\n */\n\nclass QuantitySelector extends BaseComponent {\n  // Getters\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  ValueOnLoad(element) {\n    const counterInput = element.querySelector(SELECTOR_COUNTER_INPUT)\n    const btnUp = element.querySelector(SELECTOR_STEP_UP_BUTTON)\n    const btnDown = element.querySelector(SELECTOR_STEP_DOWN_BUTTON)\n\n    const min = counterInput.getAttribute('min')\n    const max = counterInput.getAttribute('max')\n    const step = Number(counterInput.getAttribute('step'))\n\n    if (Number(counterInput.value) - step < min) {\n      btnDown.setAttribute('disabled', '')\n    }\n\n    if (Number(counterInput.value) + step > max) {\n      btnUp.setAttribute('disabled', '')\n    }\n  }\n\n  // Static\n  static StepUp(event) {\n    const parent = event.target.closest(SELECTOR_QUANTITY_SELECTOR)\n    const counterInput = parent.querySelector(SELECTOR_COUNTER_INPUT)\n\n    const max = counterInput.getAttribute('max')\n    const step = Number(counterInput.getAttribute('step'))\n    const round = Number(counterInput.getAttribute('data-bs-round'))\n\n    const eventChange = new Event('change')\n\n    if (Number(counterInput.value) < max) {\n      counterInput.value = (Number(counterInput.value) + step).toFixed(round).toString()\n    }\n\n    counterInput.dispatchEvent(eventChange)\n  }\n\n  static StepDown(event) {\n    const parent = event.target.closest(SELECTOR_QUANTITY_SELECTOR)\n    const counterInput = parent.querySelector(SELECTOR_COUNTER_INPUT)\n\n    const min = counterInput.getAttribute('min')\n    const step = Number(counterInput.getAttribute('step'))\n    const round = Number(counterInput.getAttribute('data-bs-round'))\n\n    const eventChange = new Event('change')\n\n    if (Number(counterInput.value) > min) {\n      counterInput.value = (Number(counterInput.value) - step).toFixed(round).toString()\n    }\n\n    counterInput.dispatchEvent(eventChange)\n  }\n\n  static CheckIfDisabledOnChange(event) {\n    const parent = event.target.closest(SELECTOR_QUANTITY_SELECTOR)\n    const counterInput = parent.querySelector(SELECTOR_COUNTER_INPUT)\n    const btnUp = parent.querySelector(SELECTOR_STEP_UP_BUTTON)\n    const btnDown = parent.querySelector(SELECTOR_STEP_DOWN_BUTTON)\n\n    const min = counterInput.getAttribute('min')\n    const max = counterInput.getAttribute('max')\n    const step = Number(counterInput.getAttribute('step'))\n\n    btnUp.removeAttribute('disabled', '')\n    btnDown.removeAttribute('disabled', '')\n\n    if (Number(counterInput.value) - step < min) {\n      btnDown.setAttribute('disabled', '')\n    }\n\n    if (Number(counterInput.value) + step > max) {\n      btnUp.setAttribute('disabled', '')\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = QuantitySelector.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CHANGE_DATA_API, SELECTOR_COUNTER_INPUT, QuantitySelector.CheckIfDisabledOnChange)\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_STEP_UP_BUTTON, QuantitySelector.StepUp)\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_STEP_DOWN_BUTTON, QuantitySelector.StepDown)\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  for (const el of SelectorEngine.find(SELECTOR_QUANTITY_SELECTOR)) {\n    QuantitySelector.getOrCreateInstance(el).ValueOnLoad(el)\n  }\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(QuantitySelector)\n\nexport default QuantitySelector\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport {\n  defineJQueryPlugin, getElement, isDisabled, isVisible\n} from './util/index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'scrollspy'\nconst DATA_KEY = 'bs.scrollspy'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_ACTIVATE = `activate${EVENT_KEY}`\nconst EVENT_CLICK = `click${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_ITEM = 'dropdown-item'\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_SPY = '[data-bs-spy=\"scroll\"]'\nconst SELECTOR_TARGET_LINKS = '[href]'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_NAV_LINKS = '.nav-link'\nconst SELECTOR_NAV_ITEMS = '.nav-item'\nconst SELECTOR_LIST_ITEMS = '.list-group-item'\nconst SELECTOR_LINK_ITEMS = `${SELECTOR_NAV_LINKS}, ${SELECTOR_NAV_ITEMS} > ${SELECTOR_NAV_LINKS}, ${SELECTOR_LIST_ITEMS}`\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\n\nconst Default = {\n  offset: null, // TODO: v6 @deprecated, keep it for backwards compatibility reasons\n  rootMargin: '0px 0px -25%',\n  smoothScroll: false,\n  target: null,\n  threshold: [0.1, 0.5, 1]\n}\n\nconst DefaultType = {\n  offset: '(number|null)', // TODO v6 @deprecated, keep it for backwards compatibility reasons\n  rootMargin: 'string',\n  smoothScroll: 'boolean',\n  target: 'element',\n  threshold: 'array'\n}\n\n/**\n * Class definition\n */\n\nclass ScrollSpy extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    // this._element is the observablesContainer and config.target the menu links wrapper\n    this._targetLinks = new Map()\n    this._observableSections = new Map()\n    this._rootElement = getComputedStyle(this._element).overflowY === 'visible' ? null : this._element\n    this._activeTarget = null\n    this._observer = null\n    this._previousScrollData = {\n      visibleEntryTop: 0,\n      parentScrollTop: 0\n    }\n    this.refresh() // initialize\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  refresh() {\n    this._initializeTargetsAndObservables()\n    this._maybeEnableSmoothScroll()\n\n    if (this._observer) {\n      this._observer.disconnect()\n    } else {\n      this._observer = this._getNewObserver()\n    }\n\n    for (const section of this._observableSections.values()) {\n      this._observer.observe(section)\n    }\n  }\n\n  dispose() {\n    this._observer.disconnect()\n    super.dispose()\n  }\n\n  // Private\n  _configAfterMerge(config) {\n    // TODO: on v6 target should be given explicitly & remove the {target: 'ss-target'} case\n    config.target = getElement(config.target) || document.body\n\n    // TODO: v6 Only for backwards compatibility reasons. Use rootMargin only\n    config.rootMargin = config.offset ? `${config.offset}px 0px -30%` : config.rootMargin\n\n    if (typeof config.threshold === 'string') {\n      config.threshold = config.threshold.split(',').map(value => Number.parseFloat(value))\n    }\n\n    return config\n  }\n\n  _maybeEnableSmoothScroll() {\n    if (!this._config.smoothScroll) {\n      return\n    }\n\n    // unregister any previous listeners\n    EventHandler.off(this._config.target, EVENT_CLICK)\n\n    EventHandler.on(this._config.target, EVENT_CLICK, SELECTOR_TARGET_LINKS, event => {\n      const observableSection = this._observableSections.get(event.target.hash)\n      if (observableSection) {\n        event.preventDefault()\n        const root = this._rootElement || window\n        const height = observableSection.offsetTop - this._element.offsetTop\n        if (root.scrollTo) {\n          root.scrollTo({ top: height, behavior: 'smooth' })\n          return\n        }\n\n        // Chrome 60 doesn't support `scrollTo`\n        root.scrollTop = height\n      }\n    })\n  }\n\n  _getNewObserver() {\n    const options = {\n      root: this._rootElement,\n      threshold: this._config.threshold,\n      rootMargin: this._config.rootMargin\n    }\n\n    return new IntersectionObserver(entries => this._observerCallback(entries), options)\n  }\n\n  // The logic of selection\n  _observerCallback(entries) {\n    const targetElement = entry => this._targetLinks.get(`#${entry.target.id}`)\n    const activate = entry => {\n      this._previousScrollData.visibleEntryTop = entry.target.offsetTop\n      this._process(targetElement(entry))\n    }\n\n    const parentScrollTop = (this._rootElement || document.documentElement).scrollTop\n    const userScrollsDown = parentScrollTop >= this._previousScrollData.parentScrollTop\n    this._previousScrollData.parentScrollTop = parentScrollTop\n\n    for (const entry of entries) {\n      if (!entry.isIntersecting) {\n        this._activeTarget = null\n        this._clearActiveClass(targetElement(entry))\n\n        continue\n      }\n\n      const entryIsLowerThanPrevious = entry.target.offsetTop >= this._previousScrollData.visibleEntryTop\n      // if we are scrolling down, pick the bigger offsetTop\n      if (userScrollsDown && entryIsLowerThanPrevious) {\n        activate(entry)\n        // if parent isn't scrolled, let's keep the first visible item, breaking the iteration\n        if (!parentScrollTop) {\n          return\n        }\n\n        continue\n      }\n\n      // if we are scrolling up, pick the smallest offsetTop\n      if (!userScrollsDown && !entryIsLowerThanPrevious) {\n        activate(entry)\n      }\n    }\n  }\n\n  _initializeTargetsAndObservables() {\n    this._targetLinks = new Map()\n    this._observableSections = new Map()\n\n    const targetLinks = SelectorEngine.find(SELECTOR_TARGET_LINKS, this._config.target)\n\n    for (const anchor of targetLinks) {\n      // ensure that the anchor has an id and is not disabled\n      if (!anchor.hash || isDisabled(anchor)) {\n        continue\n      }\n\n      const observableSection = SelectorEngine.findOne(decodeURI(anchor.hash), this._element)\n\n      // ensure that the observableSection exists & is visible\n      if (isVisible(observableSection)) {\n        this._targetLinks.set(decodeURI(anchor.hash), anchor)\n        this._observableSections.set(anchor.hash, observableSection)\n      }\n    }\n  }\n\n  _process(target) {\n    if (this._activeTarget === target) {\n      return\n    }\n\n    this._clearActiveClass(this._config.target)\n    this._activeTarget = target\n    target.classList.add(CLASS_NAME_ACTIVE)\n    this._activateParents(target)\n\n    EventHandler.trigger(this._element, EVENT_ACTIVATE, { relatedTarget: target })\n  }\n\n  _activateParents(target) {\n    // Activate dropdown parents\n    if (target.classList.contains(CLASS_NAME_DROPDOWN_ITEM)) {\n      SelectorEngine.findOne(SELECTOR_DROPDOWN_TOGGLE, target.closest(SELECTOR_DROPDOWN))\n        .classList.add(CLASS_NAME_ACTIVE)\n      return\n    }\n\n    for (const listGroup of SelectorEngine.parents(target, SELECTOR_NAV_LIST_GROUP)) {\n      // Set triggered links parents as active\n      // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n      for (const item of SelectorEngine.prev(listGroup, SELECTOR_LINK_ITEMS)) {\n        item.classList.add(CLASS_NAME_ACTIVE)\n      }\n    }\n  }\n\n  _clearActiveClass(parent) {\n    parent.classList.remove(CLASS_NAME_ACTIVE)\n\n    const activeNodes = SelectorEngine.find(`${SELECTOR_TARGET_LINKS}.${CLASS_NAME_ACTIVE}`, parent)\n    for (const node of activeNodes) {\n      node.classList.remove(CLASS_NAME_ACTIVE)\n    }\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = ScrollSpy.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  for (const spy of SelectorEngine.find(SELECTOR_DATA_SPY)) {\n    ScrollSpy.getOrCreateInstance(spy)\n  }\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(ScrollSpy)\n\nexport default ScrollSpy\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport { defineJQueryPlugin, getNextActiveElement, isDisabled } from './util/index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'tab'\nconst DATA_KEY = 'bs.tab'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}`\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\nconst HOME_KEY = 'Home'\nconst END_KEY = 'End'\n\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_DROPDOWN = 'dropdown'\n\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\nconst SELECTOR_DROPDOWN_MENU = '.dropdown-menu'\nconst NOT_SELECTOR_DROPDOWN_TOGGLE = `:not(${SELECTOR_DROPDOWN_TOGGLE})`\n\nconst SELECTOR_TAB_PANEL = '.list-group, .nav, [role=\"tablist\"]'\nconst SELECTOR_OUTER = '.nav-item, .list-group-item'\nconst SELECTOR_INNER = `.nav-link${NOT_SELECTOR_DROPDOWN_TOGGLE}, .list-group-item${NOT_SELECTOR_DROPDOWN_TOGGLE}, [role=\"tab\"]${NOT_SELECTOR_DROPDOWN_TOGGLE}`\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"tab\"], [data-bs-toggle=\"pill\"], [data-bs-toggle=\"list\"]' // TODO: could only be `tab` in v6\nconst SELECTOR_INNER_ELEM = `${SELECTOR_INNER}, ${SELECTOR_DATA_TOGGLE}`\n\nconst SELECTOR_DATA_TOGGLE_ACTIVE = `.${CLASS_NAME_ACTIVE}[data-bs-toggle=\"tab\"], .${CLASS_NAME_ACTIVE}[data-bs-toggle=\"pill\"], .${CLASS_NAME_ACTIVE}[data-bs-toggle=\"list\"]`\n\n/**\n * Class definition\n */\n\nclass Tab extends BaseComponent {\n  constructor(element) {\n    super(element)\n    this._parent = this._element.closest(SELECTOR_TAB_PANEL)\n\n    if (!this._parent) {\n      return\n      // TODO: should throw exception in v6\n      // throw new TypeError(`${element.outerHTML} has not a valid parent ${SELECTOR_INNER_ELEM}`)\n    }\n\n    // Set up initial aria attributes\n    this._setInitialAttributes(this._parent, this._getChildren())\n\n    EventHandler.on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n  }\n\n  // Getters\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  show() { // Shows this elem and deactivate the active sibling if exists\n    const innerElem = this._element\n    if (this._elemIsActive(innerElem)) {\n      return\n    }\n\n    // Search for active tab on same parent to deactivate it\n    const active = this._getActiveElem()\n\n    const hideEvent = active ?\n      EventHandler.trigger(active, EVENT_HIDE, { relatedTarget: innerElem }) :\n      null\n\n    const showEvent = EventHandler.trigger(innerElem, EVENT_SHOW, { relatedTarget: active })\n\n    if (showEvent.defaultPrevented || (hideEvent && hideEvent.defaultPrevented)) {\n      return\n    }\n\n    this._deactivate(active, innerElem)\n    this._activate(innerElem, active)\n  }\n\n  // Private\n  _activate(element, relatedElem) {\n    if (!element) {\n      return\n    }\n\n    element.classList.add(CLASS_NAME_ACTIVE)\n\n    this._activate(SelectorEngine.getElementFromSelector(element)) // Search and activate/show the proper section\n\n    const complete = () => {\n      if (element.getAttribute('role') !== 'tab') {\n        element.classList.add(CLASS_NAME_SHOW)\n        return\n      }\n\n      element.removeAttribute('tabindex')\n      element.setAttribute('aria-selected', true)\n      this._toggleDropDown(element, true)\n      EventHandler.trigger(element, EVENT_SHOWN, {\n        relatedTarget: relatedElem\n      })\n    }\n\n    this._queueCallback(complete, element, element.classList.contains(CLASS_NAME_FADE))\n  }\n\n  _deactivate(element, relatedElem) {\n    if (!element) {\n      return\n    }\n\n    element.classList.remove(CLASS_NAME_ACTIVE)\n    element.blur()\n\n    this._deactivate(SelectorEngine.getElementFromSelector(element)) // Search and deactivate the shown section too\n\n    const complete = () => {\n      if (element.getAttribute('role') !== 'tab') {\n        element.classList.remove(CLASS_NAME_SHOW)\n        return\n      }\n\n      element.setAttribute('aria-selected', false)\n      element.setAttribute('tabindex', '-1')\n      this._toggleDropDown(element, false)\n      EventHandler.trigger(element, EVENT_HIDDEN, { relatedTarget: relatedElem })\n    }\n\n    this._queueCallback(complete, element, element.classList.contains(CLASS_NAME_FADE))\n  }\n\n  _keydown(event) {\n    if (!([ARROW_LEFT_KEY, ARROW_RIGHT_KEY, ARROW_UP_KEY, ARROW_DOWN_KEY, HOME_KEY, END_KEY].includes(event.key))) {\n      return\n    }\n\n    event.stopPropagation()// stopPropagation/preventDefault both added to support up/down keys without scrolling the page\n    event.preventDefault()\n\n    const children = this._getChildren().filter(element => !isDisabled(element))\n    let nextActiveElement\n\n    if ([HOME_KEY, END_KEY].includes(event.key)) {\n      nextActiveElement = children[event.key === HOME_KEY ? 0 : children.length - 1]\n    } else {\n      const isNext = [ARROW_RIGHT_KEY, ARROW_DOWN_KEY].includes(event.key)\n      nextActiveElement = getNextActiveElement(children, event.target, isNext, true)\n    }\n\n    if (nextActiveElement) {\n      nextActiveElement.focus({ preventScroll: true })\n      Tab.getOrCreateInstance(nextActiveElement).show()\n    }\n  }\n\n  _getChildren() { // collection of inner elements\n    return SelectorEngine.find(SELECTOR_INNER_ELEM, this._parent)\n  }\n\n  _getActiveElem() {\n    return this._getChildren().find(child => this._elemIsActive(child)) || null\n  }\n\n  _setInitialAttributes(parent, children) {\n    this._setAttributeIfNotExists(parent, 'role', 'tablist')\n\n    for (const child of children) {\n      this._setInitialAttributesOnChild(child)\n    }\n  }\n\n  _setInitialAttributesOnChild(child) {\n    child = this._getInnerElement(child)\n    const isActive = this._elemIsActive(child)\n    const outerElem = this._getOuterElement(child)\n    child.setAttribute('aria-selected', isActive)\n\n    if (outerElem !== child) {\n      this._setAttributeIfNotExists(outerElem, 'role', 'presentation')\n    }\n\n    if (!isActive) {\n      child.setAttribute('tabindex', '-1')\n    }\n\n    this._setAttributeIfNotExists(child, 'role', 'tab')\n\n    // set attributes to the related panel too\n    this._setInitialAttributesOnTargetPanel(child)\n  }\n\n  _setInitialAttributesOnTargetPanel(child) {\n    const target = SelectorEngine.getElementFromSelector(child)\n\n    if (!target) {\n      return\n    }\n\n    this._setAttributeIfNotExists(target, 'role', 'tabpanel')\n\n    if (child.id) {\n      this._setAttributeIfNotExists(target, 'aria-labelledby', `${child.id}`)\n    }\n  }\n\n  _toggleDropDown(element, open) {\n    const outerElem = this._getOuterElement(element)\n    if (!outerElem.classList.contains(CLASS_DROPDOWN)) {\n      return\n    }\n\n    const toggle = (selector, className) => {\n      const element = SelectorEngine.findOne(selector, outerElem)\n      if (element) {\n        element.classList.toggle(className, open)\n      }\n    }\n\n    toggle(SELECTOR_DROPDOWN_TOGGLE, CLASS_NAME_ACTIVE)\n    toggle(SELECTOR_DROPDOWN_MENU, CLASS_NAME_SHOW)\n    outerElem.setAttribute('aria-expanded', open)\n  }\n\n  _setAttributeIfNotExists(element, attribute, value) {\n    if (!element.hasAttribute(attribute)) {\n      element.setAttribute(attribute, value)\n    }\n  }\n\n  _elemIsActive(elem) {\n    return elem.classList.contains(CLASS_NAME_ACTIVE)\n  }\n\n  // Try to get the inner element (usually the .nav-link)\n  _getInnerElement(elem) {\n    return elem.matches(SELECTOR_INNER_ELEM) ? elem : SelectorEngine.findOne(SELECTOR_INNER_ELEM, elem)\n  }\n\n  // Try to get the outer element (usually the .nav-item)\n  _getOuterElement(elem) {\n    return elem.closest(SELECTOR_OUTER) || elem\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Tab.getOrCreateInstance(this)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  if (isDisabled(this)) {\n    return\n  }\n\n  Tab.getOrCreateInstance(this).show()\n})\n\n/**\n * Initialize on focus\n */\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  for (const element of SelectorEngine.find(SELECTOR_DATA_TOGGLE_ACTIVE)) {\n    Tab.getOrCreateInstance(element)\n  }\n})\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Tab)\n\nexport default Tab\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport { enableDismissTrigger } from './util/component-functions.js'\nimport { defineJQueryPlugin, reflow } from './util/index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'toast'\nconst DATA_KEY = 'bs.toast'\nconst EVENT_KEY = `.${D<PERSON>A_KEY}`\n\nconst EVENT_MOUSEOVER = `mouseover${EVENT_KEY}`\nconst EVENT_MOUSEOUT = `mouseout${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_FOCUSOUT = `focusout${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_HIDE = 'hide' // @deprecated - kept here only for backwards compatibility\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\n\nconst DefaultType = {\n  animation: 'boolean',\n  autohide: 'boolean',\n  delay: 'number'\n}\n\nconst Default = {\n  animation: true,\n  autohide: true,\n  delay: 5000\n}\n\n/**\n * Class definition\n */\n\nclass Toast extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._timeout = null\n    this._hasMouseInteraction = false\n    this._hasKeyboardInteraction = false\n    this._setListeners()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  show() {\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._clearTimeout()\n\n    if (this._config.animation) {\n      this._element.classList.add(CLASS_NAME_FADE)\n    }\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n\n      this._maybeScheduleHide()\n    }\n\n    this._element.classList.remove(CLASS_NAME_HIDE) // @deprecated\n    reflow(this._element)\n    this._element.classList.add(CLASS_NAME_SHOW, CLASS_NAME_SHOWING)\n\n    this._queueCallback(complete, this._element, this._config.animation)\n  }\n\n  hide() {\n    if (!this.isShown()) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const complete = () => {\n      this._element.classList.add(CLASS_NAME_HIDE) // @deprecated\n      this._element.classList.remove(CLASS_NAME_SHOWING, CLASS_NAME_SHOW)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.classList.add(CLASS_NAME_SHOWING)\n    this._queueCallback(complete, this._element, this._config.animation)\n  }\n\n  dispose() {\n    this._clearTimeout()\n\n    if (this.isShown()) {\n      this._element.classList.remove(CLASS_NAME_SHOW)\n    }\n\n    super.dispose()\n  }\n\n  isShown() {\n    return this._element.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  // Private\n  _maybeScheduleHide() {\n    if (!this._config.autohide) {\n      return\n    }\n\n    if (this._hasMouseInteraction || this._hasKeyboardInteraction) {\n      return\n    }\n\n    this._timeout = setTimeout(() => {\n      this.hide()\n    }, this._config.delay)\n  }\n\n  _onInteraction(event, isInteracting) {\n    switch (event.type) {\n      case 'mouseover':\n      case 'mouseout': {\n        this._hasMouseInteraction = isInteracting\n        break\n      }\n\n      case 'focusin':\n      case 'focusout': {\n        this._hasKeyboardInteraction = isInteracting\n        break\n      }\n\n      default: {\n        break\n      }\n    }\n\n    if (isInteracting) {\n      this._clearTimeout()\n      return\n    }\n\n    const nextElement = event.relatedTarget\n    if (this._element === nextElement || this._element.contains(nextElement)) {\n      return\n    }\n\n    this._maybeScheduleHide()\n  }\n\n  _setListeners() {\n    EventHandler.on(this._element, EVENT_MOUSEOVER, event => this._onInteraction(event, true))\n    EventHandler.on(this._element, EVENT_MOUSEOUT, event => this._onInteraction(event, false))\n    EventHandler.on(this._element, EVENT_FOCUSIN, event => this._onInteraction(event, true))\n    EventHandler.on(this._element, EVENT_FOCUSOUT, event => this._onInteraction(event, false))\n  }\n\n  _clearTimeout() {\n    clearTimeout(this._timeout)\n    this._timeout = null\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Toast.getOrCreateInstance(this, config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](this)\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nenableDismissTrigger(Toast)\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Toast)\n\nexport default Toast\n", "(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' ? factory() :\n  typeof define === 'function' && define.amd ? define(factory) :\n  (factory());\n}(this, (function () { 'use strict';\n\n  /**\n   * Applies the :focus-visible polyfill at the given scope.\n   * A scope in this case is either the top-level Document or a Shadow Root.\n   *\n   * @param {(Document|ShadowRoot)} scope\n   * @see https://github.com/WICG/focus-visible\n   */\n  function applyFocusVisiblePolyfill(scope) {\n    var hadKeyboardEvent = true;\n    var hadFocusVisibleRecently = false;\n    var hadFocusVisibleRecentlyTimeout = null;\n\n    var inputTypesAllowlist = {\n      text: true,\n      search: true,\n      url: true,\n      tel: true,\n      email: true,\n      password: true,\n      number: true,\n      date: true,\n      month: true,\n      week: true,\n      time: true,\n      datetime: true,\n      'datetime-local': true\n    };\n\n    /**\n     * Helper function for legacy browsers and iframes which sometimes focus\n     * elements like document, body, and non-interactive SVG.\n     * @param {Element} el\n     */\n    function isValidFocusTarget(el) {\n      if (\n        el &&\n        el !== document &&\n        el.nodeName !== 'HTML' &&\n        el.nodeName !== 'BODY' &&\n        'classList' in el &&\n        'contains' in el.classList\n      ) {\n        return true;\n      }\n      return false;\n    }\n\n    /**\n     * Computes whether the given element should automatically trigger the\n     * `focus-visible` class being added, i.e. whether it should always match\n     * `:focus-visible` when focused.\n     * @param {Element} el\n     * @return {boolean}\n     */\n    function focusTriggersKeyboardModality(el) {\n      var type = el.type;\n      var tagName = el.tagName;\n\n      if (tagName === 'INPUT' && inputTypesAllowlist[type] && !el.readOnly) {\n        return true;\n      }\n\n      if (tagName === 'TEXTAREA' && !el.readOnly) {\n        return true;\n      }\n\n      if (el.isContentEditable) {\n        return true;\n      }\n\n      return false;\n    }\n\n    /**\n     * Add the `focus-visible` class to the given element if it was not added by\n     * the author.\n     * @param {Element} el\n     */\n    function addFocusVisibleClass(el) {\n      if (el.classList.contains('focus-visible')) {\n        return;\n      }\n      el.classList.add('focus-visible');\n      el.setAttribute('data-focus-visible-added', '');\n    }\n\n    /**\n     * Remove the `focus-visible` class from the given element if it was not\n     * originally added by the author.\n     * @param {Element} el\n     */\n    function removeFocusVisibleClass(el) {\n      if (!el.hasAttribute('data-focus-visible-added')) {\n        return;\n      }\n      el.classList.remove('focus-visible');\n      el.removeAttribute('data-focus-visible-added');\n    }\n\n    /**\n     * If the most recent user interaction was via the keyboard;\n     * and the key press did not include a meta, alt/option, or control key;\n     * then the modality is keyboard. Otherwise, the modality is not keyboard.\n     * Apply `focus-visible` to any current active element and keep track\n     * of our keyboard modality state with `hadKeyboardEvent`.\n     * @param {KeyboardEvent} e\n     */\n    function onKeyDown(e) {\n      if (e.metaKey || e.altKey || e.ctrlKey) {\n        return;\n      }\n\n      if (isValidFocusTarget(scope.activeElement)) {\n        addFocusVisibleClass(scope.activeElement);\n      }\n\n      hadKeyboardEvent = true;\n    }\n\n    /**\n     * If at any point a user clicks with a pointing device, ensure that we change\n     * the modality away from keyboard.\n     * This avoids the situation where a user presses a key on an already focused\n     * element, and then clicks on a different element, focusing it with a\n     * pointing device, while we still think we're in keyboard modality.\n     * @param {Event} e\n     */\n    function onPointerDown(e) {\n      hadKeyboardEvent = false;\n    }\n\n    /**\n     * On `focus`, add the `focus-visible` class to the target if:\n     * - the target received focus as a result of keyboard navigation, or\n     * - the event target is an element that will likely require interaction\n     *   via the keyboard (e.g. a text box)\n     * @param {Event} e\n     */\n    function onFocus(e) {\n      // Prevent IE from focusing the document or HTML element.\n      if (!isValidFocusTarget(e.target)) {\n        return;\n      }\n\n      if (hadKeyboardEvent || focusTriggersKeyboardModality(e.target)) {\n        addFocusVisibleClass(e.target);\n      }\n    }\n\n    /**\n     * On `blur`, remove the `focus-visible` class from the target.\n     * @param {Event} e\n     */\n    function onBlur(e) {\n      if (!isValidFocusTarget(e.target)) {\n        return;\n      }\n\n      if (\n        e.target.classList.contains('focus-visible') ||\n        e.target.hasAttribute('data-focus-visible-added')\n      ) {\n        // To detect a tab/window switch, we look for a blur event followed\n        // rapidly by a visibility change.\n        // If we don't see a visibility change within 100ms, it's probably a\n        // regular focus change.\n        hadFocusVisibleRecently = true;\n        window.clearTimeout(hadFocusVisibleRecentlyTimeout);\n        hadFocusVisibleRecentlyTimeout = window.setTimeout(function() {\n          hadFocusVisibleRecently = false;\n        }, 100);\n        removeFocusVisibleClass(e.target);\n      }\n    }\n\n    /**\n     * If the user changes tabs, keep track of whether or not the previously\n     * focused element had .focus-visible.\n     * @param {Event} e\n     */\n    function onVisibilityChange(e) {\n      if (document.visibilityState === 'hidden') {\n        // If the tab becomes active again, the browser will handle calling focus\n        // on the element (Safari actually calls it twice).\n        // If this tab change caused a blur on an element with focus-visible,\n        // re-apply the class when the user switches back to the tab.\n        if (hadFocusVisibleRecently) {\n          hadKeyboardEvent = true;\n        }\n        addInitialPointerMoveListeners();\n      }\n    }\n\n    /**\n     * Add a group of listeners to detect usage of any pointing devices.\n     * These listeners will be added when the polyfill first loads, and anytime\n     * the window is blurred, so that they are active when the window regains\n     * focus.\n     */\n    function addInitialPointerMoveListeners() {\n      document.addEventListener('mousemove', onInitialPointerMove);\n      document.addEventListener('mousedown', onInitialPointerMove);\n      document.addEventListener('mouseup', onInitialPointerMove);\n      document.addEventListener('pointermove', onInitialPointerMove);\n      document.addEventListener('pointerdown', onInitialPointerMove);\n      document.addEventListener('pointerup', onInitialPointerMove);\n      document.addEventListener('touchmove', onInitialPointerMove);\n      document.addEventListener('touchstart', onInitialPointerMove);\n      document.addEventListener('touchend', onInitialPointerMove);\n    }\n\n    function removeInitialPointerMoveListeners() {\n      document.removeEventListener('mousemove', onInitialPointerMove);\n      document.removeEventListener('mousedown', onInitialPointerMove);\n      document.removeEventListener('mouseup', onInitialPointerMove);\n      document.removeEventListener('pointermove', onInitialPointerMove);\n      document.removeEventListener('pointerdown', onInitialPointerMove);\n      document.removeEventListener('pointerup', onInitialPointerMove);\n      document.removeEventListener('touchmove', onInitialPointerMove);\n      document.removeEventListener('touchstart', onInitialPointerMove);\n      document.removeEventListener('touchend', onInitialPointerMove);\n    }\n\n    /**\n     * When the polfyill first loads, assume the user is in keyboard modality.\n     * If any event is received from a pointing device (e.g. mouse, pointer,\n     * touch), turn off keyboard modality.\n     * This accounts for situations where focus enters the page from the URL bar.\n     * @param {Event} e\n     */\n    function onInitialPointerMove(e) {\n      // Work around a Safari quirk that fires a mousemove on <html> whenever the\n      // window blurs, even if you're tabbing out of the page. ¯\\_(ツ)_/¯\n      if (e.target.nodeName && e.target.nodeName.toLowerCase() === 'html') {\n        return;\n      }\n\n      hadKeyboardEvent = false;\n      removeInitialPointerMoveListeners();\n    }\n\n    // For some kinds of state, we are interested in changes at the global scope\n    // only. For example, global pointer input, global key presses and global\n    // visibility change should affect the state at every scope:\n    document.addEventListener('keydown', onKeyDown, true);\n    document.addEventListener('mousedown', onPointerDown, true);\n    document.addEventListener('pointerdown', onPointerDown, true);\n    document.addEventListener('touchstart', onPointerDown, true);\n    document.addEventListener('visibilitychange', onVisibilityChange, true);\n\n    addInitialPointerMoveListeners();\n\n    // For focus and blur, we specifically care about state changes in the local\n    // scope. This is because focus / blur events that originate from within a\n    // shadow root are not re-dispatched from the host element if it was already\n    // the active element in its own scope:\n    scope.addEventListener('focus', onFocus, true);\n    scope.addEventListener('blur', onBlur, true);\n\n    // We detect that a node is a ShadowRoot by ensuring that it is a\n    // DocumentFragment and also has a host property. This check covers native\n    // implementation and polyfill implementation transparently. If we only cared\n    // about the native implementation, we could just check if the scope was\n    // an instance of a ShadowRoot.\n    if (scope.nodeType === Node.DOCUMENT_FRAGMENT_NODE && scope.host) {\n      // Since a ShadowRoot is a special kind of DocumentFragment, it does not\n      // have a root element to add a class to. So, we add this attribute to the\n      // host element instead:\n      scope.host.setAttribute('data-js-focus-visible', '');\n    } else if (scope.nodeType === Node.DOCUMENT_NODE) {\n      document.documentElement.classList.add('js-focus-visible');\n      document.documentElement.setAttribute('data-js-focus-visible', '');\n    }\n  }\n\n  // It is important to wrap all references to global window and document in\n  // these checks to support server-side rendering use cases\n  // @see https://github.com/WICG/focus-visible/issues/199\n  if (typeof window !== 'undefined' && typeof document !== 'undefined') {\n    // Make the polyfill helper globally available. This can be used as a signal\n    // to interested libraries that wish to coordinate with the polyfill for e.g.,\n    // applying the polyfill to a shadow root:\n    window.applyFocusVisiblePolyfill = applyFocusVisiblePolyfill;\n\n    // Notify interested libraries of the polyfill's presence, in case the\n    // polyfill was loaded lazily:\n    var event;\n\n    try {\n      event = new CustomEvent('focus-visible-polyfill-ready');\n    } catch (error) {\n      // IE11 does not support using CustomEvent as a constructor directly:\n      event = document.createEvent('CustomEvent');\n      event.initCustomEvent('focus-visible-polyfill-ready', false, false, {});\n    }\n\n    window.dispatchEvent(event);\n  }\n\n  if (typeof document !== 'undefined') {\n    // Apply the polyfill to the global document, so that no JavaScript\n    // coordination is required to use the polyfill in the top-level document:\n    applyFocusVisiblePolyfill(document);\n  }\n\n})));\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap index.umd.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport <PERSON><PERSON> from './src/alert.js'\nimport Button from './src/button.js'\nimport Carousel from './src/carousel.js'\nimport Collapse from './src/collapse.js'\nimport Dropdown from './src/dropdown.js'\nimport Modal from './src/modal.js'\nimport Offcanvas from './src/offcanvas.js'\nimport OrangeNavbar from './src/orange-navbar.js' // Boosted mod\nimport Popover from './src/popover.js'\nimport QuantitySelector from './src/quantity-selector.js' // Boosted mod\nimport ScrollSpy from './src/scrollspy.js'\nimport Tab from './src/tab.js'\nimport Toast from './src/toast.js'\nimport Tooltip from './src/tooltip.js'\nimport '../node_modules/focus-visible/dist/focus-visible.js' /* eslint-disable-line import/no-unassigned-import */ // Boosted mod\n\nexport default {\n  Alert,\n  Button,\n  Carousel,\n  Collapse,\n  Dropdown,\n  Modal,\n  Offcanvas,\n  OrangeNavbar, // Boosted mod\n  Popover,\n  QuantitySelector, // Boosted mod\n  ScrollSpy,\n  Tab,\n  Toast,\n  Tooltip\n}\n"], "mappings": ";;;;;;;;;;wOAWA,MAAMA,EAAa,IAAIC,IAEvBC,EAAe,CACbC,IAAIC,EAASC,EAAKC,GACXN,EAAWO,IAAIH,IAClBJ,EAAWG,IAAIC,EAAS,IAAIH,KAG9B,MAAMO,EAAcR,EAAWS,IAAIL,GAI9BI,EAAYD,IAAIF,IAA6B,IAArBG,EAAYE,KAMzCF,EAAYL,IAAIE,EAAKC,GAJnBK,QAAQC,MAAM,+EAA+EC,MAAMC,KAAKN,EAAYO,QAAQ,M,EAOhIN,IAAGA,CAACL,EAASC,IACPL,EAAWO,IAAIH,IACVJ,EAAWS,IAAIL,GAASK,IAAIJ,IAG9B,KAGTW,OAAOZ,EAASC,GACd,IAAKL,EAAWO,IAAIH,GAClB,OAGF,MAAMI,EAAcR,EAAWS,IAAIL,GAEnCI,EAAYS,OAAOZ,GAGM,IAArBG,EAAYE,MACdV,EAAWiB,OAAOb,EAEtB,GC5CIc,EAAiB,gBAOjBC,EAAgBC,IAChBA,GAAYC,OAAOC,KAAOD,OAAOC,IAAIC,SAEvCH,EAAWA,EAASI,QAAQ,iBAAiB,CAACC,EAAOC,IAAO,IAAIJ,IAAIC,OAAOG,QAGtEN,GA+CHO,EAAuBvB,IAC3BA,EAAQwB,cAAc,IAAIC,MAAMX,KAG5BY,EAAYC,MACXA,GAA4B,iBAAXA,UAIO,IAAlBA,EAAOC,SAChBD,EAASA,EAAO,SAGgB,IAApBA,EAAOE,UAGjBC,EAAaH,GAEbD,EAAUC,GACLA,EAAOC,OAASD,EAAO,GAAKA,EAGf,iBAAXA,GAAuBA,EAAOI,OAAS,EACzCC,SAASC,cAAclB,EAAcY,IAGvC,KAGHO,EAAYlC,IAChB,IAAK0B,EAAU1B,IAAgD,IAApCA,EAAQmC,iBAAiBJ,OAClD,OAAO,EAGT,MAAMK,EAAgF,YAA7DC,iBAAiBrC,GAASsC,iBAAiB,cAE9DC,EAAgBvC,EAAQwC,QAAQ,uBAEtC,IAAKD,EACH,OAAOH,EAGT,GAAIG,IAAkBvC,EAAS,CAC7B,MAAMyC,EAAUzC,EAAQwC,QAAQ,WAChC,GAAIC,GAAWA,EAAQC,aAAeH,EACpC,OAAO,EAGT,GAAgB,OAAZE,EACF,OAAO,CAEX,CAEA,OAAOL,GAGHO,EAAa3C,IACZA,GAAWA,EAAQ6B,WAAae,KAAKC,gBAItC7C,EAAQ8C,UAAUC,SAAS,mBAIC,IAArB/C,EAAQgD,SACVhD,EAAQgD,SAGVhD,EAAQiD,aAAa,aAAoD,UAArCjD,EAAQkD,aAAa,aAG5DC,EAAiBnD,IACrB,IAAKgC,SAASoB,gBAAgBC,aAC5B,OAAO,KAIT,GAAmC,mBAAxBrD,EAAQsD,YAA4B,CAC7C,MAAMC,EAAOvD,EAAQsD,cACrB,OAAOC,aAAgBC,WAAaD,EAAO,IAC7C,CAEA,OAAIvD,aAAmBwD,WACdxD,EAIJA,EAAQ0C,WAINS,EAAenD,EAAQ0C,YAHrB,MAMLe,EAAOA,OAUPC,EAAS1D,IACbA,EAAQ2D,cAGJC,EAAYA,IACZ3C,OAAO4C,SAAW7B,SAAS8B,KAAKb,aAAa,qBACxChC,OAAO4C,OAGT,KAGHE,EAA4B,GAmB5BC,EAAQA,IAAuC,QAAjChC,SAASoB,gBAAgBa,IAEvCC,EAAqBC,IAnBAC,QAoBN,KACjB,MAAMC,EAAIT,IAEV,GAAIS,EAAG,CACL,MAAMC,EAAOH,EAAOI,KACdC,EAAqBH,EAAEI,GAAGH,GAChCD,EAAEI,GAAGH,GAAQH,EAAOO,gBACpBL,EAAEI,GAAGH,GAAMK,YAAcR,EACzBE,EAAEI,GAAGH,GAAMM,WAAa,KACtBP,EAAEI,GAAGH,GAAQE,EACNL,EAAOO,gBAElB,GA/B0B,YAAxB1C,SAAS6C,YAENd,EAA0BhC,QAC7BC,SAAS8C,iBAAiB,oBAAoB,KAC5C,IAAK,MAAMV,KAAYL,EACrBK,OAKNL,EAA0BgB,KAAKX,IAE/BA,KAuBEY,EAAUA,CAACC,EAAkBC,EAAO,GAAIC,EAAeF,IACxB,mBAArBA,EAAkCA,EAAiBG,QAAQF,GAAQC,EAG7EE,EAAyBA,CAACjB,EAAUkB,EAAmBC,GAAoB,KAC/E,IAAKA,EAEH,YADAP,EAAQZ,GAIV,MACMoB,EA7LiCxF,KACvC,IAAKA,EACH,OAAO,EAIT,IAAIyF,mBAAEA,EAAkBC,gBAAEA,GAAoBzE,OAAOoB,iBAAiBrC,GAEtE,MAAM2F,EAA0BC,OAAOC,WAAWJ,GAC5CK,EAAuBF,OAAOC,WAAWH,GAG/C,OAAKC,GAA4BG,GAKjCL,EAAqBA,EAAmBM,MAAM,KAAK,GACnDL,EAAkBA,EAAgBK,MAAM,KAAK,GAxDf,KA0DtBH,OAAOC,WAAWJ,GAAsBG,OAAOC,WAAWH,KAPzD,GAgLgBM,CAAiCV,GADlC,EAGxB,IAAIW,GAAS,EAEb,MAAMC,EAAUA,EAAGC,aACbA,IAAWb,IAIfW,GAAS,EACTX,EAAkBc,oBAAoBtF,EAAgBoF,GACtDlB,EAAQZ,KAGVkB,EAAkBR,iBAAiBhE,EAAgBoF,GACnDG,YAAW,KACJJ,GACH1E,EAAqB+D,KAEtBE,IAYCc,EAAuBA,CAACC,EAAMC,EAAeC,EAAeC,KAChE,MAAMC,EAAaJ,EAAKxE,OACxB,IAAI6E,EAAQL,EAAKM,QAAQL,GAIzB,OAAc,IAAVI,GACMH,GAAiBC,EAAiBH,EAAKI,EAAa,GAAKJ,EAAK,IAGxEK,GAASH,EAAgB,GAAI,EAEzBC,IACFE,GAASA,EAAQD,GAAcA,GAG1BJ,EAAKO,KAAKC,IAAI,EAAGD,KAAKE,IAAIJ,EAAOD,EAAa,OC7QjDM,EAAiB,qBACjBC,EAAiB,OACjBC,EAAgB,SAChBC,EAAgB,GACtB,IAAIC,EAAW,EACf,MAAMC,EAAe,CACnBC,WAAY,YACZC,WAAY,YAGRC,EAAe,IAAIC,IAAI,CAC3B,QACA,WACA,UACA,YACA,cACA,aACA,iBACA,YACA,WACA,YACA,cACA,YACA,UACA,WACA,QACA,oBACA,aACA,YACA,WACA,cACA,cACA,cACA,YACA,eACA,gBACA,eACA,gBACA,aACA,QACA,OACA,SACA,QACA,SACA,SACA,UACA,WACA,OACA,SACA,eACA,SACA,OACA,mBACA,mBACA,QACA,QACA,WAOF,SAASC,EAAa3H,EAAS4H,GAC7B,OAAQA,GAAO,GAAGA,MAAQP,OAAiBrH,EAAQqH,UAAYA,GACjE,CAEA,SAASQ,EAAiB7H,GACxB,MAAM4H,EAAMD,EAAa3H,GAKzB,OAHAA,EAAQqH,SAAWO,EACnBR,EAAcQ,GAAOR,EAAcQ,IAAQ,GAEpCR,EAAcQ,EACvB,CAoCA,SAASE,EAAYC,EAAQC,EAAUC,EAAqB,MAC1D,OAAOC,OAAOC,OAAOJ,GAClBK,MAAKC,GAASA,EAAML,WAAaA,GAAYK,EAAMJ,qBAAuBA,GAC/E,CAEA,SAASK,EAAoBC,EAAmBrC,EAASsC,GACvD,MAAMC,EAAiC,iBAAZvC,EAErB8B,EAAWS,EAAcD,EAAsBtC,GAAWsC,EAChE,IAAIE,EAAYC,EAAaJ,GAM7B,OAJKd,EAAatH,IAAIuI,KACpBA,EAAYH,GAGP,CAACE,EAAaT,EAAUU,EACjC,CAEA,SAASE,EAAW5I,EAASuI,EAAmBrC,EAASsC,EAAoBK,GAC3E,GAAiC,iBAAtBN,IAAmCvI,EAC5C,OAGF,IAAKyI,EAAaT,EAAUU,GAAaJ,EAAoBC,EAAmBrC,EAASsC,GAIzF,GAAID,KAAqBjB,EAAc,CACrC,MAAMwB,EAAerE,GACZ,SAAU4D,GACf,IAAKA,EAAMU,eAAkBV,EAAMU,gBAAkBV,EAAMW,iBAAmBX,EAAMW,eAAejG,SAASsF,EAAMU,eAChH,OAAOtE,EAAGW,KAAK6D,KAAMZ,E,EAK3BL,EAAWc,EAAad,EAC1B,CAEA,MAAMD,EAASF,EAAiB7H,GAC1BkJ,EAAWnB,EAAOW,KAAeX,EAAOW,GAAa,IACrDS,EAAmBrB,EAAYoB,EAAUlB,EAAUS,EAAcvC,EAAU,MAEjF,GAAIiD,EAGF,YAFAA,EAAiBN,OAASM,EAAiBN,QAAUA,GAKvD,MAAMjB,EAAMD,EAAaK,EAAUO,EAAkBnH,QAAQ6F,EAAgB,KACvExC,EAAKgE,EAxEb,SAAoCzI,EAASgB,EAAUyD,GACrD,OAAO,SAASyB,EAAQmC,GACtB,MAAMe,EAAcpJ,EAAQqJ,iBAAiBrI,GAE7C,IAAK,IAAImF,OAAEA,GAAWkC,EAAOlC,GAAUA,IAAW8C,KAAM9C,EAASA,EAAOzD,WACtE,IAAK,MAAM4G,KAAcF,EACvB,GAAIE,IAAenD,EAUnB,OANAoD,EAAWlB,EAAO,CAAEW,eAAgB7C,IAEhCD,EAAQ2C,QACVW,EAAaC,IAAIzJ,EAASqI,EAAMqB,KAAM1I,EAAUyD,GAG3CA,EAAGkF,MAAMxD,EAAQ,CAACkC,G,CAIjC,CAqDIuB,CAA2B5J,EAASkG,EAAS8B,GArFjD,SAA0BhI,EAASyE,GACjC,OAAO,SAASyB,EAAQmC,GAOtB,OANAkB,EAAWlB,EAAO,CAAEW,eAAgBhJ,IAEhCkG,EAAQ2C,QACVW,EAAaC,IAAIzJ,EAASqI,EAAMqB,KAAMjF,GAGjCA,EAAGkF,MAAM3J,EAAS,CAACqI,G,CAE9B,CA4EIwB,CAAiB7J,EAASgI,GAE5BvD,EAAGwD,mBAAqBQ,EAAcvC,EAAU,KAChDzB,EAAGuD,SAAWA,EACdvD,EAAGoE,OAASA,EACZpE,EAAG4C,SAAWO,EACdsB,EAAStB,GAAOnD,EAEhBzE,EAAQ8E,iBAAiB4D,EAAWjE,EAAIgE,EAC1C,CAEA,SAASqB,EAAc9J,EAAS+H,EAAQW,EAAWxC,EAAS+B,GAC1D,MAAMxD,EAAKqD,EAAYC,EAAOW,GAAYxC,EAAS+B,GAE9CxD,IAILzE,EAAQoG,oBAAoBsC,EAAWjE,EAAIsF,QAAQ9B,WAC5CF,EAAOW,GAAWjE,EAAG4C,UAC9B,CAEA,SAAS2C,EAAyBhK,EAAS+H,EAAQW,EAAWuB,GAC5D,MAAMC,EAAoBnC,EAAOW,IAAc,GAE/C,IAAK,MAAOyB,EAAY9B,KAAUH,OAAOkC,QAAQF,GAC3CC,EAAWE,SAASJ,IACtBH,EAAc9J,EAAS+H,EAAQW,EAAWL,EAAML,SAAUK,EAAMJ,mBAGtE,CAEA,SAASU,EAAaN,GAGpB,OADAA,EAAQA,EAAMjH,QAAQ8F,EAAgB,IAC/BI,EAAae,IAAUA,CAChC,CAEA,MAAMmB,EAAe,CACnBc,GAAGtK,EAASqI,EAAOnC,EAASsC,GAC1BI,EAAW5I,EAASqI,EAAOnC,EAASsC,GAAoB,E,EAG1D+B,IAAIvK,EAASqI,EAAOnC,EAASsC,GAC3BI,EAAW5I,EAASqI,EAAOnC,EAASsC,GAAoB,E,EAG1DiB,IAAIzJ,EAASuI,EAAmBrC,EAASsC,GACvC,GAAiC,iBAAtBD,IAAmCvI,EAC5C,OAGF,MAAOyI,EAAaT,EAAUU,GAAaJ,EAAoBC,EAAmBrC,EAASsC,GACrFgC,EAAc9B,IAAcH,EAC5BR,EAASF,EAAiB7H,GAC1BkK,EAAoBnC,EAAOW,IAAc,GACzC+B,EAAclC,EAAkBmC,WAAW,KAEjD,QAAwB,IAAb1C,EAAX,CAUA,GAAIyC,EACF,IAAK,MAAME,KAAgBzC,OAAOvH,KAAKoH,GACrCiC,EAAyBhK,EAAS+H,EAAQ4C,EAAcpC,EAAkBqC,MAAM,IAIpF,IAAK,MAAOC,EAAaxC,KAAUH,OAAOkC,QAAQF,GAAoB,CACpE,MAAMC,EAAaU,EAAYzJ,QAAQ+F,EAAe,IAEjDqD,IAAejC,EAAkB8B,SAASF,IAC7CL,EAAc9J,EAAS+H,EAAQW,EAAWL,EAAML,SAAUK,EAAMJ,mBAEpE,CAdA,KARA,CAEE,IAAKC,OAAOvH,KAAKuJ,GAAmBnI,OAClC,OAGF+H,EAAc9J,EAAS+H,EAAQW,EAAWV,EAAUS,EAAcvC,EAAU,KAE9E,C,EAiBF4E,QAAQ9K,EAASqI,EAAOnD,GACtB,GAAqB,iBAAVmD,IAAuBrI,EAChC,OAAO,KAGT,MAAMqE,EAAIT,IAIV,IAAImH,EAAc,KACdC,GAAU,EACVC,GAAiB,EACjBC,GAAmB,EALH7C,IADFM,EAAaN,IAQZhE,IACjB0G,EAAc1G,EAAE5C,MAAM4G,EAAOnD,GAE7Bb,EAAErE,GAAS8K,QAAQC,GACnBC,GAAWD,EAAYI,uBACvBF,GAAkBF,EAAYK,gCAC9BF,EAAmBH,EAAYM,sBAGjC,MAAMC,EAAM/B,EAAW,IAAI9H,MAAM4G,EAAO,CAAE2C,UAASO,YAAY,IAASrG,GAcxE,OAZIgG,GACFI,EAAIE,iBAGFP,GACFjL,EAAQwB,cAAc8J,GAGpBA,EAAIJ,kBAAoBH,GAC1BA,EAAYS,iBAGPF,CACT,GAGF,SAAS/B,EAAWkC,EAAKC,EAAO,IAC9B,IAAK,MAAOzL,EAAK0L,KAAUzD,OAAOkC,QAAQsB,GACxC,IACED,EAAIxL,GAAO0L,C,CACX,MAAAC,GACA1D,OAAO2D,eAAeJ,EAAKxL,EAAK,CAC9B6L,cAAc,EACdzL,IAAGA,IACMsL,GAGb,CAGF,OAAOF,CACT,CCnTA,SAASM,EAAcJ,GACrB,GAAc,SAAVA,EACF,OAAO,EAGT,GAAc,UAAVA,EACF,OAAO,EAGT,GAAIA,IAAU/F,OAAO+F,GAAOK,WAC1B,OAAOpG,OAAO+F,GAGhB,GAAc,KAAVA,GAA0B,SAAVA,EAClB,OAAO,KAGT,GAAqB,iBAAVA,EACT,OAAOA,EAGT,IACE,OAAOM,KAAKC,MAAMC,mBAAmBR,G,CACrC,MAAAC,GACA,OAAOD,CACT,CACF,CAEA,SAASS,EAAiBnM,GACxB,OAAOA,EAAImB,QAAQ,UAAUiL,GAAO,IAAIA,EAAIC,iBAC9C,CAEA,MAAMC,EAAc,CAClBC,iBAAiBxM,EAASC,EAAK0L,GAC7B3L,EAAQyM,aAAa,WAAWL,EAAiBnM,KAAQ0L,E,EAG3De,oBAAoB1M,EAASC,GAC3BD,EAAQ2M,gBAAgB,WAAWP,EAAiBnM,K,EAGtD2M,kBAAkB5M,GAChB,IAAKA,EACH,MAAO,GAGT,MAAM6M,EAAa,GACbC,EAAS5E,OAAOvH,KAAKX,EAAQ+M,SAASC,QAAO/M,GAAOA,EAAIyK,WAAW,QAAUzK,EAAIyK,WAAW,cAElG,IAAK,MAAMzK,KAAO6M,EAAQ,CACxB,IAAIG,EAAUhN,EAAImB,QAAQ,MAAO,IACjC6L,EAAUA,EAAQC,OAAO,GAAGZ,cAAgBW,EAAQrC,MAAM,GAC1DiC,EAAWI,GAAWlB,EAAc/L,EAAQ+M,QAAQ9M,GACtD,CAEA,OAAO4M,C,EAGTM,iBAAgBA,CAACnN,EAASC,IACjB8L,EAAc/L,EAAQkD,aAAa,WAAWkJ,EAAiBnM,QCpD1E,MAAMmN,EAEJ,kBAAWC,GACT,MAAO,EACT,CAEA,sBAAWC,GACT,MAAO,EACT,CAEA,eAAW/I,GACT,MAAM,IAAIgJ,MAAM,sEAClB,CAEAC,WAAWC,GAIT,OAHAA,EAASxE,KAAKyE,gBAAgBD,GAC9BA,EAASxE,KAAK0E,kBAAkBF,GAChCxE,KAAK2E,iBAAiBH,GACfA,CACT,CAEAE,kBAAkBF,GAChB,OAAOA,CACT,CAEAC,gBAAgBD,EAAQzN,GACtB,MAAM6N,EAAanM,EAAU1B,GAAWuM,EAAYY,iBAAiBnN,EAAS,UAAY,GAE1F,MAAO,IACFiJ,KAAK6E,YAAYT,WACM,iBAAfQ,EAA0BA,EAAa,MAC9CnM,EAAU1B,GAAWuM,EAAYK,kBAAkB5M,GAAW,MAC5C,iBAAXyN,EAAsBA,EAAS,GAE9C,CAEAG,iBAAiBH,EAAQM,EAAc9E,KAAK6E,YAAYR,aACtD,IAAK,MAAOU,EAAUC,KAAkB/F,OAAOkC,QAAQ2D,GAAc,CACnE,MAAMpC,EAAQ8B,EAAOO,GACfE,EAAYxM,EAAUiK,GAAS,UH1BrChK,OADSA,EG2B+CgK,GHzBnD,GAAGhK,IAGLuG,OAAOiG,UAAUnC,SAAS5G,KAAKzD,GAAQN,MAAM,eAAe,GAAGiL,cGwBlE,IAAK,IAAI8B,OAAOH,GAAeI,KAAKH,GAClC,MAAM,IAAII,UACR,GAAGrF,KAAK6E,YAAYvJ,KAAKgK,0BAA0BP,qBAA4BE,yBAAiCD,MAGtH,CHlCWtM,KGmCb,ECvCF,MAAM6M,UAAsBpB,EAC1BU,YAAY9N,EAASyN,GACnBgB,SAEAzO,EAAU8B,EAAW9B,MAKrBiJ,KAAKyF,SAAW1O,EAChBiJ,KAAK0F,QAAU1F,KAAKuE,WAAWC,GAE/B3N,EAAKC,IAAIkJ,KAAKyF,SAAUzF,KAAK6E,YAAYc,SAAU3F,MACrD,CAGA4F,UACE/O,EAAKc,OAAOqI,KAAKyF,SAAUzF,KAAK6E,YAAYc,UAC5CpF,EAAaC,IAAIR,KAAKyF,SAAUzF,KAAK6E,YAAYgB,WAEjD,IAAK,MAAMC,KAAgB7G,OAAO8G,oBAAoB/F,MACpDA,KAAK8F,GAAgB,IAEzB,CAGAE,eAAe7K,EAAUpE,EAASkP,GAAa,GAC7C7J,EAAuBjB,EAAUpE,EAASkP,EAC5C,CAEA1B,WAAWC,GAIT,OAHAA,EAASxE,KAAKyE,gBAAgBD,EAAQxE,KAAKyF,UAC3CjB,EAASxE,KAAK0E,kBAAkBF,GAChCxE,KAAK2E,iBAAiBH,GACfA,CACT,CAGA,kBAAO0B,CAAYnP,GACjB,OAAOF,EAAKO,IAAIyB,EAAW9B,GAAUiJ,KAAK2F,SAC5C,CAEA,0BAAOQ,CAAoBpP,EAASyN,EAAS,IAC3C,OAAOxE,KAAKkG,YAAYnP,IAAY,IAAIiJ,KAAKjJ,EAA2B,iBAAXyN,EAAsBA,EAAS,KAC9F,CAEA,kBAAW4B,GACT,MArDY,OAsDd,CAEA,mBAAWT,GACT,MAAO,MAAM3F,KAAK1E,MACpB,CAEA,oBAAWuK,GACT,MAAO,IAAI7F,KAAK2F,UAClB,CAEA,gBAAOU,CAAUhL,GACf,MAAO,GAAGA,IAAO2E,KAAK6F,WACxB,ECzEF,MAAMS,EAAcvP,IAClB,IAAIgB,EAAWhB,EAAQkD,aAAa,kBAEpC,IAAKlC,GAAyB,MAAbA,EAAkB,CACjC,IAAIwO,EAAgBxP,EAAQkD,aAAa,QAMzC,IAAKsM,IAAmBA,EAAcnF,SAAS,OAASmF,EAAc9E,WAAW,KAC/E,OAAO,KAIL8E,EAAcnF,SAAS,OAASmF,EAAc9E,WAAW,OAC3D8E,EAAgB,IAAIA,EAAczJ,MAAM,KAAK,MAG/C/E,EAAWwO,GAAmC,MAAlBA,EAAwBA,EAAcC,OAAS,IAC7E,CAEA,OAAOzO,EAAWA,EAAS+E,MAAM,KAAK2J,KAAIC,GAAO5O,EAAc4O,KAAMC,KAAK,KAAO,MAG7EC,EAAiB,CACrBzH,KAAIA,CAACpH,EAAUhB,EAAUgC,SAASoB,kBACzB,GAAG0M,UAAUC,QAAQ5B,UAAU9E,iBAAiBjE,KAAKpF,EAASgB,IAGvEgP,QAAOA,CAAChP,EAAUhB,EAAUgC,SAASoB,kBAC5B2M,QAAQ5B,UAAUlM,cAAcmD,KAAKpF,EAASgB,GAGvDiP,SAAQA,CAACjQ,EAASgB,IACT,GAAG8O,UAAU9P,EAAQiQ,UAAUjD,QAAOkD,GAASA,EAAMC,QAAQnP,KAGtEoP,QAAQpQ,EAASgB,GACf,MAAMoP,EAAU,GAChB,IAAIC,EAAWrQ,EAAQ0C,WAAWF,QAAQxB,GAE1C,KAAOqP,GACLD,EAAQrL,KAAKsL,GACbA,EAAWA,EAAS3N,WAAWF,QAAQxB,GAGzC,OAAOoP,C,EAGTE,KAAKtQ,EAASgB,GACZ,IAAIuP,EAAWvQ,EAAQwQ,uBAEvB,KAAOD,GAAU,CACf,GAAIA,EAASJ,QAAQnP,GACnB,MAAO,CAACuP,GAGVA,EAAWA,EAASC,sBACtB,CAEA,MAAO,E,EAGTC,KAAKzQ,EAASgB,GACZ,IAAIyP,EAAOzQ,EAAQ0Q,mBAEnB,KAAOD,GAAM,CACX,GAAIA,EAAKN,QAAQnP,GACf,MAAO,CAACyP,GAGVA,EAAOA,EAAKC,kBACd,CAEA,MAAO,E,EAGTC,kBAAkB3Q,GAChB,MAAM4Q,EAAa,CACjB,IACA,SACA,QACA,WACA,SACA,UACA,aACA,4BACAlB,KAAI1O,GAAY,GAAGA,2BAAiC4O,KAAK,KAE3D,OAAO3G,KAAKb,KAAKwI,EAAY5Q,GAASgN,QAAO6D,IAAOlO,EAAWkO,IAAO3O,EAAU2O,I,EAGlFC,uBAAuB9Q,GACrB,MAAMgB,EAAWuO,EAAYvP,GAE7B,OAAIgB,GACK6O,EAAeG,QAAQhP,GAAYA,EAGrC,I,EAGT+P,uBAAuB/Q,GACrB,MAAMgB,EAAWuO,EAAYvP,GAE7B,OAAOgB,EAAW6O,EAAeG,QAAQhP,GAAY,I,EAGvDgQ,gCAAgChR,GAC9B,MAAMgB,EAAWuO,EAAYvP,GAE7B,OAAOgB,EAAW6O,EAAezH,KAAKpH,GAAY,EACpD,GC/GIiQ,EAAuBA,CAACC,EAAWC,EAAS,UAChD,MAAMC,EAAa,gBAAgBF,EAAUpC,YACvCxK,EAAO4M,EAAU3M,KAEvBiF,EAAac,GAAGtI,SAAUoP,EAAY,qBAAqB9M,OAAU,SAAU+D,GAK7E,GAJI,CAAC,IAAK,QAAQgC,SAASpB,KAAKoI,UAC9BhJ,EAAMmD,iBAGJ7I,EAAWsG,MACb,OAGF,MAAM9C,EAAS0J,EAAekB,uBAAuB9H,OAASA,KAAKzG,QAAQ,IAAI8B,KAC9D4M,EAAU9B,oBAAoBjJ,GAGtCgL,IACX,KCXIrC,EAAY,YAEZwC,EAAc,QAAQxC,IACtByC,EAAe,SAASzC,IAQ9B,MAAM0C,UAAchD,EAElB,eAAWjK,GACT,MAhBS,OAiBX,CAGAkN,QAGE,GAFmBjI,EAAasB,QAAQ7B,KAAKyF,SAAU4C,GAExCpG,iBACb,OAGFjC,KAAKyF,SAAS5L,UAAUlC,OApBJ,QAsBpB,MAAMsO,EAAajG,KAAKyF,SAAS5L,UAAUC,SAvBvB,QAwBpBkG,KAAKgG,gBAAe,IAAMhG,KAAKyI,mBAAmBzI,KAAKyF,SAAUQ,EACnE,CAGAwC,kBACEzI,KAAKyF,SAAS9N,SACd4I,EAAasB,QAAQ7B,KAAKyF,SAAU6C,GACpCtI,KAAK4F,SACP,CAGA,sBAAOnK,CAAgB+I,GACrB,OAAOxE,KAAK0I,MAAK,WACf,MAAMC,EAAOJ,EAAMpC,oBAAoBnG,MAEvC,GAAsB,iBAAXwE,EAAX,CAIA,QAAqBoE,IAAjBD,EAAKnE,IAAyBA,EAAO/C,WAAW,MAAmB,gBAAX+C,EAC1D,MAAM,IAAIa,UAAU,oBAAoBb,MAG1CmE,EAAKnE,GAAQxE,KANb,CAOF,GACF,EAOFgI,EAAqBO,EAAO,SAM5BtN,EAAmBsN,GCrEnB,MAMMM,EAAuB,4BAO7B,MAAMC,UAAevD,EAEnB,eAAWjK,GACT,MAhBS,QAiBX,CAGAyN,SAEE/I,KAAKyF,SAASjC,aAAa,eAAgBxD,KAAKyF,SAAS5L,UAAUkP,OAjB7C,UAkBxB,CAGA,sBAAOtN,CAAgB+I,GACrB,OAAOxE,KAAK0I,MAAK,WACf,MAAMC,EAAOG,EAAO3C,oBAAoBnG,MAEzB,WAAXwE,GACFmE,EAAKnE,IAET,GACF,EAOFjE,EAAac,GAAGtI,SAlCa,2BAkCmB8P,GAAsBzJ,IACpEA,EAAMmD,iBAEN,MAAMyG,EAAS5J,EAAMlC,OAAO3D,QAAQsP,GACvBC,EAAO3C,oBAAoB6C,GAEnCD,YAOP9N,EAAmB6N,GCtDnB,MACMjD,EAAY,YACZoD,EAAmB,aAAapD,IAChCqD,EAAkB,YAAYrD,IAC9BsD,EAAiB,WAAWtD,IAC5BuD,GAAoB,cAAcvD,IAClCwD,GAAkB,YAAYxD,IAM9BzB,GAAU,CACdkF,YAAa,KACbC,aAAc,KACdC,cAAe,MAGXnF,GAAc,CAClBiF,YAAa,kBACbC,aAAc,kBACdC,cAAe,mBAOjB,MAAMC,WAActF,EAClBU,YAAY9N,EAASyN,GACnBgB,QACAxF,KAAKyF,SAAW1O,EAEXA,GAAY0S,GAAMC,gBAIvB1J,KAAK0F,QAAU1F,KAAKuE,WAAWC,GAC/BxE,KAAK2J,QAAU,EACf3J,KAAK4J,sBAAwB9I,QAAQ9I,OAAO6R,cAC5C7J,KAAK8J,cACP,CAGA,kBAAW1F,GACT,OAAOA,EACT,CAEA,sBAAWC,GACT,OAAOA,EACT,CAEA,eAAW/I,GACT,MArDS,OAsDX,CAGAsK,UACErF,EAAaC,IAAIR,KAAKyF,SAAUI,EAClC,CAGAkE,OAAO3K,GACAY,KAAK4J,sBAMN5J,KAAKgK,wBAAwB5K,KAC/BY,KAAK2J,QAAUvK,EAAM6K,SANrBjK,KAAK2J,QAAUvK,EAAM8K,QAAQ,GAAGD,OAQpC,CAEAE,KAAK/K,GACCY,KAAKgK,wBAAwB5K,KAC/BY,KAAK2J,QAAUvK,EAAM6K,QAAUjK,KAAK2J,SAGtC3J,KAAKoK,eACLrO,EAAQiE,KAAK0F,QAAQ4D,YACvB,CAEAe,MAAMjL,GACJY,KAAK2J,QAAUvK,EAAM8K,SAAW9K,EAAM8K,QAAQpR,OAAS,EACrD,EACAsG,EAAM8K,QAAQ,GAAGD,QAAUjK,KAAK2J,OACpC,CAEAS,eACE,MAAME,EAAYzM,KAAK0M,IAAIvK,KAAK2J,SAEhC,GAAIW,GAlFgB,GAmFlB,OAGF,MAAME,EAAYF,EAAYtK,KAAK2J,QAEnC3J,KAAK2J,QAAU,EAEVa,GAILzO,EAAQyO,EAAY,EAAIxK,KAAK0F,QAAQ8D,cAAgBxJ,KAAK0F,QAAQ6D,aACpE,CAEAO,cACM9J,KAAK4J,uBACPrJ,EAAac,GAAGrB,KAAKyF,SAAU2D,IAAmBhK,GAASY,KAAK+J,OAAO3K,KACvEmB,EAAac,GAAGrB,KAAKyF,SAAU4D,IAAiBjK,GAASY,KAAKmK,KAAK/K,KAEnEY,KAAKyF,SAAS5L,UAAU4Q,IAvGG,mBAyG3BlK,EAAac,GAAGrB,KAAKyF,SAAUwD,GAAkB7J,GAASY,KAAK+J,OAAO3K,KACtEmB,EAAac,GAAGrB,KAAKyF,SAAUyD,GAAiB9J,GAASY,KAAKqK,MAAMjL,KACpEmB,EAAac,GAAGrB,KAAKyF,SAAU0D,GAAgB/J,GAASY,KAAKmK,KAAK/K,KAEtE,CAEA4K,wBAAwB5K,GACtB,OAAOY,KAAK4J,wBAjHS,QAiHiBxK,EAAMsL,aAlHrB,UAkHyDtL,EAAMsL,YACxF,CAGA,kBAAOhB,GACL,MAAO,iBAAkB3Q,SAASoB,iBAAmBwQ,UAAUC,eAAiB,CAClF,ECrHF,MAEM/E,GAAY,eACZgF,GAAe,YAEfC,GAAiB,YACjBC,GAAkB,aAGlBC,GAAa,OACbC,GAAa,OACbC,GAAiB,OACjBC,GAAkB,QAElBC,GAAc,QAAQvF,KACtBwF,GAAa,OAAOxF,KACpByF,GAAgB,UAAUzF,KAC1B0F,GAAmB,aAAa1F,KAChC2F,GAAmB,aAAa3F,KAChC4F,GAAmB,YAAY5F,KAC/B6F,GAAsB,OAAO7F,KAAYgF,KACzCc,GAAuB,QAAQ9F,KAAYgF,KAE3Ce,GAAsB,WACtBC,GAAoB,SAMpBC,GAAoB,YACpBC,GAAkB,UAClBC,GAAmB,QACnBC,GAAkB,OAElBC,GAAkB,UAClBC,GAAgB,iBAChBC,GAAuBF,GAAkBC,GAOzCE,GAAyB,+BACzBC,GAA6B,iBAC7BC,GAA8B,oBAC9BC,GAA+B,qBAC/BC,GAAsC,gBACtCC,GAAuC,iBAIvCC,GAAmB,CACvBC,CAAC9B,IAAiBK,GAClB0B,CAAC9B,IAAkBG,IAGf9G,GAAU,CACd0I,SAAU,IACVC,UAAU,EACVC,MAAO,QACPC,MAAM,EACNC,OAAO,EACPC,MAAM,GAGF9I,GAAc,CAClByI,SAAU,mBACVC,SAAU,UACVC,MAAO,mBACPC,KAAM,mBACNC,MAAO,UACPC,KAAM,WAOR,MAAMC,WAAiB7H,EACrBV,YAAY9N,EAASyN,GACnBgB,MAAMzO,EAASyN,GAEfxE,KAAKqN,UAAY,KACjBrN,KAAKsN,eAAiB,KACtBtN,KAAKuN,YAAa,EAClBvN,KAAKwN,aAAe,KACpBxN,KAAKyN,aAAe,KAEpBzN,KAAK0N,mBAAqB9G,EAAeG,QAnDjB,uBAmD8C/G,KAAKyF,UAE3EzF,KAAK2N,iBAAmB/G,EAAeG,QAAQ,GAAGsF,MAA0BC,QAAgCtM,KAAKyF,SAASpN,QAE1H2H,KAAK4N,qBAED5N,KAAK0F,QAAQuH,OAASrB,GACxB5L,KAAK6N,QACI7N,KAAK0N,oBACd1N,KAAKyF,SAAS5L,UAAU4Q,IAAIqB,GAGhC,CAGA,kBAAW1H,GACT,OAAOA,EACT,CAEA,sBAAWC,GACT,OAAOA,EACT,CAEA,eAAW/I,GACT,MAlHS,UAmHX,CAGAkM,OACExH,KAAK8N,OAAO9C,GACd,CAEA+C,mBAIOhV,SAASiV,QAAU/U,EAAU+G,KAAKyF,WACrCzF,KAAKwH,MAET,CAEAH,OACErH,KAAK8N,OAAO7C,GACd,CAEA+B,QAEMhN,KAAK0N,oBACP1N,KAAKyF,SAAS5L,UAAU4Q,IAAIqB,IAKA,OAA1B9L,KAAK2N,kBAA6B3N,KAAK2N,iBAAiB9T,UAAUC,SAASkS,MAC7EhM,KAAK2N,iBAAiB9T,UAAUlC,OAAOqU,IACvChM,KAAK2N,iBAAiB9T,UAAU4Q,IAAIwB,IAEhCjM,KAAK2N,iBAAiB1T,aAAasS,KACrCvM,KAAK2N,iBAAiBnK,aAAa,QAASxD,KAAK2N,iBAAiB1T,aAAasS,KAC/EvM,KAAK2N,iBAAiB3U,cAAc,wBAAwBiV,UAAYjO,KAAK2N,iBAAiB1T,aAAasS,MAE3GvM,KAAK2N,iBAAiBnK,aAAa,QAASiJ,IAC5CzM,KAAK2N,iBAAiB3U,cAAc,wBAAwBiV,UAAYxB,IAG1EzM,KAAKkO,aAAc,GAIjBlO,KAAKuN,YACPjV,EAAqB0H,KAAKyF,UAG5BzF,KAAKmO,gBACP,CAEAN,QAEM7N,KAAK0N,oBACP1N,KAAKyF,SAAS5L,UAAUlC,OAAOmU,IAKH,OAA1B9L,KAAK2N,kBAA6B3N,KAAK2N,iBAAiB9T,UAAUC,SAASmS,MAC7EjM,KAAK2N,iBAAiB9T,UAAUlC,OAAOsU,IACvCjM,KAAK2N,iBAAiB9T,UAAU4Q,IAAIuB,IAEhChM,KAAK2N,iBAAiB1T,aAAauS,KACrCxM,KAAK2N,iBAAiBnK,aAAa,QAASxD,KAAK2N,iBAAiB1T,aAAauS,KAC/ExM,KAAK2N,iBAAiB3U,cAAc,wBAAwBiV,UAAYjO,KAAK2N,iBAAiB1T,aAAauS,MAE3GxM,KAAK2N,iBAAiBnK,aAAa,QAASkJ,IAC5C1M,KAAK2N,iBAAiB3U,cAAc,wBAAwBiV,UAAYvB,IAG1E1M,KAAKkO,aAAc,GAIrBlO,KAAKmO,iBACLnO,KAAKoO,kBAELpO,KAAKqN,UAAYgB,aAAY,IAAMrO,KAAK+N,mBAAmB/N,KAAK0F,QAAQoH,SAC1E,CAEAwB,oBACOtO,KAAK0F,QAAQuH,OAIdjN,KAAKuN,WACPhN,EAAae,IAAItB,KAAKyF,SAAU4F,IAAY,IAAMrL,KAAK6N,UAIzD7N,KAAK6N,QACP,CAEAU,GAAG5Q,GAEGqC,KAAK0N,oBACP1N,KAAKyF,SAAS5L,UAAUlC,OAAOoU,IAIjC,MAAMyC,EAAQxO,KAAKyO,YACnB,GAAI9Q,EAAQ6Q,EAAM1V,OAAS,GAAK6E,EAAQ,EACtC,OAGF,GAAIqC,KAAKuN,WAEP,YADAhN,EAAae,IAAItB,KAAKyF,SAAU4F,IAAY,IAAMrL,KAAKuO,GAAG5Q,KAI5D,MAAM+Q,EAAc1O,KAAK2O,cAAc3O,KAAK4O,cAC5C,GAAIF,IAAgB/Q,EAClB,OAGF,MAAMkR,EAAQlR,EAAQ+Q,EAAc1D,GAAaC,GAEjDjL,KAAK8N,OAAOe,EAAOL,EAAM7Q,GAC3B,CAEAiI,UACM5F,KAAKyN,cACPzN,KAAKyN,aAAa7H,UAGpBJ,MAAMI,SACR,CAGAlB,kBAAkBF,GAEhB,OADAA,EAAOsK,gBAAkBtK,EAAOsI,SACzBtI,CACT,CAEAoJ,qBACM5N,KAAK0F,QAAQqH,UACfxM,EAAac,GAAGrB,KAAKyF,SAAU6F,IAAelM,GAASY,KAAK+O,SAAS3P,KAG5C,UAAvBY,KAAK0F,QAAQsH,QACfzM,EAAac,GAAGrB,KAAKyF,SAAU8F,IAAkB,IAAMvL,KAAKgN,UAC5DzM,EAAac,GAAGrB,KAAKyF,SAAU+F,IAAkB,IAAMxL,KAAKsO,uBAG1DtO,KAAK0F,QAAQwH,OAASzD,GAAMC,eAC9B1J,KAAKgP,yBAET,CAEAA,0BACE,IAAK,MAAMC,KAAOrI,EAAezH,KApOX,qBAoOmCa,KAAKyF,UAC5DlF,EAAac,GAAG4N,EAAKxD,IAAkBrM,GAASA,EAAMmD,mBAGxD,MAqBM2M,EAAc,CAClB3F,aAAcA,IAAMvJ,KAAK8N,OAAO9N,KAAKmP,kBAAkBjE,KACvD1B,cAAeA,IAAMxJ,KAAK8N,OAAO9N,KAAKmP,kBAAkBhE,KACxD7B,YAxBkB8F,KACS,UAAvBpP,KAAK0F,QAAQsH,QAYjBhN,KAAKgN,QACDhN,KAAKwN,cACP6B,aAAarP,KAAKwN,cAGpBxN,KAAKwN,aAAepQ,YAAW,IAAM4C,KAAKsO,qBAzRjB,IAyR+DtO,KAAK0F,QAAQoH,aASvG9M,KAAKyN,aAAe,IAAIhE,GAAMzJ,KAAKyF,SAAUyJ,EAC/C,CAEAH,SAAS3P,GACP,GAAI,kBAAkBgG,KAAKhG,EAAMlC,OAAOkL,SACtC,OAGF,MAAMoC,EAAYmC,GAAiBvN,EAAMpI,KACrCwT,IACFpL,EAAMmD,iBACNvC,KAAK8N,OAAO9N,KAAKmP,kBAAkB3E,IAEvC,CAGA8E,gBAAgBvY,GACW,WAArBA,EAAQwY,SACVxY,EAAQgD,UAAW,GAEnBhD,EAAQyM,aAAa,iBAAiB,GACtCzM,EAAQyM,aAAa,WAAY,MAErC,CAEAgM,eAAezY,GACY,WAArBA,EAAQwY,SACVxY,EAAQgD,UAAW,GAEnBhD,EAAQ2M,gBAAgB,iBACxB3M,EAAQ2M,gBAAgB,YAE5B,CAGAiL,cAAc5X,GACZ,OAAOiJ,KAAKyO,YAAY7Q,QAAQ7G,EAClC,CAEA0Y,2BAA2B9R,GACzB,IAAKqC,KAAK0N,mBACR,OAGF,MAAMgC,EAAkB9I,EAAeG,QAAQmF,GAAiBlM,KAAK0N,oBAErEgC,EAAgB7V,UAAUlC,OAAOkU,IACjC6D,EAAgBhM,gBAAgB,gBAEhC,MAAMiM,EAAqB/I,EAAeG,QAAQ,sBAAsBpJ,MAAWqC,KAAK0N,oBAEpFiC,IACFA,EAAmB9V,UAAU4Q,IAAIoB,IACjC8D,EAAmBnM,aAAa,eAAgB,QAEpD,CAEA4K,kBACE,MAAMrX,EAAUiJ,KAAKsN,gBAAkBtN,KAAK4O,aAE5C,IAAK7X,EACH,OAGF,MAAM6Y,EAAkBjT,OAAOkT,SAAS9Y,EAAQkD,aAAa,oBAAqB,IAKlF,GAHA+F,KAAK0F,QAAQoH,SAAW8C,GAAmB5P,KAAK0F,QAAQoJ,gBAGpD9O,KAAK0N,oBAAsB1N,KAAK0F,QAAQoH,WAAa1I,GAAQ0I,SAAU,CACzE,MAAMgD,EAAe9P,KAAK2O,cAAc5X,GACf6P,EAAeG,QAAQ,cAAc+I,EAAe,KAAM9P,KAAK0N,oBACvEqC,MAAMC,YAAY,yBAA6C,GAAGhQ,KAAK0F,QAAQoH,aAClG,CAEF,CAEAgB,OAAOe,EAAO9X,EAAU,MACtB,GAAIiJ,KAAKuN,WACP,OAGF,MAAMhQ,EAAgByC,KAAK4O,aACrBqB,EAASpB,IAAU7D,GAGzB,IAAKhL,KAAK0F,QAAQyH,KAAM,CACtB,MAAM+C,EAASrB,IAAU5D,GACnByD,EAAc1O,KAAK2O,cAAcpR,GACjC4S,EAAgBnQ,KAAKyO,YAAY3V,OAAS,EAGhD,GAFuBoX,GAA0B,IAAhBxB,GAAuBuB,GAAUvB,IAAgByB,EAQhF,OAJIF,GAAUjQ,KAAK0N,qBAAuB1N,KAAKyF,SAASzL,aAAa,kBACnEgG,KAAKyF,SAAS5L,UAAU4Q,IAAIsB,IAGvBxO,EAILyC,KAAK0N,oBACP1N,KAAKyF,SAAS5L,UAAUlC,OAAOoU,GAEnC,CAGA,MAAMqE,EAAcrZ,GAAWsG,EAAqB2C,KAAKyO,YAAalR,EAAe0S,EAAQjQ,KAAK0F,QAAQyH,MAE1G,GAAIiD,IAAgB7S,EAClB,OAGF,MAAM8S,EAAmBrQ,KAAK2O,cAAcyB,GAEtCE,EAAejK,GACZ9F,EAAasB,QAAQ7B,KAAKyF,SAAUY,EAAW,CACpDvG,cAAesQ,EACf5F,UAAWxK,KAAKuQ,kBAAkB1B,GAClCpX,KAAMuI,KAAK2O,cAAcpR,GACzBgR,GAAI8B,IAMR,GAFmBC,EAAalF,IAEjBnJ,iBACb,OAGF,IAAK1E,IAAkB6S,EAGrB,OAGF,MAAMI,EAAY1P,QAAQd,KAAKqN,WAS/B,GARArN,KAAKgN,QAELhN,KAAKuN,YAAa,EAElBvN,KAAKyP,2BAA2BY,GAChCrQ,KAAKsN,eAAiB8C,GAGjBpQ,KAAK0F,QAAQyH,KAAM,CACtB,MAAMsD,EAAc7J,EAAeG,QAlZX,yBAkZ0C/G,KAAKyF,UACjEiL,EAAc9J,EAAeG,QAlZX,yBAkZ0C/G,KAAKyF,UAEvEzF,KAAKwP,eAAeiB,GACpBzQ,KAAKwP,eAAekB,GAEK,IAArBL,EACFrQ,KAAKsP,gBAAgBmB,GACZJ,IAAsBrQ,KAAKyO,YAAY3V,OAAS,GACzDkH,KAAKsP,gBAAgBoB,EAEzB,CAGA,MAAMC,EAAuBV,EA/aR,sBADF,oBAibbW,EAAiBX,EA/aH,qBACA,qBAgbpBG,EAAYvW,UAAU4Q,IAAImG,GAE1BnW,EAAO2V,GAEP7S,EAAc1D,UAAU4Q,IAAIkG,GAC5BP,EAAYvW,UAAU4Q,IAAIkG,GAa1B3Q,KAAKgG,gBAXoB6K,KACvBT,EAAYvW,UAAUlC,OAAOgZ,EAAsBC,GACnDR,EAAYvW,UAAU4Q,IAAIoB,IAE1BtO,EAAc1D,UAAUlC,OAAOkU,GAAmB+E,EAAgBD,GAElE3Q,KAAKuN,YAAa,EAElB+C,EAAajF,MAGuB9N,EAAeyC,KAAK8Q,eAEtDN,GACFxQ,KAAK6N,OAET,CAEAiD,cACE,OAAO9Q,KAAKyF,SAAS5L,UAAUC,SA9cV,QA+cvB,CAEA8U,aACE,OAAOhI,EAAeG,QAAQqF,GAAsBpM,KAAKyF,SAC3D,CAEAgJ,YACE,OAAO7H,EAAezH,KAAKgN,GAAenM,KAAKyF,SACjD,CAEA0I,iBACMnO,KAAKqN,YACP0D,cAAc/Q,KAAKqN,WACnBrN,KAAKqN,UAAY,KAErB,CAEA8B,kBAAkB3E,GAChB,OAAIzP,IACKyP,IAAcU,GAAiBD,GAAaD,GAG9CR,IAAcU,GAAiBF,GAAaC,EACrD,CAEAsF,kBAAkB1B,GAChB,OAAI9T,IACK8T,IAAU5D,GAAaC,GAAiBC,GAG1C0D,IAAU5D,GAAaE,GAAkBD,EAClD,CAIA,oBAAO8F,CAAc5R,GACnB,MAAM6R,EAAc7R,EAAMlC,OACpBgU,EAAuBD,EAAYhX,aAAaqS,IAChD6E,EAAkB/D,GAASjH,oBAAoBpN,SAASC,cAAckY,IACxED,EAAYpX,UAAUC,SAASkS,IACjCmF,EAAgBnE,QAEhBmE,EAAgBtD,OAEpB,CAGA,sBAAOpS,CAAgB+I,GACrB,OAAOxE,KAAK0I,MAAK,WACf,MAAMC,EAAOyE,GAASjH,oBAAoBnG,KAAMwE,GAEhD,GAAsB,iBAAXA,GAKX,GAAsB,iBAAXA,EAAqB,CAC9B,QAAqBoE,IAAjBD,EAAKnE,IAAyBA,EAAO/C,WAAW,MAAmB,gBAAX+C,EAC1D,MAAM,IAAIa,UAAU,oBAAoBb,MAG1CmE,EAAKnE,IACP,OAVEmE,EAAK4F,GAAG/J,EAWZ,GACF,EAOFjE,EAAac,GAAGtI,SAAU4S,GAvgBE,uCAugByC,SAAUvM,GAC7E,MAAMlC,EAAS0J,EAAekB,uBAAuB9H,MAErD,IAAK9C,IAAWA,EAAOrD,UAAUC,SAAS8R,IACxC,OAGFxM,EAAMmD,iBAEN,MAAM6O,EAAWhE,GAASjH,oBAAoBjJ,GACxCmU,EAAarR,KAAK/F,aAAa,oBAErC,OAAIoX,GACFD,EAAS7C,GAAG8C,QACZD,EAAS9C,qBAIyC,SAAhDhL,EAAYY,iBAAiBlE,KAAM,UACrCoR,EAAS5J,YACT4J,EAAS9C,sBAIX8C,EAAS/J,YACT+J,EAAS9C,oBACX,IAEA/N,EAAac,GAAGtI,SAAU4S,GAAsBU,GAAwBe,GAAS4D,eAEjFzQ,EAAac,GAAGrJ,OAAQ0T,IAAqB,KAC3C,MAAM4F,EAAY1K,EAAezH,KAriBR,6BAuiBzB,IAAK,MAAMiS,KAAYE,EACrBlE,GAASjH,oBAAoBiL,MAQjCnW,EAAmBmS,IC9lBnB,MAEMvH,GAAY,eAGZ0L,GAAa,OAAO1L,KACpB2L,GAAc,QAAQ3L,KACtB4L,GAAa,OAAO5L,KACpB6L,GAAe,SAAS7L,KACxB8F,GAAuB,QAAQ9F,cAE/B8L,GAAkB,OAClBC,GAAsB,WACtBC,GAAwB,aAExBC,GAA6B,WAAWF,OAAwBA,KAOhE/I,GAAuB,8BAEvBzE,GAAU,CACd2N,OAAQ,KACRhJ,QAAQ,GAGJ1E,GAAc,CAClB0N,OAAQ,iBACRhJ,OAAQ,WAOV,MAAMiJ,WAAiBzM,EACrBV,YAAY9N,EAASyN,GACnBgB,MAAMzO,EAASyN,GAEfxE,KAAKiS,kBAAmB,EACxBjS,KAAKkS,cAAgB,GAErB,MAAMC,EAAavL,EAAezH,KAAK0J,IAEvC,IAAK,MAAMuJ,KAAQD,EAAY,CAC7B,MAAMpa,EAAW6O,EAAeiB,uBAAuBuK,GACjDC,EAAgBzL,EAAezH,KAAKpH,GACvCgM,QAAOuO,GAAgBA,IAAiBtS,KAAKyF,WAE/B,OAAb1N,GAAqBsa,EAAcvZ,QACrCkH,KAAKkS,cAAcpW,KAAKsW,EAE5B,CAEApS,KAAKuS,sBAEAvS,KAAK0F,QAAQqM,QAChB/R,KAAKwS,0BAA0BxS,KAAKkS,cAAelS,KAAKyS,YAGtDzS,KAAK0F,QAAQqD,QACf/I,KAAK+I,QAET,CAGA,kBAAW3E,GACT,OAAOA,EACT,CAEA,sBAAWC,GACT,OAAOA,EACT,CAEA,eAAW/I,GACT,MA9ES,UA+EX,CAGAyN,SACM/I,KAAKyS,WACPzS,KAAK0S,OAEL1S,KAAK2S,MAET,CAEAA,OACE,GAAI3S,KAAKiS,kBAAoBjS,KAAKyS,WAChC,OAGF,IAAIG,EAAiB,GASrB,GANI5S,KAAK0F,QAAQqM,SACfa,EAAiB5S,KAAK6S,uBA9EH,wCA+EhB9O,QAAOhN,GAAWA,IAAYiJ,KAAKyF,WACnCgB,KAAI1P,GAAWib,GAAS7L,oBAAoBpP,EAAS,CAAEgS,QAAQ,OAGhE6J,EAAe9Z,QAAU8Z,EAAe,GAAGX,iBAC7C,OAIF,GADmB1R,EAAasB,QAAQ7B,KAAKyF,SAAU8L,IACxCtP,iBACb,OAGF,IAAK,MAAM6Q,KAAkBF,EAC3BE,EAAeJ,OAGjB,MAAMK,EAAY/S,KAAKgT,gBAEvBhT,KAAKyF,SAAS5L,UAAUlC,OAAOia,IAC/B5R,KAAKyF,SAAS5L,UAAU4Q,IAAIoH,IAE5B7R,KAAKyF,SAASsK,MAAMgD,GAAa,EAEjC/S,KAAKwS,0BAA0BxS,KAAKkS,eAAe,GACnDlS,KAAKiS,kBAAmB,EAExB,MAYMgB,EAAa,SADUF,EAAU,GAAGzN,cAAgByN,EAAUpR,MAAM,KAG1E3B,KAAKgG,gBAdYkN,KACflT,KAAKiS,kBAAmB,EAExBjS,KAAKyF,SAAS5L,UAAUlC,OAAOka,IAC/B7R,KAAKyF,SAAS5L,UAAU4Q,IAAImH,GAAqBD,IAEjD3R,KAAKyF,SAASsK,MAAMgD,GAAa,GAEjCxS,EAAasB,QAAQ7B,KAAKyF,SAAU+L,MAMRxR,KAAKyF,UAAU,GAC7CzF,KAAKyF,SAASsK,MAAMgD,GAAa,GAAG/S,KAAKyF,SAASwN,MACpD,CAEAP,OACE,GAAI1S,KAAKiS,mBAAqBjS,KAAKyS,WACjC,OAIF,GADmBlS,EAAasB,QAAQ7B,KAAKyF,SAAUgM,IACxCxP,iBACb,OAGF,MAAM8Q,EAAY/S,KAAKgT,gBAEvBhT,KAAKyF,SAASsK,MAAMgD,GAAa,GAAG/S,KAAKyF,SAAS0N,wBAAwBJ,OAE1EtY,EAAOuF,KAAKyF,UAEZzF,KAAKyF,SAAS5L,UAAU4Q,IAAIoH,IAC5B7R,KAAKyF,SAAS5L,UAAUlC,OAAOia,GAAqBD,IAEpD3R,KAAKiS,kBAAmB,EAoBxBjS,KAAKyF,SAASsK,MAAMgD,GAAa,GAEjC/S,KAAKgG,gBApBYkN,KACflT,KAAKiS,kBAAmB,EACxBjS,KAAKyF,SAAS5L,UAAUlC,OAAOka,IAC/B7R,KAAKyF,SAAS5L,UAAU4Q,IAAImH,IAG5B,IAAK,MAAM/P,KAAW7B,KAAKkS,cAAe,CACxC,MAAMnb,EAAU6P,EAAekB,uBAAuBjG,GAElD9K,IAAYiJ,KAAKyS,SAAS1b,IAC5BiJ,KAAKwS,0BAA0B,CAAC3Q,IAAU,EAE9C,CAGAtB,EAAasB,QAAQ7B,KAAKyF,SAAUiM,MAKR1R,KAAKyF,UAAU,EAC/C,CAGAgN,SAAS1b,EAAUiJ,KAAKyF,UACtB,OAAO1O,EAAQ8C,UAAUC,SAAS6X,GACpC,CAEAjN,kBAAkBF,GAGhB,OAFAA,EAAOuE,OAASjI,QAAQ0D,EAAOuE,QAC/BvE,EAAOuN,OAASlZ,EAAW2L,EAAOuN,QAC3BvN,CACT,CAEAwO,gBACE,OAAOhT,KAAKyF,SAAS5L,UAAUC,SAzLL,uBAEhB,QACC,QAuLb,CAEAyY,sBACE,IAAKvS,KAAK0F,QAAQqM,OAChB,OAGF,MAAM/K,EAAWhH,KAAK6S,uBAAuBhK,IAE7C,IAAK,MAAM9R,KAAWiQ,EAAU,CAC9B,MAAMoM,EAAWxM,EAAekB,uBAAuB/Q,GAEnDqc,GACFpT,KAAKwS,0BAA0B,CAACzb,GAAUiJ,KAAKyS,SAASW,GAE5D,CACF,CAEAP,uBAAuB9a,GACrB,MAAMiP,EAAWJ,EAAezH,KAAK2S,GAA4B9R,KAAK0F,QAAQqM,QAE9E,OAAOnL,EAAezH,KAAKpH,EAAUiI,KAAK0F,QAAQqM,QAAQhO,QAAOhN,IAAYiQ,EAAS5F,SAASrK,IACjG,CAEAyb,0BAA0Ba,EAAcC,GACtC,GAAKD,EAAava,OAIlB,IAAK,MAAM/B,KAAWsc,EACpBtc,EAAQ8C,UAAUkP,OA1NK,aA0NyBuK,GAChDvc,EAAQyM,aAAa,gBAAiB8P,EAE1C,CAGA,sBAAO7X,CAAgB+I,GACrB,MAAMkB,EAAU,GAKhB,MAJsB,iBAAXlB,GAAuB,YAAYY,KAAKZ,KACjDkB,EAAQqD,QAAS,GAGZ/I,KAAK0I,MAAK,WACf,MAAMC,EAAOqJ,GAAS7L,oBAAoBnG,KAAM0F,GAEhD,GAAsB,iBAAXlB,EAAqB,CAC9B,QAA4B,IAAjBmE,EAAKnE,GACd,MAAM,IAAIa,UAAU,oBAAoBb,MAG1CmE,EAAKnE,IACP,CACF,GACF,EAOFjE,EAAac,GAAGtI,SAAU4S,GAAsB9C,IAAsB,SAAUzJ,IAEjD,MAAzBA,EAAMlC,OAAOkL,SAAoBhJ,EAAMW,gBAAmD,MAAjCX,EAAMW,eAAeqI,UAChFhJ,EAAMmD,iBAGR,IAAK,MAAMxL,KAAW6P,EAAemB,gCAAgC/H,MACnEgS,GAAS7L,oBAAoBpP,EAAS,CAAEgS,QAAQ,IAASA,QAE7D,IAMA9N,EAAmB+W,ICzSZ,IAAIuB,GAAM,MACNC,GAAS,SACTC,GAAQ,QACRC,GAAO,OACPC,GAAO,OACPC,GAAiB,CAACL,GAAKC,GAAQC,GAAOC,IACtCG,GAAQ,QACRC,GAAM,MACNC,GAAkB,kBAClBC,GAAW,WACXC,GAAS,SACTC,GAAY,YACZC,GAAmCP,GAAeQ,QAAO,SAAUC,EAAKC,GACjF,OAAOD,EAAIxN,OAAO,CAACyN,EAAY,IAAMT,GAAOS,EAAY,IAAMR,IAChE,GAAG,IACQS,GAA0B,GAAG1N,OAAO+M,GAAgB,CAACD,KAAOS,QAAO,SAAUC,EAAKC,GAC3F,OAAOD,EAAIxN,OAAO,CAACyN,EAAWA,EAAY,IAAMT,GAAOS,EAAY,IAAMR,IAC3E,GAAG,IAEQU,GAAa,aACbC,GAAO,OACPC,GAAY,YAEZC,GAAa,aACbC,GAAO,OACPC,GAAY,YAEZC,GAAc,cACdC,GAAQ,QACRC,GAAa,aACbC,GAAiB,CAACT,GAAYC,GAAMC,GAAWC,GAAYC,GAAMC,GAAWC,GAAaC,GAAOC,IC9B5F,SAASE,GAAYne,GAClC,OAAOA,GAAWA,EAAQwY,UAAY,IAAIlM,cAAgB,IAC5D,CCFe,SAAS8R,GAAUC,GAChC,GAAY,MAARA,EACF,OAAOpd,OAGT,GAAwB,oBAApBod,EAAKrS,WAAkC,CACzC,IAAIsS,EAAgBD,EAAKC,cACzB,OAAOA,GAAgBA,EAAcC,aAAwBtd,MACjE,CAEE,OAAOod,CACT,CCTA,SAAS3c,GAAU2c,GAEjB,OAAOA,aADUD,GAAUC,GAAMtO,SACIsO,aAAgBtO,OACvD,CAEA,SAASyO,GAAcH,GAErB,OAAOA,aADUD,GAAUC,GAAMI,aACIJ,aAAgBI,WACvD,CAEA,SAASC,GAAaL,GAEpB,MAA0B,oBAAf7a,aAKJ6a,aADUD,GAAUC,GAAM7a,YACI6a,aAAgB7a,WACvD,CCwDA,MAAAmb,GAAe,CACbra,KAAM,cACNsa,SAAS,EACTC,MAAO,QACPpa,GA5EF,SAAqBqa,GACnB,IAAIC,EAAQD,EAAKC,MACjB7W,OAAOvH,KAAKoe,EAAMC,UAAUC,SAAQ,SAAU3a,GAC5C,IAAI0U,EAAQ+F,EAAMG,OAAO5a,IAAS,GAC9BuI,EAAakS,EAAMlS,WAAWvI,IAAS,GACvCtE,EAAU+e,EAAMC,SAAS1a,GAExBka,GAAcxe,IAAame,GAAYne,KAO5CkI,OAAOiX,OAAOnf,EAAQgZ,MAAOA,GAC7B9Q,OAAOvH,KAAKkM,GAAYoS,SAAQ,SAAU3a,GACxC,IAAIqH,EAAQkB,EAAWvI,IAET,IAAVqH,EACF3L,EAAQ2M,gBAAgBrI,GAExBtE,EAAQyM,aAAanI,GAAgB,IAAVqH,EAAiB,GAAKA,EAEzD,IACA,GACA,EAoDEyT,OAlDF,SAAgBC,GACd,IAAIN,EAAQM,EAAMN,MACdO,EAAgB,CAClBpC,OAAQ,CACNqC,SAAUR,EAAMS,QAAQC,SACxB9C,KAAM,IACNH,IAAK,IACLkD,OAAQ,KAEVC,MAAO,CACLJ,SAAU,YAEZpC,UAAW,IASb,OAPAjV,OAAOiX,OAAOJ,EAAMC,SAAS9B,OAAOlE,MAAOsG,EAAcpC,QACzD6B,EAAMG,OAASI,EAEXP,EAAMC,SAASW,OACjBzX,OAAOiX,OAAOJ,EAAMC,SAASW,MAAM3G,MAAOsG,EAAcK,OAGnD,WACLzX,OAAOvH,KAAKoe,EAAMC,UAAUC,SAAQ,SAAU3a,GAC5C,IAAItE,EAAU+e,EAAMC,SAAS1a,GACzBuI,EAAakS,EAAMlS,WAAWvI,IAAS,GAGvC0U,EAFkB9Q,OAAOvH,KAAKoe,EAAMG,OAAOU,eAAetb,GAAQya,EAAMG,OAAO5a,GAAQgb,EAAchb,IAE7E+Y,QAAO,SAAUrE,EAAOhL,GAElD,OADAgL,EAAMhL,GAAY,GACXgL,CACf,GAAS,IAEEwF,GAAcxe,IAAame,GAAYne,KAI5CkI,OAAOiX,OAAOnf,EAAQgZ,MAAOA,GAC7B9Q,OAAOvH,KAAKkM,GAAYoS,SAAQ,SAAUY,GACxC7f,EAAQ2M,gBAAgBkT,EAChC,IACA,GACA,CACA,EASEC,SAAU,CAAC,kBCjFE,SAASC,GAAiBxC,GACvC,OAAOA,EAAUxX,MAAM,KAAK,EAC9B,CCHO,IAAIgB,GAAMD,KAAKC,IACXC,GAAMF,KAAKE,IACXgZ,GAAQlZ,KAAKkZ,MCFT,SAASC,KACtB,IAAIC,EAAStM,UAAUuM,cAEvB,OAAc,MAAVD,GAAkBA,EAAOE,QAAU3f,MAAM4f,QAAQH,EAAOE,QACnDF,EAAOE,OAAO1Q,KAAI,SAAU4Q,GACjC,OAAOA,EAAKC,MAAQ,IAAMD,EAAKE,OACrC,IAAO5Q,KAAK,KAGHgE,UAAU6M,SACnB,CCTe,SAASC,KACtB,OAAQ,iCAAiCrS,KAAK4R,KAChD,CCCe,SAAS7D,GAAsBpc,EAAS2gB,EAAcC,QAC9C,IAAjBD,IACFA,GAAe,QAGO,IAApBC,IACFA,GAAkB,GAGpB,IAAIC,EAAa7gB,EAAQoc,wBACrB0E,EAAS,EACTC,EAAS,EAETJ,GAAgBnC,GAAcxe,KAChC8gB,EAAS9gB,EAAQghB,YAAc,GAAIhB,GAAMa,EAAWI,OAASjhB,EAAQghB,aAAmB,EACxFD,EAAS/gB,EAAQ2D,aAAe,GAAIqc,GAAMa,EAAWK,QAAUlhB,EAAQ2D,cAAoB,GAG7F,IACIwd,GADOzf,GAAU1B,GAAWoe,GAAUpe,GAAWiB,QAC3BkgB,eAEtBC,GAAoBV,MAAsBE,EAC1CS,GAAKR,EAAWlE,MAAQyE,GAAoBD,EAAiBA,EAAeG,WAAa,IAAMR,EAC/FS,GAAKV,EAAWrE,KAAO4E,GAAoBD,EAAiBA,EAAeK,UAAY,IAAMT,EAC7FE,EAAQJ,EAAWI,MAAQH,EAC3BI,EAASL,EAAWK,OAASH,EACjC,MAAO,CACLE,MAAOA,EACPC,OAAQA,EACR1E,IAAK+E,EACL7E,MAAO2E,EAAIJ,EACXxE,OAAQ8E,EAAIL,EACZvE,KAAM0E,EACNA,EAAGA,EACHE,EAAGA,EAEP,CCrCe,SAASE,GAAczhB,GACpC,IAAI6gB,EAAazE,GAAsBpc,GAGnCihB,EAAQjhB,EAAQghB,YAChBE,EAASlhB,EAAQ2D,aAUrB,OARImD,KAAK0M,IAAIqN,EAAWI,MAAQA,IAAU,IACxCA,EAAQJ,EAAWI,OAGjBna,KAAK0M,IAAIqN,EAAWK,OAASA,IAAW,IAC1CA,EAASL,EAAWK,QAGf,CACLG,EAAGrhB,EAAQshB,WACXC,EAAGvhB,EAAQwhB,UACXP,MAAOA,EACPC,OAAQA,EAEZ,CCvBe,SAASne,GAASiY,EAAQ9K,GACvC,IAAIwR,EAAWxR,EAAM5M,aAAe4M,EAAM5M,cAE1C,GAAI0X,EAAOjY,SAASmN,GAClB,OAAO,EAEJ,GAAIwR,GAAYhD,GAAagD,GAAW,CACzC,IAAIjR,EAAOP,EAEX,EAAG,CACD,GAAIO,GAAQuK,EAAO2G,WAAWlR,GAC5B,OAAO,EAITA,EAAOA,EAAK/N,YAAc+N,EAAKmR,IACvC,OAAenR,EACf,CAGE,OAAO,CACT,CCrBe,SAASpO,GAAiBrC,GACvC,OAAOoe,GAAUpe,GAASqC,iBAAiBrC,EAC7C,CCFe,SAAS6hB,GAAe7hB,GACrC,MAAO,CAAC,QAAS,KAAM,MAAM6G,QAAQsX,GAAYne,KAAa,CAChE,CCFe,SAAS8hB,GAAmB9hB,GAEzC,QAAS0B,GAAU1B,GAAWA,EAAQse,cACtCte,EAAQgC,WAAaf,OAAOe,UAAUoB,eACxC,CCFe,SAAS2e,GAAc/hB,GACpC,MAA6B,SAAzBme,GAAYne,GACPA,EAMPA,EAAQgiB,cACRhiB,EAAQ0C,aACRgc,GAAa1e,GAAWA,EAAQ4hB,KAAO,OAEvCE,GAAmB9hB,EAGvB,CCVA,SAASiiB,GAAoBjiB,GAC3B,OAAKwe,GAAcxe,IACoB,UAAvCqC,GAAiBrC,GAASuf,SAInBvf,EAAQkiB,aAHN,IAIX,CAwCe,SAASC,GAAgBniB,GAItC,IAHA,IAAIiB,EAASmd,GAAUpe,GACnBkiB,EAAeD,GAAoBjiB,GAEhCkiB,GAAgBL,GAAeK,IAA6D,WAA5C7f,GAAiB6f,GAAc3C,UACpF2C,EAAeD,GAAoBC,GAGrC,OAAIA,IAA+C,SAA9B/D,GAAY+D,IAA0D,SAA9B/D,GAAY+D,IAAwE,WAA5C7f,GAAiB6f,GAAc3C,UAC3Hte,EAGFihB,GAhDT,SAA4BliB,GAC1B,IAAIoiB,EAAY,WAAW/T,KAAK4R,MAGhC,GAFW,WAAW5R,KAAK4R,OAEfzB,GAAcxe,IAII,UAFXqC,GAAiBrC,GAEnBuf,SACb,OAAO,KAIX,IAAI8C,EAAcN,GAAc/hB,GAMhC,IAJI0e,GAAa2D,KACfA,EAAcA,EAAYT,MAGrBpD,GAAc6D,IAAgB,CAAC,OAAQ,QAAQxb,QAAQsX,GAAYkE,IAAgB,GAAG,CAC3F,IAAIC,EAAMjgB,GAAiBggB,GAI3B,GAAsB,SAAlBC,EAAIC,WAA4C,SAApBD,EAAIE,aAA0C,UAAhBF,EAAIG,UAAgF,IAAzD,CAAC,YAAa,eAAe5b,QAAQyb,EAAII,aAAsBN,GAAgC,WAAnBE,EAAII,YAA2BN,GAAaE,EAAItV,QAAyB,SAAfsV,EAAItV,OACjO,OAAOqV,EAEPA,EAAcA,EAAY3f,UAEhC,CAEE,OAAO,IACT,CAgByBigB,CAAmB3iB,IAAYiB,CACxD,CCpEe,SAAS2hB,GAAyBrF,GAC/C,MAAO,CAAC,MAAO,UAAU1W,QAAQ0W,IAAc,EAAI,IAAM,GAC3D,CCDO,SAASsF,GAAO7b,EAAK2E,EAAO5E,GACjC,OAAO+b,GAAQ9b,EAAK+b,GAAQpX,EAAO5E,GACrC,CCFe,SAASic,GAAmBC,GACzC,OAAO/a,OAAOiX,OAAO,GCDd,CACL3C,IAAK,EACLE,MAAO,EACPD,OAAQ,EACRE,KAAM,GDHuCsG,EACjD,CEHe,SAASC,GAAgBvX,EAAOhL,GAC7C,OAAOA,EAAK0c,QAAO,SAAU8F,EAASljB,GAEpC,OADAkjB,EAAQljB,GAAO0L,EACRwX,CACX,GAAK,GACL,CC4EA,MAAAC,GAAe,CACb9e,KAAM,QACNsa,SAAS,EACTC,MAAO,OACPpa,GApEF,SAAeqa,GACb,IAAIuE,EAEAtE,EAAQD,EAAKC,MACbza,EAAOwa,EAAKxa,KACZkb,EAAUV,EAAKU,QACf8D,EAAevE,EAAMC,SAASW,MAC9B4D,EAAgBxE,EAAMyE,cAAcD,cACpCE,EAAgB1D,GAAiBhB,EAAMxB,WACvCmG,EAAOd,GAAyBa,GAEhCE,EADa,CAAChH,GAAMD,IAAO7V,QAAQ4c,IAAkB,EAClC,SAAW,QAElC,GAAKH,GAAiBC,EAAtB,CAIA,IAAIN,EAxBgB,SAAyBW,EAAS7E,GAItD,OAAOiE,GAAsC,iBAH7CY,EAA6B,mBAAZA,EAAyBA,EAAQ1b,OAAOiX,OAAO,GAAIJ,EAAM8E,MAAO,CAC/EtG,UAAWwB,EAAMxB,aACbqG,GACkDA,EAAUV,GAAgBU,EAAS/G,IAC7F,CAmBsBiH,CAAgBtE,EAAQoE,QAAS7E,GACjDgF,EAAYtC,GAAc6B,GAC1BU,EAAmB,MAATN,EAAelH,GAAMG,GAC/BsH,EAAmB,MAATP,EAAejH,GAASC,GAClCwH,EAAUnF,EAAM8E,MAAM1G,UAAUwG,GAAO5E,EAAM8E,MAAM1G,UAAUuG,GAAQH,EAAcG,GAAQ3E,EAAM8E,MAAM3G,OAAOyG,GAC9GQ,EAAYZ,EAAcG,GAAQ3E,EAAM8E,MAAM1G,UAAUuG,GACxDU,EAAoBjC,GAAgBmB,GACpCe,EAAaD,EAA6B,MAATV,EAAeU,EAAkBE,cAAgB,EAAIF,EAAkBG,aAAe,EAAI,EAC3HC,EAAoBN,EAAU,EAAIC,EAAY,EAG9Cnd,EAAMic,EAAce,GACpBjd,EAAMsd,EAAaN,EAAUJ,GAAOV,EAAcgB,GAClDQ,EAASJ,EAAa,EAAIN,EAAUJ,GAAO,EAAIa,EAC/CE,EAAS7B,GAAO7b,EAAKyd,EAAQ1d,GAE7B4d,EAAWjB,EACf3E,EAAMyE,cAAclf,KAAS+e,EAAwB,IAA0BsB,GAAYD,EAAQrB,EAAsBuB,aAAeF,EAASD,EAAQpB,EAnB3J,CAoBA,EAkCEjE,OAhCF,SAAgBC,GACd,IAAIN,EAAQM,EAAMN,MAEd8F,EADUxF,EAAMG,QACWxf,QAC3BsjB,OAAoC,IAArBuB,EAA8B,sBAAwBA,EAErD,MAAhBvB,IAKwB,iBAAjBA,IACTA,EAAevE,EAAMC,SAAS9B,OAAOjb,cAAcqhB,MAOhDvgB,GAASgc,EAAMC,SAAS9B,OAAQoG,KAIrCvE,EAAMC,SAASW,MAAQ2D,EACzB,EASExD,SAAU,CAAC,iBACXgF,iBAAkB,CAAC,oBCxFN,SAASC,GAAaxH,GACnC,OAAOA,EAAUxX,MAAM,KAAK,EAC9B,CCOA,IAAIif,GAAa,CACfxI,IAAK,OACLE,MAAO,OACPD,OAAQ,OACRE,KAAM,QAeD,SAASsI,GAAY5F,GAC1B,IAAI6F,EAEAhI,EAASmC,EAAMnC,OACfiI,EAAa9F,EAAM8F,WACnB5H,EAAY8B,EAAM9B,UAClB6H,EAAY/F,EAAM+F,UAClBC,EAAUhG,EAAMgG,QAChB9F,EAAWF,EAAME,SACjB+F,EAAkBjG,EAAMiG,gBACxBC,EAAWlG,EAAMkG,SACjBC,EAAenG,EAAMmG,aACrBC,EAAUpG,EAAMoG,QAChBC,EAAaL,EAAQhE,EACrBA,OAAmB,IAAfqE,EAAwB,EAAIA,EAChCC,EAAaN,EAAQ9D,EACrBA,OAAmB,IAAfoE,EAAwB,EAAIA,EAEhCC,EAAgC,mBAAjBJ,EAA8BA,EAAa,CAC5DnE,EAAGA,EACHE,EAAGA,IACA,CACHF,EAAGA,EACHE,EAAGA,GAGLF,EAAIuE,EAAMvE,EACVE,EAAIqE,EAAMrE,EACV,IAAIsE,EAAOR,EAAQzF,eAAe,KAC9BkG,EAAOT,EAAQzF,eAAe,KAC9BmG,EAAQpJ,GACRqJ,EAAQxJ,GACRyJ,EAAMhlB,OAEV,GAAIskB,EAAU,CACZ,IAAIrD,EAAeC,GAAgBjF,GAC/BgJ,EAAa,eACbC,EAAY,cAEZjE,IAAiB9D,GAAUlB,IAGmB,WAA5C7a,GAFJ6f,EAAeJ,GAAmB5E,IAECqC,UAAsC,aAAbA,IAC1D2G,EAAa,eACbC,EAAY,gBAOZ5I,IAAcf,KAAQe,IAAcZ,IAAQY,IAAcb,KAAU0I,IAAcrI,MACpFiJ,EAAQvJ,GAGR8E,IAFckE,GAAWvD,IAAiB+D,GAAOA,EAAI9E,eAAiB8E,EAAI9E,eAAeD,OACzFgB,EAAagE,IACEf,EAAWjE,OAC1BK,GAAK+D,EAAkB,GAAI,GAGzB/H,IAAcZ,KAASY,IAAcf,IAAOe,IAAcd,IAAW2I,IAAcrI,MACrFgJ,EAAQrJ,GAGR2E,IAFcoE,GAAWvD,IAAiB+D,GAAOA,EAAI9E,eAAiB8E,EAAI9E,eAAeF,MACzFiB,EAAaiE,IACEhB,EAAWlE,MAC1BI,GAAKiE,EAAkB,GAAI,EAEjC,CAEE,IAgBMc,EAhBFC,EAAene,OAAOiX,OAAO,CAC/BI,SAAUA,GACTgG,GAAYP,IAEXsB,GAAyB,IAAjBd,EAlFd,SAA2B1G,EAAMmH,GAC/B,IAAI5E,EAAIvC,EAAKuC,EACTE,EAAIzC,EAAKyC,EACTgF,EAAMN,EAAIO,kBAAoB,EAClC,MAAO,CACLnF,EAAGrB,GAAMqB,EAAIkF,GAAOA,GAAO,EAC3BhF,EAAGvB,GAAMuB,EAAIgF,GAAOA,GAAO,EAE/B,CA0EsCE,CAAkB,CACpDpF,EAAGA,EACHE,EAAGA,GACFnD,GAAUlB,IAAW,CACtBmE,EAAGA,EACHE,EAAGA,GAML,OAHAF,EAAIiF,EAAMjF,EACVE,EAAI+E,EAAM/E,EAEN+D,EAGKpd,OAAOiX,OAAO,GAAIkH,IAAeD,EAAiB,IAAmBJ,GAASF,EAAO,IAAM,GAAIM,EAAeL,GAASF,EAAO,IAAM,GAAIO,EAAe7D,WAAa0D,EAAIO,kBAAoB,IAAM,EAAI,aAAenF,EAAI,OAASE,EAAI,MAAQ,eAAiBF,EAAI,OAASE,EAAI,SAAU6E,IAG5Rle,OAAOiX,OAAO,GAAIkH,IAAenB,EAAkB,IAAoBc,GAASF,EAAOvE,EAAI,KAAO,GAAI2D,EAAgBa,GAASF,EAAOxE,EAAI,KAAO,GAAI6D,EAAgB3C,UAAY,GAAI2C,GAC9L,CA4CA,MAAAwB,GAAe,CACbpiB,KAAM,gBACNsa,SAAS,EACTC,MAAO,cACPpa,GA9CF,SAAuBkiB,GACrB,IAAI5H,EAAQ4H,EAAM5H,MACdS,EAAUmH,EAAMnH,QAChBoH,EAAwBpH,EAAQ8F,gBAChCA,OAA4C,IAA1BsB,GAA0CA,EAC5DC,EAAoBrH,EAAQ+F,SAC5BA,OAAiC,IAAtBsB,GAAsCA,EACjDC,EAAwBtH,EAAQgG,aAChCA,OAAyC,IAA1BsB,GAA0CA,EACzDT,EAAe,CACjB9I,UAAWwC,GAAiBhB,EAAMxB,WAClC6H,UAAWL,GAAahG,EAAMxB,WAC9BL,OAAQ6B,EAAMC,SAAS9B,OACvBiI,WAAYpG,EAAM8E,MAAM3G,OACxBoI,gBAAiBA,EACjBG,QAAoC,UAA3B1G,EAAMS,QAAQC,UAGgB,MAArCV,EAAMyE,cAAcD,gBACtBxE,EAAMG,OAAOhC,OAAShV,OAAOiX,OAAO,GAAIJ,EAAMG,OAAOhC,OAAQ+H,GAAY/c,OAAOiX,OAAO,GAAIkH,EAAc,CACvGhB,QAAStG,EAAMyE,cAAcD,cAC7BhE,SAAUR,EAAMS,QAAQC,SACxB8F,SAAUA,EACVC,aAAcA,OAIe,MAA7BzG,EAAMyE,cAAc7D,QACtBZ,EAAMG,OAAOS,MAAQzX,OAAOiX,OAAO,GAAIJ,EAAMG,OAAOS,MAAOsF,GAAY/c,OAAOiX,OAAO,GAAIkH,EAAc,CACrGhB,QAAStG,EAAMyE,cAAc7D,MAC7BJ,SAAU,WACVgG,UAAU,EACVC,aAAcA,OAIlBzG,EAAMlS,WAAWqQ,OAAShV,OAAOiX,OAAO,GAAIJ,EAAMlS,WAAWqQ,OAAQ,CACnE,wBAAyB6B,EAAMxB,WAEnC,EAQE3L,KAAM,ICrKR,IAAImV,GAAU,CACZA,SAAS,GAsCX,MAAAC,GAAe,CACb1iB,KAAM,iBACNsa,SAAS,EACTC,MAAO,QACPpa,GAAI,WAAc,EAClB2a,OAxCF,SAAgBN,GACd,IAAIC,EAAQD,EAAKC,MACb7e,EAAW4e,EAAK5e,SAChBsf,EAAUV,EAAKU,QACfyH,EAAkBzH,EAAQ0H,OAC1BA,OAA6B,IAApBD,GAAoCA,EAC7CE,EAAkB3H,EAAQ4H,OAC1BA,OAA6B,IAApBD,GAAoCA,EAC7ClmB,EAASmd,GAAUW,EAAMC,SAAS9B,QAClCmK,EAAgB,GAAGvX,OAAOiP,EAAMsI,cAAclK,UAAW4B,EAAMsI,cAAcnK,QAYjF,OAVIgK,GACFG,EAAcpI,SAAQ,SAAUqI,GAC9BA,EAAaxiB,iBAAiB,SAAU5E,EAASqnB,OAAQR,GAC/D,IAGMK,GACFnmB,EAAO6D,iBAAiB,SAAU5E,EAASqnB,OAAQR,IAG9C,WACDG,GACFG,EAAcpI,SAAQ,SAAUqI,GAC9BA,EAAalhB,oBAAoB,SAAUlG,EAASqnB,OAAQR,GACpE,IAGQK,GACFnmB,EAAOmF,oBAAoB,SAAUlG,EAASqnB,OAAQR,GAE5D,CACA,EASEnV,KAAM,IC/CR,IAAI4V,GAAO,CACT7K,KAAM,QACND,MAAO,OACPD,OAAQ,MACRD,IAAK,UAEQ,SAASiL,GAAqBlK,GAC3C,OAAOA,EAAUnc,QAAQ,0BAA0B,SAAUsmB,GAC3D,OAAOF,GAAKE,EAChB,GACA,CCVA,IAAIF,GAAO,CACT1K,MAAO,MACPC,IAAK,SAEQ,SAAS4K,GAA8BpK,GACpD,OAAOA,EAAUnc,QAAQ,cAAc,SAAUsmB,GAC/C,OAAOF,GAAKE,EAChB,GACA,CCPe,SAASE,GAAgBvJ,GACtC,IAAI4H,EAAM7H,GAAUC,GAGpB,MAAO,CACLwJ,WAHe5B,EAAI6B,YAInBC,UAHc9B,EAAI+B,YAKtB,CCNe,SAASC,GAAoBjoB,GAQ1C,OAAOoc,GAAsB0F,GAAmB9hB,IAAU2c,KAAOiL,GAAgB5nB,GAAS6nB,UAC5F,CCXe,SAASK,GAAeloB,GAErC,IAAImoB,EAAoB9lB,GAAiBrC,GACrCooB,EAAWD,EAAkBC,SAC7BC,EAAYF,EAAkBE,UAC9BC,EAAYH,EAAkBG,UAElC,MAAO,6BAA6Bja,KAAK+Z,EAAWE,EAAYD,EAClE,CCLe,SAASE,GAAgBlK,GACtC,MAAI,CAAC,OAAQ,OAAQ,aAAaxX,QAAQsX,GAAYE,KAAU,EAEvDA,EAAKC,cAAcxa,KAGxB0a,GAAcH,IAAS6J,GAAe7J,GACjCA,EAGFkK,GAAgBxG,GAAc1D,GACvC,CCJe,SAASmK,GAAkBxoB,EAASuG,GACjD,IAAIkiB,OAES,IAATliB,IACFA,EAAO,IAGT,IAAI+gB,EAAeiB,GAAgBvoB,GAC/B0oB,EAASpB,KAAqE,OAAlDmB,EAAwBzoB,EAAQse,oBAAyB,EAASmK,EAAsB3kB,MACpHmiB,EAAM7H,GAAUkJ,GAChBnhB,EAASuiB,EAAS,CAACzC,GAAKnW,OAAOmW,EAAI9E,gBAAkB,GAAI+G,GAAeZ,GAAgBA,EAAe,IAAMA,EAC7GqB,EAAcpiB,EAAKuJ,OAAO3J,GAC9B,OAAOuiB,EAASC,EAChBA,EAAY7Y,OAAO0Y,GAAkBzG,GAAc5b,IACrD,CCzBe,SAASyiB,GAAiBC,GACvC,OAAO3gB,OAAOiX,OAAO,GAAI0J,EAAM,CAC7BlM,KAAMkM,EAAKxH,EACX7E,IAAKqM,EAAKtH,EACV7E,MAAOmM,EAAKxH,EAAIwH,EAAK5H,MACrBxE,OAAQoM,EAAKtH,EAAIsH,EAAK3H,QAE1B,CCqBA,SAAS4H,GAA2B9oB,EAAS+oB,EAAgBtJ,GAC3D,OAAOsJ,IAAmB9L,GAAW2L,GCzBxB,SAAyB5oB,EAASyf,GAC/C,IAAIwG,EAAM7H,GAAUpe,GAChBgpB,EAAOlH,GAAmB9hB,GAC1BmhB,EAAiB8E,EAAI9E,eACrBF,EAAQ+H,EAAKzE,YACbrD,EAAS8H,EAAK1E,aACdjD,EAAI,EACJE,EAAI,EAER,GAAIJ,EAAgB,CAClBF,EAAQE,EAAeF,MACvBC,EAASC,EAAeD,OACxB,IAAI+H,EAAiBvI,MAEjBuI,IAAmBA,GAA+B,UAAbxJ,KACvC4B,EAAIF,EAAeG,WACnBC,EAAIJ,EAAeK,UAEzB,CAEE,MAAO,CACLP,MAAOA,EACPC,OAAQA,EACRG,EAAGA,EAAI4G,GAAoBjoB,GAC3BuhB,EAAGA,EAEP,CDDwD2H,CAAgBlpB,EAASyf,IAAa/d,GAAUqnB,GAdxG,SAAoC/oB,EAASyf,GAC3C,IAAIoJ,EAAOzM,GAAsBpc,GAAS,EAAoB,UAAbyf,GASjD,OARAoJ,EAAKrM,IAAMqM,EAAKrM,IAAMxc,EAAQmpB,UAC9BN,EAAKlM,KAAOkM,EAAKlM,KAAO3c,EAAQopB,WAChCP,EAAKpM,OAASoM,EAAKrM,IAAMxc,EAAQskB,aACjCuE,EAAKnM,MAAQmM,EAAKlM,KAAO3c,EAAQukB,YACjCsE,EAAK5H,MAAQjhB,EAAQukB,YACrBsE,EAAK3H,OAASlhB,EAAQskB,aACtBuE,EAAKxH,EAAIwH,EAAKlM,KACdkM,EAAKtH,EAAIsH,EAAKrM,IACPqM,CACT,CAG0HQ,CAA2BN,EAAgBtJ,GAAYmJ,GEtBlK,SAAyB5oB,GACtC,IAAIyoB,EAEAO,EAAOlH,GAAmB9hB,GAC1BspB,EAAY1B,GAAgB5nB,GAC5B8D,EAA0D,OAAlD2kB,EAAwBzoB,EAAQse,oBAAyB,EAASmK,EAAsB3kB,KAChGmd,EAAQla,GAAIiiB,EAAKO,YAAaP,EAAKzE,YAAazgB,EAAOA,EAAKylB,YAAc,EAAGzlB,EAAOA,EAAKygB,YAAc,GACvGrD,EAASna,GAAIiiB,EAAKQ,aAAcR,EAAK1E,aAAcxgB,EAAOA,EAAK0lB,aAAe,EAAG1lB,EAAOA,EAAKwgB,aAAe,GAC5GjD,GAAKiI,EAAUzB,WAAaI,GAAoBjoB,GAChDuhB,GAAK+H,EAAUvB,UAMnB,MAJiD,QAA7C1lB,GAAiByB,GAAQklB,GAAMvV,YACjC4N,GAAKta,GAAIiiB,EAAKzE,YAAazgB,EAAOA,EAAKygB,YAAc,GAAKtD,GAGrD,CACLA,MAAOA,EACPC,OAAQA,EACRG,EAAGA,EACHE,EAAGA,EAEP,CFCkMkI,CAAgB3H,GAAmB9hB,IACrO,CG1Be,SAAS0pB,GAAe5K,GACrC,IAOIuG,EAPAlI,EAAY2B,EAAK3B,UACjBnd,EAAU8e,EAAK9e,QACfud,EAAYuB,EAAKvB,UACjBkG,EAAgBlG,EAAYwC,GAAiBxC,GAAa,KAC1D6H,EAAY7H,EAAYwH,GAAaxH,GAAa,KAClDoM,EAAUxM,EAAUkE,EAAIlE,EAAU8D,MAAQ,EAAIjhB,EAAQihB,MAAQ,EAC9D2I,EAAUzM,EAAUoE,EAAIpE,EAAU+D,OAAS,EAAIlhB,EAAQkhB,OAAS,EAGpE,OAAQuC,GACN,KAAKjH,GACH6I,EAAU,CACRhE,EAAGsI,EACHpI,EAAGpE,EAAUoE,EAAIvhB,EAAQkhB,QAE3B,MAEF,KAAKzE,GACH4I,EAAU,CACRhE,EAAGsI,EACHpI,EAAGpE,EAAUoE,EAAIpE,EAAU+D,QAE7B,MAEF,KAAKxE,GACH2I,EAAU,CACRhE,EAAGlE,EAAUkE,EAAIlE,EAAU8D,MAC3BM,EAAGqI,GAEL,MAEF,KAAKjN,GACH0I,EAAU,CACRhE,EAAGlE,EAAUkE,EAAIrhB,EAAQihB,MACzBM,EAAGqI,GAEL,MAEF,QACEvE,EAAU,CACRhE,EAAGlE,EAAUkE,EACbE,EAAGpE,EAAUoE,GAInB,IAAIsI,EAAWpG,EAAgBb,GAAyBa,GAAiB,KAEzE,GAAgB,MAAZoG,EAAkB,CACpB,IAAIlG,EAAmB,MAAbkG,EAAmB,SAAW,QAExC,OAAQzE,GACN,KAAKtI,GACHuI,EAAQwE,GAAYxE,EAAQwE,IAAa1M,EAAUwG,GAAO,EAAI3jB,EAAQ2jB,GAAO,GAC7E,MAEF,KAAK5G,GACHsI,EAAQwE,GAAYxE,EAAQwE,IAAa1M,EAAUwG,GAAO,EAAI3jB,EAAQ2jB,GAAO,GAKrF,CAEE,OAAO0B,CACT,CC3De,SAASyE,GAAe/K,EAAOS,QAC5B,IAAZA,IACFA,EAAU,IAGZ,IAAIuK,EAAWvK,EACXwK,EAAqBD,EAASxM,UAC9BA,OAAmC,IAAvByM,EAAgCjL,EAAMxB,UAAYyM,EAC9DC,EAAoBF,EAAStK,SAC7BA,OAAiC,IAAtBwK,EAA+BlL,EAAMU,SAAWwK,EAC3DC,EAAoBH,EAASI,SAC7BA,OAAiC,IAAtBD,EAA+BlN,GAAkBkN,EAC5DE,EAAwBL,EAASM,aACjCA,OAAyC,IAA1BD,EAAmCnN,GAAWmN,EAC7DE,EAAwBP,EAASQ,eACjCA,OAA2C,IAA1BD,EAAmCpN,GAASoN,EAC7DE,EAAuBT,EAASU,YAChCA,OAAuC,IAAzBD,GAA0CA,EACxDE,EAAmBX,EAASnG,QAC5BA,OAA+B,IAArB8G,EAA8B,EAAIA,EAC5CzH,EAAgBD,GAAsC,iBAAZY,EAAuBA,EAAUV,GAAgBU,EAAS/G,KACpG8N,EAAaJ,IAAmBrN,GAASC,GAAYD,GACrDiI,EAAapG,EAAM8E,MAAM3G,OACzBld,EAAU+e,EAAMC,SAASyL,EAAcE,EAAaJ,GACpDK,EJkBS,SAAyB5qB,EAASmqB,EAAUE,EAAc5K,GACvE,IAAIoL,EAAmC,oBAAbV,EAlB5B,SAA4BnqB,GAC1B,IAAIgd,EAAkBwL,GAAkBzG,GAAc/hB,IAElD8qB,EADoB,CAAC,WAAY,SAASjkB,QAAQxE,GAAiBrC,GAASuf,WAAa,GACnDf,GAAcxe,GAAWmiB,GAAgBniB,GAAWA,EAE9F,OAAK0B,GAAUopB,GAKR9N,EAAgBhQ,QAAO,SAAU+b,GACtC,OAAOrnB,GAAUqnB,IAAmBhmB,GAASgmB,EAAgB+B,IAAmD,SAAhC3M,GAAY4K,EAChG,IANW,EAOX,CAK6DgC,CAAmB/qB,GAAW,GAAG8P,OAAOqa,GAC/FnN,EAAkB,GAAGlN,OAAO+a,EAAqB,CAACR,IAClDW,EAAsBhO,EAAgB,GACtCiO,EAAejO,EAAgBK,QAAO,SAAU6N,EAASnC,GAC3D,IAAIF,EAAOC,GAA2B9oB,EAAS+oB,EAAgBtJ,GAK/D,OAJAyL,EAAQ1O,IAAMzV,GAAI8hB,EAAKrM,IAAK0O,EAAQ1O,KACpC0O,EAAQxO,MAAQ1V,GAAI6hB,EAAKnM,MAAOwO,EAAQxO,OACxCwO,EAAQzO,OAASzV,GAAI6hB,EAAKpM,OAAQyO,EAAQzO,QAC1CyO,EAAQvO,KAAO5V,GAAI8hB,EAAKlM,KAAMuO,EAAQvO,MAC/BuO,CACX,GAAKpC,GAA2B9oB,EAASgrB,EAAqBvL,IAK5D,OAJAwL,EAAahK,MAAQgK,EAAavO,MAAQuO,EAAatO,KACvDsO,EAAa/J,OAAS+J,EAAaxO,OAASwO,EAAazO,IACzDyO,EAAa5J,EAAI4J,EAAatO,KAC9BsO,EAAa1J,EAAI0J,EAAazO,IACvByO,CACT,CInC2BE,CAAgBzpB,GAAU1B,GAAWA,EAAUA,EAAQorB,gBAAkBtJ,GAAmB/C,EAAMC,SAAS9B,QAASiN,EAAUE,EAAc5K,GACjK4L,EAAsBjP,GAAsB2C,EAAMC,SAAS7B,WAC3DoG,EAAgBmG,GAAe,CACjCvM,UAAWkO,EACXrrB,QAASmlB,EAET5H,UAAWA,IAET+N,EAAmB1C,GAAiB1gB,OAAOiX,OAAO,GAAIgG,EAAY5B,IAClEgI,EAAoBhB,IAAmBrN,GAASoO,EAAmBD,EAGnEG,EAAkB,CACpBhP,IAAKoO,EAAmBpO,IAAM+O,EAAkB/O,IAAMyG,EAAczG,IACpEC,OAAQ8O,EAAkB9O,OAASmO,EAAmBnO,OAASwG,EAAcxG,OAC7EE,KAAMiO,EAAmBjO,KAAO4O,EAAkB5O,KAAOsG,EAActG,KACvED,MAAO6O,EAAkB7O,MAAQkO,EAAmBlO,MAAQuG,EAAcvG,OAExE+O,EAAa1M,EAAMyE,cAAckB,OAErC,GAAI6F,IAAmBrN,IAAUuO,EAAY,CAC3C,IAAI/G,EAAS+G,EAAWlO,GACxBrV,OAAOvH,KAAK6qB,GAAiBvM,SAAQ,SAAUhf,GAC7C,IAAIyrB,EAAW,CAAChP,GAAOD,IAAQ5V,QAAQ5G,IAAQ,EAAI,GAAI,EACnDyjB,EAAO,CAAClH,GAAKC,IAAQ5V,QAAQ5G,IAAQ,EAAI,IAAM,IACnDurB,EAAgBvrB,IAAQykB,EAAOhB,GAAQgI,CAC7C,GACA,CAEE,OAAOF,CACT,CC5De,SAASG,GAAqB5M,EAAOS,QAClC,IAAZA,IACFA,EAAU,IAGZ,IAAIuK,EAAWvK,EACXjC,EAAYwM,EAASxM,UACrB4M,EAAWJ,EAASI,SACpBE,EAAeN,EAASM,aACxBzG,EAAUmG,EAASnG,QACnBgI,EAAiB7B,EAAS6B,eAC1BC,EAAwB9B,EAAS+B,sBACjCA,OAAkD,IAA1BD,EAAmCE,GAAgBF,EAC3EzG,EAAYL,GAAaxH,GACzBC,EAAa4H,EAAYwG,EAAiBxO,GAAsBA,GAAoBpQ,QAAO,SAAUuQ,GACvG,OAAOwH,GAAaxH,KAAe6H,CACvC,IAAOvI,GACDmP,EAAoBxO,EAAWxQ,QAAO,SAAUuQ,GAClD,OAAOuO,EAAsBjlB,QAAQ0W,IAAc,CACvD,IAEmC,IAA7ByO,EAAkBjqB,SACpBiqB,EAAoBxO,GAItB,IAAIyO,EAAYD,EAAkB3O,QAAO,SAAUC,EAAKC,GAOtD,OANAD,EAAIC,GAAauM,GAAe/K,EAAO,CACrCxB,UAAWA,EACX4M,SAAUA,EACVE,aAAcA,EACdzG,QAASA,IACR7D,GAAiBxC,IACbD,CACX,GAAK,IACH,OAAOpV,OAAOvH,KAAKsrB,GAAWC,MAAK,SAAUC,EAAGC,GAC9C,OAAOH,EAAUE,GAAKF,EAAUG,EACpC,GACA,CC+FA,MAAAC,GAAe,CACb/nB,KAAM,OACNsa,SAAS,EACTC,MAAO,OACPpa,GA5HF,SAAcqa,GACZ,IAAIC,EAAQD,EAAKC,MACbS,EAAUV,EAAKU,QACflb,EAAOwa,EAAKxa,KAEhB,IAAIya,EAAMyE,cAAclf,GAAMgoB,MAA9B,CAoCA,IAhCA,IAAIC,EAAoB/M,EAAQqK,SAC5B2C,OAAsC,IAAtBD,GAAsCA,EACtDE,EAAmBjN,EAAQkN,QAC3BC,OAAoC,IAArBF,GAAqCA,EACpDG,EAA8BpN,EAAQqN,mBACtCjJ,EAAUpE,EAAQoE,QAClBuG,EAAW3K,EAAQ2K,SACnBE,EAAe7K,EAAQ6K,aACvBI,EAAcjL,EAAQiL,YACtBqC,EAAwBtN,EAAQoM,eAChCA,OAA2C,IAA1BkB,GAA0CA,EAC3DhB,EAAwBtM,EAAQsM,sBAChCiB,EAAqBhO,EAAMS,QAAQjC,UACnCkG,EAAgB1D,GAAiBgN,GAEjCF,EAAqBD,IADHnJ,IAAkBsJ,GACqCnB,EAjC/E,SAAuCrO,GACrC,GAAIwC,GAAiBxC,KAAeX,GAClC,MAAO,GAGT,IAAIoQ,EAAoBvF,GAAqBlK,GAC7C,MAAO,CAACoK,GAA8BpK,GAAYyP,EAAmBrF,GAA8BqF,GACrG,CA0B6IC,CAA8BF,GAA3E,CAACtF,GAAqBsF,KAChHvP,EAAa,CAACuP,GAAoBjd,OAAO+c,GAAoBxP,QAAO,SAAUC,EAAKC,GACrF,OAAOD,EAAIxN,OAAOiQ,GAAiBxC,KAAeX,GAAO+O,GAAqB5M,EAAO,CACnFxB,UAAWA,EACX4M,SAAUA,EACVE,aAAcA,EACdzG,QAASA,EACTgI,eAAgBA,EAChBE,sBAAuBA,IACpBvO,EACT,GAAK,IACC2P,EAAgBnO,EAAM8E,MAAM1G,UAC5BgI,EAAapG,EAAM8E,MAAM3G,OACzBiQ,EAAY,IAAIttB,IAChButB,GAAqB,EACrBC,EAAwB7P,EAAW,GAE9B8P,EAAI,EAAGA,EAAI9P,EAAWzb,OAAQurB,IAAK,CAC1C,IAAI/P,EAAYC,EAAW8P,GAEvBC,EAAiBxN,GAAiBxC,GAElCiQ,EAAmBzI,GAAaxH,KAAeT,GAC/C2Q,EAAa,CAACjR,GAAKC,IAAQ5V,QAAQ0mB,IAAmB,EACtD5J,EAAM8J,EAAa,QAAU,SAC7BrF,EAAW0B,GAAe/K,EAAO,CACnCxB,UAAWA,EACX4M,SAAUA,EACVE,aAAcA,EACdI,YAAaA,EACb7G,QAASA,IAEP8J,EAAoBD,EAAaD,EAAmB9Q,GAAQC,GAAO6Q,EAAmB/Q,GAASD,GAE/F0Q,EAAcvJ,GAAOwB,EAAWxB,KAClC+J,EAAoBjG,GAAqBiG,IAG3C,IAAIC,EAAmBlG,GAAqBiG,GACxCE,EAAS,GAUb,GARIpB,GACFoB,EAAO7oB,KAAKqjB,EAASmF,IAAmB,GAGtCZ,GACFiB,EAAO7oB,KAAKqjB,EAASsF,IAAsB,EAAGtF,EAASuF,IAAqB,GAG1EC,EAAOC,OAAM,SAAUC,GACzB,OAAOA,CACb,IAAQ,CACFT,EAAwB9P,EACxB6P,GAAqB,EACrB,KACN,CAEID,EAAUptB,IAAIwd,EAAWqQ,EAC7B,CAEE,GAAIR,EAqBF,IAnBA,IAEIW,EAAQ,SAAeC,GACzB,IAAIC,EAAmBzQ,EAAWpV,MAAK,SAAUmV,GAC/C,IAAIqQ,EAAST,EAAU9sB,IAAIkd,GAE3B,GAAIqQ,EACF,OAAOA,EAAOhjB,MAAM,EAAGojB,GAAIH,OAAM,SAAUC,GACzC,OAAOA,CACnB,GAEA,IAEM,GAAIG,EAEF,OADAZ,EAAwBY,EACjB,OAEf,EAEaD,EAnBYpC,EAAiB,EAAI,EAmBZoC,EAAK,GAGpB,UAFFD,EAAMC,GADmBA,KAOpCjP,EAAMxB,YAAc8P,IACtBtO,EAAMyE,cAAclf,GAAMgoB,OAAQ,EAClCvN,EAAMxB,UAAY8P,EAClBtO,EAAMmP,OAAQ,EA5GlB,CA8GA,EAQEpJ,iBAAkB,CAAC,UACnBlT,KAAM,CACJ0a,OAAO,IC7IX,SAAS6B,GAAe/F,EAAUS,EAAMuF,GAQtC,YAPyB,IAArBA,IACFA,EAAmB,CACjB/M,EAAG,EACHE,EAAG,IAIA,CACL/E,IAAK4L,EAAS5L,IAAMqM,EAAK3H,OAASkN,EAAiB7M,EACnD7E,MAAO0L,EAAS1L,MAAQmM,EAAK5H,MAAQmN,EAAiB/M,EACtD5E,OAAQ2L,EAAS3L,OAASoM,EAAK3H,OAASkN,EAAiB7M,EACzD5E,KAAMyL,EAASzL,KAAOkM,EAAK5H,MAAQmN,EAAiB/M,EAExD,CAEA,SAASgN,GAAsBjG,GAC7B,MAAO,CAAC5L,GAAKE,GAAOD,GAAQE,IAAM2R,MAAK,SAAUC,GAC/C,OAAOnG,EAASmG,IAAS,CAC7B,GACA,CA+BA,MAAAC,GAAe,CACblqB,KAAM,OACNsa,SAAS,EACTC,MAAO,OACPiG,iBAAkB,CAAC,mBACnBrgB,GAlCF,SAAcqa,GACZ,IAAIC,EAAQD,EAAKC,MACbza,EAAOwa,EAAKxa,KACZ4oB,EAAgBnO,EAAM8E,MAAM1G,UAC5BgI,EAAapG,EAAM8E,MAAM3G,OACzBkR,EAAmBrP,EAAMyE,cAAciL,gBACvCC,EAAoB5E,GAAe/K,EAAO,CAC5CwL,eAAgB,cAEdoE,EAAoB7E,GAAe/K,EAAO,CAC5C0L,aAAa,IAEXmE,EAA2BT,GAAeO,EAAmBxB,GAC7D2B,EAAsBV,GAAeQ,EAAmBxJ,EAAYiJ,GACpEU,EAAoBT,GAAsBO,GAC1CG,EAAmBV,GAAsBQ,GAC7C9P,EAAMyE,cAAclf,GAAQ,CAC1BsqB,yBAA0BA,EAC1BC,oBAAqBA,EACrBC,kBAAmBA,EACnBC,iBAAkBA,GAEpBhQ,EAAMlS,WAAWqQ,OAAShV,OAAOiX,OAAO,GAAIJ,EAAMlS,WAAWqQ,OAAQ,CACnE,+BAAgC4R,EAChC,sBAAuBC,GAE3B,GCJAC,GAAe,CACb1qB,KAAM,SACNsa,SAAS,EACTC,MAAO,OACPiB,SAAU,CAAC,iBACXrb,GA5BF,SAAgB4a,GACd,IAAIN,EAAQM,EAAMN,MACdS,EAAUH,EAAMG,QAChBlb,EAAO+a,EAAM/a,KACb2qB,EAAkBzP,EAAQkF,OAC1BA,OAA6B,IAApBuK,EAA6B,CAAC,EAAG,GAAKA,EAC/Crd,EAAO4L,GAAWH,QAAO,SAAUC,EAAKC,GAE1C,OADAD,EAAIC,GA5BD,SAAiCA,EAAWsG,EAAOa,GACxD,IAAIjB,EAAgB1D,GAAiBxC,GACjC2R,EAAiB,CAACvS,GAAMH,IAAK3V,QAAQ4c,IAAkB,GAAI,EAAK,EAEhE3E,EAAyB,mBAAX4F,EAAwBA,EAAOxc,OAAOiX,OAAO,GAAI0E,EAAO,CACxEtG,UAAWA,KACPmH,EACFyK,EAAWrQ,EAAK,GAChBsQ,EAAWtQ,EAAK,GAIpB,OAFAqQ,EAAWA,GAAY,EACvBC,GAAYA,GAAY,GAAKF,EACtB,CAACvS,GAAMD,IAAO7V,QAAQ4c,IAAkB,EAAI,CACjDpC,EAAG+N,EACH7N,EAAG4N,GACD,CACF9N,EAAG8N,EACH5N,EAAG6N,EAEP,CASqBC,CAAwB9R,EAAWwB,EAAM8E,MAAOa,GAC1DpH,CACX,GAAK,IACCgS,EAAwB1d,EAAKmN,EAAMxB,WACnC8D,EAAIiO,EAAsBjO,EAC1BE,EAAI+N,EAAsB/N,EAEW,MAArCxC,EAAMyE,cAAcD,gBACtBxE,EAAMyE,cAAcD,cAAclC,GAAKA,EACvCtC,EAAMyE,cAAcD,cAAchC,GAAKA,GAGzCxC,EAAMyE,cAAclf,GAAQsN,CAC9B,GC1BA2d,GAAe,CACbjrB,KAAM,gBACNsa,SAAS,EACTC,MAAO,OACPpa,GApBF,SAAuBqa,GACrB,IAAIC,EAAQD,EAAKC,MACbza,EAAOwa,EAAKxa,KAKhBya,EAAMyE,cAAclf,GAAQolB,GAAe,CACzCvM,UAAW4B,EAAM8E,MAAM1G,UACvBnd,QAAS+e,EAAM8E,MAAM3G,OAErBK,UAAWwB,EAAMxB,WAErB,EAQE3L,KAAM,ICgHR4d,GAAe,CACblrB,KAAM,kBACNsa,SAAS,EACTC,MAAO,OACPpa,GA/HF,SAAyBqa,GACvB,IAAIC,EAAQD,EAAKC,MACbS,EAAUV,EAAKU,QACflb,EAAOwa,EAAKxa,KACZioB,EAAoB/M,EAAQqK,SAC5B2C,OAAsC,IAAtBD,GAAsCA,EACtDE,EAAmBjN,EAAQkN,QAC3BC,OAAoC,IAArBF,GAAsCA,EACrDtC,EAAW3K,EAAQ2K,SACnBE,EAAe7K,EAAQ6K,aACvBI,EAAcjL,EAAQiL,YACtB7G,EAAUpE,EAAQoE,QAClB6L,EAAkBjQ,EAAQkQ,OAC1BA,OAA6B,IAApBD,GAAoCA,EAC7CE,EAAwBnQ,EAAQoQ,aAChCA,OAAyC,IAA1BD,EAAmC,EAAIA,EACtDvH,EAAW0B,GAAe/K,EAAO,CACnCoL,SAAUA,EACVE,aAAcA,EACdzG,QAASA,EACT6G,YAAaA,IAEXhH,EAAgB1D,GAAiBhB,EAAMxB,WACvC6H,EAAYL,GAAahG,EAAMxB,WAC/BsS,GAAmBzK,EACnByE,EAAWjH,GAAyBa,GACpCiJ,ECrCY,MDqCS7C,ECrCH,IAAM,IDsCxBtG,EAAgBxE,EAAMyE,cAAcD,cACpC2J,EAAgBnO,EAAM8E,MAAM1G,UAC5BgI,EAAapG,EAAM8E,MAAM3G,OACzB4S,EAA4C,mBAAjBF,EAA8BA,EAAa1nB,OAAOiX,OAAO,GAAIJ,EAAM8E,MAAO,CACvGtG,UAAWwB,EAAMxB,aACbqS,EACFG,EAA2D,iBAAtBD,EAAiC,CACxEjG,SAAUiG,EACVpD,QAASoD,GACP5nB,OAAOiX,OAAO,CAChB0K,SAAU,EACV6C,QAAS,GACRoD,GACCE,EAAsBjR,EAAMyE,cAAckB,OAAS3F,EAAMyE,cAAckB,OAAO3F,EAAMxB,WAAa,KACjG3L,EAAO,CACTyP,EAAG,EACHE,EAAG,GAGL,GAAKgC,EAAL,CAIA,GAAIiJ,EAAe,CACjB,IAAIyD,EAEAC,EAAwB,MAAbrG,EAAmBrN,GAAMG,GACpCwT,EAAuB,MAAbtG,EAAmBpN,GAASC,GACtCiH,EAAmB,MAAbkG,EAAmB,SAAW,QACpCnF,EAASnB,EAAcsG,GACvB7iB,EAAM0d,EAAS0D,EAAS8H,GACxBnpB,EAAM2d,EAAS0D,EAAS+H,GACxBC,EAAWV,GAAUvK,EAAWxB,GAAO,EAAI,EAC3C0M,EAASjL,IAActI,GAAQoQ,EAAcvJ,GAAOwB,EAAWxB,GAC/D2M,EAASlL,IAActI,IAASqI,EAAWxB,IAAQuJ,EAAcvJ,GAGjEL,EAAevE,EAAMC,SAASW,MAC9BoE,EAAY2L,GAAUpM,EAAe7B,GAAc6B,GAAgB,CACrErC,MAAO,EACPC,OAAQ,GAENqP,EAAqBxR,EAAMyE,cAAc,oBAAsBzE,EAAMyE,cAAc,oBAAoBI,QxBhFtG,CACLpH,IAAK,EACLE,MAAO,EACPD,OAAQ,EACRE,KAAM,GwB6EF6T,EAAkBD,EAAmBL,GACrCO,EAAkBF,EAAmBJ,GAMrCO,EAAW7N,GAAO,EAAGqK,EAAcvJ,GAAMI,EAAUJ,IACnDgN,EAAYd,EAAkB3C,EAAcvJ,GAAO,EAAIyM,EAAWM,EAAWF,EAAkBT,EAA4BlG,SAAWwG,EAASK,EAAWF,EAAkBT,EAA4BlG,SACxM+G,EAAYf,GAAmB3C,EAAcvJ,GAAO,EAAIyM,EAAWM,EAAWD,EAAkBV,EAA4BlG,SAAWyG,EAASI,EAAWD,EAAkBV,EAA4BlG,SACzMzF,EAAoBrF,EAAMC,SAASW,OAASwC,GAAgBpD,EAAMC,SAASW,OAC3EkR,EAAezM,EAAiC,MAAbyF,EAAmBzF,EAAkB+E,WAAa,EAAI/E,EAAkBgF,YAAc,EAAI,EAC7H0H,EAAwH,OAAjGb,EAA+C,MAAvBD,OAA8B,EAASA,EAAoBnG,IAAqBoG,EAAwB,EAEvJc,EAAYrM,EAASkM,EAAYE,EACjCE,EAAkBnO,GAAO6M,EAAS3M,GAAQ/b,EAF9B0d,EAASiM,EAAYG,EAAsBD,GAEK7pB,EAAK0d,EAAQgL,EAAS5M,GAAQ/b,EAAKgqB,GAAahqB,GAChHwc,EAAcsG,GAAYmH,EAC1Bpf,EAAKiY,GAAYmH,EAAkBtM,CACvC,CAEE,GAAIiI,EAAc,CAChB,IAAIsE,EAEAC,EAAyB,MAAbrH,EAAmBrN,GAAMG,GAErCwU,GAAwB,MAAbtH,EAAmBpN,GAASC,GAEvC0U,GAAU7N,EAAcmJ,GAExB2E,GAAmB,MAAZ3E,EAAkB,SAAW,QAEpC4E,GAAOF,GAAUhJ,EAAS8I,GAE1BK,GAAOH,GAAUhJ,EAAS+I,IAE1BK,IAAsD,IAAvC,CAAChV,GAAKG,IAAM9V,QAAQ4c,GAEnCgO,GAAyH,OAAjGR,EAAgD,MAAvBjB,OAA8B,EAASA,EAAoBtD,IAAoBuE,EAAyB,EAEzJS,GAAaF,GAAeF,GAAOF,GAAUlE,EAAcmE,IAAQlM,EAAWkM,IAAQI,GAAuB1B,EAA4BrD,QAEzIiF,GAAaH,GAAeJ,GAAUlE,EAAcmE,IAAQlM,EAAWkM,IAAQI,GAAuB1B,EAA4BrD,QAAU6E,GAE5IK,GAAmBlC,GAAU8B,G1BzH9B,SAAwBxqB,EAAK2E,EAAO5E,GACzC,IAAI8qB,EAAIhP,GAAO7b,EAAK2E,EAAO5E,GAC3B,OAAO8qB,EAAI9qB,EAAMA,EAAM8qB,CACzB,C0BsHoDC,CAAeJ,GAAYN,GAASO,IAAc9O,GAAO6M,EAASgC,GAAaJ,GAAMF,GAAS1B,EAASiC,GAAaJ,IAEpKhO,EAAcmJ,GAAWkF,GACzBhgB,EAAK8a,GAAWkF,GAAmBR,EACvC,CAEErS,EAAMyE,cAAclf,GAAQsN,CAvE9B,CAwEA,EAQEkT,iBAAkB,CAAC,WE1HN,SAASiN,GAAiBC,EAAyB9P,EAAcuD,QAC9D,IAAZA,IACFA,GAAU,GAGZ,ICnBoCpH,ECJOre,EFuBvCiyB,EAA0BzT,GAAc0D,GACxCgQ,EAAuB1T,GAAc0D,IAf3C,SAAyBliB,GACvB,IAAI6oB,EAAO7oB,EAAQoc,wBACf0E,EAASd,GAAM6I,EAAK5H,OAASjhB,EAAQghB,aAAe,EACpDD,EAASf,GAAM6I,EAAK3H,QAAUlhB,EAAQ2D,cAAgB,EAC1D,OAAkB,IAAXmd,GAA2B,IAAXC,CACzB,CAU4DoR,CAAgBjQ,GACtE9e,EAAkB0e,GAAmBI,GACrC2G,EAAOzM,GAAsB4V,EAAyBE,EAAsBzM,GAC5EyB,EAAS,CACXW,WAAY,EACZE,UAAW,GAET1C,EAAU,CACZhE,EAAG,EACHE,EAAG,GAkBL,OAfI0Q,IAA4BA,IAA4BxM,MACxB,SAA9BtH,GAAY+D,IAChBgG,GAAe9kB,MACb8jB,GCnCgC7I,EDmCT6D,KClCd9D,GAAUC,IAAUG,GAAcH,GCJxC,CACLwJ,YAFyC7nB,EDQbqe,GCNRwJ,WACpBE,UAAW/nB,EAAQ+nB,WDGZH,GAAgBvJ,IDoCnBG,GAAc0D,KAChBmD,EAAUjJ,GAAsB8F,GAAc,IACtCb,GAAKa,EAAakH,WAC1B/D,EAAQ9D,GAAKW,EAAaiH,WACjB/lB,IACTiiB,EAAQhE,EAAI4G,GAAoB7kB,KAI7B,CACLie,EAAGwH,EAAKlM,KAAOuK,EAAOW,WAAaxC,EAAQhE,EAC3CE,EAAGsH,EAAKrM,IAAM0K,EAAOa,UAAY1C,EAAQ9D,EACzCN,MAAO4H,EAAK5H,MACZC,OAAQ2H,EAAK3H,OAEjB,CGvDA,SAASpJ,GAAMsa,GACb,IAAI1iB,EAAM,IAAI7P,IACVwyB,EAAU,IAAI3qB,IACd4qB,EAAS,GAKb,SAASpG,EAAKqG,GACZF,EAAQ3e,IAAI6e,EAASjuB,MACN,GAAGwL,OAAOyiB,EAASzS,UAAY,GAAIyS,EAASzN,kBAAoB,IACtE7F,SAAQ,SAAUuT,GACzB,IAAKH,EAAQlyB,IAAIqyB,GAAM,CACrB,IAAIC,EAAc/iB,EAAIrP,IAAImyB,GAEtBC,GACFvG,EAAKuG,EAEf,CACA,IACIH,EAAOvtB,KAAKwtB,EAChB,CAQE,OAzBAH,EAAUnT,SAAQ,SAAUsT,GAC1B7iB,EAAI3P,IAAIwyB,EAASjuB,KAAMiuB,EAC3B,IAiBEH,EAAUnT,SAAQ,SAAUsT,GACrBF,EAAQlyB,IAAIoyB,EAASjuB,OAExB4nB,EAAKqG,EAEX,IACSD,CACT,CCvBA,IAAII,GAAkB,CACpBnV,UAAW,SACX6U,UAAW,GACX3S,SAAU,YAGZ,SAASkT,KACP,IAAK,IAAItB,EAAOuB,UAAU7wB,OAAQmD,EAAO,IAAIzE,MAAM4wB,GAAOwB,EAAO,EAAGA,EAAOxB,EAAMwB,IAC/E3tB,EAAK2tB,GAAQD,UAAUC,GAGzB,OAAQ3tB,EAAKopB,MAAK,SAAUtuB,GAC1B,QAASA,GAAoD,mBAAlCA,EAAQoc,sBACvC,GACA,CAEO,SAAS0W,GAAgBC,QACL,IAArBA,IACFA,EAAmB,IAGrB,IAAIC,EAAoBD,EACpBE,EAAwBD,EAAkBE,iBAC1CA,OAA6C,IAA1BD,EAAmC,GAAKA,EAC3DE,EAAyBH,EAAkBI,eAC3CA,OAA4C,IAA3BD,EAAoCT,GAAkBS,EAC3E,OAAO,SAAsBhW,EAAWD,EAAQsC,QAC9B,IAAZA,IACFA,EAAU4T,GAGZ,ICxC6B3uB,EAC3B4uB,EDuCEtU,EAAQ,CACVxB,UAAW,SACX+V,iBAAkB,GAClB9T,QAAStX,OAAOiX,OAAO,GAAIuT,GAAiBU,GAC5C5P,cAAe,GACfxE,SAAU,CACR7B,UAAWA,EACXD,OAAQA,GAEVrQ,WAAY,GACZqS,OAAQ,IAENqU,EAAmB,GACnBC,GAAc,EACdtzB,EAAW,CACb6e,MAAOA,EACP0U,WAAY,SAAoBC,GAC9B,IAAIlU,EAAsC,mBAArBkU,EAAkCA,EAAiB3U,EAAMS,SAAWkU,EACzFC,IACA5U,EAAMS,QAAUtX,OAAOiX,OAAO,GAAIiU,EAAgBrU,EAAMS,QAASA,GACjET,EAAMsI,cAAgB,CACpBlK,UAAWzb,GAAUyb,GAAaqL,GAAkBrL,GAAaA,EAAUiO,eAAiB5C,GAAkBrL,EAAUiO,gBAAkB,GAC1IlO,OAAQsL,GAAkBtL,IAI5B,IElE4BkV,EAC9BwB,EFiEMN,EDhCG,SAAwBlB,GAErC,IAAIkB,EAAmBxb,GAAMsa,GAE7B,OAAOlU,GAAeb,QAAO,SAAUC,EAAKuB,GAC1C,OAAOvB,EAAIxN,OAAOwjB,EAAiBtmB,QAAO,SAAUulB,GAClD,OAAOA,EAAS1T,QAAUA,CAChC,IACA,GAAK,GACL,CCuB+BgV,EElEKzB,EFkEsB,GAAGtiB,OAAOojB,EAAkBnU,EAAMS,QAAQ4S,WEjE9FwB,EAASxB,EAAU/U,QAAO,SAAUuW,EAAQE,GAC9C,IAAIC,EAAWH,EAAOE,EAAQxvB,MAK9B,OAJAsvB,EAAOE,EAAQxvB,MAAQyvB,EAAW7rB,OAAOiX,OAAO,GAAI4U,EAAUD,EAAS,CACrEtU,QAAStX,OAAOiX,OAAO,GAAI4U,EAASvU,QAASsU,EAAQtU,SACrD5N,KAAM1J,OAAOiX,OAAO,GAAI4U,EAASniB,KAAMkiB,EAAQliB,QAC5CkiB,EACEF,CACX,GAAK,IAEI1rB,OAAOvH,KAAKizB,GAAQlkB,KAAI,SAAUzP,GACvC,OAAO2zB,EAAO3zB,EAClB,MF4DQ,OAJA8e,EAAMuU,iBAAmBA,EAAiBtmB,QAAO,SAAUgnB,GACzD,OAAOA,EAAEpV,OACnB,IA+FMG,EAAMuU,iBAAiBrU,SAAQ,SAAUH,GACvC,IAAIxa,EAAOwa,EAAKxa,KACZ2vB,EAAenV,EAAKU,QACpBA,OAA2B,IAAjByU,EAA0B,GAAKA,EACzC7U,EAASN,EAAKM,OAElB,GAAsB,mBAAXA,EAAuB,CAChC,IAAI8U,EAAY9U,EAAO,CACrBL,MAAOA,EACPza,KAAMA,EACNpE,SAAUA,EACVsf,QAASA,IAKX+T,EAAiBxuB,KAAKmvB,GAFT,WAAkB,EAGzC,CACA,IA/Geh0B,EAASqnB,QACxB,EAMM4M,YAAa,WACX,IAAIX,EAAJ,CAIA,IAAIY,EAAkBrV,EAAMC,SACxB7B,EAAYiX,EAAgBjX,UAC5BD,EAASkX,EAAgBlX,OAG7B,GAAKyV,GAAiBxV,EAAWD,GAAjC,CAKA6B,EAAM8E,MAAQ,CACZ1G,UAAW4U,GAAiB5U,EAAWgF,GAAgBjF,GAAoC,UAA3B6B,EAAMS,QAAQC,UAC9EvC,OAAQuE,GAAcvE,IAOxB6B,EAAMmP,OAAQ,EACdnP,EAAMxB,UAAYwB,EAAMS,QAAQjC,UAKhCwB,EAAMuU,iBAAiBrU,SAAQ,SAAUsT,GACvC,OAAOxT,EAAMyE,cAAc+O,EAASjuB,MAAQ4D,OAAOiX,OAAO,GAAIoT,EAAS3gB,KACjF,IAEQ,IAAK,IAAIhL,EAAQ,EAAGA,EAAQmY,EAAMuU,iBAAiBvxB,OAAQ6E,IACzD,IAAoB,IAAhBmY,EAAMmP,MAAV,CAMA,IAAImG,EAAwBtV,EAAMuU,iBAAiB1sB,GAC/CnC,EAAK4vB,EAAsB5vB,GAC3B6vB,EAAyBD,EAAsB7U,QAC/CuK,OAAsC,IAA3BuK,EAAoC,GAAKA,EACpDhwB,EAAO+vB,EAAsB/vB,KAEf,mBAAPG,IACTsa,EAAQta,EAAG,CACTsa,MAAOA,EACPS,QAASuK,EACTzlB,KAAMA,EACNpE,SAAUA,KACN6e,EAdlB,MAHYA,EAAMmP,OAAQ,EACdtnB,GAAQ,CAzBpB,CATA,CAqDA,EAGM2gB,QC1I2B9iB,ED0IV,WACf,OAAO,IAAI8vB,SAAQ,SAAUC,GAC3Bt0B,EAASi0B,cACTK,EAAQzV,EAClB,GACA,EC7IS,WAUL,OATKsU,IACHA,EAAU,IAAIkB,SAAQ,SAAUC,GAC9BD,QAAQC,UAAUC,MAAK,WACrBpB,OAAUxhB,EACV2iB,EAAQ/vB,IAClB,GACA,KAGW4uB,CACX,GDmIMqB,QAAS,WACPf,IACAH,GAAc,CACtB,GAGI,IAAKb,GAAiBxV,EAAWD,GAC/B,OAAOhd,EAmCT,SAASyzB,IACPJ,EAAiBtU,SAAQ,SAAUxa,GACjC,OAAOA,GACf,IACM8uB,EAAmB,EACzB,CAEI,OAvCArzB,EAASuzB,WAAWjU,GAASiV,MAAK,SAAU1V,IACrCyU,GAAehU,EAAQmV,eAC1BnV,EAAQmV,cAAc5V,EAE9B,IAmCW7e,CACX,CACA,CACO,IAAI00B,GAA4B9B,KG9LnC8B,GAA4B9B,GAAgB,CAC9CI,iBAFqB,CAAClM,GAAgBzD,GAAesR,GAAeC,MCMlEF,GAA4B9B,GAAgB,CAC9CI,iBAFqB,CAAClM,GAAgBzD,GAAesR,GAAeC,GAAapQ,GAAQqQ,GAAMtG,GAAiB9O,GAAOhE,M,+lBCkBnHpX,GAAO,WAEPuK,GAAY,eACZgF,GAAe,YAIfkhB,GAAe,UACfC,GAAiB,YAGjBva,GAAa,OAAO5L,KACpB6L,GAAe,SAAS7L,KACxB0L,GAAa,OAAO1L,KACpB2L,GAAc,QAAQ3L,KACtB8F,GAAuB,QAAQ9F,KAAYgF,KAC3CohB,GAAyB,UAAUpmB,KAAYgF,KAC/CqhB,GAAuB,QAAQrmB,KAAYgF,KAE3C8G,GAAkB,OAOlB9I,GAAuB,4DACvBsjB,GAA6B,GAAGtjB,MAAwB8I,KACxDya,GAAgB,iBAKhBC,GAAgBtxB,IAAU,UAAY,YACtCuxB,GAAmBvxB,IAAU,YAAc,UAC3CwxB,GAAmBxxB,IAAU,aAAe,eAC5CyxB,GAAsBzxB,IAAU,eAAiB,aACjD0xB,GAAkB1xB,IAAU,aAAe,cAC3C2xB,GAAiB3xB,IAAU,cAAgB,aAI3CqJ,GAAU,CACduoB,WAAW,EACXzL,SAAU,kBACV0L,QAAS,UACTnR,OAAQ,CAAC,EAAG,GACZoR,aAAc,KACd3Y,UAAW,UAGP7P,GAAc,CAClBsoB,UAAW,mBACXzL,SAAU,mBACV0L,QAAS,SACTnR,OAAQ,0BACRoR,aAAc,yBACd3Y,UAAW,2BAOb,MAAM4Y,WAAiBvnB,EACrBV,YAAY9N,EAASyN,GACnBgB,MAAMzO,EAASyN,GAEfxE,KAAK+sB,QAAU,KACf/sB,KAAKgtB,QAAUhtB,KAAKyF,SAAShM,WAE7BuG,KAAKitB,MAAQrmB,EAAeY,KAAKxH,KAAKyF,SAAU2mB,IAAe,IAC7DxlB,EAAeS,KAAKrH,KAAKyF,SAAU2mB,IAAe,IAClDxlB,EAAeG,QAAQqlB,GAAepsB,KAAKgtB,SAC7ChtB,KAAKktB,UAAYltB,KAAKmtB,eACxB,CAGA,kBAAW/oB,GACT,OAAOA,EACT,CAEA,sBAAWC,GACT,OAAOA,EACT,CAEA,eAAW/I,GACT,OAAOA,EACT,CAGAyN,SACE,OAAO/I,KAAKyS,WAAazS,KAAK0S,OAAS1S,KAAK2S,MAC9C,CAEAA,OACE,GAAIjZ,EAAWsG,KAAKyF,WAAazF,KAAKyS,WACpC,OAGF,MAAM3S,EAAgB,CACpBA,cAAeE,KAAKyF,UAKtB,IAFkBlF,EAAasB,QAAQ7B,KAAKyF,SAAU8L,GAAYzR,GAEpDmC,iBAAd,CAUA,GANAjC,KAAKotB,gBAMD,iBAAkBr0B,SAASoB,kBAAoB6F,KAAKgtB,QAAQzzB,QAtFxC,eAuFtB,IAAK,MAAMxC,IAAW,GAAG8P,UAAU9N,SAAS8B,KAAKmM,UAC/CzG,EAAac,GAAGtK,EAAS,YAAayD,GAI1CwF,KAAKyF,SAAS4nB,QACdrtB,KAAKyF,SAASjC,aAAa,iBAAiB,GAE5CxD,KAAKitB,MAAMpzB,UAAU4Q,IAAIkH,IACzB3R,KAAKyF,SAAS5L,UAAU4Q,IAAIkH,IAC5BpR,EAAasB,QAAQ7B,KAAKyF,SAAU+L,GAAa1R,EAnBjD,CAoBF,CAEA4S,OACE,GAAIhZ,EAAWsG,KAAKyF,YAAczF,KAAKyS,WACrC,OAGF,MAAM3S,EAAgB,CACpBA,cAAeE,KAAKyF,UAGtBzF,KAAKstB,cAAcxtB,EACrB,CAEA8F,UACM5F,KAAK+sB,SACP/sB,KAAK+sB,QAAQtB,UAGfjmB,MAAMI,SACR,CAEA0Y,SACEte,KAAKktB,UAAYltB,KAAKmtB,gBAClBntB,KAAK+sB,SACP/sB,KAAK+sB,QAAQzO,QAEjB,CAGAgP,cAAcxtB,GAEZ,IADkBS,EAAasB,QAAQ7B,KAAKyF,SAAUgM,GAAY3R,GACpDmC,iBAAd,CAMA,GAAI,iBAAkBlJ,SAASoB,gBAC7B,IAAK,MAAMpD,IAAW,GAAG8P,UAAU9N,SAAS8B,KAAKmM,UAC/CzG,EAAaC,IAAIzJ,EAAS,YAAayD,GAIvCwF,KAAK+sB,SACP/sB,KAAK+sB,QAAQtB,UAGfzrB,KAAKitB,MAAMpzB,UAAUlC,OAAOga,IAC5B3R,KAAKyF,SAAS5L,UAAUlC,OAAOga,IAC/B3R,KAAKyF,SAASjC,aAAa,gBAAiB,SAC5CF,EAAYG,oBAAoBzD,KAAKitB,MAAO,UAC5C1sB,EAAasB,QAAQ7B,KAAKyF,SAAUiM,GAAc5R,GAGlDE,KAAKyF,SAAS4nB,OArBd,CAsBF,CAEA9oB,WAAWC,GAGT,GAAgC,iBAFhCA,EAASgB,MAAMjB,WAAWC,IAER0P,YAA2Bzb,EAAU+L,EAAO0P,YACV,mBAA3C1P,EAAO0P,UAAUf,sBAGxB,MAAM,IAAI9N,UAAU,GAAG/J,GAAKgK,+GAG9B,OAAOd,CACT,CAEA4oB,gBACE,QAAsB,IAAXG,GACT,MAAM,IAAIloB,UAAU,yEAGtB,IAAImoB,EAAmBxtB,KAAKyF,SAEG,WAA3BzF,KAAK0F,QAAQwO,UACfsZ,EAAmBxtB,KAAKgtB,QACfv0B,EAAUuH,KAAK0F,QAAQwO,WAChCsZ,EAAmB30B,EAAWmH,KAAK0F,QAAQwO,WACA,iBAA3BlU,KAAK0F,QAAQwO,YAC7BsZ,EAAmBxtB,KAAK0F,QAAQwO,WAGlC,MAAM2Y,EAAe7sB,KAAKytB,mBAC1BztB,KAAK+sB,QAAUQ,GAAoBC,EAAkBxtB,KAAKitB,MAAOJ,EACnE,CAEApa,WACE,OAAOzS,KAAKitB,MAAMpzB,UAAUC,SAAS6X,GACvC,CAEA+b,gBACE,MAAMC,EAAiB3tB,KAAKgtB,QAE5B,GAAIW,EAAe9zB,UAAUC,SA5MN,WA6MrB,OAAO2yB,GAGT,GAAIkB,EAAe9zB,UAAUC,SA/MJ,aAgNvB,OAAO4yB,GAGT,GAAIiB,EAAe9zB,UAAUC,SAlNA,iBAmN3B,MAnMsB,MAsMxB,GAAI6zB,EAAe9zB,UAAUC,SArNE,mBAsN7B,MAtMyB,SA0M3B,MAAM8zB,EAAkF,QAA1Ex0B,iBAAiB4G,KAAKitB,OAAO5zB,iBAAiB,iBAAiBmN,OAE7E,OAAImnB,EAAe9zB,UAAUC,SAhOP,UAiOb8zB,EAAQtB,GAAmBD,GAG7BuB,EAAQpB,GAAsBD,EACvC,CAEAY,gBACE,OAAkD,OAA3CntB,KAAKyF,SAASlM,QA/ND,UAgOtB,CAEAs0B,aACE,MAAMpS,OAAEA,GAAWzb,KAAK0F,QAExB,MAAsB,iBAAX+V,EACFA,EAAO3e,MAAM,KAAK2J,KAAI/D,GAAS/F,OAAOkT,SAASnN,EAAO,MAGzC,mBAAX+Y,EACFqS,GAAcrS,EAAOqS,EAAY9tB,KAAKyF,UAGxCgW,CACT,CAEAgS,mBACE,MAAMM,EAAwB,CAC5BzZ,UAAWtU,KAAK0tB,gBAChBvE,UAAW,CAAC,CACV9tB,KAAM,kBACNkb,QAAS,CACP2K,SAAUlhB,KAAK0F,QAAQwb,WAG3B,CACE7lB,KAAM,SACNkb,QAAS,CACPkF,OAAQzb,KAAK6tB,iBAcnB,OARI7tB,KAAKktB,WAAsC,WAAzBltB,KAAK0F,QAAQknB,WACjCtpB,EAAYC,iBAAiBvD,KAAKitB,MAAO,SAAU,UACnDc,EAAsB5E,UAAY,CAAC,CACjC9tB,KAAM,cACNsa,SAAS,KAIN,IACFoY,KACAhyB,EAAQiE,KAAK0F,QAAQmnB,aAAc,MAACjkB,EAAWmlB,IAEtD,CAEAC,iBAAgBh3B,IAAEA,EAAGkG,OAAEA,IACrB,MAAMsR,EAAQ5H,EAAezH,KA/QF,8DA+Q+Ba,KAAKitB,OAAOlpB,QAAOhN,GAAWkC,EAAUlC,KAE7FyX,EAAM1V,QAMXuE,EAAqBmR,EAAOtR,EAAQlG,IAAQg1B,IAAiBxd,EAAMpN,SAASlE,IAASmwB,OACvF,CAGA,sBAAO5xB,CAAgB+I,GACrB,OAAOxE,KAAK0I,MAAK,WACf,MAAMC,EAAOmkB,GAAS3mB,oBAAoBnG,KAAMwE,GAEhD,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjBmE,EAAKnE,GACd,MAAM,IAAIa,UAAU,oBAAoBb,MAG1CmE,EAAKnE,IANL,CAOF,GACF,CAEA,iBAAOypB,CAAW7uB,GAChB,GAlUuB,IAkUnBA,EAAM4J,QAAiD,UAAf5J,EAAMqB,MArUtC,QAqU0DrB,EAAMpI,IAC1E,OAGF,MAAMk3B,EAActnB,EAAezH,KAAKgtB,IAExC,IAAK,MAAMpjB,KAAUmlB,EAAa,CAChC,MAAMC,EAAUrB,GAAS5mB,YAAY6C,GACrC,IAAKolB,IAAyC,IAA9BA,EAAQzoB,QAAQinB,UAC9B,SAGF,MAAMyB,EAAehvB,EAAMgvB,eACrBC,EAAeD,EAAahtB,SAAS+sB,EAAQlB,OACnD,GACEmB,EAAahtB,SAAS+sB,EAAQ1oB,WACC,WAA9B0oB,EAAQzoB,QAAQinB,YAA2B0B,GACb,YAA9BF,EAAQzoB,QAAQinB,WAA2B0B,EAE5C,SAIF,GAAIF,EAAQlB,MAAMnzB,SAASsF,EAAMlC,UAA4B,UAAfkC,EAAMqB,MA5V1C,QA4V8DrB,EAAMpI,KAAoB,qCAAqCoO,KAAKhG,EAAMlC,OAAOkL,UACvJ,SAGF,MAAMtI,EAAgB,CAAEA,cAAequB,EAAQ1oB,UAE5B,UAAfrG,EAAMqB,OACRX,EAAcqI,WAAa/I,GAG7B+uB,EAAQb,cAAcxtB,EACxB,CACF,CAEA,4BAAOwuB,CAAsBlvB,GAI3B,MAAMmvB,EAAU,kBAAkBnpB,KAAKhG,EAAMlC,OAAOkL,SAC9ComB,EAhXS,WAgXOpvB,EAAMpI,IACtBy3B,EAAkB,CAAC1C,GAAcC,IAAgB5qB,SAAShC,EAAMpI,KAEtE,IAAKy3B,IAAoBD,EACvB,OAGF,GAAID,IAAYC,EACd,OAGFpvB,EAAMmD,iBAGN,MAAMmsB,EAAkB1uB,KAAKkH,QAAQ2B,IACnC7I,KACC4G,EAAeS,KAAKrH,KAAM6I,IAAsB,IAC/CjC,EAAeY,KAAKxH,KAAM6I,IAAsB,IAChDjC,EAAeG,QAAQ8B,GAAsBzJ,EAAMW,eAAetG,YAEhExC,EAAW61B,GAAS3mB,oBAAoBuoB,GAE9C,GAAID,EAIF,OAHArvB,EAAMuvB,kBACN13B,EAAS0b,YACT1b,EAAS+2B,gBAAgB5uB,GAIvBnI,EAASwb,aACXrT,EAAMuvB,kBACN13B,EAASyb,OACTgc,EAAgBrB,QAEpB,EAOF9sB,EAAac,GAAGtI,SAAUkzB,GAAwBpjB,GAAsBikB,GAASwB,uBACjF/tB,EAAac,GAAGtI,SAAUkzB,GAAwBG,GAAeU,GAASwB,uBAC1E/tB,EAAac,GAAGtI,SAAU4S,GAAsBmhB,GAASmB,YACzD1tB,EAAac,GAAGtI,SAAUmzB,GAAsBY,GAASmB,YACzD1tB,EAAac,GAAGtI,SAAU4S,GAAsB9C,IAAsB,SAAUzJ,GAC9EA,EAAMmD,iBACNuqB,GAAS3mB,oBAAoBnG,MAAM+I,QACrC,IAMA9N,EAAmB6xB,ICtbnB,MAAMxxB,GAAO,WAEPqW,GAAkB,OAClBid,GAAkB,gBAAgBtzB,KAElC8I,GAAU,CACdyqB,UAAW,iBACXC,cAAe,KACf7oB,YAAY,EACZhN,WAAW,EACX81B,YAAa,QAGT1qB,GAAc,CAClBwqB,UAAW,SACXC,cAAe,kBACf7oB,WAAY,UACZhN,UAAW,UACX81B,YAAa,oBAOf,MAAMC,WAAiB7qB,EACrBU,YAAYL,GACVgB,QACAxF,KAAK0F,QAAU1F,KAAKuE,WAAWC,GAC/BxE,KAAKivB,aAAc,EACnBjvB,KAAKyF,SAAW,IAClB,CAGA,kBAAWrB,GACT,OAAOA,EACT,CAEA,sBAAWC,GACT,OAAOA,EACT,CAEA,eAAW/I,GACT,OAAOA,EACT,CAGAqX,KAAKxX,GACH,IAAK6E,KAAK0F,QAAQzM,UAEhB,YADA8C,EAAQZ,GAIV6E,KAAKkvB,UAEL,MAAMn4B,EAAUiJ,KAAKmvB,cACjBnvB,KAAK0F,QAAQO,YACfxL,EAAO1D,GAGTA,EAAQ8C,UAAU4Q,IAAIkH,IAEtB3R,KAAKovB,mBAAkB,KACrBrzB,EAAQZ,KAEZ,CAEAuX,KAAKvX,GACE6E,KAAK0F,QAAQzM,WAKlB+G,KAAKmvB,cAAct1B,UAAUlC,OAAOga,IAEpC3R,KAAKovB,mBAAkB,KACrBpvB,KAAK4F,UACL7J,EAAQZ,OARRY,EAAQZ,EAUZ,CAEAyK,UACO5F,KAAKivB,cAIV1uB,EAAaC,IAAIR,KAAKyF,SAAUmpB,IAEhC5uB,KAAKyF,SAAS9N,SACdqI,KAAKivB,aAAc,EACrB,CAGAE,cACE,IAAKnvB,KAAKyF,SAAU,CAClB,MAAM4pB,EAAWt2B,SAASu2B,cAAc,OACxCD,EAASR,UAAY7uB,KAAK0F,QAAQmpB,UAC9B7uB,KAAK0F,QAAQO,YACfopB,EAASx1B,UAAU4Q,IAjGH,QAoGlBzK,KAAKyF,SAAW4pB,CAClB,CAEA,OAAOrvB,KAAKyF,QACd,CAEAf,kBAAkBF,GAGhB,OADAA,EAAOuqB,YAAcl2B,EAAW2L,EAAOuqB,aAChCvqB,CACT,CAEA0qB,UACE,GAAIlvB,KAAKivB,YACP,OAGF,MAAMl4B,EAAUiJ,KAAKmvB,cACrBnvB,KAAK0F,QAAQqpB,YAAYQ,OAAOx4B,GAEhCwJ,EAAac,GAAGtK,EAAS63B,IAAiB,KACxC7yB,EAAQiE,KAAK0F,QAAQopB,kBAGvB9uB,KAAKivB,aAAc,CACrB,CAEAG,kBAAkBj0B,GAChBiB,EAAuBjB,EAAU6E,KAAKmvB,cAAenvB,KAAK0F,QAAQO,WACpE,ECpIF,MAEMJ,GAAY,gBACZ2pB,GAAgB,UAAU3pB,KAC1B4pB,GAAoB,cAAc5pB,KAIlC6pB,GAAmB,WAEnBtrB,GAAU,CACdurB,WAAW,EACXC,YAAa,MAGTvrB,GAAc,CAClBsrB,UAAW,UACXC,YAAa,WAOf,MAAMC,WAAkB1rB,EACtBU,YAAYL,GACVgB,QACAxF,KAAK0F,QAAU1F,KAAKuE,WAAWC,GAC/BxE,KAAK8vB,WAAY,EACjB9vB,KAAK+vB,qBAAuB,IAC9B,CAGA,kBAAW3rB,GACT,OAAOA,EACT,CAEA,sBAAWC,GACT,OAAOA,EACT,CAEA,eAAW/I,GACT,MA1CS,WA2CX,CAGA00B,WACMhwB,KAAK8vB,YAIL9vB,KAAK0F,QAAQiqB,WACf3vB,KAAK0F,QAAQkqB,YAAYvC,QAG3B9sB,EAAaC,IAAIzH,SAAU8M,IAC3BtF,EAAac,GAAGtI,SAAUy2B,IAAepwB,GAASY,KAAKiwB,eAAe7wB,KACtEmB,EAAac,GAAGtI,SAAU02B,IAAmBrwB,GAASY,KAAKkwB,eAAe9wB,KAE1EY,KAAK8vB,WAAY,EACnB,CAEAK,aACOnwB,KAAK8vB,YAIV9vB,KAAK8vB,WAAY,EACjBvvB,EAAaC,IAAIzH,SAAU8M,IAC7B,CAGAoqB,eAAe7wB,GACb,MAAMwwB,YAAEA,GAAgB5vB,KAAK0F,QAE7B,GAAItG,EAAMlC,SAAWnE,UAAYqG,EAAMlC,SAAW0yB,GAAeA,EAAY91B,SAASsF,EAAMlC,QAC1F,OAGF,MAAM6Y,EAAWnP,EAAec,kBAAkBkoB,GAE1B,IAApB7Z,EAASjd,OACX82B,EAAYvC,QACHrtB,KAAK+vB,uBAAyBL,GACvC3Z,EAASA,EAASjd,OAAS,GAAGu0B,QAE9BtX,EAAS,GAAGsX,OAEhB,CAEA6C,eAAe9wB,GApFD,QAqFRA,EAAMpI,MAIVgJ,KAAK+vB,qBAAuB3wB,EAAMgxB,SAAWV,GAxFzB,UAyFtB,EChGF,MAAMW,GAAyB,oDACzBC,GAA0B,cAC1BC,GAAmB,gBACnBC,GAAkB,eAMxB,MAAMC,GACJ5rB,cACE7E,KAAKyF,SAAW1M,SAAS8B,IAC3B,CAGA61B,WAEE,MAAMC,EAAgB53B,SAASoB,gBAAgBmhB,YAC/C,OAAOzd,KAAK0M,IAAIvS,OAAO44B,WAAaD,EACtC,CAEAje,OACE,MAAMsF,EAAQhY,KAAK0wB,WACnB1wB,KAAK6wB,mBAEL7wB,KAAK8wB,sBAAsB9wB,KAAKyF,SAAU8qB,IAAkBQ,GAAmBA,EAAkB/Y,IAEjGhY,KAAK8wB,sBAAsBT,GAAwBE,IAAkBQ,GAAmBA,EAAkB/Y,IAC1GhY,KAAK8wB,sBAAsBR,GAAyBE,IAAiBO,GAAmBA,EAAkB/Y,GAC5G,CAEAiN,QACEjlB,KAAKgxB,wBAAwBhxB,KAAKyF,SAAU,YAC5CzF,KAAKgxB,wBAAwBhxB,KAAKyF,SAAU8qB,IAC5CvwB,KAAKgxB,wBAAwBX,GAAwBE,IACrDvwB,KAAKgxB,wBAAwBV,GAAyBE,GACxD,CAEAS,gBACE,OAAOjxB,KAAK0wB,WAAa,CAC3B,CAGAG,mBACE7wB,KAAKkxB,sBAAsBlxB,KAAKyF,SAAU,YAC1CzF,KAAKyF,SAASsK,MAAMoP,SAAW,QACjC,CAEA2R,sBAAsB/4B,EAAUo5B,EAAeh2B,GAC7C,MAAMi2B,EAAiBpxB,KAAK0wB,WAW5B1wB,KAAKqxB,2BAA2Bt5B,GAVHhB,IAC3B,GAAIA,IAAYiJ,KAAKyF,UAAYzN,OAAO44B,WAAa75B,EAAQukB,YAAc8V,EACzE,OAGFpxB,KAAKkxB,sBAAsBn6B,EAASo6B,GACpC,MAAMJ,EAAkB/4B,OAAOoB,iBAAiBrC,GAASsC,iBAAiB83B,GAC1Ep6B,EAAQgZ,MAAMC,YAAYmhB,EAAe,GAAGh2B,EAASwB,OAAOC,WAAWm0B,WAI3E,CAEAG,sBAAsBn6B,EAASo6B,GAC7B,MAAMG,EAAcv6B,EAAQgZ,MAAM1W,iBAAiB83B,GAC/CG,GACFhuB,EAAYC,iBAAiBxM,EAASo6B,EAAeG,EAEzD,CAEAN,wBAAwBj5B,EAAUo5B,GAahCnxB,KAAKqxB,2BAA2Bt5B,GAZHhB,IAC3B,MAAM2L,EAAQY,EAAYY,iBAAiBnN,EAASo6B,GAEtC,OAAVzuB,GAKJY,EAAYG,oBAAoB1M,EAASo6B,GACzCp6B,EAAQgZ,MAAMC,YAAYmhB,EAAezuB,IALvC3L,EAAQgZ,MAAMwhB,eAAeJ,KASnC,CAEAE,2BAA2Bt5B,EAAUy5B,GACnC,GAAI/4B,EAAUV,GACZy5B,EAASz5B,QAIX,IAAK,MAAM2O,KAAOE,EAAezH,KAAKpH,EAAUiI,KAAKyF,UACnD+rB,EAAS9qB,EAEb,ECxFF,MAEMb,GAAY,YAIZ4L,GAAa,OAAO5L,KACpB4rB,GAAuB,gBAAgB5rB,KACvC6L,GAAe,SAAS7L,KACxB0L,GAAa,OAAO1L,KACpB2L,GAAc,QAAQ3L,KACtB6rB,GAAe,SAAS7rB,KACxB8rB,GAAsB,gBAAgB9rB,KACtC+rB,GAA0B,oBAAoB/rB,KAC9CgsB,GAAwB,kBAAkBhsB,KAC1C8F,GAAuB,QAAQ9F,cAE/BisB,GAAkB,aAElBngB,GAAkB,OAClBogB,GAAoB,eAOpB3tB,GAAU,CACdirB,UAAU,EACVhC,OAAO,EACPtgB,UAAU,GAGN1I,GAAc,CAClBgrB,SAAU,mBACVhC,MAAO,UACPtgB,SAAU,WAOZ,MAAMilB,WAAczsB,EAClBV,YAAY9N,EAASyN,GACnBgB,MAAMzO,EAASyN,GAEfxE,KAAKiyB,QAAUrrB,EAAeG,QAxBV,gBAwBmC/G,KAAKyF,UAC5DzF,KAAKkyB,UAAYlyB,KAAKmyB,sBACtBnyB,KAAKoyB,WAAapyB,KAAKqyB,uBACvBryB,KAAKyS,UAAW,EAChBzS,KAAKiS,kBAAmB,EACxBjS,KAAKsyB,WAAa,IAAI7B,GAEtBzwB,KAAK4N,oBACP,CAGA,kBAAWxJ,GACT,OAAOA,EACT,CAEA,sBAAWC,GACT,OAAOA,EACT,CAEA,eAAW/I,GACT,MAnES,OAoEX,CAGAyN,OAAOjJ,GACL,OAAOE,KAAKyS,SAAWzS,KAAK0S,OAAS1S,KAAK2S,KAAK7S,EACjD,CAEA6S,KAAK7S,GACCE,KAAKyS,UAAYzS,KAAKiS,kBAIR1R,EAAasB,QAAQ7B,KAAKyF,SAAU8L,GAAY,CAChEzR,kBAGYmC,mBAIdjC,KAAKyS,UAAW,EAChBzS,KAAKiS,kBAAmB,EAExBjS,KAAKsyB,WAAW5f,OAEhB3Z,SAAS8B,KAAKhB,UAAU4Q,IAAIqnB,IAE5B9xB,KAAKuyB,gBAELvyB,KAAKkyB,UAAUvf,MAAK,IAAM3S,KAAKwyB,aAAa1yB,KAC9C,CAEA4S,OACO1S,KAAKyS,WAAYzS,KAAKiS,mBAIT1R,EAAasB,QAAQ7B,KAAKyF,SAAUgM,IAExCxP,mBAIdjC,KAAKyS,UAAW,EAChBzS,KAAKiS,kBAAmB,EACxBjS,KAAKoyB,WAAWjC,aAEhBnwB,KAAKyF,SAAS5L,UAAUlC,OAAOga,IAE/B3R,KAAKgG,gBAAe,IAAMhG,KAAKyyB,cAAczyB,KAAKyF,SAAUzF,KAAK8Q,gBACnE,CAEAlL,UACErF,EAAaC,IAAIxI,OAAQ6N,IACzBtF,EAAaC,IAAIR,KAAKiyB,QAASpsB,IAE/B7F,KAAKkyB,UAAUtsB,UACf5F,KAAKoyB,WAAWjC,aAEhB3qB,MAAMI,SACR,CAEA8sB,eACE1yB,KAAKuyB,eACP,CAGAJ,sBACE,OAAO,IAAInD,GAAS,CAClB/1B,UAAW6H,QAAQd,KAAK0F,QAAQ2pB,UAChCppB,WAAYjG,KAAK8Q,eAErB,CAEAuhB,uBACE,OAAO,IAAIxC,GAAU,CACnBD,YAAa5vB,KAAKyF,UAEtB,CAEA+sB,aAAa1yB,GAEN/G,SAAS8B,KAAKf,SAASkG,KAAKyF,WAC/B1M,SAAS8B,KAAK00B,OAAOvvB,KAAKyF,UAG5BzF,KAAKyF,SAASsK,MAAM6c,QAAU,QAC9B5sB,KAAKyF,SAAS/B,gBAAgB,eAC9B1D,KAAKyF,SAASjC,aAAa,cAAc,GACzCxD,KAAKyF,SAASjC,aAAa,OAAQ,UACnCxD,KAAKyF,SAASqZ,UAAY,EAE1B,MAAM6T,EAAY/rB,EAAeG,QAxIT,cAwIsC/G,KAAKiyB,SAC/DU,IACFA,EAAU7T,UAAY,GAGxBrkB,EAAOuF,KAAKyF,UAEZzF,KAAKyF,SAAS5L,UAAU4Q,IAAIkH,IAa5B3R,KAAKgG,gBAXsB4sB,KACrB5yB,KAAK0F,QAAQ2nB,OACfrtB,KAAKoyB,WAAWpC,WAGlBhwB,KAAKiS,kBAAmB,EACxB1R,EAAasB,QAAQ7B,KAAKyF,SAAU+L,GAAa,CAC/C1R,oBAIoCE,KAAKiyB,QAASjyB,KAAK8Q,cAC7D,CAEAlD,qBACErN,EAAac,GAAGrB,KAAKyF,SAAUosB,IAAuBzyB,IApLvC,WAqLTA,EAAMpI,MAINgJ,KAAK0F,QAAQqH,SACf/M,KAAK0S,OAIP1S,KAAK6yB,iCAGPtyB,EAAac,GAAGrJ,OAAQ05B,IAAc,KAChC1xB,KAAKyS,WAAazS,KAAKiS,kBACzBjS,KAAKuyB,mBAIThyB,EAAac,GAAGrB,KAAKyF,SAAUmsB,IAAyBxyB,IAEtDmB,EAAae,IAAItB,KAAKyF,SAAUksB,IAAqBmB,IAC/C9yB,KAAKyF,WAAarG,EAAMlC,QAAU8C,KAAKyF,WAAaqtB,EAAO51B,SAIjC,WAA1B8C,KAAK0F,QAAQ2pB,SAKbrvB,KAAK0F,QAAQ2pB,UACfrvB,KAAK0S,OALL1S,KAAK6yB,mCASb,CAEAJ,aACEzyB,KAAKyF,SAASsK,MAAM6c,QAAU,OAC9B5sB,KAAKyF,SAASjC,aAAa,eAAe,GAC1CxD,KAAKyF,SAAS/B,gBAAgB,cAC9B1D,KAAKyF,SAAS/B,gBAAgB,QAC9B1D,KAAKiS,kBAAmB,EAExBjS,KAAKkyB,UAAUxf,MAAK,KAClB3Z,SAAS8B,KAAKhB,UAAUlC,OAAOm6B,IAC/B9xB,KAAK+yB,oBACL/yB,KAAKsyB,WAAWrN,QAChB1kB,EAAasB,QAAQ7B,KAAKyF,SAAUiM,MAExC,CAEAZ,cACE,OAAO9Q,KAAKyF,SAAS5L,UAAUC,SA5NX,OA6NtB,CAEA+4B,6BAEE,GADkBtyB,EAAasB,QAAQ7B,KAAKyF,SAAUgsB,IACxCxvB,iBACZ,OAGF,MAAM+wB,EAAqBhzB,KAAKyF,SAAS8a,aAAexnB,SAASoB,gBAAgBkhB,aAC3E4X,EAAmBjzB,KAAKyF,SAASsK,MAAMsP,UAEpB,WAArB4T,GAAiCjzB,KAAKyF,SAAS5L,UAAUC,SAASi4B,MAIjEiB,IACHhzB,KAAKyF,SAASsK,MAAMsP,UAAY,UAGlCrf,KAAKyF,SAAS5L,UAAU4Q,IAAIsnB,IAC5B/xB,KAAKgG,gBAAe,KAClBhG,KAAKyF,SAAS5L,UAAUlC,OAAOo6B,IAC/B/xB,KAAKgG,gBAAe,KAClBhG,KAAKyF,SAASsK,MAAMsP,UAAY4T,IAC/BjzB,KAAKiyB,WACPjyB,KAAKiyB,SAERjyB,KAAKyF,SAAS4nB,QAChB,CAMAkF,gBACE,MAAMS,EAAqBhzB,KAAKyF,SAAS8a,aAAexnB,SAASoB,gBAAgBkhB,aAC3E+V,EAAiBpxB,KAAKsyB,WAAW5B,WACjCwC,EAAoB9B,EAAiB,EAE3C,GAAI8B,IAAsBF,EAAoB,CAC5C,MAAMjuB,EAAWhK,IAAU,cAAgB,eAC3CiF,KAAKyF,SAASsK,MAAMhL,GAAY,GAAGqsB,KACrC,CAEA,IAAK8B,GAAqBF,EAAoB,CAC5C,MAAMjuB,EAAWhK,IAAU,eAAiB,cAC5CiF,KAAKyF,SAASsK,MAAMhL,GAAY,GAAGqsB,KACrC,CACF,CAEA2B,oBACE/yB,KAAKyF,SAASsK,MAAMojB,YAAc,GAClCnzB,KAAKyF,SAASsK,MAAMqjB,aAAe,EACrC,CAGA,sBAAO33B,CAAgB+I,EAAQ1E,GAC7B,OAAOE,KAAK0I,MAAK,WACf,MAAMC,EAAOqpB,GAAM7rB,oBAAoBnG,KAAMwE,GAE7C,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjBmE,EAAKnE,GACd,MAAM,IAAIa,UAAU,oBAAoBb,MAG1CmE,EAAKnE,GAAQ1E,EANb,CAOF,GACF,EAOFS,EAAac,GAAGtI,SAAU4S,GAnSG,4BAmSyC,SAAUvM,GAC9E,MAAMlC,EAAS0J,EAAekB,uBAAuB9H,MAEjD,CAAC,IAAK,QAAQoB,SAASpB,KAAKoI,UAC9BhJ,EAAMmD,iBAGRhC,EAAae,IAAIpE,EAAQqU,IAAY8hB,IAC/BA,EAAUpxB,kBAKd1B,EAAae,IAAIpE,EAAQwU,IAAc,KACjCzY,EAAU+G,OACZA,KAAKqtB,cAMX,MAAMiG,EAAc1sB,EAAeG,QA3Tf,eA4ThBusB,GACFtB,GAAM9rB,YAAYotB,GAAa5gB,OAGpBsf,GAAM7rB,oBAAoBjJ,GAElC6L,OAAO/I,KACd,IAEAgI,EAAqBgqB,IAMrB/2B,EAAmB+2B,IC/VnB,MAEMnsB,GAAY,gBACZgF,GAAe,YACfa,GAAsB,OAAO7F,KAAYgF,KAGzC8G,GAAkB,OAClB4hB,GAAqB,UACrBC,GAAoB,SAEpBC,GAAgB,kBAEhBliB,GAAa,OAAO1L,KACpB2L,GAAc,QAAQ3L,KACtB4L,GAAa,OAAO5L,KACpB4rB,GAAuB,gBAAgB5rB,KACvC6L,GAAe,SAAS7L,KACxB6rB,GAAe,SAAS7rB,KACxB8F,GAAuB,QAAQ9F,KAAYgF,KAC3CgnB,GAAwB,kBAAkBhsB,KAI1CzB,GAAU,CACdirB,UAAU,EACVtiB,UAAU,EACVkR,QAAQ,GAGJ5Z,GAAc,CAClBgrB,SAAU,mBACVtiB,SAAU,UACVkR,OAAQ,WAOV,MAAMyV,WAAkBnuB,EACtBV,YAAY9N,EAASyN,GACnBgB,MAAMzO,EAASyN,GAEfxE,KAAKyS,UAAW,EAChBzS,KAAKkyB,UAAYlyB,KAAKmyB,sBACtBnyB,KAAKoyB,WAAapyB,KAAKqyB,uBACvBryB,KAAK4N,oBACP,CAGA,kBAAWxJ,GACT,OAAOA,EACT,CAEA,sBAAWC,GACT,OAAOA,EACT,CAEA,eAAW/I,GACT,MA5DS,WA6DX,CAGAyN,OAAOjJ,GACL,OAAOE,KAAKyS,SAAWzS,KAAK0S,OAAS1S,KAAK2S,KAAK7S,EACjD,CAEA6S,KAAK7S,GACCE,KAAKyS,UAISlS,EAAasB,QAAQ7B,KAAKyF,SAAU8L,GAAY,CAAEzR,kBAEtDmC,mBAIdjC,KAAKyS,UAAW,EAChBzS,KAAKkyB,UAAUvf,OAEV3S,KAAK0F,QAAQuY,SAChB,IAAIwS,IAAkB/d,OAGxB1S,KAAKyF,SAASjC,aAAa,cAAc,GACzCxD,KAAKyF,SAASjC,aAAa,OAAQ,UACnCxD,KAAKyF,SAAS5L,UAAU4Q,IAAI8oB,IAY5BvzB,KAAKgG,gBAVoB6K,KAClB7Q,KAAK0F,QAAQuY,SAAUje,KAAK0F,QAAQ2pB,UACvCrvB,KAAKoyB,WAAWpC,WAGlBhwB,KAAKyF,SAAS5L,UAAU4Q,IAAIkH,IAC5B3R,KAAKyF,SAAS5L,UAAUlC,OAAO47B,IAC/BhzB,EAAasB,QAAQ7B,KAAKyF,SAAU+L,GAAa,CAAE1R,oBAGfE,KAAKyF,UAAU,GACvD,CAEAiN,OACO1S,KAAKyS,WAIQlS,EAAasB,QAAQ7B,KAAKyF,SAAUgM,IAExCxP,mBAIdjC,KAAKoyB,WAAWjC,aAChBnwB,KAAKyF,SAASkuB,OACd3zB,KAAKyS,UAAW,EAChBzS,KAAKyF,SAAS5L,UAAU4Q,IAAI+oB,IAC5BxzB,KAAKkyB,UAAUxf,OAcf1S,KAAKgG,gBAZoB4tB,KACvB5zB,KAAKyF,SAAS5L,UAAUlC,OAAOga,GAAiB6hB,IAChDxzB,KAAKyF,SAAS/B,gBAAgB,cAC9B1D,KAAKyF,SAAS/B,gBAAgB,QAEzB1D,KAAK0F,QAAQuY,SAChB,IAAIwS,IAAkBxL,QAGxB1kB,EAAasB,QAAQ7B,KAAKyF,SAAUiM,MAGA1R,KAAKyF,UAAU,IACvD,CAEAG,UACE5F,KAAKkyB,UAAUtsB,UACf5F,KAAKoyB,WAAWjC,aAChB3qB,MAAMI,SACR,CAGAusB,sBACE,MAUMl5B,EAAY6H,QAAQd,KAAK0F,QAAQ2pB,UAEvC,OAAO,IAAIL,GAAS,CAClBH,UAlJsB,qBAmJtB51B,YACAgN,YAAY,EACZ8oB,YAAa/uB,KAAKyF,SAAShM,WAC3Bq1B,cAAe71B,EAjBK61B,KACU,WAA1B9uB,KAAK0F,QAAQ2pB,SAKjBrvB,KAAK0S,OAJHnS,EAAasB,QAAQ7B,KAAKyF,SAAUgsB,KAeK,MAE/C,CAEAY,uBACE,OAAO,IAAIxC,GAAU,CACnBD,YAAa5vB,KAAKyF,UAEtB,CAEAmI,qBACErN,EAAac,GAAGrB,KAAKyF,SAAUosB,IAAuBzyB,IAtKvC,WAuKTA,EAAMpI,MAINgJ,KAAK0F,QAAQqH,SACf/M,KAAK0S,OAIPnS,EAAasB,QAAQ7B,KAAKyF,SAAUgsB,OAExC,CAGA,sBAAOh2B,CAAgB+I,GACrB,OAAOxE,KAAK0I,MAAK,WACf,MAAMC,EAAO+qB,GAAUvtB,oBAAoBnG,KAAMwE,GAEjD,GAAsB,iBAAXA,EAAX,CAIA,QAAqBoE,IAAjBD,EAAKnE,IAAyBA,EAAO/C,WAAW,MAAmB,gBAAX+C,EAC1D,MAAM,IAAIa,UAAU,oBAAoBb,MAG1CmE,EAAKnE,GAAQxE,KANb,CAOF,GACF,EAOFO,EAAac,GAAGtI,SAAU4S,GAzLG,gCAyLyC,SAAUvM,GAC9E,MAAMlC,EAAS0J,EAAekB,uBAAuB9H,MAMrD,GAJI,CAAC,IAAK,QAAQoB,SAASpB,KAAKoI,UAC9BhJ,EAAMmD,iBAGJ7I,EAAWsG,MACb,OAGFO,EAAae,IAAIpE,EAAQwU,IAAc,KAEjCzY,EAAU+G,OACZA,KAAKqtB,WAKT,MAAMiG,EAAc1sB,EAAeG,QAAQ0sB,IACvCH,GAAeA,IAAgBp2B,GACjCw2B,GAAUxtB,YAAYotB,GAAa5gB,OAGxBghB,GAAUvtB,oBAAoBjJ,GACtC6L,OAAO/I,KACd,IAEAO,EAAac,GAAGrJ,OAAQ0T,IAAqB,KAC3C,IAAK,MAAM3T,KAAY6O,EAAezH,KAAKs0B,IACzCC,GAAUvtB,oBAAoBpO,GAAU4a,UAI5CpS,EAAac,GAAGrJ,OAAQ05B,IAAc,KACpC,IAAK,MAAM36B,KAAW6P,EAAezH,KAAK,gDACG,UAAvC/F,iBAAiBrC,GAASuf,UAC5Bod,GAAUvtB,oBAAoBpP,GAAS2b,UAK7C1K,EAAqB0rB,IAMrBz4B,EAAmBy4B,ICvQnB,MAEM7tB,GAAY,mBACZgF,GAAe,YACfgpB,GAAwB,SAAShuB,KAAYgF,KAC7Ca,GAAsB,OAAO7F,KAAYgF,KACzCipB,GAAsB,oBAM5B,MAAMC,WAAqBxuB,EAEzB,eAAWjK,GACT,MAfS,cAgBX,CAGA,uBAAO04B,CAAiBpsB,GAElB5P,OAAOi8B,QAAU,EACnBrsB,EAAG/N,UAAU4Q,IAAI,oBAEjB7C,EAAG/N,UAAUlC,OAAO,mBAExB,CAEA,sBAAO8D,CAAgB+I,GACrB,OAAOxE,KAAK0I,MAAK,WACf,MAAMC,EAAOorB,GAAa5tB,oBAAoBnG,KAAMwE,GAEpD,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjBmE,EAAKnE,GACd,MAAM,IAAIa,UAAU,oBAAoBb,MAG1CmE,EAAKnE,IANL,CAOF,GACF,EAOFjE,EAAac,GAAGrJ,OAAQ67B,IAAuB,KAC7C,IAAK,MAAMjsB,KAAMhB,EAAezH,KAAK20B,IACnCC,GAAaC,iBAAiBpsB,MAIlCrH,EAAac,GAAGrJ,OAAQ0T,IAAqB,KAC3C,IAAK,MAAM9D,KAAMhB,EAAezH,KAAK20B,IACnCC,GAAaC,iBAAiBpsB,MAQlC3M,EAAmB84B,ICzEnB,MAEaG,GAAmB,CAE9B,IAAK,CAAC,QAAS,MAAO,KAAM,OAAQ,OAJP,kBAK7BhR,EAAG,CAAC,SAAU,OAAQ,QAAS,OAC/BiR,KAAM,GACNhR,EAAG,GACHiR,GAAI,GACJC,IAAK,GACLC,KAAM,GACNC,GAAI,GACJC,IAAK,GACLC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJ7Q,EAAG,GACHpV,IAAK,CAAC,MAAO,SAAU,MAAO,QAAS,QAAS,UAChDkmB,GAAI,GACJC,GAAI,GACJC,EAAG,GACHC,IAAK,GACLC,EAAG,GACHC,MAAO,GACPC,KAAM,GACNC,IAAK,GACLC,IAAK,GACLC,OAAQ,GACRC,EAAG,GACHC,GAAI,IAIAC,GAAgB,IAAIt3B,IAAI,CAC5B,aACA,OACA,OACA,WACA,WACA,SACA,MACA,eASIu3B,GAAmB,0DAEnBC,GAAmBA,CAACrf,EAAWsf,KACnC,MAAMC,EAAgBvf,EAAUrH,SAASlM,cAEzC,OAAI6yB,EAAqB90B,SAAS+0B,IAC5BJ,GAAc7+B,IAAIi/B,IACbr1B,QAAQk1B,GAAiB5wB,KAAKwR,EAAUwf,YAO5CF,EAAqBnyB,QAAOsyB,GAAkBA,aAA0BlxB,SAC5EkgB,MAAKiR,GAASA,EAAMlxB,KAAK+wB,MC9DxB/xB,GAAU,CACdmyB,UAAWrC,GACXsC,QAAS,GACTC,WAAY,GACZ1W,MAAM,EACN2W,UAAU,EACVC,WAAY,KACZC,SAAU,eAGNvyB,GAAc,CAClBkyB,UAAW,SACXC,QAAS,SACTC,WAAY,oBACZ1W,KAAM,UACN2W,SAAU,UACVC,WAAY,kBACZC,SAAU,UAGNC,GAAqB,CACzB9+B,SAAU,mBACV++B,MAAO,kCAOT,MAAMC,WAAwB5yB,EAC5BU,YAAYL,GACVgB,QACAxF,KAAK0F,QAAU1F,KAAKuE,WAAWC,EACjC,CAGA,kBAAWJ,GACT,OAAOA,EACT,CAEA,sBAAWC,GACT,OAAOA,EACT,CAEA,eAAW/I,GACT,MA/CS,iBAgDX,CAGA07B,aACE,OAAO/3B,OAAOC,OAAOc,KAAK0F,QAAQ8wB,SAC/B/vB,KAAIjC,GAAUxE,KAAKi3B,yBAAyBzyB,KAC5CT,OAAOjD,QACZ,CAEAo2B,aACE,OAAOl3B,KAAKg3B,aAAal+B,OAAS,CACpC,CAEAq+B,cAAcX,GAGZ,OAFAx2B,KAAKo3B,cAAcZ,GACnBx2B,KAAK0F,QAAQ8wB,QAAU,IAAKx2B,KAAK0F,QAAQ8wB,WAAYA,GAC9Cx2B,IACT,CAEAq3B,SACE,MAAMC,EAAkBv+B,SAASu2B,cAAc,OAC/CgI,EAAgBrpB,UAAYjO,KAAKu3B,eAAev3B,KAAK0F,QAAQkxB,UAE7D,IAAK,MAAO7+B,EAAUy/B,KAASv4B,OAAOkC,QAAQnB,KAAK0F,QAAQ8wB,SACzDx2B,KAAKy3B,YAAYH,EAAiBE,EAAMz/B,GAG1C,MAAM6+B,EAAWU,EAAgBtwB,SAAS,GACpCyvB,EAAaz2B,KAAKi3B,yBAAyBj3B,KAAK0F,QAAQ+wB,YAM9D,OAJIA,GACFG,EAAS/8B,UAAU4Q,OAAOgsB,EAAW35B,MAAM,MAGtC85B,CACT,CAGAjyB,iBAAiBH,GACfgB,MAAMb,iBAAiBH,GACvBxE,KAAKo3B,cAAc5yB,EAAOgyB,QAC5B,CAEAY,cAAcM,GACZ,IAAK,MAAO3/B,EAAUy+B,KAAYv3B,OAAOkC,QAAQu2B,GAC/ClyB,MAAMb,iBAAiB,CAAE5M,WAAU++B,MAAON,GAAWK,GAEzD,CAEAY,YAAYb,EAAUJ,EAASz+B,GAC7B,MAAM4/B,EAAkB/wB,EAAeG,QAAQhP,EAAU6+B,GAEpDe,KAILnB,EAAUx2B,KAAKi3B,yBAAyBT,IAOpC/9B,EAAU+9B,GACZx2B,KAAK43B,sBAAsB/+B,EAAW29B,GAAUmB,GAI9C33B,KAAK0F,QAAQqa,KACf4X,EAAgB1pB,UAAYjO,KAAKu3B,eAAef,GAIlDmB,EAAgBE,YAAcrB,EAd5BmB,EAAgBhgC,SAepB,CAEA4/B,eAAeG,GACb,OAAO13B,KAAK0F,QAAQgxB,SD1DjB,SAAsBoB,EAAYvB,EAAWwB,GAClD,IAAKD,EAAWh/B,OACd,OAAOg/B,EAGT,GAAIC,GAAgD,mBAArBA,EAC7B,OAAOA,EAAiBD,GAG1B,MACME,GADY,IAAIhgC,OAAOigC,WACKC,gBAAgBJ,EAAY,aACxD/hB,EAAW,GAAGlP,UAAUmxB,EAAgBn9B,KAAKuF,iBAAiB,MAEpE,IAAK,MAAMrJ,KAAWgf,EAAU,CAC9B,MAAMoiB,EAAcphC,EAAQwY,SAASlM,cAErC,IAAKpE,OAAOvH,KAAK6+B,GAAWn1B,SAAS+2B,GAAc,CACjDphC,EAAQY,SACR,QACF,CAEA,MAAMygC,EAAgB,GAAGvxB,UAAU9P,EAAQ6M,YACrCy0B,EAAoB,GAAGxxB,OAAO0vB,EAAU,MAAQ,GAAIA,EAAU4B,IAAgB,IAEpF,IAAK,MAAMvhB,KAAawhB,EACjBnC,GAAiBrf,EAAWyhB,IAC/BthC,EAAQ2M,gBAAgBkT,EAAUrH,SAGxC,CAEA,OAAOyoB,EAAgBn9B,KAAKoT,SAC9B,CC0BmCqqB,CAAaZ,EAAK13B,KAAK0F,QAAQ6wB,UAAWv2B,KAAK0F,QAAQixB,YAAce,CACtG,CAEAT,yBAAyBS,GACvB,OAAO37B,EAAQ27B,EAAK,MAAC9uB,EAAW5I,MAClC,CAEA43B,sBAAsB7gC,EAAS4gC,GAC7B,GAAI33B,KAAK0F,QAAQqa,KAGf,OAFA4X,EAAgB1pB,UAAY,QAC5B0pB,EAAgBpI,OAAOx4B,GAIzB4gC,EAAgBE,YAAc9gC,EAAQ8gC,WACxC,ECvIF,MACMU,GAAwB,IAAI95B,IAAI,CAAC,WAAY,YAAa,eAE1D+5B,GAAkB,OAElB7mB,GAAkB,OAElB8mB,GAAyB,iBACzBC,GAAiB,SAEjBC,GAAmB,gBAEnBC,GAAgB,QAChBC,GAAgB,QAChBC,GAAgB,QAchBC,GAAgB,CACpBC,KAAM,OACNC,IAAK,MACLC,MAAOn+B,IAAU,OAAS,QAC1Bo+B,OAAQ,SACRC,KAAMr+B,IAAU,QAAU,QAGtBqJ,GAAU,CACdmyB,UAAWrC,GACXmF,WAAW,EACXnY,SAAU,kBACVoY,WAAW,EACXC,YAAa,GACbC,MAAO,EACP5V,mBAAoB,CAAC,MAAO,QAAS,SAAU,QAC/C7D,MAAM,EACNtE,OAAQ,CAAC,EAAG,IACZnH,UAAW,MACXuY,aAAc,KACd6J,UAAU,EACVC,WAAY,KACZ5+B,UAAU,EACV6+B,SAAU,+GAIV6C,MAAO,GACP53B,QAAS,eAGLwC,GAAc,CAClBkyB,UAAW,SACX8C,UAAW,UACXnY,SAAU,mBACVoY,UAAW,2BACXC,YAAa,oBACbC,MAAO,kBACP5V,mBAAoB,QACpB7D,KAAM,UACNtE,OAAQ,0BACRnH,UAAW,oBACXuY,aAAc,yBACd6J,SAAU,UACVC,WAAY,kBACZ5+B,SAAU,mBACV6+B,SAAU,SACV6C,MAAO,4BACP53B,QAAS,UAOX,MAAM63B,WAAgBn0B,EACpBV,YAAY9N,EAASyN,GACnB,QAAsB,IAAX+oB,GACT,MAAM,IAAIloB,UAAU,wEAGtBG,MAAMzO,EAASyN,GAGfxE,KAAK25B,YAAa,EAClB35B,KAAK45B,SAAW,EAChB55B,KAAK65B,WAAa,KAClB75B,KAAK85B,eAAiB,GACtB95B,KAAK+sB,QAAU,KACf/sB,KAAK+5B,iBAAmB,KACxB/5B,KAAKg6B,YAAc,KAGnBh6B,KAAKi6B,IAAM,KAEXj6B,KAAKk6B,gBAEAl6B,KAAK0F,QAAQ3N,UAChBiI,KAAKm6B,WAET,CAGA,kBAAW/1B,GACT,OAAOA,EACT,CAEA,sBAAWC,GACT,OAAOA,EACT,CAEA,eAAW/I,GACT,MAxHS,SAyHX,CAGA8+B,SACEp6B,KAAK25B,YAAa,CACpB,CAEAU,UACEr6B,KAAK25B,YAAa,CACpB,CAEAW,gBACEt6B,KAAK25B,YAAc35B,KAAK25B,UAC1B,CAEA5wB,SACO/I,KAAK25B,aAIN35B,KAAKyS,WACPzS,KAAKu6B,SAIPv6B,KAAKw6B,SACP,CAEA50B,UACEyJ,aAAarP,KAAK45B,UAElBr5B,EAAaC,IAAIR,KAAKyF,SAASlM,QAAQm/B,IAAiBC,GAAkB34B,KAAKy6B,mBAE3Ez6B,KAAKyF,SAASxL,aAAa,2BAC7B+F,KAAKyF,SAASjC,aAAa,QAASxD,KAAKyF,SAASxL,aAAa,2BAGjE+F,KAAK06B,iBACLl1B,MAAMI,SACR,CAEA+M,OACE,GAAoC,SAAhC3S,KAAKyF,SAASsK,MAAM6c,QACtB,MAAM,IAAItoB,MAAM,uCAGlB,IAAMtE,KAAK26B,mBAAoB36B,KAAK25B,WAClC,OAGF,MAAMtG,EAAY9yB,EAAasB,QAAQ7B,KAAKyF,SAAUzF,KAAK6E,YAAYwB,UAxJxD,SA0JTu0B,GADa1gC,EAAe8F,KAAKyF,WACLzF,KAAKyF,SAAS4P,cAAclb,iBAAiBL,SAASkG,KAAKyF,UAE7F,GAAI4tB,EAAUpxB,mBAAqB24B,EACjC,OAIF56B,KAAK06B,iBAEL,MAAMT,EAAMj6B,KAAK66B,iBAEjB76B,KAAKyF,SAASjC,aAAa,mBAAoBy2B,EAAIhgC,aAAa,OAEhE,MAAMq/B,UAAEA,GAAct5B,KAAK0F,QAe3B,GAbK1F,KAAKyF,SAAS4P,cAAclb,gBAAgBL,SAASkG,KAAKi6B,OAC7DX,EAAU/J,OAAO0K,GACjB15B,EAAasB,QAAQ7B,KAAKyF,SAAUzF,KAAK6E,YAAYwB,UAzKpC,cA4KnBrG,KAAK+sB,QAAU/sB,KAAKotB,cAAc6M,GAElCA,EAAIpgC,UAAU4Q,IAAIkH,IAMd,iBAAkB5Y,SAASoB,gBAC7B,IAAK,MAAMpD,IAAW,GAAG8P,UAAU9N,SAAS8B,KAAKmM,UAC/CzG,EAAac,GAAGtK,EAAS,YAAayD,GAc1CwF,KAAKgG,gBAVYkN,KACf3S,EAAasB,QAAQ7B,KAAKyF,SAAUzF,KAAK6E,YAAYwB,UA5LvC,WA8LU,IAApBrG,KAAK65B,YACP75B,KAAKu6B,SAGPv6B,KAAK65B,YAAa,IAGU75B,KAAKi6B,IAAKj6B,KAAK8Q,cAC/C,CAEA4B,OACE,GAAK1S,KAAKyS,aAIQlS,EAAasB,QAAQ7B,KAAKyF,SAAUzF,KAAK6E,YAAYwB,UAhNxD,SAiNDpE,iBAAd,CASA,GALYjC,KAAK66B,iBACbhhC,UAAUlC,OAAOga,IAIjB,iBAAkB5Y,SAASoB,gBAC7B,IAAK,MAAMpD,IAAW,GAAG8P,UAAU9N,SAAS8B,KAAKmM,UAC/CzG,EAAaC,IAAIzJ,EAAS,YAAayD,GAI3CwF,KAAK85B,eAAehB,KAAiB,EACrC94B,KAAK85B,eAAejB,KAAiB,EACrC74B,KAAK85B,eAAelB,KAAiB,EACrC54B,KAAK65B,WAAa,KAelB75B,KAAKgG,gBAbYkN,KACXlT,KAAK86B,yBAIJ96B,KAAK65B,YACR75B,KAAK06B,iBAGP16B,KAAKyF,SAAS/B,gBAAgB,oBAC9BnD,EAAasB,QAAQ7B,KAAKyF,SAAUzF,KAAK6E,YAAYwB,UA9OtC,cAiParG,KAAKi6B,IAAKj6B,KAAK8Q,cA/B7C,CAgCF,CAEAwN,SACMte,KAAK+sB,SACP/sB,KAAK+sB,QAAQzO,QAEjB,CAGAqc,iBACE,OAAO75B,QAAQd,KAAK+6B,YACtB,CAEAF,iBAKE,OAJK76B,KAAKi6B,MACRj6B,KAAKi6B,IAAMj6B,KAAKg7B,kBAAkBh7B,KAAKg6B,aAAeh6B,KAAKi7B,2BAGtDj7B,KAAKi6B,GACd,CAEAe,kBAAkBxE,GAChB,MAAMyD,EAAMj6B,KAAKk7B,oBAAoB1E,GAASa,SAG9C,IAAK4C,EACH,OAAO,KAGTA,EAAIpgC,UAAUlC,OAAO6gC,GAAiB7mB,IAEtCsoB,EAAIpgC,UAAU4Q,IAAI,MAAMzK,KAAK6E,YAAYvJ,aAEzC,MAAM6/B,E5EpRKC,KACb,GACEA,GAAUv9B,KAAKw9B,MAjCH,IAiCSx9B,KAAKy9B,gBACnBviC,SAASwiC,eAAeH,IAEjC,OAAOA,G4E+QSI,CAAOx7B,KAAK6E,YAAYvJ,MAAMyH,WAQ5C,OANAk3B,EAAIz2B,aAAa,KAAM23B,GAEnBn7B,KAAK8Q,eACPmpB,EAAIpgC,UAAU4Q,IAAI+tB,IAGbyB,CACT,CAEAwB,WAAWjF,GACTx2B,KAAKg6B,YAAcxD,EACfx2B,KAAKyS,aACPzS,KAAK06B,iBACL16B,KAAK2S,OAET,CAEAuoB,oBAAoB1E,GAalB,OAZIx2B,KAAK+5B,iBACP/5B,KAAK+5B,iBAAiB5C,cAAcX,GAEpCx2B,KAAK+5B,iBAAmB,IAAIhD,GAAgB,IACvC/2B,KAAK0F,QAGR8wB,UACAC,WAAYz2B,KAAKi3B,yBAAyBj3B,KAAK0F,QAAQ6zB,eAIpDv5B,KAAK+5B,gBACd,CAEAkB,yBACE,MAAO,CACLxC,CAACA,IAAyBz4B,KAAK+6B,YAEnC,CAEAA,YACE,OAAO/6B,KAAKi3B,yBAAyBj3B,KAAK0F,QAAQ+zB,QAAUz5B,KAAKyF,SAASxL,aAAa,yBACzF,CAGAyhC,6BAA6Bt8B,GAC3B,OAAOY,KAAK6E,YAAYsB,oBAAoB/G,EAAMW,eAAgBC,KAAK27B,qBACzE,CAEA7qB,cACE,OAAO9Q,KAAK0F,QAAQ2zB,WAAcr5B,KAAKi6B,KAAOj6B,KAAKi6B,IAAIpgC,UAAUC,SAAS0+B,GAC5E,CAEA/lB,WACE,OAAOzS,KAAKi6B,KAAOj6B,KAAKi6B,IAAIpgC,UAAUC,SAAS6X,GACjD,CAEAyb,cAAc6M,GACZ,MAAM3lB,EAAYvY,EAAQiE,KAAK0F,QAAQ4O,UAAW,CAACtU,KAAMi6B,EAAKj6B,KAAKyF,WAC7Dm2B,EAAa7C,GAAczkB,EAAUhP,eAC3C,OAAOioB,GAAoBvtB,KAAKyF,SAAUw0B,EAAKj6B,KAAKytB,iBAAiBmO,GACvE,CAEA/N,aACE,MAAMpS,OAAEA,GAAWzb,KAAK0F,QAExB,MAAsB,iBAAX+V,EACFA,EAAO3e,MAAM,KAAK2J,KAAI/D,GAAS/F,OAAOkT,SAASnN,EAAO,MAGzC,mBAAX+Y,EACFqS,GAAcrS,EAAOqS,EAAY9tB,KAAKyF,UAGxCgW,CACT,CAEAwb,yBAAyBS,GACvB,OAAO37B,EAAQ27B,EAAK,CAAC13B,KAAKyF,SAAUzF,KAAKyF,UAC3C,CAEAgoB,iBAAiBmO,GACf,MAAM7N,EAAwB,CAC5BzZ,UAAWsnB,EACXzS,UAAW,CACT,CACE9tB,KAAM,OACNkb,QAAS,CACPqN,mBAAoB5jB,KAAK0F,QAAQke,qBAGrC,CACEvoB,KAAM,SACNkb,QAAS,CACPkF,OAAQzb,KAAK6tB,eAGjB,CACExyB,KAAM,kBACNkb,QAAS,CACP2K,SAAUlhB,KAAK0F,QAAQwb,WAG3B,CACE7lB,KAAM,QACNkb,QAAS,CACPxf,QAAS,IAAIiJ,KAAK6E,YAAYvJ,eAGlC,CACED,KAAM,kBACNsa,SAAS,EACTC,MAAO,aACPpa,GAAImN,IAGF3I,KAAK66B,iBAAiBr3B,aAAa,wBAAyBmF,EAAKmN,MAAMxB,eAM/E,MAAO,IACFyZ,KACAhyB,EAAQiE,KAAK0F,QAAQmnB,aAAc,MAACjkB,EAAWmlB,IAEtD,CAEAmM,gBACE,MAAM2B,EAAW77B,KAAK0F,QAAQ7D,QAAQ/E,MAAM,KAE5C,IAAK,MAAM+E,KAAWg6B,EACpB,GAAgB,UAAZh6B,EACFtB,EAAac,GAAGrB,KAAKyF,SAAUzF,KAAK6E,YAAYwB,UArZpC,SAqZ4DrG,KAAK0F,QAAQ3N,UAAUqH,IAC7F,MAAM+uB,EAAUnuB,KAAK07B,6BAA6Bt8B,GAClD+uB,EAAQ2L,eAAehB,MAAmB3K,EAAQ1b,YAAc0b,EAAQ2L,eAAehB,KACvF3K,EAAQplB,iBAEL,GAjaU,WAiaNlH,EAA4B,CACrC,MAAMi6B,EAAUj6B,IAAY+2B,GAC1B54B,KAAK6E,YAAYwB,UAzZF,cA0ZfrG,KAAK6E,YAAYwB,UA5ZL,WA6ZR01B,EAAWl6B,IAAY+2B,GAC3B54B,KAAK6E,YAAYwB,UA3ZF,cA4ZfrG,KAAK6E,YAAYwB,UA9ZJ,YAgaf9F,EAAac,GAAGrB,KAAKyF,SAAUq2B,EAAS97B,KAAK0F,QAAQ3N,UAAUqH,IAC7D,MAAM+uB,EAAUnuB,KAAK07B,6BAA6Bt8B,GAClD+uB,EAAQ2L,eAA8B,YAAf16B,EAAMqB,KAAqBo4B,GAAgBD,KAAiB,EACnFzK,EAAQqM,YAEVj6B,EAAac,GAAGrB,KAAKyF,SAAUs2B,EAAU/7B,KAAK0F,QAAQ3N,UAAUqH,IAC9D,MAAM+uB,EAAUnuB,KAAK07B,6BAA6Bt8B,GAClD+uB,EAAQ2L,eAA8B,aAAf16B,EAAMqB,KAAsBo4B,GAAgBD,IACjEzK,EAAQ1oB,SAAS3L,SAASsF,EAAMU,eAElCquB,EAAQoM,WAEZ,CAGFv6B,KAAKy6B,kBAAoB,KACnBz6B,KAAKyF,UACPzF,KAAK0S,QAITnS,EAAac,GAAGrB,KAAKyF,SAASlM,QAAQm/B,IAAiBC,GAAkB34B,KAAKy6B,kBAChF,CAEAN,YACE,MAAMV,EAAQz5B,KAAKyF,SAASxL,aAAa,SAEpCw/B,IAIAz5B,KAAKyF,SAASxL,aAAa,eAAkB+F,KAAKyF,SAASoyB,YAAYrxB,QAC1ExG,KAAKyF,SAASjC,aAAa,aAAci2B,GAG3Cz5B,KAAKyF,SAASjC,aAAa,yBAA0Bi2B,GACrDz5B,KAAKyF,SAAS/B,gBAAgB,SAChC,CAEA82B,SACMx6B,KAAKyS,YAAczS,KAAK65B,WAC1B75B,KAAK65B,YAAa,GAIpB75B,KAAK65B,YAAa,EAElB75B,KAAKg8B,aAAY,KACXh8B,KAAK65B,YACP75B,KAAK2S,SAEN3S,KAAK0F,QAAQ8zB,MAAM7mB,MACxB,CAEA4nB,SACMv6B,KAAK86B,yBAIT96B,KAAK65B,YAAa,EAElB75B,KAAKg8B,aAAY,KACVh8B,KAAK65B,YACR75B,KAAK0S,SAEN1S,KAAK0F,QAAQ8zB,MAAM9mB,MACxB,CAEAspB,YAAY/+B,EAASg/B,GACnB5sB,aAAarP,KAAK45B,UAClB55B,KAAK45B,SAAWx8B,WAAWH,EAASg/B,EACtC,CAEAnB,uBACE,OAAO77B,OAAOC,OAAOc,KAAK85B,gBAAgB14B,UAAS,EACrD,CAEAmD,WAAWC,GACT,MAAM03B,EAAiB54B,EAAYK,kBAAkB3D,KAAKyF,UAE1D,IAAK,MAAM02B,KAAiBl9B,OAAOvH,KAAKwkC,GAClC3D,GAAsBrhC,IAAIilC,WACrBD,EAAeC,GAW1B,OAPA33B,EAAS,IACJ03B,KACmB,iBAAX13B,GAAuBA,EAASA,EAAS,IAEtDA,EAASxE,KAAKyE,gBAAgBD,GAC9BA,EAASxE,KAAK0E,kBAAkBF,GAChCxE,KAAK2E,iBAAiBH,GACfA,CACT,CAEAE,kBAAkBF,GAkBhB,OAjBAA,EAAO80B,WAAiC,IAArB90B,EAAO80B,UAAsBvgC,SAAS8B,KAAOhC,EAAW2L,EAAO80B,WAEtD,iBAAjB90B,EAAOg1B,QAChBh1B,EAAOg1B,MAAQ,CACb7mB,KAAMnO,EAAOg1B,MACb9mB,KAAMlO,EAAOg1B,QAIW,iBAAjBh1B,EAAOi1B,QAChBj1B,EAAOi1B,MAAQj1B,EAAOi1B,MAAM12B,YAGA,iBAAnByB,EAAOgyB,UAChBhyB,EAAOgyB,QAAUhyB,EAAOgyB,QAAQzzB,YAG3ByB,CACT,CAEAm3B,qBACE,MAAMn3B,EAAS,GAEf,IAAK,MAAOxN,EAAK0L,KAAUzD,OAAOkC,QAAQnB,KAAK0F,SACzC1F,KAAK6E,YAAYT,QAAQpN,KAAS0L,IACpC8B,EAAOxN,GAAO0L,GAUlB,OANA8B,EAAOzM,UAAW,EAClByM,EAAO3C,QAAU,SAKV2C,CACT,CAEAk2B,iBACM16B,KAAK+sB,UACP/sB,KAAK+sB,QAAQtB,UACbzrB,KAAK+sB,QAAU,MAGb/sB,KAAKi6B,MACPj6B,KAAKi6B,IAAItiC,SACTqI,KAAKi6B,IAAM,KAEf,CAGA,sBAAOx+B,CAAgB+I,GACrB,OAAOxE,KAAK0I,MAAK,WACf,MAAMC,EAAO+wB,GAAQvzB,oBAAoBnG,KAAMwE,GAE/C,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjBmE,EAAKnE,GACd,MAAM,IAAIa,UAAU,oBAAoBb,MAG1CmE,EAAKnE,IANL,CAOF,GACF,EAOFvJ,EAAmBy+B,ICxmBnB,MAEM0C,GAAiB,kBACjBC,GAAmB,gBAEnBj4B,GAAU,IACXs1B,GAAQt1B,QACXoyB,QAAS,GACT/a,OAAQ,CAAC,EAAG,IACZnH,UAAW,QACXsiB,SAAU,8IAKV/0B,QAAS,SAGLwC,GAAc,IACfq1B,GAAQr1B,YACXmyB,QAAS,kCAOX,MAAM8F,WAAgB5C,GAEpB,kBAAWt1B,GACT,OAAOA,EACT,CAEA,sBAAWC,GACT,OAAOA,EACT,CAEA,eAAW/I,GACT,MAtCS,SAuCX,CAGAq/B,iBACE,OAAO36B,KAAK+6B,aAAe/6B,KAAKu8B,aAClC,CAGAtB,yBACE,MAAO,CACLmB,CAACA,IAAiBp8B,KAAK+6B,YACvBsB,CAACA,IAAmBr8B,KAAKu8B,cAE7B,CAEAA,cACE,OAAOv8B,KAAKi3B,yBAAyBj3B,KAAK0F,QAAQ8wB,QACpD,CAGA,sBAAO/6B,CAAgB+I,GACrB,OAAOxE,KAAK0I,MAAK,WACf,MAAMC,EAAO2zB,GAAQn2B,oBAAoBnG,KAAMwE,GAE/C,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjBmE,EAAKnE,GACd,MAAM,IAAIa,UAAU,oBAAoBb,MAG1CmE,EAAKnE,IANL,CAOF,GACF,EAOFvJ,EAAmBqhC,IC9EnB,MAEMz2B,GAAY,uBACZgF,GAAe,YAEfa,GAAsB,OAAO7F,KAAYgF,KACzC2xB,GAAwB,SAAS32B,KAAYgF,KAC7Cc,GAAuB,QAAQ9F,KAAYgF,KAE3C4xB,GAA0B,sBAC1BC,GAA4B,wBAC5BC,GAAyB,2BACzBC,GAA6B,qBAMnC,MAAMC,WAAyBt3B,EAE7B,eAAWjK,GACT,MArBS,kBAsBX,CAGAwhC,YAAY/lC,GACV,MAAMgmC,EAAehmC,EAAQiC,cAAc2jC,IACrCK,EAAQjmC,EAAQiC,cAAcyjC,IAC9BQ,EAAUlmC,EAAQiC,cAAc0jC,IAEhC3+B,EAAMg/B,EAAa9iC,aAAa,OAChC6D,EAAMi/B,EAAa9iC,aAAa,OAChCijC,EAAOvgC,OAAOogC,EAAa9iC,aAAa,SAE1C0C,OAAOogC,EAAar6B,OAASw6B,EAAOn/B,GACtCk/B,EAAQz5B,aAAa,WAAY,IAG/B7G,OAAOogC,EAAar6B,OAASw6B,EAAOp/B,GACtCk/B,EAAMx5B,aAAa,WAAY,GAEnC,CAGA,aAAO25B,CAAO/9B,GACZ,MACM29B,EADS39B,EAAMlC,OAAO3D,QAAQqjC,IACR5jC,cAAc2jC,IAEpC7+B,EAAMi/B,EAAa9iC,aAAa,OAChCijC,EAAOvgC,OAAOogC,EAAa9iC,aAAa,SACxC8c,EAAQpa,OAAOogC,EAAa9iC,aAAa,kBAEzCmjC,EAAc,IAAI5kC,MAAM,UAE1BmE,OAAOogC,EAAar6B,OAAS5E,IAC/Bi/B,EAAar6B,OAAS/F,OAAOogC,EAAar6B,OAASw6B,GAAMG,QAAQtmB,GAAOhU,YAG1Eg6B,EAAaxkC,cAAc6kC,EAC7B,CAEA,eAAOE,CAASl+B,GACd,MACM29B,EADS39B,EAAMlC,OAAO3D,QAAQqjC,IACR5jC,cAAc2jC,IAEpC5+B,EAAMg/B,EAAa9iC,aAAa,OAChCijC,EAAOvgC,OAAOogC,EAAa9iC,aAAa,SACxC8c,EAAQpa,OAAOogC,EAAa9iC,aAAa,kBAEzCmjC,EAAc,IAAI5kC,MAAM,UAE1BmE,OAAOogC,EAAar6B,OAAS3E,IAC/Bg/B,EAAar6B,OAAS/F,OAAOogC,EAAar6B,OAASw6B,GAAMG,QAAQtmB,GAAOhU,YAG1Eg6B,EAAaxkC,cAAc6kC,EAC7B,CAEA,8BAAOG,CAAwBn+B,GAC7B,MAAM2S,EAAS3S,EAAMlC,OAAO3D,QAAQqjC,IAC9BG,EAAehrB,EAAO/Y,cAAc2jC,IACpCK,EAAQjrB,EAAO/Y,cAAcyjC,IAC7BQ,EAAUlrB,EAAO/Y,cAAc0jC,IAE/B3+B,EAAMg/B,EAAa9iC,aAAa,OAChC6D,EAAMi/B,EAAa9iC,aAAa,OAChCijC,EAAOvgC,OAAOogC,EAAa9iC,aAAa,SAE9C+iC,EAAMt5B,gBAAgB,WAAY,IAClCu5B,EAAQv5B,gBAAgB,WAAY,IAEhC/G,OAAOogC,EAAar6B,OAASw6B,EAAOn/B,GACtCk/B,EAAQz5B,aAAa,WAAY,IAG/B7G,OAAOogC,EAAar6B,OAASw6B,EAAOp/B,GACtCk/B,EAAMx5B,aAAa,WAAY,GAEnC,CAEA,sBAAO/H,CAAgB+I,GACrB,OAAOxE,KAAK0I,MAAK,WACf,MAAMC,EAAOk0B,GAAiB12B,oBAAoBnG,KAAMwE,GAExD,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjBmE,EAAKnE,GACd,MAAM,IAAIa,UAAU,oBAAoBb,MAG1CmE,EAAKnE,IANL,CAOF,GACF,EAOFjE,EAAac,GAAGtI,SAAUyjC,GAAuBG,GAAwBE,GAAiBU,yBAC1Fh9B,EAAac,GAAGtI,SAAU4S,GAAsB8wB,GAAyBI,GAAiBM,QAC1F58B,EAAac,GAAGtI,SAAU4S,GAAsB+wB,GAA2BG,GAAiBS,UAE5F/8B,EAAac,GAAGrJ,OAAQ0T,IAAqB,KAC3C,IAAK,MAAM9D,KAAMhB,EAAezH,KAAKy9B,IACnCC,GAAiB12B,oBAAoByB,GAAIk1B,YAAYl1B,MAQzD3M,EAAmB4hC,ICrInB,MAEMh3B,GAAY,gBAGZ23B,GAAiB,WAAW33B,KAC5B43B,GAAc,QAAQ53B,KACtB6F,GAAsB,OAAO7F,cAG7BgG,GAAoB,SAGpB6xB,GAAwB,SAExBC,GAAqB,YAGrBC,GAAsB,GAAGD,mBAA+CA,uBAIxEv5B,GAAU,CACdqX,OAAQ,KACRoiB,WAAY,eACZC,cAAc,EACd5gC,OAAQ,KACR6gC,UAAW,CAAC,GAAK,GAAK,IAGlB15B,GAAc,CAClBoX,OAAQ,gBACRoiB,WAAY,SACZC,aAAc,UACd5gC,OAAQ,UACR6gC,UAAW,SAOb,MAAMC,WAAkBz4B,EACtBV,YAAY9N,EAASyN,GACnBgB,MAAMzO,EAASyN,GAGfxE,KAAKi+B,aAAe,IAAIrnC,IACxBoJ,KAAKk+B,oBAAsB,IAAItnC,IAC/BoJ,KAAKm+B,aAA6D,YAA9C/kC,iBAAiB4G,KAAKyF,UAAU4Z,UAA0B,KAAOrf,KAAKyF,SAC1FzF,KAAKo+B,cAAgB,KACrBp+B,KAAKq+B,UAAY,KACjBr+B,KAAKs+B,oBAAsB,CACzBC,gBAAiB,EACjBC,gBAAiB,GAEnBx+B,KAAKy+B,SACP,CAGA,kBAAWr6B,GACT,OAAOA,EACT,CAEA,sBAAWC,GACT,OAAOA,EACT,CAEA,eAAW/I,GACT,MArES,WAsEX,CAGAmjC,UACEz+B,KAAK0+B,mCACL1+B,KAAK2+B,2BAED3+B,KAAKq+B,UACPr+B,KAAKq+B,UAAUO,aAEf5+B,KAAKq+B,UAAYr+B,KAAK6+B,kBAGxB,IAAK,MAAMC,KAAW9+B,KAAKk+B,oBAAoBh/B,SAC7Cc,KAAKq+B,UAAUU,QAAQD,EAE3B,CAEAl5B,UACE5F,KAAKq+B,UAAUO,aACfp5B,MAAMI,SACR,CAGAlB,kBAAkBF,GAWhB,OATAA,EAAOtH,OAASrE,EAAW2L,EAAOtH,SAAWnE,SAAS8B,KAGtD2J,EAAOq5B,WAAar5B,EAAOiX,OAAS,GAAGjX,EAAOiX,oBAAsBjX,EAAOq5B,WAE3C,iBAArBr5B,EAAOu5B,YAChBv5B,EAAOu5B,UAAYv5B,EAAOu5B,UAAUjhC,MAAM,KAAK2J,KAAI/D,GAAS/F,OAAOC,WAAW8F,MAGzE8B,CACT,CAEAm6B,2BACO3+B,KAAK0F,QAAQo4B,eAKlBv9B,EAAaC,IAAIR,KAAK0F,QAAQxI,OAAQugC,IAEtCl9B,EAAac,GAAGrB,KAAK0F,QAAQxI,OAAQugC,GAAaC,IAAuBt+B,IACvE,MAAM4/B,EAAoBh/B,KAAKk+B,oBAAoB9mC,IAAIgI,EAAMlC,OAAOqhB,MACpE,GAAIygB,EAAmB,CACrB5/B,EAAMmD,iBACN,MAAMjI,EAAO0F,KAAKm+B,cAAgBnmC,OAC5BigB,EAAS+mB,EAAkBzmB,UAAYvY,KAAKyF,SAAS8S,UAC3D,GAAIje,EAAK2kC,SAEP,YADA3kC,EAAK2kC,SAAS,CAAE1rB,IAAK0E,EAAQinB,SAAU,WAKzC5kC,EAAKwkB,UAAY7G,CACnB,KAEJ,CAEA4mB,kBACE,MAAMtoB,EAAU,CACdjc,KAAM0F,KAAKm+B,aACXJ,UAAW/9B,KAAK0F,QAAQq4B,UACxBF,WAAY79B,KAAK0F,QAAQm4B,YAG3B,OAAO,IAAIsB,sBAAqBh+B,GAAWnB,KAAKo/B,kBAAkBj+B,IAAUoV,EAC9E,CAGA6oB,kBAAkBj+B,GAChB,MAAMk+B,EAAgBvI,GAAS92B,KAAKi+B,aAAa7mC,IAAI,IAAI0/B,EAAM55B,OAAO7E,MAChE23B,EAAW8G,IACf92B,KAAKs+B,oBAAoBC,gBAAkBzH,EAAM55B,OAAOqb,UACxDvY,KAAKs/B,SAASD,EAAcvI,KAGxB0H,GAAmBx+B,KAAKm+B,cAAgBplC,SAASoB,iBAAiB2kB,UAClEygB,EAAkBf,GAAmBx+B,KAAKs+B,oBAAoBE,gBACpEx+B,KAAKs+B,oBAAoBE,gBAAkBA,EAE3C,IAAK,MAAM1H,KAAS31B,EAAS,CAC3B,IAAK21B,EAAM0I,eAAgB,CACzBx/B,KAAKo+B,cAAgB,KACrBp+B,KAAKy/B,kBAAkBJ,EAAcvI,IAErC,QACF,CAEA,MAAM4I,EAA2B5I,EAAM55B,OAAOqb,WAAavY,KAAKs+B,oBAAoBC,gBAEpF,GAAIgB,GAAmBG,GAGrB,GAFA1P,EAAS8G,IAEJ0H,EACH,YAOCe,GAAoBG,GACvB1P,EAAS8G,EAEb,CACF,CAEA4H,mCACE1+B,KAAKi+B,aAAe,IAAIrnC,IACxBoJ,KAAKk+B,oBAAsB,IAAItnC,IAE/B,MAAM+oC,EAAc/4B,EAAezH,KAAKu+B,GAAuB19B,KAAK0F,QAAQxI,QAE5E,IAAK,MAAM0iC,KAAUD,EAAa,CAEhC,IAAKC,EAAOrhB,MAAQ7kB,EAAWkmC,GAC7B,SAGF,MAAMZ,EAAoBp4B,EAAeG,QAAQ84B,UAAUD,EAAOrhB,MAAOve,KAAKyF,UAG1ExM,EAAU+lC,KACZh/B,KAAKi+B,aAAannC,IAAI+oC,UAAUD,EAAOrhB,MAAOqhB,GAC9C5/B,KAAKk+B,oBAAoBpnC,IAAI8oC,EAAOrhB,KAAMygB,GAE9C,CACF,CAEAM,SAASpiC,GACH8C,KAAKo+B,gBAAkBlhC,IAI3B8C,KAAKy/B,kBAAkBz/B,KAAK0F,QAAQxI,QACpC8C,KAAKo+B,cAAgBlhC,EACrBA,EAAOrD,UAAU4Q,IAAIoB,IACrB7L,KAAK8/B,iBAAiB5iC,GAEtBqD,EAAasB,QAAQ7B,KAAKyF,SAAU+3B,GAAgB,CAAE19B,cAAe5C,IACvE,CAEA4iC,iBAAiB5iC,GAEf,GAAIA,EAAOrD,UAAUC,SAlNQ,iBAmN3B8M,EAAeG,QAxMY,mBAwMsB7J,EAAO3D,QAzMpC,cA0MjBM,UAAU4Q,IAAIoB,SAInB,IAAK,MAAMk0B,KAAan5B,EAAeO,QAAQjK,EAnNnB,qBAsN1B,IAAK,MAAMma,KAAQzQ,EAAeS,KAAK04B,EAAWnC,IAChDvmB,EAAKxd,UAAU4Q,IAAIoB,GAGzB,CAEA4zB,kBAAkB1tB,GAChBA,EAAOlY,UAAUlC,OAAOkU,IAExB,MAAMm0B,EAAcp5B,EAAezH,KAAK,GAAGu+B,MAAyB7xB,KAAqBkG,GACzF,IAAK,MAAMqD,KAAQ4qB,EACjB5qB,EAAKvb,UAAUlC,OAAOkU,GAE1B,CAGA,sBAAOpQ,CAAgB+I,GACrB,OAAOxE,KAAK0I,MAAK,WACf,MAAMC,EAAOq1B,GAAU73B,oBAAoBnG,KAAMwE,GAEjD,GAAsB,iBAAXA,EAAX,CAIA,QAAqBoE,IAAjBD,EAAKnE,IAAyBA,EAAO/C,WAAW,MAAmB,gBAAX+C,EAC1D,MAAM,IAAIa,UAAU,oBAAoBb,MAG1CmE,EAAKnE,IANL,CAOF,GACF,EAOFjE,EAAac,GAAGrJ,OAAQ0T,IAAqB,KAC3C,IAAK,MAAMu0B,KAAOr5B,EAAezH,KA9PT,0BA+PtB6+B,GAAU73B,oBAAoB85B,MAQlChlC,EAAmB+iC,ICrRnB,MAEMn4B,GAAY,UAEZ4L,GAAa,OAAO5L,KACpB6L,GAAe,SAAS7L,KACxB0L,GAAa,OAAO1L,KACpB2L,GAAc,QAAQ3L,KACtB8F,GAAuB,QAAQ9F,KAC/ByF,GAAgB,UAAUzF,KAC1B6F,GAAsB,OAAO7F,KAE7BiF,GAAiB,YACjBC,GAAkB,aAClBghB,GAAe,UACfC,GAAiB,YACjBkU,GAAW,OACXC,GAAU,MAEVt0B,GAAoB,SACpB2sB,GAAkB,OAClB7mB,GAAkB,OAGlByuB,GAA2B,mBAE3BC,GAA+B,QAAQD,MAKvCv3B,GAAuB,2EACvBy3B,GAAsB,YAFOD,uBAAiDA,mBAA6CA,OAE/Ex3B,KAE5C03B,GAA8B,IAAI10B,8BAA6CA,+BAA8CA,4BAMnI,MAAM20B,WAAYj7B,EAChBV,YAAY9N,GACVyO,MAAMzO,GACNiJ,KAAKgtB,QAAUhtB,KAAKyF,SAASlM,QAfN,uCAiBlByG,KAAKgtB,UAOVhtB,KAAKygC,sBAAsBzgC,KAAKgtB,QAAShtB,KAAK0gC,gBAE9CngC,EAAac,GAAGrB,KAAKyF,SAAU6F,IAAelM,GAASY,KAAK+O,SAAS3P,KACvE,CAGA,eAAW9D,GACT,MA3DS,KA4DX,CAGAqX,OACE,MAAMguB,EAAY3gC,KAAKyF,SACvB,GAAIzF,KAAK4gC,cAAcD,GACrB,OAIF,MAAME,EAAS7gC,KAAK8gC,iBAEdC,EAAYF,EAChBtgC,EAAasB,QAAQg/B,EAAQpvB,GAAY,CAAE3R,cAAe6gC,IAC1D,KAEgBpgC,EAAasB,QAAQ8+B,EAAWpvB,GAAY,CAAEzR,cAAe+gC,IAEjE5+B,kBAAqB8+B,GAAaA,EAAU9+B,mBAI1DjC,KAAKghC,YAAYH,EAAQF,GACzB3gC,KAAKihC,UAAUN,EAAWE,GAC5B,CAGAI,UAAUlqC,EAASmqC,GACZnqC,IAILA,EAAQ8C,UAAU4Q,IAAIoB,IAEtB7L,KAAKihC,UAAUr6B,EAAekB,uBAAuB/Q,IAgBrDiJ,KAAKgG,gBAdYkN,KACsB,QAAjCnc,EAAQkD,aAAa,SAKzBlD,EAAQ2M,gBAAgB,YACxB3M,EAAQyM,aAAa,iBAAiB,GACtCxD,KAAKmhC,gBAAgBpqC,GAAS,GAC9BwJ,EAAasB,QAAQ9K,EAASya,GAAa,CACzC1R,cAAeohC,KARfnqC,EAAQ8C,UAAU4Q,IAAIkH,MAYI5a,EAASA,EAAQ8C,UAAUC,SAAS0+B,KACpE,CAEAwI,YAAYjqC,EAASmqC,GACdnqC,IAILA,EAAQ8C,UAAUlC,OAAOkU,IACzB9U,EAAQ48B,OAER3zB,KAAKghC,YAAYp6B,EAAekB,uBAAuB/Q,IAcvDiJ,KAAKgG,gBAZYkN,KACsB,QAAjCnc,EAAQkD,aAAa,SAKzBlD,EAAQyM,aAAa,iBAAiB,GACtCzM,EAAQyM,aAAa,WAAY,MACjCxD,KAAKmhC,gBAAgBpqC,GAAS,GAC9BwJ,EAAasB,QAAQ9K,EAAS2a,GAAc,CAAE5R,cAAeohC,KAP3DnqC,EAAQ8C,UAAUlC,OAAOga,MAUC5a,EAASA,EAAQ8C,UAAUC,SAAS0+B,KACpE,CAEAzpB,SAAS3P,GACP,IAAM,CAAC0L,GAAgBC,GAAiBghB,GAAcC,GAAgBkU,GAAUC,IAAS/+B,SAAShC,EAAMpI,KACtG,OAGFoI,EAAMuvB,kBACNvvB,EAAMmD,iBAEN,MAAMyE,EAAWhH,KAAK0gC,eAAe38B,QAAOhN,IAAY2C,EAAW3C,KACnE,IAAIqqC,EAEJ,GAAI,CAAClB,GAAUC,IAAS/+B,SAAShC,EAAMpI,KACrCoqC,EAAoBp6B,EAAS5H,EAAMpI,MAAQkpC,GAAW,EAAIl5B,EAASlO,OAAS,OACvE,CACL,MAAMmX,EAAS,CAAClF,GAAiBihB,IAAgB5qB,SAAShC,EAAMpI,KAChEoqC,EAAoB/jC,EAAqB2J,EAAU5H,EAAMlC,OAAQ+S,GAAQ,EAC3E,CAEImxB,IACFA,EAAkB/T,MAAM,CAAEgU,eAAe,IACzCb,GAAIr6B,oBAAoBi7B,GAAmBzuB,OAE/C,CAEA+tB,eACE,OAAO95B,EAAezH,KAAKmhC,GAAqBtgC,KAAKgtB,QACvD,CAEA8T,iBACE,OAAO9gC,KAAK0gC,eAAevhC,MAAK8H,GAASjH,KAAK4gC,cAAc35B,MAAW,IACzE,CAEAw5B,sBAAsB1uB,EAAQ/K,GAC5BhH,KAAKshC,yBAAyBvvB,EAAQ,OAAQ,WAE9C,IAAK,MAAM9K,KAASD,EAClBhH,KAAKuhC,6BAA6Bt6B,EAEtC,CAEAs6B,6BAA6Bt6B,GAC3BA,EAAQjH,KAAKwhC,iBAAiBv6B,GAC9B,MAAMw6B,EAAWzhC,KAAK4gC,cAAc35B,GAC9By6B,EAAY1hC,KAAK2hC,iBAAiB16B,GACxCA,EAAMzD,aAAa,gBAAiBi+B,GAEhCC,IAAcz6B,GAChBjH,KAAKshC,yBAAyBI,EAAW,OAAQ,gBAG9CD,GACHx6B,EAAMzD,aAAa,WAAY,MAGjCxD,KAAKshC,yBAAyBr6B,EAAO,OAAQ,OAG7CjH,KAAK4hC,mCAAmC36B,EAC1C,CAEA26B,mCAAmC36B,GACjC,MAAM/J,EAAS0J,EAAekB,uBAAuBb,GAEhD/J,IAIL8C,KAAKshC,yBAAyBpkC,EAAQ,OAAQ,YAE1C+J,EAAM5O,IACR2H,KAAKshC,yBAAyBpkC,EAAQ,kBAAmB,GAAG+J,EAAM5O,MAEtE,CAEA8oC,gBAAgBpqC,EAAS8qC,GACvB,MAAMH,EAAY1hC,KAAK2hC,iBAAiB5qC,GACxC,IAAK2qC,EAAU7nC,UAAUC,SAhMN,YAiMjB,OAGF,MAAMiP,EAASA,CAAChR,EAAU82B,KACxB,MAAM93B,EAAU6P,EAAeG,QAAQhP,EAAU2pC,GAC7C3qC,GACFA,EAAQ8C,UAAUkP,OAAO8lB,EAAWgT,IAIxC94B,EAAOq3B,GAA0Bv0B,IACjC9C,EAzM2B,iBAyMI4I,IAC/B+vB,EAAUl+B,aAAa,gBAAiBq+B,EAC1C,CAEAP,yBAAyBvqC,EAAS6f,EAAWlU,GACtC3L,EAAQiD,aAAa4c,IACxB7f,EAAQyM,aAAaoT,EAAWlU,EAEpC,CAEAk+B,cAAcxuB,GACZ,OAAOA,EAAKvY,UAAUC,SAAS+R,GACjC,CAGA21B,iBAAiBpvB,GACf,OAAOA,EAAKlL,QAAQo5B,IAAuBluB,EAAOxL,EAAeG,QAAQu5B,GAAqBluB,EAChG,CAGAuvB,iBAAiBvvB,GACf,OAAOA,EAAK7Y,QA1NO,gCA0NoB6Y,CACzC,CAGA,sBAAO3W,CAAgB+I,GACrB,OAAOxE,KAAK0I,MAAK,WACf,MAAMC,EAAO63B,GAAIr6B,oBAAoBnG,MAErC,GAAsB,iBAAXwE,EAAX,CAIA,QAAqBoE,IAAjBD,EAAKnE,IAAyBA,EAAO/C,WAAW,MAAmB,gBAAX+C,EAC1D,MAAM,IAAIa,UAAU,oBAAoBb,MAG1CmE,EAAKnE,IANL,CAOF,GACF,EAOFjE,EAAac,GAAGtI,SAAU4S,GAAsB9C,IAAsB,SAAUzJ,GAC1E,CAAC,IAAK,QAAQgC,SAASpB,KAAKoI,UAC9BhJ,EAAMmD,iBAGJ7I,EAAWsG,OAIfwgC,GAAIr6B,oBAAoBnG,MAAM2S,MAChC,IAKApS,EAAac,GAAGrJ,OAAQ0T,IAAqB,KAC3C,IAAK,MAAM3U,KAAW6P,EAAezH,KAAKohC,IACxCC,GAAIr6B,oBAAoBpP,MAO5BkE,EAAmBulC,ICxSnB,MAEM36B,GAAY,YAEZi8B,GAAkB,YAAYj8B,KAC9Bk8B,GAAiB,WAAWl8B,KAC5B2pB,GAAgB,UAAU3pB,KAC1Bm8B,GAAiB,WAAWn8B,KAC5B4L,GAAa,OAAO5L,KACpB6L,GAAe,SAAS7L,KACxB0L,GAAa,OAAO1L,KACpB2L,GAAc,QAAQ3L,KAGtBo8B,GAAkB,OAClBtwB,GAAkB,OAClB4hB,GAAqB,UAErBlvB,GAAc,CAClBg1B,UAAW,UACX6I,SAAU,UACV1I,MAAO,UAGHp1B,GAAU,CACdi1B,WAAW,EACX6I,UAAU,EACV1I,MAAO,KAOT,MAAM2I,WAAc58B,EAClBV,YAAY9N,EAASyN,GACnBgB,MAAMzO,EAASyN,GAEfxE,KAAK45B,SAAW,KAChB55B,KAAKoiC,sBAAuB,EAC5BpiC,KAAKqiC,yBAA0B,EAC/BriC,KAAKk6B,eACP,CAGA,kBAAW91B,GACT,OAAOA,EACT,CAEA,sBAAWC,GACT,OAAOA,EACT,CAEA,eAAW/I,GACT,MAtDS,OAuDX,CAGAqX,OACoBpS,EAAasB,QAAQ7B,KAAKyF,SAAU8L,IAExCtP,mBAIdjC,KAAKsiC,gBAEDtiC,KAAK0F,QAAQ2zB,WACfr5B,KAAKyF,SAAS5L,UAAU4Q,IAvDN,QAiEpBzK,KAAKyF,SAAS5L,UAAUlC,OAAOsqC,IAC/BxnC,EAAOuF,KAAKyF,UACZzF,KAAKyF,SAAS5L,UAAU4Q,IAAIkH,GAAiB4hB,IAE7CvzB,KAAKgG,gBAXYkN,KACflT,KAAKyF,SAAS5L,UAAUlC,OAAO47B,IAC/BhzB,EAAasB,QAAQ7B,KAAKyF,SAAU+L,IAEpCxR,KAAKuiC,uBAOuBviC,KAAKyF,SAAUzF,KAAK0F,QAAQ2zB,WAC5D,CAEA3mB,OACO1S,KAAKwiC,YAIQjiC,EAAasB,QAAQ7B,KAAKyF,SAAUgM,IAExCxP,mBAUdjC,KAAKyF,SAAS5L,UAAU4Q,IAAI8oB,IAC5BvzB,KAAKgG,gBAPYkN,KACflT,KAAKyF,SAAS5L,UAAU4Q,IAAIw3B,IAC5BjiC,KAAKyF,SAAS5L,UAAUlC,OAAO47B,GAAoB5hB,IACnDpR,EAAasB,QAAQ7B,KAAKyF,SAAUiM,MAIR1R,KAAKyF,SAAUzF,KAAK0F,QAAQ2zB,YAC5D,CAEAzzB,UACE5F,KAAKsiC,gBAEDtiC,KAAKwiC,WACPxiC,KAAKyF,SAAS5L,UAAUlC,OAAOga,IAGjCnM,MAAMI,SACR,CAEA48B,UACE,OAAOxiC,KAAKyF,SAAS5L,UAAUC,SAAS6X,GAC1C,CAGA4wB,qBACOviC,KAAK0F,QAAQw8B,WAIdliC,KAAKoiC,sBAAwBpiC,KAAKqiC,0BAItCriC,KAAK45B,SAAWx8B,YAAW,KACzB4C,KAAK0S,SACJ1S,KAAK0F,QAAQ8zB,QAClB,CAEAiJ,eAAerjC,EAAOsjC,GACpB,OAAQtjC,EAAMqB,MACZ,IAAK,YACL,IAAK,WACHT,KAAKoiC,qBAAuBM,EAC5B,MAGF,IAAK,UACL,IAAK,WACH1iC,KAAKqiC,wBAA0BK,EASnC,GAAIA,EAEF,YADA1iC,KAAKsiC,gBAIP,MAAMlyB,EAAchR,EAAMU,cACtBE,KAAKyF,WAAa2K,GAAepQ,KAAKyF,SAAS3L,SAASsW,IAI5DpQ,KAAKuiC,oBACP,CAEArI,gBACE35B,EAAac,GAAGrB,KAAKyF,SAAUq8B,IAAiB1iC,GAASY,KAAKyiC,eAAerjC,GAAO,KACpFmB,EAAac,GAAGrB,KAAKyF,SAAUs8B,IAAgB3iC,GAASY,KAAKyiC,eAAerjC,GAAO,KACnFmB,EAAac,GAAGrB,KAAKyF,SAAU+pB,IAAepwB,GAASY,KAAKyiC,eAAerjC,GAAO,KAClFmB,EAAac,GAAGrB,KAAKyF,SAAUu8B,IAAgB5iC,GAASY,KAAKyiC,eAAerjC,GAAO,IACrF,CAEAkjC,gBACEjzB,aAAarP,KAAK45B,UAClB55B,KAAK45B,SAAW,IAClB,CAGA,sBAAOn+B,CAAgB+I,GACrB,OAAOxE,KAAK0I,MAAK,WACf,MAAMC,EAAOw5B,GAAMh8B,oBAAoBnG,KAAMwE,GAE7C,GAAsB,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjBmE,EAAKnE,GACd,MAAM,IAAIa,UAAU,oBAAoBb,MAG1CmE,EAAKnE,GAAQxE,KACf,CACF,GACF,EChND,IAAkB2iC,G,ODuNnB36B,EAAqBm6B,IAMrBlnC,EAAmBknC,IC7NAQ,GAIb,WASJ,SAASC,EAA0BC,GACjC,IAAIC,GAAmB,EACnBC,GAA0B,EAC1BC,EAAiC,KAEjCC,EAAsB,CACxBzL,MAAM,EACN0L,QAAQ,EACRC,KAAK,EACLC,KAAK,EACLC,OAAO,EACPC,UAAU,EACVC,QAAQ,EACRC,MAAM,EACNC,OAAO,EACPC,MAAM,EACNC,MAAM,EACNC,UAAU,EACV,kBAAkB,GAQpB,SAASC,EAAmBj8B,GAC1B,SACEA,GACAA,IAAO7O,UACS,SAAhB6O,EAAG2H,UACa,SAAhB3H,EAAG2H,UACH,cAAe3H,GACf,aAAcA,EAAG/N,UAKzB,CAiCI,SAASiqC,EAAqBl8B,GACxBA,EAAG/N,UAAUC,SAAS,mBAG1B8N,EAAG/N,UAAU4Q,IAAI,iBACjB7C,EAAGpE,aAAa,2BAA4B,IAClD,CA2CI,SAASugC,EAAcC,GACrBlB,GAAmB,CACzB,CAsEI,SAASmB,IACPlrC,SAAS8C,iBAAiB,YAAaqoC,GACvCnrC,SAAS8C,iBAAiB,YAAaqoC,GACvCnrC,SAAS8C,iBAAiB,UAAWqoC,GACrCnrC,SAAS8C,iBAAiB,cAAeqoC,GACzCnrC,SAAS8C,iBAAiB,cAAeqoC,GACzCnrC,SAAS8C,iBAAiB,YAAaqoC,GACvCnrC,SAAS8C,iBAAiB,YAAaqoC,GACvCnrC,SAAS8C,iBAAiB,aAAcqoC,GACxCnrC,SAAS8C,iBAAiB,WAAYqoC,EAC5C,CAqBI,SAASA,EAAqBF,GAGxBA,EAAE9mC,OAAOqS,UAAgD,SAApCy0B,EAAE9mC,OAAOqS,SAASlM,gBAI3Cy/B,GAAmB,EAzBnB/pC,SAASoE,oBAAoB,YAAa+mC,GAC1CnrC,SAASoE,oBAAoB,YAAa+mC,GAC1CnrC,SAASoE,oBAAoB,UAAW+mC,GACxCnrC,SAASoE,oBAAoB,cAAe+mC,GAC5CnrC,SAASoE,oBAAoB,cAAe+mC,GAC5CnrC,SAASoE,oBAAoB,YAAa+mC,GAC1CnrC,SAASoE,oBAAoB,YAAa+mC,GAC1CnrC,SAASoE,oBAAoB,aAAc+mC,GAC3CnrC,SAASoE,oBAAoB,WAAY+mC,GAmB/C,CAKInrC,SAAS8C,iBAAiB,WAzI1B,SAAmBmoC,GACbA,EAAEG,SAAWH,EAAEI,QAAUJ,EAAEK,UAI3BR,EAAmBhB,EAAMtlC,gBAC3BumC,EAAqBjB,EAAMtlC,eAG7BulC,GAAmB,EACzB,IA+HoD,GAChD/pC,SAAS8C,iBAAiB,YAAakoC,GAAe,GACtDhrC,SAAS8C,iBAAiB,cAAekoC,GAAe,GACxDhrC,SAAS8C,iBAAiB,aAAckoC,GAAe,GACvDhrC,SAAS8C,iBAAiB,oBApE1B,SAA4BmoC,GACO,WAA7BjrC,SAASurC,kBAKPvB,IACFD,GAAmB,GAErBmB,IAER,IAyDsE,GAElEA,IAMApB,EAAMhnC,iBAAiB,SAtHvB,SAAiBmoC,GApFjB,IAAuCp8B,EACjCnH,EACA2H,EAoFCy7B,EAAmBG,EAAE9mC,UAItB4lC,IAzFAriC,GADiCmH,EA0FiBo8B,EAAE9mC,QAzF1CuD,KAGE,WAFZ2H,EAAUR,EAAGQ,UAEU66B,EAAoBxiC,KAAUmH,EAAG28B,UAI5C,aAAZn8B,IAA2BR,EAAG28B,UAI9B38B,EAAG48B,qBA+ELV,EAAqBE,EAAE9mC,OAE/B,IA6G6C,GACzC2lC,EAAMhnC,iBAAiB,QAxGvB,SAAgBmoC,GA9DhB,IAAiCp8B,EA+D1Bi8B,EAAmBG,EAAE9mC,UAKxB8mC,EAAE9mC,OAAOrD,UAAUC,SAAS,kBAC5BkqC,EAAE9mC,OAAOlD,aAAa,+BAMtB+oC,GAA0B,EAC1B/qC,OAAOqX,aAAa2zB,GACpBA,EAAiChrC,OAAOoF,YAAW,WACjD2lC,GAA0B,CACpC,GAAW,MA/E0Bn7B,EAgFLo8B,EAAE9mC,QA/EpBlD,aAAa,8BAGrB4N,EAAG/N,UAAUlC,OAAO,iBACpBiQ,EAAGlE,gBAAgB,6BA6EzB,IAoF2C,GAOnCm/B,EAAMjqC,WAAae,KAAK8qC,wBAA0B5B,EAAMlqB,KAI1DkqB,EAAMlqB,KAAKnV,aAAa,wBAAyB,IACxCq/B,EAAMjqC,WAAae,KAAK+qC,gBACjC3rC,SAASoB,gBAAgBN,UAAU4Q,IAAI,oBACvC1R,SAASoB,gBAAgBqJ,aAAa,wBAAyB,IAErE,CAKE,GAAsB,oBAAXxL,QAA8C,oBAAbe,SAA0B,CAQpE,IAAIqG,EAJJpH,OAAO4qC,0BAA4BA,EAMnC,IACExjC,EAAQ,IAAIulC,YAAY,+BAC9B,CAAM,MAAOptC,IAEP6H,EAAQrG,SAAS6rC,YAAY,gBACvBC,gBAAgB,gCAAgC,GAAO,EAAO,GAC1E,CAEI7sC,OAAOO,cAAc6G,EACzB,CAE0B,oBAAbrG,UAGT6pC,EAA0B7pC,SAG7B,EAtToB,iBAAZ+rC,SAA0C,oBAAXC,OAAyBpC,KAC7C,mBAAXqC,QAAyBA,OAAOC,IAAMD,OAAOrC,IACnDA,KCoBY,CACbp6B,QACAO,SACAsE,YACA4E,YACA8a,YACAkF,SACA0B,aACAK,gBACAuI,WACAO,oBACAmB,aACAwC,OACA2B,SACAzI,W", "ignoreList": []}