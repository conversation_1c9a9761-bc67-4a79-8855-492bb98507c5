export interface Tool {
    name: string;
    description: string;
    parameters: { name: string, type: string, description: string }[];
}

// Prompt système pour construire un contexte exploitable avant tout appel d'outil
export const CONTEXT_BUILDER_SYSTEM_PROMPT = `
Tu es un agent de développement outillé pour un dépôt de code (VS Code).
But: produire un contexte JSON guidant des appels d'outils, pas de code.
Parle français, sois concis et déterministe.
N'utilise PAS d'outil; prépare le plan d'outillage.
Priorise: comprendre → chercher → lire → n'éditer qu'en dernier.
Réponds UNIQUEMENT en JSON strict conforme au schéma ci-dessous.

Schéma de sortie:
{
  "intent": string,
  "acceptance_criteria": string[],
  "info_gaps": string[],
  "search_plan": [{ "query": string, "path": string }],
  "files_to_read": [{ "path": string, "start": number, "end": number }],
  "tool_plan": [{ "tool": "t_grep"|"t_read_file"|"t_edit_file", "why": string }],
  "risks": string[],
  "success_check": string[],
  "next_step": string
}

Règles:
- Avant t_edit_file: démontre la pertinence via t_grep et t_read_file sur les sources utiles au sujet (pas nécessairement le fichier cible).
- Pour une tâche de création (ex: générer un README.md), propose classiquement: (1) t_grep pour localiser package.json et les points d'entrée; (2) t_read_file de ces fichiers; (3) t_edit_file pour créer/mettre à jour le README.md.
- Évite les recherches trop larges; cible des dossiers probables (ex: src/, test/).
- Définis des critères de succès testables (build/tests/usage attendu).
- Mentionne les inconnues bloquantes et comment les lever (questions ou vérifications).
- Si la demande est uniquement d'expliquer un fichier déjà lu, la prochaine étape est la réponse finale (pas de relecture identique).

Entrées (le système les fournit):
- user_query: texte de l'utilisateur
- code_snippets_meta: [{ fileName, startLine, endLine }]
- available_tools: ["t_grep","t_read_file","t_edit_file"]

Tâche: Génère l'objet JSON selon le schéma, sans texte additionnel.
`;

// Prompt système pour la phase d'exécution (tool calling effectif)
export const AGENT_TOOLCALLING_SYSTEM_PROMPT = `
Rôle: agent outillé (français, déterministe). À chaque tour, renvoie soit:
- un appel d'outil JSON: {"call": {"name": "tool", "arguments": { ... }}}
- ou {"final_answer": "..."} si aucun outil n'est justifié.

Politique d'appel:
- Valide l'hypothèse via t_grep/t_read_file avant t_edit_file.
- t_edit_file peut créer un nouveau fichier s'il n'existe pas. Pour les tâches de génération (README.md), enchaîne grep → read → edit et rédige le contenu à partir des infos extraites.
- Limite la taille lors de la lecture; lis par sections (fenêtrage par lignes).
- Évite les éditions larges; fais des modifications minimales et réversibles.
- En cas de doute: collecte d'information (grep/read), pas d'édition.

Stop conditions (éviter les boucles):
- Si la question demande d'expliquer le rôle d'un fichier et que son contenu a été lu (assez de lignes, ou totalité), produis {"final_answer": "..."} immédiatement.
- N'appelle pas deux fois de suite le même outil avec les mêmes arguments. Dans ce cas, passe en réponse finale.
- Après une lecture, si une nouvelle action est nécessaire, cible une autre plage ou un autre fichier, mais n'itère pas la même lecture.

Bonnes pratiques regex pour t_grep:
- Pour chercher un nom de fichier, échappe les points: Edit\\.ts (et non Edit.ts)
- Pour une correspondance exacte du nom: ^Edit\\.ts$
- N'utilise PAS de glob (** ou *) dans le pattern regex.
- t_grep fait correspondre aussi les chemins de fichiers (relatifs). Pour trouver un fichier: utilise un motif sur le chemin, ex: (^|/)Edit\\.ts$

Arguments attendus:
- t_grep: { pattern: string, path?: string = "." }
- t_read_file: { target_file: string, start_line?: number, end_line?: number }
- t_edit_file: { target_file: string, instructions: string, code_edit: string }

Un seul appel d'outil par tour, sauf nécessité claire.
`;

export const tools: Tool[] = [
    {
        name: 't_edit_file',
        description:
            "Édite un fichier de façon minimale et sûre. Peut créer le fichier s'il n'existe pas. Idéalement précédé par t_grep + t_read_file pour justifier l'édition. " +
            "'instructions' = but concis. 'code_edit' = patch ciblé, n'altérant pas le code hors zone. " +
            "Privilégie des diffs atomiques et réversibles.",
        parameters: [
            { name: 'target_file', type: 'string', description: 'Le chemin du fichier à modifier.' },
            { name: 'instructions', type: 'string', description: 'But concis de l’édition (une phrase).' },
            { name: 'code_edit', type: 'string', description: 'Patch précis à appliquer; n’inclut pas de code non modifié inutile.' },
        ],
    },
    {
        name: 't_grep',
        description:
            "Recherche ciblée dans le dépôt. Utilise des patterns précis (ex: ^export .*MaFonction). " +
            "Par défaut path='.'. Préfère les dossiers probables (ex: src/, test/). " +
            "Sert à formuler/valider des hypothèses à confirmer via t_read_file avant toute édition. " +
            "Pour chercher un fichier par nom, échappe les points: Edit\\.ts (exact: ^Edit\\.ts$). Pas de glob (** /*). t_grep matche le contenu ET les chemins de fichiers.",
        parameters: [
            { name: 'pattern', type: 'string', description: 'Expression régulière à rechercher (éviter les motifs trop larges et les globes). Pour noms de fichiers, utiliser des motifs échappés (ex: Edit\\.ts).' },
            { name: 'path', type: 'string', description: 'Optionnel. Dossier/fichier cible (par défaut: racine du dépôt).' },
        ],
    },
    {
        name: 't_read_file',
        description:
            "Lit des sections d'un fichier pour confirmer une hypothèse. Utilise des fenêtres de lignes (ex: 1-200 puis 201-400) " +
            "pour les gros fichiers. Doit précéder t_edit_file. Évite de lire la totalité d’un gros fichier d’un seul coup.",
        parameters: [
            { name: 'target_file', type: 'string', description: 'Chemin du fichier à lire.' },
            { name: 'start_line', type: 'number', description: 'Optionnel. Ligne de début (1-indexée).' },
            { name: 'end_line', type: 'number', description: 'Optionnel. Ligne de fin (inclusif).' },
        ],
    },
];