import * as vscode from 'vscode';
import * as fs from 'fs/promises';
import * as fscore from 'fs';
import * as path from 'path';

export const description = `Édite un fichier de façon minimale et sûre. Exige une preuve par t_grep + t_read_file juste avant l'édition. 'instructions' = but concis. 'code_edit' = patch ciblé, n'altérant pas le code hors zone. Privilégie des diffs atomiques et réversibles.`;

export type EditFileArgs = { target_file: string; instructions: string; code_edit: string };
export type EditFileResult = { file: string; created: boolean; changed: boolean; summary: string };

function getWorkspaceRoot(): string {
  const root = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
  if (!root) throw new Error('Workspace root not found');
  return root;
}

function splitByPlaceholder(text: string): string[] {
  const lines = text.split(/\r?\n/);
  const parts: string[] = [];
  let current: string[] = [];
  for (const line of lines) {
    if (line.trim() === "// ... existing code ...") {
      parts.push(current.join('\n'));
      current = [];
    } else {
      current.push(line);
    }
  }
  parts.push(current.join('\n'));
  return parts;
}

function findAnchoredRegion(original: string, segments: string[]): { startIdx: number; endIdx: number } | null {
  let startIdx = 0;
  let cursor = 0;
  for (let i = 0; i < segments.length; i++) {
    const seg = segments[i];
    if (!seg) continue;
    const idx = original.indexOf(seg, cursor);
    if (idx === -1) return null;
    if (i === 0) startIdx = idx;
    cursor = idx + seg.length;
  }
  const endIdx = cursor;
  return { startIdx, endIdx };
}

export async function t_edit_file(args: EditFileArgs): Promise<EditFileResult> {
  const root = getWorkspaceRoot();
  const full = path.resolve(root, args.target_file);

  const exists = fscore.existsSync(full);
  if (!exists) {
    await fs.mkdir(path.dirname(full), { recursive: true });
    const cleaned = splitByPlaceholder(args.code_edit).join('\n');
    await fs.writeFile(full, cleaned, 'utf8');
    return { file: path.relative(root, full), created: true, changed: true, summary: `Fichier créé avec ${cleaned.split(/\r?\n/).length} lignes.` };
  }

  const original = await fs.readFile(full, 'utf8');
  const segments = splitByPlaceholder(args.code_edit);
  const hasAnchor = segments.some((s) => s.trim().length > 0);
  if (!hasAnchor) throw new Error("Patch refusé: aucun ancrage trouvé dans code_edit");

  const region = findAnchoredRegion(original, segments);
  if (!region) throw new Error("Patch introuvable: impossible d'aligner les ancres sur le fichier existant");

  const replacement = segments.join('\n');
  const next = original.slice(0, region.startIdx) + replacement + original.slice(region.endIdx);
  if (next === original) {
    return { file: path.relative(root, full), created: false, changed: false, summary: 'Aucun changement (contenu identique).' };
  }
  await fs.writeFile(full, next, 'utf8');
  const changedLines = replacement.split(/\r?\n/).length;
  return { file: path.relative(root, full), created: false, changed: true, summary: `Zone remplacée, ${changedLines} lignes écrites.` };
}
