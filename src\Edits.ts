import * as vscode from 'vscode';
import { SettingsPanel } from './SettingsPanel';

export function registerEditFeature(context: vscode.ExtensionContext, statusBarItem: vscode.StatusBarItem) {

        // Fournisseur persistent pour documents proposés (diff)
        const PROPOSED_SCHEME = 'dinootoo-companion-proposed';
        const proposedContentMap = new Map<string, string>();
        const proposedProvider = vscode.workspace.registerTextDocumentContentProvider(PROPOSED_SCHEME, {
                provideTextDocumentContent(uri) {
                        return proposedContentMap.get(uri.toString()) ?? '';
                }
        });
        context.subscriptions.push(proposedProvider);

        // Mémoire des propositions par URI + dernier utilisé
        const proposalMap = new Map<string, {
                originalUri: vscode.Uri;
                selection: vscode.Selection | null;
                insertPosition: vscode.Position | null;
                replacementText: string;
        }>();
        let lastProposal: {
                originalUri: vscode.Uri;
                selection: vscode.Selection | null;
                insertPosition: vscode.Position | null;
                replacementText: string;
                proposedUri: vscode.Uri;
        } | null = null;

        const inlineGenerateCode = vscode.commands.registerCommand('dinootoo-companion.inlineGenerateCode', async () => {
                const editor = vscode.window.activeTextEditor;
                if (!editor) {
                        vscode.window.showInformationMessage('Aucun éditeur actif.');
                        return;
                }

                const userPrompt = await vscode.window.showInputBox({
                        prompt: 'Décrivez le code à générer/modifier',
                        placeHolder: 'Ex: Ajoute une fonction utilitaire pour formater une date',
                        ignoreFocusOut: true
                });
                if (!userPrompt) {
                        return;
                }

                const document = editor.document;
                const selection = editor.selection;
                const hasSelection = !selection.isEmpty;
                const originalText = hasSelection
                        ? document.getText(selection)
                        : '';

                // Vérifier la clef API OpenRouter
                const apiKey = await context.secrets.get('openrouterApiKey');
                if (!apiKey) {
                        const open = 'Ouvrir les paramètres';
                        vscode.window.showInformationMessage('Veuillez définir une clef API OpenRouter.', open).then(sel => {
                                if (sel === open) {
                                        SettingsPanel.createOrShow(context.extensionUri, context);
                                }
                        });
                        return;
                }

                const model = context.globalState.get('openrouterModel', 'anthropic/claude-3-haiku:beta') as string;

                // Prompt système strict pour génération de code
                const strictSystemPrompt = [
                        'Rôle: Assistant d\'édition de code STRICT.',
                        'Règles:',
                        '- Réponds uniquement par du code ou des commentaires valides pour le langage concerné.',
                        '- N\'inclue aucun texte explicatif hors du code.',
                        '- Si la demande n\'implique pas de génération de code, réponds vide.',
                        '- N\'ajoute rien en dehors de la zone fournie; retourne uniquement le remplacement/suggestion de code.',
                ].join('\n');

                // Construire le message utilisateur en contexte
                const fileName = document.fileName.split('\\').pop()?.split('/').pop() ?? 'unknown';
                const languageId = document.languageId;
                const selectionInfo = hasSelection
                        ? `Remplace le code sélectionné dans ${fileName} (${languageId}).` 
                        : `Insère le code à la position du curseur dans ${fileName} (${languageId}).`;

                const userMessage = hasSelection
                        ? `${selectionInfo}\n\nContexte (sélection):\n\n\`\`\`\n${originalText}\n\`\`\`\n\nDemande:\n${userPrompt}`
                        : `${selectionInfo}\n\nDemande:\n${userPrompt}`;

                // Appel API OpenRouter
                let aiReply = '';
                
                statusBarItem.text = '$(sync~spin) Dinootoo : Génération...';
                
                try {
                        const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
                                method: 'POST',
                                headers: {
                                        'Content-Type': 'application/json',
                                        'Authorization': `Bearer ${apiKey}`,
                                        'HTTP-Referer': 'https://github.com/RedTheFoxx/dinootoo-companion',
                                        'X-Title': 'Dinootoo Companion'
                                },
                                body: JSON.stringify({
                                        model,
                                        messages: [
                                                { role: 'system', content: strictSystemPrompt },
                                                { role: 'user', content: userMessage }
                                        ],
                                        stream: false
                                })
                        });

                        if (!response.ok) {
                                const errText = await response.text();
                                vscode.window.showErrorMessage(`Erreur OpenRouter: ${response.status} ${errText}`);
                                return;
                        }
                        const data = await response.json() as { choices?: { message?: { content?: string } }[] };
                        aiReply = data.choices?.[0]?.message?.content?.trim() ?? '';
                } catch (e) {
                        const msg = e instanceof Error ? e.message : String(e);
                        vscode.window.showErrorMessage(`Erreur OpenRouter: ${msg}`);
                        return;
                } finally {
                        statusBarItem.text = '$(robot) Dinootoo : Prêt';
                }

                if (!aiReply) {
                        vscode.window.showInformationMessage('Aucune réponse de génération de code.');
                        return;
                }

                // Nettoyer les balises de code éventuelles renvoyées par le modèle
                const cleaned = aiReply.replace(/^```[a-zA-Z0-9]*\n?/,'').replace(/```\s*$/,'');

                // Détecter l'indentation du contexte
                let indentation = '';
                if (hasSelection) {
                        const firstLine = document.lineAt(selection.start.line);
                        indentation = firstLine.text.match(/^\s*/)?.[0] || '';
                } else {
                        const currentLine = document.lineAt(selection.active.line);
                        indentation = currentLine.text.match(/^\s*/)?.[0] || '';
                }

                // Appliquer l'indentation au code généré
                const lines = cleaned.split('\n');
                while (lines.length > 0 && lines[lines.length - 1].trim() === '') {
                        lines.pop();
                }
                
                const indentedCode = lines.map((line, index) => {
                        if (index === 0 && (line.trim() === '' || lines.length === 1)) {
                                return line;
                        }
                        return indentation + line;
                }).join('\n');

                // Construire contenu proposé
                const before = hasSelection ? originalText : '';
                const after = indentedCode;

                // Construire le contenu complet proposé du document
                const full = document.getText();
                const proposedFull = hasSelection
                        ? (() => {
                                const startOffset = document.offsetAt(selection.start);
                                const endOffset = document.offsetAt(selection.end);
                                return full.slice(0, startOffset) + after + full.slice(endOffset);
                        })()
                        : (() => {
                                const index = document.offsetAt(selection.active);
                                return full.slice(0, index) + after + full.slice(index);
                        })();

                // Créer un document virtuel pour diff
                const originalUri = document.uri;
                const proposedUri = vscode.Uri.parse(`${PROPOSED_SCHEME}://${originalUri.authority}${originalUri.path}?${Date.now()}`);
                proposedContentMap.set(proposedUri.toString(), proposedFull);

                // Retenir pour application ultérieure
                proposalMap.set(proposedUri.toString(), {
                        originalUri,
                        selection: hasSelection ? selection : null,
                        insertPosition: hasSelection ? null : selection.active,
                        replacementText: after,
                });
                lastProposal = {
                        originalUri,
                        selection: hasSelection ? selection : null,
                        insertPosition: hasSelection ? null : selection.active,
                        replacementText: after,
                        proposedUri
                };

                const title = hasSelection ? 'Aperçu des modifications (remplacement sélection)' : 'Aperçu des modifications (insertion curseur)';
                await vscode.commands.executeCommand('vscode.diff', originalUri, proposedUri, title);
        });

        const applyProposedInlineCode = vscode.commands.registerCommand('dinootoo-companion.applyProposedInlineCode', async () => {
                // Choisir la proposition en fonction de l'onglet actif ou du dernier
                const activeUri = vscode.window.activeTextEditor?.document.uri;
                let key: string | undefined;
                if (activeUri && activeUri.scheme === PROPOSED_SCHEME) {
                        key = activeUri.toString();
                } else if (proposalMap.size === 1) {
                        key = Array.from(proposalMap.keys())[0];
                } else if (lastProposal) {
                        key = lastProposal.proposedUri.toString();
                }
                if (!key) {
                        vscode.window.showInformationMessage('Aucune génération proposée active. Ouvrez l\'onglet diff ou générez une proposition.');
                        return;
                }
                const stored = proposalMap.get(key) || (lastProposal ? {
                        originalUri: lastProposal.originalUri,
                        selection: lastProposal.selection,
                        insertPosition: lastProposal.insertPosition,
                        replacementText: lastProposal.replacementText,
                } : undefined);
                if (!stored) {
                        vscode.window.showInformationMessage('Proposition introuvable.');
                        return;
                }
                const { originalUri, selection, insertPosition, replacementText } = stored;
                let targetEditor = vscode.window.visibleTextEditors.find(e => e.document.uri.toString() === originalUri.toString());
                if (!targetEditor) {
                        const doc = await vscode.workspace.openTextDocument(originalUri);
                        targetEditor = await vscode.window.showTextDocument(doc, { preview: false });
                }
                await targetEditor.edit(editBuilder => {
                        if (selection) {
                                editBuilder.replace(selection, replacementText);
                        } else if (insertPosition) {
                                editBuilder.insert(insertPosition, replacementText);
                        }
                });
                
                // Nettoyer les données
                proposedContentMap.delete(key);
                proposalMap.delete(key);
                if (lastProposal && lastProposal.proposedUri.toString() === key) {
                        lastProposal = null;
                }
                
                // Fermer automatiquement le diff editor
                const diffEditors = vscode.window.visibleTextEditors.filter(editor => 
                        editor.document.uri.scheme === PROPOSED_SCHEME
                );
                for (const diffEditor of diffEditors) {
                        await diffEditor.document.save();
                        await vscode.commands.executeCommand('workbench.action.closeActiveEditor');
                }
                
                // Notification de succès
                vscode.window.showInformationMessage('Modifications appliquées avec succès !');
        });

        const discardProposedInlineCode = vscode.commands.registerCommand('dinootoo-companion.discardProposedInlineCode', async () => {
                const activeUri = vscode.window.activeTextEditor?.document.uri;
                let key: string | undefined;
                if (activeUri && activeUri.scheme === PROPOSED_SCHEME) {
                        key = activeUri.toString();
                } else if (proposalMap.size === 1) {
                        key = Array.from(proposalMap.keys())[0];
                } else if (lastProposal) {
                        key = lastProposal.proposedUri.toString();
                }
                if (!key) {
                        vscode.window.showInformationMessage('Aucune génération proposée active.');
                        return;
                }
                
                // Nettoyer les données
                proposedContentMap.delete(key);
                proposalMap.delete(key);
                if (lastProposal && lastProposal.proposedUri.toString() === key) {
                        lastProposal = null;
                }
                
                // Fermer automatiquement le diff editor
                const diffEditors = vscode.window.visibleTextEditors.filter(editor => 
                        editor.document.uri.scheme === PROPOSED_SCHEME
                );
                for (const diffEditor of diffEditors) {
                        await diffEditor.document.save();
                        await vscode.commands.executeCommand('workbench.action.closeActiveEditor');
                }
                
                // Notification de rejet
                vscode.window.showInformationMessage('Modifications rejetées.');
        });

        context.subscriptions.push(
                inlineGenerateCode,
                applyProposedInlineCode,
                discardProposedInlineCode
        );
}


