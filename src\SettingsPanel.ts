import * as vscode from 'vscode';

export class SettingsPanel {
    public static currentPanel: SettingsPanel | undefined;
    public static readonly viewType = 'dinootoo-companion-settings';

    private readonly _panel: vscode.WebviewPanel;
    private readonly _extensionUri: vscode.Uri;
    private readonly _context: vscode.ExtensionContext;

    private constructor(panel: vscode.WebviewPanel, extensionUri: vscode.Uri, context: vscode.ExtensionContext) {
        this._panel = panel;
        this._extensionUri = extensionUri;
        this._context = context;

        void this._setHtml();

        this._panel.onDidDispose(() => this.dispose(), null, this._context.subscriptions);

        this._panel.webview.onDidReceiveMessage(async message => {
            switch (message.type) {
                case 'saveSettings':
                    {
                        if (message.apiKey) {
                            await this._context.secrets.store('openrouterApiKey', message.apiKey);
                        }
                        await this._context.globalState.update('openrouterModel', message.model);
                        await this._context.globalState.update('systemPrompt', message.systemPrompt || '');
                        await this._context.globalState.update('codeBlocksExpanded', message.codeBlocksExpanded || false);
                        vscode.window.showInformationMessage('Paramètres sauvegardés');
                        break;
                    }
                case 'clearHistory':
                    {
                        const result = await vscode.window.showWarningMessage(
                            'Êtes-vous sûr de vouloir effacer l\'historique de toutes les conversations ? Cette action est irréversible.',
                            { modal: true },
                            'Oui, effacer',
                            'Annuler'
                        );
                        
                        if (result === 'Oui, effacer') {
                            // Effacer l'historique des conversations
                            await this._context.globalState.update('conversationHistories', []);
                            
                            const reloadResult = await vscode.window.showInformationMessage(
                                'Historique des conversations effacé. Voulez-vous recharger la fenêtre VSCode pour mettre à jour l\'interface ?',
                                'Recharger',
                                'Plus tard'
                            );
                            
                            if (reloadResult === 'Recharger') {
                                await vscode.commands.executeCommand('workbench.action.reloadWindow');
                            } else {
                                // Si l'utilisateur ne veut pas recharger, on peut essayer de notifier la webview
                                // mais cela peut ne pas fonctionner si la webview n'est pas active
                                try {
                                    const commands = await vscode.commands.getCommands(true);
                                    if (commands.includes('dinootoo-companion.refreshChatView')) {
                                        await vscode.commands.executeCommand('dinootoo-companion.refreshChatView');
                                    }
                                } catch {
                                    // Ignorer si la commande est absente ou échoue
                                }                            
                            }
                        }
                        break;
                    }
            }
        });
    }

    public static createOrShow(extensionUri: vscode.Uri, context: vscode.ExtensionContext) {
        const column = vscode.window.activeTextEditor ? vscode.window.activeTextEditor.viewColumn : undefined;

        if (SettingsPanel.currentPanel) {
            SettingsPanel.currentPanel._panel.reveal(column);
            return;
        }

        const panel = vscode.window.createWebviewPanel(
            SettingsPanel.viewType,
            'Paramètres Dinootoo',
            column || vscode.ViewColumn.One,
            {
                enableScripts: true,
                localResourceRoots: [extensionUri]
            }
        );

        SettingsPanel.currentPanel = new SettingsPanel(panel, extensionUri, context);
    }

    public dispose() {
        SettingsPanel.currentPanel = undefined;
        this._panel.dispose();
    }

    private async _setHtml() {
        const webview = this._panel.webview;
        const styleResetUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'media', 'reset.css'));
        const styleVSCodeUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'media', 'vscode.css'));
        const styleMainUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'media', 'main.css'));
        const styleSettingsUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'media', 'settings.css'));
        const boostedCssUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'media', 'boosted', 'css', 'boosted.min.css'));
        const boostedJsUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'media', 'boosted', 'js', 'boosted.bundle.min.js'));
        const scriptUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'media', 'settings.js'));
        const nonce = getNonce();

        const model = this._context.globalState.get('openrouterModel');
        const apiKey = await this._context.secrets.get('openrouterApiKey');
        const apiPlaceholder = apiKey ? 'Clef API enregistrée' : '';
        const apiKeySource = this._context.globalState.get('openrouterApiKeySource');
        const systemPrompt = this._context.globalState.get<string>('systemPrompt') || '';
        const systemPromptEscaped = systemPrompt
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;');
        const codeBlocksExpanded = this._context.globalState.get<boolean>('codeBlocksExpanded', false);

        // Déterminer le nom de la variable d'environnement utilisée
        let envVarName = 'OPENROUTER_API';
        if (process.env.OPENROUTER_API_KEY) {
            envVarName = 'OPENROUTER_API_KEY';
        } else if (process.env.OPENROUTER_KEY) {
            envVarName = 'OPENROUTER_KEY';
        }

        webview.html = `<!DOCTYPE html>
<html lang="fr">
<head>
<meta charset="UTF-8">
<meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource} 'unsafe-inline'; script-src 'nonce-${nonce}'; img-src ${webview.cspSource} https: data:; font-src ${webview.cspSource} https: data:;">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link href="${styleResetUri}" rel="stylesheet">
<link href="${styleVSCodeUri}" rel="stylesheet">
<link href="${boostedCssUri}" rel="stylesheet">
<link href="${styleMainUri}" rel="stylesheet">
<link href="${styleSettingsUri}" rel="stylesheet">
<title>Paramètres</title>
</head>
<body>
<div class="settings-container">
    <h3>Paramètres OpenRouter</h3>

    ${apiKeySource === 'env' ? `
    <p class="form-text text-body-secondary mb-3">
        Si ce message apparait, bonne nouvelle ! Votre clef API à déjà été chargée depuis la variable d'environnement OPENROUTER_API.
    </p>
    ` : ''}

    <div class="form-group">
        <label for="api-key">Clef d'API</label>
        <input type="password" id="api-key" class="form-control form-control-sm" placeholder="${apiPlaceholder}">
    </div>

    <div class="form-group">
        <label for="model">Modèle</label>
        <input type="text" id="model" class="form-control form-control-sm" value="${model}" placeholder="Ex: openai/gpt-4, anthropic/claude-3-opus, etc.">
    </div>

    <h3>Gestion du contexte</h3>
    <div class="form-group">
        <label for="system-prompt">Prompt système</label>
        <textarea id="system-prompt" class="form-control form-control-sm" rows="3">${systemPromptEscaped}</textarea>
    </div>

    <h3>Comportement de l'application</h3>
    <div class="form-group">
        <label for="code-blocks-expanded">État par défaut des blocs de code</label>
        <select id="code-blocks-expanded" class="form-select form-select-sm">
            <option value="false" ${!codeBlocksExpanded ? 'selected' : ''}>Repliés</option>
            <option value="true" ${codeBlocksExpanded ? 'selected' : ''}>Dépliés</option>
        </select>
    </div>

    <button id="save-button" class="btn btn-primary btn-sm">Sauvegarder</button>

    <hr>

    <h3>Gestion des conversations</h3>
    <div class="form-group">
        <button id="clear-history-button" class="btn btn-danger btn-sm">Effacer l'historique des conversations</button>
        <p class="form-text text-body-secondary mt-2">
            Cette action supprimera définitivement toutes les conversations sauvegardées.
        </p>
    </div>
</div>

<script nonce="${nonce}" src="${boostedJsUri}"></script>
<script nonce="${nonce}" src="${scriptUri}"></script>
</body>
</html>`;
    }
}

function getNonce() {
    let text = '';
    const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    for (let i = 0; i < 32; i++) {
        text += possible.charAt(Math.floor(Math.random() * possible.length));
    }
    return text;
}

