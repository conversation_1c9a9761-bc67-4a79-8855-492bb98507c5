{"version": 3, "sources": ["../../scss/mixins/_banner.scss", "../../scss/_containers.scss", "../../scss/mixins/_container.scss", "boosted-grid.css", "../../scss/mixins/_breakpoints.scss", "../../scss/_variables.scss", "../../scss/_grid.scss", "../../scss/mixins/_grid.scss", "../../scss/mixins/_utilities.scss", "../../scss/utilities/_api.scss"], "names": [], "mappings": "AACE;;;;;;;;;EAAA;ACKA;;;;;;;;ECFA,sBAAA;EACA,gBAAA;EACA,WAAA;EACA,8CAAA;EACA,6CAAA;EACA,kBAAA;EACA,iBAAA;ACeF;ACsCI;EHzDF;;;;;;;;ICSE,qBAAA;ECqBF;AACF;;AFxBE;ECWI,4BAAA;ACiBN;ACsBI;EHlDF;ICWI,6BAAA;ECqBJ;AACF;ACiBI;EHlDF;ICWI,6BAAA;EC0BJ;AACF;ACYI;EHlDF;ICWI,6BAAA;EC+BJ;AACF;ACOI;EHlDF;ICWI,6BAAA;ECoCJ;AACF;ACEI;EHlDF;ICWI,8BAAA;ECyCJ;AACF;;AFzCM;EACE,gBIwiBe;AF5fvB;;ACPI;EHtCE;IACE,gBIwiBe;EFvfrB;AACF;ACbI;EHtCE;IACE,gBIwiBe;EFlfrB;AACF;AClBI;EHtCE;IACE,gBIwiBe;EF7erB;AACF;ACvBI;EHtCE;IACE,iBIwiBe;EFxerB;AACF;AC5BI;EHtCE;IACE,iBIwiBe;EFnerB;AACF;AG5FA;EAEI,qBAAA;EAAA,yBAAA;EAAA,yBAAA;EAAA,0BAAA;EAAA,0BAAA;EAAA,2BAAA;AHkGJ;;AG7FE;ECNA,uBAAA;EACA,gBAAA;EACA,aAAA;EACA,eAAA;EAEA,yCAAA;EACA,6CAAA;EACA,4CAAA;AJsGF;ACnDI;EEpDF;ICKE,sBAAA;EJsGF;AACF;AGzGI;ECSF,sBAAA;EAIA,cAAA;EACA,WAAA;EACA,eAAA;EACA,6CAAA;EACA,4CAAA;EACA,8BAAA;AJgGF;;AIjDM;EACE,WAAA;AJoDR;;AIjDM;EApCJ,cAAA;EACA,WAAA;AJyFF;;AI3EE;EACE,cAAA;EACA,WAAA;AJ8EJ;;AIhFE;EACE,cAAA;EACA,UAAA;AJmFJ;;AIrFE;EACE,cAAA;EACA,mBAAA;AJwFJ;;AI1FE;EACE,cAAA;EACA,UAAA;AJ6FJ;;AI/FE;EACE,cAAA;EACA,UAAA;AJkGJ;;AIpGE;EACE,cAAA;EACA,mBAAA;AJuGJ;;AIxEM;EAhDJ,cAAA;EACA,WAAA;AJ4HF;;AIvEU;EAhEN,cAAA;EACA,kBAAA;AJ2IJ;;AI5EU;EAhEN,cAAA;EACA,mBAAA;AJgJJ;;AIjFU;EAhEN,cAAA;EACA,UAAA;AJqJJ;;AItFU;EAhEN,cAAA;EACA,mBAAA;AJ0JJ;;AI3FU;EAhEN,cAAA;EACA,mBAAA;AJ+JJ;;AIhGU;EAhEN,cAAA;EACA,UAAA;AJoKJ;;AIrGU;EAhEN,cAAA;EACA,mBAAA;AJyKJ;;AI1GU;EAhEN,cAAA;EACA,mBAAA;AJ8KJ;;AI/GU;EAhEN,cAAA;EACA,UAAA;AJmLJ;;AIpHU;EAhEN,cAAA;EACA,mBAAA;AJwLJ;;AIzHU;EAhEN,cAAA;EACA,mBAAA;AJ6LJ;;AI9HU;EAhEN,cAAA;EACA,WAAA;AJkMJ;;AI3HY;EAxDV,wBAAA;AJuLF;;AI/HY;EAxDV,yBAAA;AJ2LF;;AInIY;EAxDV,gBAAA;AJ+LF;;AIvIY;EAxDV,yBAAA;AJmMF;;AI3IY;EAxDV,yBAAA;AJuMF;;AI/IY;EAxDV,gBAAA;AJ2MF;;AInJY;EAxDV,yBAAA;AJ+MF;;AIvJY;EAxDV,yBAAA;AJmNF;;AI3JY;EAxDV,gBAAA;AJuNF;;AI/JY;EAxDV,yBAAA;AJ2NF;;AInKY;EAxDV,yBAAA;AJ+NF;;AI5JQ;;EAEE,gBAAA;AJ+JV;;AI5JQ;;EAEE,gBAAA;AJ+JV;;AItKQ;;EAEE,wBAAA;AJyKV;;AItKQ;;EAEE,wBAAA;AJyKV;;AIhLQ;;EAEE,uBAAA;AJmLV;;AIhLQ;;EAEE,uBAAA;AJmLV;;AI1LQ;;EAEE,sBAAA;AJ6LV;;AI1LQ;;EAEE,sBAAA;AJ6LV;;AIpMQ;;EAEE,uBAAA;AJuMV;;AIpMQ;;EAEE,uBAAA;AJuMV;;AI9MQ;;EAEE,sBAAA;AJiNV;;AI9MQ;;EAEE,sBAAA;AJiNV;;ACjRI;EGgBE;IACE,WAAA;EJqQN;EIlQI;IApCJ,cAAA;IACA,WAAA;EJySA;EI3RA;IACE,cAAA;IACA,WAAA;EJ6RF;EI/RA;IACE,cAAA;IACA,UAAA;EJiSF;EInSA;IACE,cAAA;IACA,mBAAA;EJqSF;EIvSA;IACE,cAAA;IACA,UAAA;EJySF;EI3SA;IACE,cAAA;IACA,UAAA;EJ6SF;EI/SA;IACE,cAAA;IACA,mBAAA;EJiTF;EIlRI;IAhDJ,cAAA;IACA,WAAA;EJqUA;EIhRQ;IAhEN,cAAA;IACA,kBAAA;EJmVF;EIpRQ;IAhEN,cAAA;IACA,mBAAA;EJuVF;EIxRQ;IAhEN,cAAA;IACA,UAAA;EJ2VF;EI5RQ;IAhEN,cAAA;IACA,mBAAA;EJ+VF;EIhSQ;IAhEN,cAAA;IACA,mBAAA;EJmWF;EIpSQ;IAhEN,cAAA;IACA,UAAA;EJuWF;EIxSQ;IAhEN,cAAA;IACA,mBAAA;EJ2WF;EI5SQ;IAhEN,cAAA;IACA,mBAAA;EJ+WF;EIhTQ;IAhEN,cAAA;IACA,UAAA;EJmXF;EIpTQ;IAhEN,cAAA;IACA,mBAAA;EJuXF;EIxTQ;IAhEN,cAAA;IACA,mBAAA;EJ2XF;EI5TQ;IAhEN,cAAA;IACA,WAAA;EJ+XF;EIxTU;IAxDV,cAAA;EJmXA;EI3TU;IAxDV,wBAAA;EJsXA;EI9TU;IAxDV,yBAAA;EJyXA;EIjUU;IAxDV,gBAAA;EJ4XA;EIpUU;IAxDV,yBAAA;EJ+XA;EIvUU;IAxDV,yBAAA;EJkYA;EI1UU;IAxDV,gBAAA;EJqYA;EI7UU;IAxDV,yBAAA;EJwYA;EIhVU;IAxDV,yBAAA;EJ2YA;EInVU;IAxDV,gBAAA;EJ8YA;EItVU;IAxDV,yBAAA;EJiZA;EIzVU;IAxDV,yBAAA;EJoZA;EIjVM;;IAEE,gBAAA;EJmVR;EIhVM;;IAEE,gBAAA;EJkVR;EIzVM;;IAEE,wBAAA;EJ2VR;EIxVM;;IAEE,wBAAA;EJ0VR;EIjWM;;IAEE,uBAAA;EJmWR;EIhWM;;IAEE,uBAAA;EJkWR;EIzWM;;IAEE,sBAAA;EJ2WR;EIxWM;;IAEE,sBAAA;EJ0WR;EIjXM;;IAEE,uBAAA;EJmXR;EIhXM;;IAEE,uBAAA;EJkXR;EIzXM;;IAEE,sBAAA;EJ2XR;EIxXM;;IAEE,sBAAA;EJ0XR;AACF;AC3bI;EGgBE;IACE,WAAA;EJ8aN;EI3aI;IApCJ,cAAA;IACA,WAAA;EJkdA;EIpcA;IACE,cAAA;IACA,WAAA;EJscF;EIxcA;IACE,cAAA;IACA,UAAA;EJ0cF;EI5cA;IACE,cAAA;IACA,mBAAA;EJ8cF;EIhdA;IACE,cAAA;IACA,UAAA;EJkdF;EIpdA;IACE,cAAA;IACA,UAAA;EJsdF;EIxdA;IACE,cAAA;IACA,mBAAA;EJ0dF;EI3bI;IAhDJ,cAAA;IACA,WAAA;EJ8eA;EIzbQ;IAhEN,cAAA;IACA,kBAAA;EJ4fF;EI7bQ;IAhEN,cAAA;IACA,mBAAA;EJggBF;EIjcQ;IAhEN,cAAA;IACA,UAAA;EJogBF;EIrcQ;IAhEN,cAAA;IACA,mBAAA;EJwgBF;EIzcQ;IAhEN,cAAA;IACA,mBAAA;EJ4gBF;EI7cQ;IAhEN,cAAA;IACA,UAAA;EJghBF;EIjdQ;IAhEN,cAAA;IACA,mBAAA;EJohBF;EIrdQ;IAhEN,cAAA;IACA,mBAAA;EJwhBF;EIzdQ;IAhEN,cAAA;IACA,UAAA;EJ4hBF;EI7dQ;IAhEN,cAAA;IACA,mBAAA;EJgiBF;EIjeQ;IAhEN,cAAA;IACA,mBAAA;EJoiBF;EIreQ;IAhEN,cAAA;IACA,WAAA;EJwiBF;EIjeU;IAxDV,cAAA;EJ4hBA;EIpeU;IAxDV,wBAAA;EJ+hBA;EIveU;IAxDV,yBAAA;EJkiBA;EI1eU;IAxDV,gBAAA;EJqiBA;EI7eU;IAxDV,yBAAA;EJwiBA;EIhfU;IAxDV,yBAAA;EJ2iBA;EInfU;IAxDV,gBAAA;EJ8iBA;EItfU;IAxDV,yBAAA;EJijBA;EIzfU;IAxDV,yBAAA;EJojBA;EI5fU;IAxDV,gBAAA;EJujBA;EI/fU;IAxDV,yBAAA;EJ0jBA;EIlgBU;IAxDV,yBAAA;EJ6jBA;EI1fM;;IAEE,gBAAA;EJ4fR;EIzfM;;IAEE,gBAAA;EJ2fR;EIlgBM;;IAEE,wBAAA;EJogBR;EIjgBM;;IAEE,wBAAA;EJmgBR;EI1gBM;;IAEE,uBAAA;EJ4gBR;EIzgBM;;IAEE,uBAAA;EJ2gBR;EIlhBM;;IAEE,sBAAA;EJohBR;EIjhBM;;IAEE,sBAAA;EJmhBR;EI1hBM;;IAEE,uBAAA;EJ4hBR;EIzhBM;;IAEE,uBAAA;EJ2hBR;EIliBM;;IAEE,sBAAA;EJoiBR;EIjiBM;;IAEE,sBAAA;EJmiBR;AACF;ACpmBI;EGgBE;IACE,WAAA;EJulBN;EIplBI;IApCJ,cAAA;IACA,WAAA;EJ2nBA;EI7mBA;IACE,cAAA;IACA,WAAA;EJ+mBF;EIjnBA;IACE,cAAA;IACA,UAAA;EJmnBF;EIrnBA;IACE,cAAA;IACA,mBAAA;EJunBF;EIznBA;IACE,cAAA;IACA,UAAA;EJ2nBF;EI7nBA;IACE,cAAA;IACA,UAAA;EJ+nBF;EIjoBA;IACE,cAAA;IACA,mBAAA;EJmoBF;EIpmBI;IAhDJ,cAAA;IACA,WAAA;EJupBA;EIlmBQ;IAhEN,cAAA;IACA,kBAAA;EJqqBF;EItmBQ;IAhEN,cAAA;IACA,mBAAA;EJyqBF;EI1mBQ;IAhEN,cAAA;IACA,UAAA;EJ6qBF;EI9mBQ;IAhEN,cAAA;IACA,mBAAA;EJirBF;EIlnBQ;IAhEN,cAAA;IACA,mBAAA;EJqrBF;EItnBQ;IAhEN,cAAA;IACA,UAAA;EJyrBF;EI1nBQ;IAhEN,cAAA;IACA,mBAAA;EJ6rBF;EI9nBQ;IAhEN,cAAA;IACA,mBAAA;EJisBF;EIloBQ;IAhEN,cAAA;IACA,UAAA;EJqsBF;EItoBQ;IAhEN,cAAA;IACA,mBAAA;EJysBF;EI1oBQ;IAhEN,cAAA;IACA,mBAAA;EJ6sBF;EI9oBQ;IAhEN,cAAA;IACA,WAAA;EJitBF;EI1oBU;IAxDV,cAAA;EJqsBA;EI7oBU;IAxDV,wBAAA;EJwsBA;EIhpBU;IAxDV,yBAAA;EJ2sBA;EInpBU;IAxDV,gBAAA;EJ8sBA;EItpBU;IAxDV,yBAAA;EJitBA;EIzpBU;IAxDV,yBAAA;EJotBA;EI5pBU;IAxDV,gBAAA;EJutBA;EI/pBU;IAxDV,yBAAA;EJ0tBA;EIlqBU;IAxDV,yBAAA;EJ6tBA;EIrqBU;IAxDV,gBAAA;EJguBA;EIxqBU;IAxDV,yBAAA;EJmuBA;EI3qBU;IAxDV,yBAAA;EJsuBA;EInqBM;;IAEE,gBAAA;EJqqBR;EIlqBM;;IAEE,gBAAA;EJoqBR;EI3qBM;;IAEE,wBAAA;EJ6qBR;EI1qBM;;IAEE,wBAAA;EJ4qBR;EInrBM;;IAEE,uBAAA;EJqrBR;EIlrBM;;IAEE,uBAAA;EJorBR;EI3rBM;;IAEE,sBAAA;EJ6rBR;EI1rBM;;IAEE,sBAAA;EJ4rBR;EInsBM;;IAEE,uBAAA;EJqsBR;EIlsBM;;IAEE,uBAAA;EJosBR;EI3sBM;;IAEE,sBAAA;EJ6sBR;EI1sBM;;IAEE,sBAAA;EJ4sBR;AACF;AC7wBI;EGgBE;IACE,WAAA;EJgwBN;EI7vBI;IApCJ,cAAA;IACA,WAAA;EJoyBA;EItxBA;IACE,cAAA;IACA,WAAA;EJwxBF;EI1xBA;IACE,cAAA;IACA,UAAA;EJ4xBF;EI9xBA;IACE,cAAA;IACA,mBAAA;EJgyBF;EIlyBA;IACE,cAAA;IACA,UAAA;EJoyBF;EItyBA;IACE,cAAA;IACA,UAAA;EJwyBF;EI1yBA;IACE,cAAA;IACA,mBAAA;EJ4yBF;EI7wBI;IAhDJ,cAAA;IACA,WAAA;EJg0BA;EI3wBQ;IAhEN,cAAA;IACA,kBAAA;EJ80BF;EI/wBQ;IAhEN,cAAA;IACA,mBAAA;EJk1BF;EInxBQ;IAhEN,cAAA;IACA,UAAA;EJs1BF;EIvxBQ;IAhEN,cAAA;IACA,mBAAA;EJ01BF;EI3xBQ;IAhEN,cAAA;IACA,mBAAA;EJ81BF;EI/xBQ;IAhEN,cAAA;IACA,UAAA;EJk2BF;EInyBQ;IAhEN,cAAA;IACA,mBAAA;EJs2BF;EIvyBQ;IAhEN,cAAA;IACA,mBAAA;EJ02BF;EI3yBQ;IAhEN,cAAA;IACA,UAAA;EJ82BF;EI/yBQ;IAhEN,cAAA;IACA,mBAAA;EJk3BF;EInzBQ;IAhEN,cAAA;IACA,mBAAA;EJs3BF;EIvzBQ;IAhEN,cAAA;IACA,WAAA;EJ03BF;EInzBU;IAxDV,cAAA;EJ82BA;EItzBU;IAxDV,wBAAA;EJi3BA;EIzzBU;IAxDV,yBAAA;EJo3BA;EI5zBU;IAxDV,gBAAA;EJu3BA;EI/zBU;IAxDV,yBAAA;EJ03BA;EIl0BU;IAxDV,yBAAA;EJ63BA;EIr0BU;IAxDV,gBAAA;EJg4BA;EIx0BU;IAxDV,yBAAA;EJm4BA;EI30BU;IAxDV,yBAAA;EJs4BA;EI90BU;IAxDV,gBAAA;EJy4BA;EIj1BU;IAxDV,yBAAA;EJ44BA;EIp1BU;IAxDV,yBAAA;EJ+4BA;EI50BM;;IAEE,gBAAA;EJ80BR;EI30BM;;IAEE,gBAAA;EJ60BR;EIp1BM;;IAEE,wBAAA;EJs1BR;EIn1BM;;IAEE,wBAAA;EJq1BR;EI51BM;;IAEE,uBAAA;EJ81BR;EI31BM;;IAEE,uBAAA;EJ61BR;EIp2BM;;IAEE,sBAAA;EJs2BR;EIn2BM;;IAEE,sBAAA;EJq2BR;EI52BM;;IAEE,uBAAA;EJ82BR;EI32BM;;IAEE,uBAAA;EJ62BR;EIp3BM;;IAEE,sBAAA;EJs3BR;EIn3BM;;IAEE,sBAAA;EJq3BR;AACF;ACt7BI;EGgBE;IACE,WAAA;EJy6BN;EIt6BI;IApCJ,cAAA;IACA,WAAA;EJ68BA;EI/7BA;IACE,cAAA;IACA,WAAA;EJi8BF;EIn8BA;IACE,cAAA;IACA,UAAA;EJq8BF;EIv8BA;IACE,cAAA;IACA,mBAAA;EJy8BF;EI38BA;IACE,cAAA;IACA,UAAA;EJ68BF;EI/8BA;IACE,cAAA;IACA,UAAA;EJi9BF;EIn9BA;IACE,cAAA;IACA,mBAAA;EJq9BF;EIt7BI;IAhDJ,cAAA;IACA,WAAA;EJy+BA;EIp7BQ;IAhEN,cAAA;IACA,kBAAA;EJu/BF;EIx7BQ;IAhEN,cAAA;IACA,mBAAA;EJ2/BF;EI57BQ;IAhEN,cAAA;IACA,UAAA;EJ+/BF;EIh8BQ;IAhEN,cAAA;IACA,mBAAA;EJmgCF;EIp8BQ;IAhEN,cAAA;IACA,mBAAA;EJugCF;EIx8BQ;IAhEN,cAAA;IACA,UAAA;EJ2gCF;EI58BQ;IAhEN,cAAA;IACA,mBAAA;EJ+gCF;EIh9BQ;IAhEN,cAAA;IACA,mBAAA;EJmhCF;EIp9BQ;IAhEN,cAAA;IACA,UAAA;EJuhCF;EIx9BQ;IAhEN,cAAA;IACA,mBAAA;EJ2hCF;EI59BQ;IAhEN,cAAA;IACA,mBAAA;EJ+hCF;EIh+BQ;IAhEN,cAAA;IACA,WAAA;EJmiCF;EI59BU;IAxDV,cAAA;EJuhCA;EI/9BU;IAxDV,wBAAA;EJ0hCA;EIl+BU;IAxDV,yBAAA;EJ6hCA;EIr+BU;IAxDV,gBAAA;EJgiCA;EIx+BU;IAxDV,yBAAA;EJmiCA;EI3+BU;IAxDV,yBAAA;EJsiCA;EI9+BU;IAxDV,gBAAA;EJyiCA;EIj/BU;IAxDV,yBAAA;EJ4iCA;EIp/BU;IAxDV,yBAAA;EJ+iCA;EIv/BU;IAxDV,gBAAA;EJkjCA;EI1/BU;IAxDV,yBAAA;EJqjCA;EI7/BU;IAxDV,yBAAA;EJwjCA;EIr/BM;;IAEE,gBAAA;EJu/BR;EIp/BM;;IAEE,gBAAA;EJs/BR;EI7/BM;;IAEE,wBAAA;EJ+/BR;EI5/BM;;IAEE,wBAAA;EJ8/BR;EIrgCM;;IAEE,uBAAA;EJugCR;EIpgCM;;IAEE,uBAAA;EJsgCR;EI7gCM;;IAEE,sBAAA;EJ+gCR;EI5gCM;;IAEE,sBAAA;EJ8gCR;EIrhCM;;IAEE,uBAAA;EJuhCR;EIphCM;;IAEE,uBAAA;EJshCR;EI7hCM;;IAEE,sBAAA;EJ+hCR;EI5hCM;;IAEE,sBAAA;EJ8hCR;AACF;AK5lCQ;EAOI,0BAAA;ALwlCZ;;AK/lCQ;EAOI,gCAAA;AL4lCZ;;AKnmCQ;EAOI,yBAAA;ALgmCZ;;AKvmCQ;EAOI,wBAAA;ALomCZ;;AK3mCQ;EAOI,+BAAA;ALwmCZ;;AK/mCQ;EAOI,yBAAA;AL4mCZ;;AKnnCQ;EAOI,6BAAA;ALgnCZ;;AKvnCQ;EAOI,8BAAA;ALonCZ;;AK3nCQ;EAOI,wBAAA;ALwnCZ;;AK/nCQ;EAOI,+BAAA;AL4nCZ;;AKnoCQ;EAOI,wBAAA;ALgoCZ;;AKvoCQ;EAOI,yBAAA;ALooCZ;;AK3oCQ;EAOI,8BAAA;ALwoCZ;;AK/oCQ;EAOI,iCAAA;AL4oCZ;;AKnpCQ;EAOI,sCAAA;ALgpCZ;;AKvpCQ;EAOI,yCAAA;ALopCZ;;AK3pCQ;EAOI,uBAAA;ALwpCZ;;AK/pCQ;EAOI,uBAAA;AL4pCZ;;AKnqCQ;EAOI,yBAAA;ALgqCZ;;AKvqCQ;EAOI,yBAAA;ALoqCZ;;AK3qCQ;EAOI,0BAAA;ALwqCZ;;AK/qCQ;EAOI,4BAAA;AL4qCZ;;AKnrCQ;EAOI,kCAAA;ALgrCZ;;AKvrCQ;EAOI,sCAAA;ALorCZ;;AK3rCQ;EAOI,oCAAA;ALwrCZ;;AK/rCQ;EAOI,kCAAA;AL4rCZ;;AKnsCQ;EAOI,yCAAA;ALgsCZ;;AKvsCQ;EAOI,wCAAA;ALosCZ;;AK3sCQ;EAOI,wCAAA;ALwsCZ;;AK/sCQ;EAOI,kCAAA;AL4sCZ;;AKntCQ;EAOI,gCAAA;ALgtCZ;;AKvtCQ;EAOI,8BAAA;ALotCZ;;AK3tCQ;EAOI,gCAAA;ALwtCZ;;AK/tCQ;EAOI,+BAAA;AL4tCZ;;AKnuCQ;EAOI,oCAAA;ALguCZ;;AKvuCQ;EAOI,kCAAA;ALouCZ;;AK3uCQ;EAOI,gCAAA;ALwuCZ;;AK/uCQ;EAOI,uCAAA;AL4uCZ;;AKnvCQ;EAOI,sCAAA;ALgvCZ;;AKvvCQ;EAOI,iCAAA;ALovCZ;;AK3vCQ;EAOI,2BAAA;ALwvCZ;;AK/vCQ;EAOI,iCAAA;AL4vCZ;;AKnwCQ;EAOI,+BAAA;ALgwCZ;;AKvwCQ;EAOI,6BAAA;ALowCZ;;AK3wCQ;EAOI,+BAAA;ALwwCZ;;AK/wCQ;EAOI,8BAAA;AL4wCZ;;AKnxCQ;EAOI,oBAAA;ALgxCZ;;AKvxCQ;EAOI,mBAAA;ALoxCZ;;AK3xCQ;EAOI,mBAAA;ALwxCZ;;AK/xCQ;EAOI,mBAAA;AL4xCZ;;AKnyCQ;EAOI,mBAAA;ALgyCZ;;AKvyCQ;EAOI,mBAAA;ALoyCZ;;AK3yCQ;EAOI,mBAAA;ALwyCZ;;AK/yCQ;EAOI,mBAAA;AL4yCZ;;AKnzCQ;EAOI,oBAAA;ALgzCZ;;AKvzCQ;EAOI,4BAAA;ALozCZ;;AK3zCQ;EAOI,2BAAA;ALwzCZ;;AK/zCQ;EAOI,0BAAA;AL4zCZ;;AKn0CQ;EAOI,2BAAA;ALg0CZ;;AKv0CQ;EAOI,0BAAA;ALo0CZ;;AK30CQ;EAOI,uBAAA;ALw0CZ;;AK/0CQ;EAOI,0BAAA;EAAA,yBAAA;AL60CZ;;AKp1CQ;EAOI,kCAAA;EAAA,iCAAA;ALk1CZ;;AKz1CQ;EAOI,iCAAA;EAAA,gCAAA;ALu1CZ;;AK91CQ;EAOI,gCAAA;EAAA,+BAAA;AL41CZ;;AKn2CQ;EAOI,iCAAA;EAAA,gCAAA;ALi2CZ;;AKx2CQ;EAOI,gCAAA;EAAA,+BAAA;ALs2CZ;;AK72CQ;EAOI,6BAAA;EAAA,4BAAA;AL22CZ;;AKl3CQ;EAOI,wBAAA;EAAA,2BAAA;ALg3CZ;;AKv3CQ;EAOI,gCAAA;EAAA,mCAAA;ALq3CZ;;AK53CQ;EAOI,+BAAA;EAAA,kCAAA;AL03CZ;;AKj4CQ;EAOI,8BAAA;EAAA,iCAAA;AL+3CZ;;AKt4CQ;EAOI,+BAAA;EAAA,kCAAA;ALo4CZ;;AK34CQ;EAOI,8BAAA;EAAA,iCAAA;ALy4CZ;;AKh5CQ;EAOI,2BAAA;EAAA,8BAAA;AL84CZ;;AKr5CQ;EAOI,wBAAA;ALk5CZ;;AKz5CQ;EAOI,gCAAA;ALs5CZ;;AK75CQ;EAOI,+BAAA;AL05CZ;;AKj6CQ;EAOI,8BAAA;AL85CZ;;AKr6CQ;EAOI,+BAAA;ALk6CZ;;AKz6CQ;EAOI,8BAAA;ALs6CZ;;AK76CQ;EAOI,2BAAA;AL06CZ;;AKj7CQ;EAOI,0BAAA;AL86CZ;;AKr7CQ;EAOI,kCAAA;ALk7CZ;;AKz7CQ;EAOI,iCAAA;ALs7CZ;;AK77CQ;EAOI,gCAAA;AL07CZ;;AKj8CQ;EAOI,iCAAA;AL87CZ;;AKr8CQ;EAOI,gCAAA;ALk8CZ;;AKz8CQ;EAOI,6BAAA;ALs8CZ;;AK78CQ;EAOI,2BAAA;AL08CZ;;AKj9CQ;EAOI,mCAAA;AL88CZ;;AKr9CQ;EAOI,kCAAA;ALk9CZ;;AKz9CQ;EAOI,iCAAA;ALs9CZ;;AK79CQ;EAOI,kCAAA;AL09CZ;;AKj+CQ;EAOI,iCAAA;AL89CZ;;AKr+CQ;EAOI,8BAAA;ALk+CZ;;AKz+CQ;EAOI,yBAAA;ALs+CZ;;AK7+CQ;EAOI,iCAAA;AL0+CZ;;AKj/CQ;EAOI,gCAAA;AL8+CZ;;AKr/CQ;EAOI,+BAAA;ALk/CZ;;AKz/CQ;EAOI,gCAAA;ALs/CZ;;AK7/CQ;EAOI,+BAAA;AL0/CZ;;AKjgDQ;EAOI,4BAAA;AL8/CZ;;AKrgDQ;EAOI,qBAAA;ALkgDZ;;AKzgDQ;EAOI,6BAAA;ALsgDZ;;AK7gDQ;EAOI,4BAAA;AL0gDZ;;AKjhDQ;EAOI,2BAAA;AL8gDZ;;AKrhDQ;EAOI,4BAAA;ALkhDZ;;AKzhDQ;EAOI,2BAAA;ALshDZ;;AK7hDQ;EAOI,2BAAA;EAAA,0BAAA;AL2hDZ;;AKliDQ;EAOI,mCAAA;EAAA,kCAAA;ALgiDZ;;AKviDQ;EAOI,kCAAA;EAAA,iCAAA;ALqiDZ;;AK5iDQ;EAOI,iCAAA;EAAA,gCAAA;AL0iDZ;;AKjjDQ;EAOI,kCAAA;EAAA,iCAAA;AL+iDZ;;AKtjDQ;EAOI,iCAAA;EAAA,gCAAA;ALojDZ;;AK3jDQ;EAOI,yBAAA;EAAA,4BAAA;ALyjDZ;;AKhkDQ;EAOI,iCAAA;EAAA,oCAAA;AL8jDZ;;AKrkDQ;EAOI,gCAAA;EAAA,mCAAA;ALmkDZ;;AK1kDQ;EAOI,+BAAA;EAAA,kCAAA;ALwkDZ;;AK/kDQ;EAOI,gCAAA;EAAA,mCAAA;AL6kDZ;;AKplDQ;EAOI,+BAAA;EAAA,kCAAA;ALklDZ;;AKzlDQ;EAOI,yBAAA;ALslDZ;;AK7lDQ;EAOI,iCAAA;AL0lDZ;;AKjmDQ;EAOI,gCAAA;AL8lDZ;;AKrmDQ;EAOI,+BAAA;ALkmDZ;;AKzmDQ;EAOI,gCAAA;ALsmDZ;;AK7mDQ;EAOI,+BAAA;AL0mDZ;;AKjnDQ;EAOI,2BAAA;AL8mDZ;;AKrnDQ;EAOI,mCAAA;ALknDZ;;AKznDQ;EAOI,kCAAA;ALsnDZ;;AK7nDQ;EAOI,iCAAA;AL0nDZ;;AKjoDQ;EAOI,kCAAA;AL8nDZ;;AKroDQ;EAOI,iCAAA;ALkoDZ;;AKzoDQ;EAOI,4BAAA;ALsoDZ;;AK7oDQ;EAOI,oCAAA;AL0oDZ;;AKjpDQ;EAOI,mCAAA;AL8oDZ;;AKrpDQ;EAOI,kCAAA;ALkpDZ;;AKzpDQ;EAOI,mCAAA;ALspDZ;;AK7pDQ;EAOI,kCAAA;AL0pDZ;;AKjqDQ;EAOI,0BAAA;AL8pDZ;;AKrqDQ;EAOI,kCAAA;ALkqDZ;;AKzqDQ;EAOI,iCAAA;ALsqDZ;;AK7qDQ;EAOI,gCAAA;AL0qDZ;;AKjrDQ;EAOI,iCAAA;AL8qDZ;;AKrrDQ;EAOI,gCAAA;ALkrDZ;;AC5rDI;EIGI;IAOI,0BAAA;ELurDV;EK9rDM;IAOI,gCAAA;EL0rDV;EKjsDM;IAOI,yBAAA;EL6rDV;EKpsDM;IAOI,wBAAA;ELgsDV;EKvsDM;IAOI,+BAAA;ELmsDV;EK1sDM;IAOI,yBAAA;ELssDV;EK7sDM;IAOI,6BAAA;ELysDV;EKhtDM;IAOI,8BAAA;EL4sDV;EKntDM;IAOI,wBAAA;EL+sDV;EKttDM;IAOI,+BAAA;ELktDV;EKztDM;IAOI,wBAAA;ELqtDV;EK5tDM;IAOI,yBAAA;ELwtDV;EK/tDM;IAOI,8BAAA;EL2tDV;EKluDM;IAOI,iCAAA;EL8tDV;EKruDM;IAOI,sCAAA;ELiuDV;EKxuDM;IAOI,yCAAA;ELouDV;EK3uDM;IAOI,uBAAA;ELuuDV;EK9uDM;IAOI,uBAAA;EL0uDV;EKjvDM;IAOI,yBAAA;EL6uDV;EKpvDM;IAOI,yBAAA;ELgvDV;EKvvDM;IAOI,0BAAA;ELmvDV;EK1vDM;IAOI,4BAAA;ELsvDV;EK7vDM;IAOI,kCAAA;ELyvDV;EKhwDM;IAOI,sCAAA;EL4vDV;EKnwDM;IAOI,oCAAA;EL+vDV;EKtwDM;IAOI,kCAAA;ELkwDV;EKzwDM;IAOI,yCAAA;ELqwDV;EK5wDM;IAOI,wCAAA;ELwwDV;EK/wDM;IAOI,wCAAA;EL2wDV;EKlxDM;IAOI,kCAAA;EL8wDV;EKrxDM;IAOI,gCAAA;ELixDV;EKxxDM;IAOI,8BAAA;ELoxDV;EK3xDM;IAOI,gCAAA;ELuxDV;EK9xDM;IAOI,+BAAA;EL0xDV;EKjyDM;IAOI,oCAAA;EL6xDV;EKpyDM;IAOI,kCAAA;ELgyDV;EKvyDM;IAOI,gCAAA;ELmyDV;EK1yDM;IAOI,uCAAA;ELsyDV;EK7yDM;IAOI,sCAAA;ELyyDV;EKhzDM;IAOI,iCAAA;EL4yDV;EKnzDM;IAOI,2BAAA;EL+yDV;EKtzDM;IAOI,iCAAA;ELkzDV;EKzzDM;IAOI,+BAAA;ELqzDV;EK5zDM;IAOI,6BAAA;ELwzDV;EK/zDM;IAOI,+BAAA;EL2zDV;EKl0DM;IAOI,8BAAA;EL8zDV;EKr0DM;IAOI,oBAAA;ELi0DV;EKx0DM;IAOI,mBAAA;ELo0DV;EK30DM;IAOI,mBAAA;ELu0DV;EK90DM;IAOI,mBAAA;EL00DV;EKj1DM;IAOI,mBAAA;EL60DV;EKp1DM;IAOI,mBAAA;ELg1DV;EKv1DM;IAOI,mBAAA;ELm1DV;EK11DM;IAOI,mBAAA;ELs1DV;EK71DM;IAOI,oBAAA;ELy1DV;EKh2DM;IAOI,4BAAA;EL41DV;EKn2DM;IAOI,2BAAA;EL+1DV;EKt2DM;IAOI,0BAAA;ELk2DV;EKz2DM;IAOI,2BAAA;ELq2DV;EK52DM;IAOI,0BAAA;ELw2DV;EK/2DM;IAOI,uBAAA;EL22DV;EKl3DM;IAOI,0BAAA;IAAA,yBAAA;EL+2DV;EKt3DM;IAOI,kCAAA;IAAA,iCAAA;ELm3DV;EK13DM;IAOI,iCAAA;IAAA,gCAAA;ELu3DV;EK93DM;IAOI,gCAAA;IAAA,+BAAA;EL23DV;EKl4DM;IAOI,iCAAA;IAAA,gCAAA;EL+3DV;EKt4DM;IAOI,gCAAA;IAAA,+BAAA;ELm4DV;EK14DM;IAOI,6BAAA;IAAA,4BAAA;ELu4DV;EK94DM;IAOI,wBAAA;IAAA,2BAAA;EL24DV;EKl5DM;IAOI,gCAAA;IAAA,mCAAA;EL+4DV;EKt5DM;IAOI,+BAAA;IAAA,kCAAA;ELm5DV;EK15DM;IAOI,8BAAA;IAAA,iCAAA;ELu5DV;EK95DM;IAOI,+BAAA;IAAA,kCAAA;EL25DV;EKl6DM;IAOI,8BAAA;IAAA,iCAAA;EL+5DV;EKt6DM;IAOI,2BAAA;IAAA,8BAAA;ELm6DV;EK16DM;IAOI,wBAAA;ELs6DV;EK76DM;IAOI,gCAAA;ELy6DV;EKh7DM;IAOI,+BAAA;EL46DV;EKn7DM;IAOI,8BAAA;EL+6DV;EKt7DM;IAOI,+BAAA;ELk7DV;EKz7DM;IAOI,8BAAA;ELq7DV;EK57DM;IAOI,2BAAA;ELw7DV;EK/7DM;IAOI,0BAAA;EL27DV;EKl8DM;IAOI,kCAAA;EL87DV;EKr8DM;IAOI,iCAAA;ELi8DV;EKx8DM;IAOI,gCAAA;ELo8DV;EK38DM;IAOI,iCAAA;ELu8DV;EK98DM;IAOI,gCAAA;EL08DV;EKj9DM;IAOI,6BAAA;EL68DV;EKp9DM;IAOI,2BAAA;ELg9DV;EKv9DM;IAOI,mCAAA;ELm9DV;EK19DM;IAOI,kCAAA;ELs9DV;EK79DM;IAOI,iCAAA;ELy9DV;EKh+DM;IAOI,kCAAA;EL49DV;EKn+DM;IAOI,iCAAA;EL+9DV;EKt+DM;IAOI,8BAAA;ELk+DV;EKz+DM;IAOI,yBAAA;ELq+DV;EK5+DM;IAOI,iCAAA;ELw+DV;EK/+DM;IAOI,gCAAA;EL2+DV;EKl/DM;IAOI,+BAAA;EL8+DV;EKr/DM;IAOI,gCAAA;ELi/DV;EKx/DM;IAOI,+BAAA;ELo/DV;EK3/DM;IAOI,4BAAA;ELu/DV;EK9/DM;IAOI,qBAAA;EL0/DV;EKjgEM;IAOI,6BAAA;EL6/DV;EKpgEM;IAOI,4BAAA;ELggEV;EKvgEM;IAOI,2BAAA;ELmgEV;EK1gEM;IAOI,4BAAA;ELsgEV;EK7gEM;IAOI,2BAAA;ELygEV;EKhhEM;IAOI,2BAAA;IAAA,0BAAA;EL6gEV;EKphEM;IAOI,mCAAA;IAAA,kCAAA;ELihEV;EKxhEM;IAOI,kCAAA;IAAA,iCAAA;ELqhEV;EK5hEM;IAOI,iCAAA;IAAA,gCAAA;ELyhEV;EKhiEM;IAOI,kCAAA;IAAA,iCAAA;EL6hEV;EKpiEM;IAOI,iCAAA;IAAA,gCAAA;ELiiEV;EKxiEM;IAOI,yBAAA;IAAA,4BAAA;ELqiEV;EK5iEM;IAOI,iCAAA;IAAA,oCAAA;ELyiEV;EKhjEM;IAOI,gCAAA;IAAA,mCAAA;EL6iEV;EKpjEM;IAOI,+BAAA;IAAA,kCAAA;ELijEV;EKxjEM;IAOI,gCAAA;IAAA,mCAAA;ELqjEV;EK5jEM;IAOI,+BAAA;IAAA,kCAAA;ELyjEV;EKhkEM;IAOI,yBAAA;EL4jEV;EKnkEM;IAOI,iCAAA;EL+jEV;EKtkEM;IAOI,gCAAA;ELkkEV;EKzkEM;IAOI,+BAAA;ELqkEV;EK5kEM;IAOI,gCAAA;ELwkEV;EK/kEM;IAOI,+BAAA;EL2kEV;EKllEM;IAOI,2BAAA;EL8kEV;EKrlEM;IAOI,mCAAA;ELilEV;EKxlEM;IAOI,kCAAA;ELolEV;EK3lEM;IAOI,iCAAA;ELulEV;EK9lEM;IAOI,kCAAA;EL0lEV;EKjmEM;IAOI,iCAAA;EL6lEV;EKpmEM;IAOI,4BAAA;ELgmEV;EKvmEM;IAOI,oCAAA;ELmmEV;EK1mEM;IAOI,mCAAA;ELsmEV;EK7mEM;IAOI,kCAAA;ELymEV;EKhnEM;IAOI,mCAAA;EL4mEV;EKnnEM;IAOI,kCAAA;EL+mEV;EKtnEM;IAOI,0BAAA;ELknEV;EKznEM;IAOI,kCAAA;ELqnEV;EK5nEM;IAOI,iCAAA;ELwnEV;EK/nEM;IAOI,gCAAA;EL2nEV;EKloEM;IAOI,iCAAA;EL8nEV;EKroEM;IAOI,gCAAA;ELioEV;AACF;AC5oEI;EIGI;IAOI,0BAAA;ELsoEV;EK7oEM;IAOI,gCAAA;ELyoEV;EKhpEM;IAOI,yBAAA;EL4oEV;EKnpEM;IAOI,wBAAA;EL+oEV;EKtpEM;IAOI,+BAAA;ELkpEV;EKzpEM;IAOI,yBAAA;ELqpEV;EK5pEM;IAOI,6BAAA;ELwpEV;EK/pEM;IAOI,8BAAA;EL2pEV;EKlqEM;IAOI,wBAAA;EL8pEV;EKrqEM;IAOI,+BAAA;ELiqEV;EKxqEM;IAOI,wBAAA;ELoqEV;EK3qEM;IAOI,yBAAA;ELuqEV;EK9qEM;IAOI,8BAAA;EL0qEV;EKjrEM;IAOI,iCAAA;EL6qEV;EKprEM;IAOI,sCAAA;ELgrEV;EKvrEM;IAOI,yCAAA;ELmrEV;EK1rEM;IAOI,uBAAA;ELsrEV;EK7rEM;IAOI,uBAAA;ELyrEV;EKhsEM;IAOI,yBAAA;EL4rEV;EKnsEM;IAOI,yBAAA;EL+rEV;EKtsEM;IAOI,0BAAA;ELksEV;EKzsEM;IAOI,4BAAA;ELqsEV;EK5sEM;IAOI,kCAAA;ELwsEV;EK/sEM;IAOI,sCAAA;EL2sEV;EKltEM;IAOI,oCAAA;EL8sEV;EKrtEM;IAOI,kCAAA;ELitEV;EKxtEM;IAOI,yCAAA;ELotEV;EK3tEM;IAOI,wCAAA;ELutEV;EK9tEM;IAOI,wCAAA;EL0tEV;EKjuEM;IAOI,kCAAA;EL6tEV;EKpuEM;IAOI,gCAAA;ELguEV;EKvuEM;IAOI,8BAAA;ELmuEV;EK1uEM;IAOI,gCAAA;ELsuEV;EK7uEM;IAOI,+BAAA;ELyuEV;EKhvEM;IAOI,oCAAA;EL4uEV;EKnvEM;IAOI,kCAAA;EL+uEV;EKtvEM;IAOI,gCAAA;ELkvEV;EKzvEM;IAOI,uCAAA;ELqvEV;EK5vEM;IAOI,sCAAA;ELwvEV;EK/vEM;IAOI,iCAAA;EL2vEV;EKlwEM;IAOI,2BAAA;EL8vEV;EKrwEM;IAOI,iCAAA;ELiwEV;EKxwEM;IAOI,+BAAA;ELowEV;EK3wEM;IAOI,6BAAA;ELuwEV;EK9wEM;IAOI,+BAAA;EL0wEV;EKjxEM;IAOI,8BAAA;EL6wEV;EKpxEM;IAOI,oBAAA;ELgxEV;EKvxEM;IAOI,mBAAA;ELmxEV;EK1xEM;IAOI,mBAAA;ELsxEV;EK7xEM;IAOI,mBAAA;ELyxEV;EKhyEM;IAOI,mBAAA;EL4xEV;EKnyEM;IAOI,mBAAA;EL+xEV;EKtyEM;IAOI,mBAAA;ELkyEV;EKzyEM;IAOI,mBAAA;ELqyEV;EK5yEM;IAOI,oBAAA;ELwyEV;EK/yEM;IAOI,4BAAA;EL2yEV;EKlzEM;IAOI,2BAAA;EL8yEV;EKrzEM;IAOI,0BAAA;ELizEV;EKxzEM;IAOI,2BAAA;ELozEV;EK3zEM;IAOI,0BAAA;ELuzEV;EK9zEM;IAOI,uBAAA;EL0zEV;EKj0EM;IAOI,0BAAA;IAAA,yBAAA;EL8zEV;EKr0EM;IAOI,kCAAA;IAAA,iCAAA;ELk0EV;EKz0EM;IAOI,iCAAA;IAAA,gCAAA;ELs0EV;EK70EM;IAOI,gCAAA;IAAA,+BAAA;EL00EV;EKj1EM;IAOI,iCAAA;IAAA,gCAAA;EL80EV;EKr1EM;IAOI,gCAAA;IAAA,+BAAA;ELk1EV;EKz1EM;IAOI,6BAAA;IAAA,4BAAA;ELs1EV;EK71EM;IAOI,wBAAA;IAAA,2BAAA;EL01EV;EKj2EM;IAOI,gCAAA;IAAA,mCAAA;EL81EV;EKr2EM;IAOI,+BAAA;IAAA,kCAAA;ELk2EV;EKz2EM;IAOI,8BAAA;IAAA,iCAAA;ELs2EV;EK72EM;IAOI,+BAAA;IAAA,kCAAA;EL02EV;EKj3EM;IAOI,8BAAA;IAAA,iCAAA;EL82EV;EKr3EM;IAOI,2BAAA;IAAA,8BAAA;ELk3EV;EKz3EM;IAOI,wBAAA;ELq3EV;EK53EM;IAOI,gCAAA;ELw3EV;EK/3EM;IAOI,+BAAA;EL23EV;EKl4EM;IAOI,8BAAA;EL83EV;EKr4EM;IAOI,+BAAA;ELi4EV;EKx4EM;IAOI,8BAAA;ELo4EV;EK34EM;IAOI,2BAAA;ELu4EV;EK94EM;IAOI,0BAAA;EL04EV;EKj5EM;IAOI,kCAAA;EL64EV;EKp5EM;IAOI,iCAAA;ELg5EV;EKv5EM;IAOI,gCAAA;ELm5EV;EK15EM;IAOI,iCAAA;ELs5EV;EK75EM;IAOI,gCAAA;ELy5EV;EKh6EM;IAOI,6BAAA;EL45EV;EKn6EM;IAOI,2BAAA;EL+5EV;EKt6EM;IAOI,mCAAA;ELk6EV;EKz6EM;IAOI,kCAAA;ELq6EV;EK56EM;IAOI,iCAAA;ELw6EV;EK/6EM;IAOI,kCAAA;EL26EV;EKl7EM;IAOI,iCAAA;EL86EV;EKr7EM;IAOI,8BAAA;ELi7EV;EKx7EM;IAOI,yBAAA;ELo7EV;EK37EM;IAOI,iCAAA;ELu7EV;EK97EM;IAOI,gCAAA;EL07EV;EKj8EM;IAOI,+BAAA;EL67EV;EKp8EM;IAOI,gCAAA;ELg8EV;EKv8EM;IAOI,+BAAA;ELm8EV;EK18EM;IAOI,4BAAA;ELs8EV;EK78EM;IAOI,qBAAA;ELy8EV;EKh9EM;IAOI,6BAAA;EL48EV;EKn9EM;IAOI,4BAAA;EL+8EV;EKt9EM;IAOI,2BAAA;ELk9EV;EKz9EM;IAOI,4BAAA;ELq9EV;EK59EM;IAOI,2BAAA;ELw9EV;EK/9EM;IAOI,2BAAA;IAAA,0BAAA;EL49EV;EKn+EM;IAOI,mCAAA;IAAA,kCAAA;ELg+EV;EKv+EM;IAOI,kCAAA;IAAA,iCAAA;ELo+EV;EK3+EM;IAOI,iCAAA;IAAA,gCAAA;ELw+EV;EK/+EM;IAOI,kCAAA;IAAA,iCAAA;EL4+EV;EKn/EM;IAOI,iCAAA;IAAA,gCAAA;ELg/EV;EKv/EM;IAOI,yBAAA;IAAA,4BAAA;ELo/EV;EK3/EM;IAOI,iCAAA;IAAA,oCAAA;ELw/EV;EK//EM;IAOI,gCAAA;IAAA,mCAAA;EL4/EV;EKngFM;IAOI,+BAAA;IAAA,kCAAA;ELggFV;EKvgFM;IAOI,gCAAA;IAAA,mCAAA;ELogFV;EK3gFM;IAOI,+BAAA;IAAA,kCAAA;ELwgFV;EK/gFM;IAOI,yBAAA;EL2gFV;EKlhFM;IAOI,iCAAA;EL8gFV;EKrhFM;IAOI,gCAAA;ELihFV;EKxhFM;IAOI,+BAAA;ELohFV;EK3hFM;IAOI,gCAAA;ELuhFV;EK9hFM;IAOI,+BAAA;EL0hFV;EKjiFM;IAOI,2BAAA;EL6hFV;EKpiFM;IAOI,mCAAA;ELgiFV;EKviFM;IAOI,kCAAA;ELmiFV;EK1iFM;IAOI,iCAAA;ELsiFV;EK7iFM;IAOI,kCAAA;ELyiFV;EKhjFM;IAOI,iCAAA;EL4iFV;EKnjFM;IAOI,4BAAA;EL+iFV;EKtjFM;IAOI,oCAAA;ELkjFV;EKzjFM;IAOI,mCAAA;ELqjFV;EK5jFM;IAOI,kCAAA;ELwjFV;EK/jFM;IAOI,mCAAA;EL2jFV;EKlkFM;IAOI,kCAAA;EL8jFV;EKrkFM;IAOI,0BAAA;ELikFV;EKxkFM;IAOI,kCAAA;ELokFV;EK3kFM;IAOI,iCAAA;ELukFV;EK9kFM;IAOI,gCAAA;EL0kFV;EKjlFM;IAOI,iCAAA;EL6kFV;EKplFM;IAOI,gCAAA;ELglFV;AACF;AC3lFI;EIGI;IAOI,0BAAA;ELqlFV;EK5lFM;IAOI,gCAAA;ELwlFV;EK/lFM;IAOI,yBAAA;EL2lFV;EKlmFM;IAOI,wBAAA;EL8lFV;EKrmFM;IAOI,+BAAA;ELimFV;EKxmFM;IAOI,yBAAA;ELomFV;EK3mFM;IAOI,6BAAA;ELumFV;EK9mFM;IAOI,8BAAA;EL0mFV;EKjnFM;IAOI,wBAAA;EL6mFV;EKpnFM;IAOI,+BAAA;ELgnFV;EKvnFM;IAOI,wBAAA;ELmnFV;EK1nFM;IAOI,yBAAA;ELsnFV;EK7nFM;IAOI,8BAAA;ELynFV;EKhoFM;IAOI,iCAAA;EL4nFV;EKnoFM;IAOI,sCAAA;EL+nFV;EKtoFM;IAOI,yCAAA;ELkoFV;EKzoFM;IAOI,uBAAA;ELqoFV;EK5oFM;IAOI,uBAAA;ELwoFV;EK/oFM;IAOI,yBAAA;EL2oFV;EKlpFM;IAOI,yBAAA;EL8oFV;EKrpFM;IAOI,0BAAA;ELipFV;EKxpFM;IAOI,4BAAA;ELopFV;EK3pFM;IAOI,kCAAA;ELupFV;EK9pFM;IAOI,sCAAA;EL0pFV;EKjqFM;IAOI,oCAAA;EL6pFV;EKpqFM;IAOI,kCAAA;ELgqFV;EKvqFM;IAOI,yCAAA;ELmqFV;EK1qFM;IAOI,wCAAA;ELsqFV;EK7qFM;IAOI,wCAAA;ELyqFV;EKhrFM;IAOI,kCAAA;EL4qFV;EKnrFM;IAOI,gCAAA;EL+qFV;EKtrFM;IAOI,8BAAA;ELkrFV;EKzrFM;IAOI,gCAAA;ELqrFV;EK5rFM;IAOI,+BAAA;ELwrFV;EK/rFM;IAOI,oCAAA;EL2rFV;EKlsFM;IAOI,kCAAA;EL8rFV;EKrsFM;IAOI,gCAAA;ELisFV;EKxsFM;IAOI,uCAAA;ELosFV;EK3sFM;IAOI,sCAAA;ELusFV;EK9sFM;IAOI,iCAAA;EL0sFV;EKjtFM;IAOI,2BAAA;EL6sFV;EKptFM;IAOI,iCAAA;ELgtFV;EKvtFM;IAOI,+BAAA;ELmtFV;EK1tFM;IAOI,6BAAA;ELstFV;EK7tFM;IAOI,+BAAA;ELytFV;EKhuFM;IAOI,8BAAA;EL4tFV;EKnuFM;IAOI,oBAAA;EL+tFV;EKtuFM;IAOI,mBAAA;ELkuFV;EKzuFM;IAOI,mBAAA;ELquFV;EK5uFM;IAOI,mBAAA;ELwuFV;EK/uFM;IAOI,mBAAA;EL2uFV;EKlvFM;IAOI,mBAAA;EL8uFV;EKrvFM;IAOI,mBAAA;ELivFV;EKxvFM;IAOI,mBAAA;ELovFV;EK3vFM;IAOI,oBAAA;ELuvFV;EK9vFM;IAOI,4BAAA;EL0vFV;EKjwFM;IAOI,2BAAA;EL6vFV;EKpwFM;IAOI,0BAAA;ELgwFV;EKvwFM;IAOI,2BAAA;ELmwFV;EK1wFM;IAOI,0BAAA;ELswFV;EK7wFM;IAOI,uBAAA;ELywFV;EKhxFM;IAOI,0BAAA;IAAA,yBAAA;EL6wFV;EKpxFM;IAOI,kCAAA;IAAA,iCAAA;ELixFV;EKxxFM;IAOI,iCAAA;IAAA,gCAAA;ELqxFV;EK5xFM;IAOI,gCAAA;IAAA,+BAAA;ELyxFV;EKhyFM;IAOI,iCAAA;IAAA,gCAAA;EL6xFV;EKpyFM;IAOI,gCAAA;IAAA,+BAAA;ELiyFV;EKxyFM;IAOI,6BAAA;IAAA,4BAAA;ELqyFV;EK5yFM;IAOI,wBAAA;IAAA,2BAAA;ELyyFV;EKhzFM;IAOI,gCAAA;IAAA,mCAAA;EL6yFV;EKpzFM;IAOI,+BAAA;IAAA,kCAAA;ELizFV;EKxzFM;IAOI,8BAAA;IAAA,iCAAA;ELqzFV;EK5zFM;IAOI,+BAAA;IAAA,kCAAA;ELyzFV;EKh0FM;IAOI,8BAAA;IAAA,iCAAA;EL6zFV;EKp0FM;IAOI,2BAAA;IAAA,8BAAA;ELi0FV;EKx0FM;IAOI,wBAAA;ELo0FV;EK30FM;IAOI,gCAAA;ELu0FV;EK90FM;IAOI,+BAAA;EL00FV;EKj1FM;IAOI,8BAAA;EL60FV;EKp1FM;IAOI,+BAAA;ELg1FV;EKv1FM;IAOI,8BAAA;ELm1FV;EK11FM;IAOI,2BAAA;ELs1FV;EK71FM;IAOI,0BAAA;ELy1FV;EKh2FM;IAOI,kCAAA;EL41FV;EKn2FM;IAOI,iCAAA;EL+1FV;EKt2FM;IAOI,gCAAA;ELk2FV;EKz2FM;IAOI,iCAAA;ELq2FV;EK52FM;IAOI,gCAAA;ELw2FV;EK/2FM;IAOI,6BAAA;EL22FV;EKl3FM;IAOI,2BAAA;EL82FV;EKr3FM;IAOI,mCAAA;ELi3FV;EKx3FM;IAOI,kCAAA;ELo3FV;EK33FM;IAOI,iCAAA;ELu3FV;EK93FM;IAOI,kCAAA;EL03FV;EKj4FM;IAOI,iCAAA;EL63FV;EKp4FM;IAOI,8BAAA;ELg4FV;EKv4FM;IAOI,yBAAA;ELm4FV;EK14FM;IAOI,iCAAA;ELs4FV;EK74FM;IAOI,gCAAA;ELy4FV;EKh5FM;IAOI,+BAAA;EL44FV;EKn5FM;IAOI,gCAAA;EL+4FV;EKt5FM;IAOI,+BAAA;ELk5FV;EKz5FM;IAOI,4BAAA;ELq5FV;EK55FM;IAOI,qBAAA;ELw5FV;EK/5FM;IAOI,6BAAA;EL25FV;EKl6FM;IAOI,4BAAA;EL85FV;EKr6FM;IAOI,2BAAA;ELi6FV;EKx6FM;IAOI,4BAAA;ELo6FV;EK36FM;IAOI,2BAAA;ELu6FV;EK96FM;IAOI,2BAAA;IAAA,0BAAA;EL26FV;EKl7FM;IAOI,mCAAA;IAAA,kCAAA;EL+6FV;EKt7FM;IAOI,kCAAA;IAAA,iCAAA;ELm7FV;EK17FM;IAOI,iCAAA;IAAA,gCAAA;ELu7FV;EK97FM;IAOI,kCAAA;IAAA,iCAAA;EL27FV;EKl8FM;IAOI,iCAAA;IAAA,gCAAA;EL+7FV;EKt8FM;IAOI,yBAAA;IAAA,4BAAA;ELm8FV;EK18FM;IAOI,iCAAA;IAAA,oCAAA;ELu8FV;EK98FM;IAOI,gCAAA;IAAA,mCAAA;EL28FV;EKl9FM;IAOI,+BAAA;IAAA,kCAAA;EL+8FV;EKt9FM;IAOI,gCAAA;IAAA,mCAAA;ELm9FV;EK19FM;IAOI,+BAAA;IAAA,kCAAA;ELu9FV;EK99FM;IAOI,yBAAA;EL09FV;EKj+FM;IAOI,iCAAA;EL69FV;EKp+FM;IAOI,gCAAA;ELg+FV;EKv+FM;IAOI,+BAAA;ELm+FV;EK1+FM;IAOI,gCAAA;ELs+FV;EK7+FM;IAOI,+BAAA;ELy+FV;EKh/FM;IAOI,2BAAA;EL4+FV;EKn/FM;IAOI,mCAAA;EL++FV;EKt/FM;IAOI,kCAAA;ELk/FV;EKz/FM;IAOI,iCAAA;ELq/FV;EK5/FM;IAOI,kCAAA;ELw/FV;EK//FM;IAOI,iCAAA;EL2/FV;EKlgGM;IAOI,4BAAA;EL8/FV;EKrgGM;IAOI,oCAAA;ELigGV;EKxgGM;IAOI,mCAAA;ELogGV;EK3gGM;IAOI,kCAAA;ELugGV;EK9gGM;IAOI,mCAAA;EL0gGV;EKjhGM;IAOI,kCAAA;EL6gGV;EKphGM;IAOI,0BAAA;ELghGV;EKvhGM;IAOI,kCAAA;ELmhGV;EK1hGM;IAOI,iCAAA;ELshGV;EK7hGM;IAOI,gCAAA;ELyhGV;EKhiGM;IAOI,iCAAA;EL4hGV;EKniGM;IAOI,gCAAA;EL+hGV;AACF;AC1iGI;EIGI;IAOI,0BAAA;ELoiGV;EK3iGM;IAOI,gCAAA;ELuiGV;EK9iGM;IAOI,yBAAA;EL0iGV;EKjjGM;IAOI,wBAAA;EL6iGV;EKpjGM;IAOI,+BAAA;ELgjGV;EKvjGM;IAOI,yBAAA;ELmjGV;EK1jGM;IAOI,6BAAA;ELsjGV;EK7jGM;IAOI,8BAAA;ELyjGV;EKhkGM;IAOI,wBAAA;EL4jGV;EKnkGM;IAOI,+BAAA;EL+jGV;EKtkGM;IAOI,wBAAA;ELkkGV;EKzkGM;IAOI,yBAAA;ELqkGV;EK5kGM;IAOI,8BAAA;ELwkGV;EK/kGM;IAOI,iCAAA;EL2kGV;EKllGM;IAOI,sCAAA;EL8kGV;EKrlGM;IAOI,yCAAA;ELilGV;EKxlGM;IAOI,uBAAA;ELolGV;EK3lGM;IAOI,uBAAA;ELulGV;EK9lGM;IAOI,yBAAA;EL0lGV;EKjmGM;IAOI,yBAAA;EL6lGV;EKpmGM;IAOI,0BAAA;ELgmGV;EKvmGM;IAOI,4BAAA;ELmmGV;EK1mGM;IAOI,kCAAA;ELsmGV;EK7mGM;IAOI,sCAAA;ELymGV;EKhnGM;IAOI,oCAAA;EL4mGV;EKnnGM;IAOI,kCAAA;EL+mGV;EKtnGM;IAOI,yCAAA;ELknGV;EKznGM;IAOI,wCAAA;ELqnGV;EK5nGM;IAOI,wCAAA;ELwnGV;EK/nGM;IAOI,kCAAA;EL2nGV;EKloGM;IAOI,gCAAA;EL8nGV;EKroGM;IAOI,8BAAA;ELioGV;EKxoGM;IAOI,gCAAA;ELooGV;EK3oGM;IAOI,+BAAA;ELuoGV;EK9oGM;IAOI,oCAAA;EL0oGV;EKjpGM;IAOI,kCAAA;EL6oGV;EKppGM;IAOI,gCAAA;ELgpGV;EKvpGM;IAOI,uCAAA;ELmpGV;EK1pGM;IAOI,sCAAA;ELspGV;EK7pGM;IAOI,iCAAA;ELypGV;EKhqGM;IAOI,2BAAA;EL4pGV;EKnqGM;IAOI,iCAAA;EL+pGV;EKtqGM;IAOI,+BAAA;ELkqGV;EKzqGM;IAOI,6BAAA;ELqqGV;EK5qGM;IAOI,+BAAA;ELwqGV;EK/qGM;IAOI,8BAAA;EL2qGV;EKlrGM;IAOI,oBAAA;EL8qGV;EKrrGM;IAOI,mBAAA;ELirGV;EKxrGM;IAOI,mBAAA;ELorGV;EK3rGM;IAOI,mBAAA;ELurGV;EK9rGM;IAOI,mBAAA;EL0rGV;EKjsGM;IAOI,mBAAA;EL6rGV;EKpsGM;IAOI,mBAAA;ELgsGV;EKvsGM;IAOI,mBAAA;ELmsGV;EK1sGM;IAOI,oBAAA;ELssGV;EK7sGM;IAOI,4BAAA;ELysGV;EKhtGM;IAOI,2BAAA;EL4sGV;EKntGM;IAOI,0BAAA;EL+sGV;EKttGM;IAOI,2BAAA;ELktGV;EKztGM;IAOI,0BAAA;ELqtGV;EK5tGM;IAOI,uBAAA;ELwtGV;EK/tGM;IAOI,0BAAA;IAAA,yBAAA;EL4tGV;EKnuGM;IAOI,kCAAA;IAAA,iCAAA;ELguGV;EKvuGM;IAOI,iCAAA;IAAA,gCAAA;ELouGV;EK3uGM;IAOI,gCAAA;IAAA,+BAAA;ELwuGV;EK/uGM;IAOI,iCAAA;IAAA,gCAAA;EL4uGV;EKnvGM;IAOI,gCAAA;IAAA,+BAAA;ELgvGV;EKvvGM;IAOI,6BAAA;IAAA,4BAAA;ELovGV;EK3vGM;IAOI,wBAAA;IAAA,2BAAA;ELwvGV;EK/vGM;IAOI,gCAAA;IAAA,mCAAA;EL4vGV;EKnwGM;IAOI,+BAAA;IAAA,kCAAA;ELgwGV;EKvwGM;IAOI,8BAAA;IAAA,iCAAA;ELowGV;EK3wGM;IAOI,+BAAA;IAAA,kCAAA;ELwwGV;EK/wGM;IAOI,8BAAA;IAAA,iCAAA;EL4wGV;EKnxGM;IAOI,2BAAA;IAAA,8BAAA;ELgxGV;EKvxGM;IAOI,wBAAA;ELmxGV;EK1xGM;IAOI,gCAAA;ELsxGV;EK7xGM;IAOI,+BAAA;ELyxGV;EKhyGM;IAOI,8BAAA;EL4xGV;EKnyGM;IAOI,+BAAA;EL+xGV;EKtyGM;IAOI,8BAAA;ELkyGV;EKzyGM;IAOI,2BAAA;ELqyGV;EK5yGM;IAOI,0BAAA;ELwyGV;EK/yGM;IAOI,kCAAA;EL2yGV;EKlzGM;IAOI,iCAAA;EL8yGV;EKrzGM;IAOI,gCAAA;ELizGV;EKxzGM;IAOI,iCAAA;ELozGV;EK3zGM;IAOI,gCAAA;ELuzGV;EK9zGM;IAOI,6BAAA;EL0zGV;EKj0GM;IAOI,2BAAA;EL6zGV;EKp0GM;IAOI,mCAAA;ELg0GV;EKv0GM;IAOI,kCAAA;ELm0GV;EK10GM;IAOI,iCAAA;ELs0GV;EK70GM;IAOI,kCAAA;ELy0GV;EKh1GM;IAOI,iCAAA;EL40GV;EKn1GM;IAOI,8BAAA;EL+0GV;EKt1GM;IAOI,yBAAA;ELk1GV;EKz1GM;IAOI,iCAAA;ELq1GV;EK51GM;IAOI,gCAAA;ELw1GV;EK/1GM;IAOI,+BAAA;EL21GV;EKl2GM;IAOI,gCAAA;EL81GV;EKr2GM;IAOI,+BAAA;ELi2GV;EKx2GM;IAOI,4BAAA;ELo2GV;EK32GM;IAOI,qBAAA;ELu2GV;EK92GM;IAOI,6BAAA;EL02GV;EKj3GM;IAOI,4BAAA;EL62GV;EKp3GM;IAOI,2BAAA;ELg3GV;EKv3GM;IAOI,4BAAA;ELm3GV;EK13GM;IAOI,2BAAA;ELs3GV;EK73GM;IAOI,2BAAA;IAAA,0BAAA;EL03GV;EKj4GM;IAOI,mCAAA;IAAA,kCAAA;EL83GV;EKr4GM;IAOI,kCAAA;IAAA,iCAAA;ELk4GV;EKz4GM;IAOI,iCAAA;IAAA,gCAAA;ELs4GV;EK74GM;IAOI,kCAAA;IAAA,iCAAA;EL04GV;EKj5GM;IAOI,iCAAA;IAAA,gCAAA;EL84GV;EKr5GM;IAOI,yBAAA;IAAA,4BAAA;ELk5GV;EKz5GM;IAOI,iCAAA;IAAA,oCAAA;ELs5GV;EK75GM;IAOI,gCAAA;IAAA,mCAAA;EL05GV;EKj6GM;IAOI,+BAAA;IAAA,kCAAA;EL85GV;EKr6GM;IAOI,gCAAA;IAAA,mCAAA;ELk6GV;EKz6GM;IAOI,+BAAA;IAAA,kCAAA;ELs6GV;EK76GM;IAOI,yBAAA;ELy6GV;EKh7GM;IAOI,iCAAA;EL46GV;EKn7GM;IAOI,gCAAA;EL+6GV;EKt7GM;IAOI,+BAAA;ELk7GV;EKz7GM;IAOI,gCAAA;ELq7GV;EK57GM;IAOI,+BAAA;ELw7GV;EK/7GM;IAOI,2BAAA;EL27GV;EKl8GM;IAOI,mCAAA;EL87GV;EKr8GM;IAOI,kCAAA;ELi8GV;EKx8GM;IAOI,iCAAA;ELo8GV;EK38GM;IAOI,kCAAA;ELu8GV;EK98GM;IAOI,iCAAA;EL08GV;EKj9GM;IAOI,4BAAA;EL68GV;EKp9GM;IAOI,oCAAA;ELg9GV;EKv9GM;IAOI,mCAAA;ELm9GV;EK19GM;IAOI,kCAAA;ELs9GV;EK79GM;IAOI,mCAAA;ELy9GV;EKh+GM;IAOI,kCAAA;EL49GV;EKn+GM;IAOI,0BAAA;EL+9GV;EKt+GM;IAOI,kCAAA;ELk+GV;EKz+GM;IAOI,iCAAA;ELq+GV;EK5+GM;IAOI,gCAAA;ELw+GV;EK/+GM;IAOI,iCAAA;EL2+GV;EKl/GM;IAOI,gCAAA;EL8+GV;AACF;ACz/GI;EIGI;IAOI,0BAAA;ELm/GV;EK1/GM;IAOI,gCAAA;ELs/GV;EK7/GM;IAOI,yBAAA;ELy/GV;EKhgHM;IAOI,wBAAA;EL4/GV;EKngHM;IAOI,+BAAA;EL+/GV;EKtgHM;IAOI,yBAAA;ELkgHV;EKzgHM;IAOI,6BAAA;ELqgHV;EK5gHM;IAOI,8BAAA;ELwgHV;EK/gHM;IAOI,wBAAA;EL2gHV;EKlhHM;IAOI,+BAAA;EL8gHV;EKrhHM;IAOI,wBAAA;ELihHV;EKxhHM;IAOI,yBAAA;ELohHV;EK3hHM;IAOI,8BAAA;ELuhHV;EK9hHM;IAOI,iCAAA;EL0hHV;EKjiHM;IAOI,sCAAA;EL6hHV;EKpiHM;IAOI,yCAAA;ELgiHV;EKviHM;IAOI,uBAAA;ELmiHV;EK1iHM;IAOI,uBAAA;ELsiHV;EK7iHM;IAOI,yBAAA;ELyiHV;EKhjHM;IAOI,yBAAA;EL4iHV;EKnjHM;IAOI,0BAAA;EL+iHV;EKtjHM;IAOI,4BAAA;ELkjHV;EKzjHM;IAOI,kCAAA;ELqjHV;EK5jHM;IAOI,sCAAA;ELwjHV;EK/jHM;IAOI,oCAAA;EL2jHV;EKlkHM;IAOI,kCAAA;EL8jHV;EKrkHM;IAOI,yCAAA;ELikHV;EKxkHM;IAOI,wCAAA;ELokHV;EK3kHM;IAOI,wCAAA;ELukHV;EK9kHM;IAOI,kCAAA;EL0kHV;EKjlHM;IAOI,gCAAA;EL6kHV;EKplHM;IAOI,8BAAA;ELglHV;EKvlHM;IAOI,gCAAA;ELmlHV;EK1lHM;IAOI,+BAAA;ELslHV;EK7lHM;IAOI,oCAAA;ELylHV;EKhmHM;IAOI,kCAAA;EL4lHV;EKnmHM;IAOI,gCAAA;EL+lHV;EKtmHM;IAOI,uCAAA;ELkmHV;EKzmHM;IAOI,sCAAA;ELqmHV;EK5mHM;IAOI,iCAAA;ELwmHV;EK/mHM;IAOI,2BAAA;EL2mHV;EKlnHM;IAOI,iCAAA;EL8mHV;EKrnHM;IAOI,+BAAA;ELinHV;EKxnHM;IAOI,6BAAA;ELonHV;EK3nHM;IAOI,+BAAA;ELunHV;EK9nHM;IAOI,8BAAA;EL0nHV;EKjoHM;IAOI,oBAAA;EL6nHV;EKpoHM;IAOI,mBAAA;ELgoHV;EKvoHM;IAOI,mBAAA;ELmoHV;EK1oHM;IAOI,mBAAA;ELsoHV;EK7oHM;IAOI,mBAAA;ELyoHV;EKhpHM;IAOI,mBAAA;EL4oHV;EKnpHM;IAOI,mBAAA;EL+oHV;EKtpHM;IAOI,mBAAA;ELkpHV;EKzpHM;IAOI,oBAAA;ELqpHV;EK5pHM;IAOI,4BAAA;ELwpHV;EK/pHM;IAOI,2BAAA;EL2pHV;EKlqHM;IAOI,0BAAA;EL8pHV;EKrqHM;IAOI,2BAAA;ELiqHV;EKxqHM;IAOI,0BAAA;ELoqHV;EK3qHM;IAOI,uBAAA;ELuqHV;EK9qHM;IAOI,0BAAA;IAAA,yBAAA;EL2qHV;EKlrHM;IAOI,kCAAA;IAAA,iCAAA;EL+qHV;EKtrHM;IAOI,iCAAA;IAAA,gCAAA;ELmrHV;EK1rHM;IAOI,gCAAA;IAAA,+BAAA;ELurHV;EK9rHM;IAOI,iCAAA;IAAA,gCAAA;EL2rHV;EKlsHM;IAOI,gCAAA;IAAA,+BAAA;EL+rHV;EKtsHM;IAOI,6BAAA;IAAA,4BAAA;ELmsHV;EK1sHM;IAOI,wBAAA;IAAA,2BAAA;ELusHV;EK9sHM;IAOI,gCAAA;IAAA,mCAAA;EL2sHV;EKltHM;IAOI,+BAAA;IAAA,kCAAA;EL+sHV;EKttHM;IAOI,8BAAA;IAAA,iCAAA;ELmtHV;EK1tHM;IAOI,+BAAA;IAAA,kCAAA;ELutHV;EK9tHM;IAOI,8BAAA;IAAA,iCAAA;EL2tHV;EKluHM;IAOI,2BAAA;IAAA,8BAAA;EL+tHV;EKtuHM;IAOI,wBAAA;ELkuHV;EKzuHM;IAOI,gCAAA;ELquHV;EK5uHM;IAOI,+BAAA;ELwuHV;EK/uHM;IAOI,8BAAA;EL2uHV;EKlvHM;IAOI,+BAAA;EL8uHV;EKrvHM;IAOI,8BAAA;ELivHV;EKxvHM;IAOI,2BAAA;ELovHV;EK3vHM;IAOI,0BAAA;ELuvHV;EK9vHM;IAOI,kCAAA;EL0vHV;EKjwHM;IAOI,iCAAA;EL6vHV;EKpwHM;IAOI,gCAAA;ELgwHV;EKvwHM;IAOI,iCAAA;ELmwHV;EK1wHM;IAOI,gCAAA;ELswHV;EK7wHM;IAOI,6BAAA;ELywHV;EKhxHM;IAOI,2BAAA;EL4wHV;EKnxHM;IAOI,mCAAA;EL+wHV;EKtxHM;IAOI,kCAAA;ELkxHV;EKzxHM;IAOI,iCAAA;ELqxHV;EK5xHM;IAOI,kCAAA;ELwxHV;EK/xHM;IAOI,iCAAA;EL2xHV;EKlyHM;IAOI,8BAAA;EL8xHV;EKryHM;IAOI,yBAAA;ELiyHV;EKxyHM;IAOI,iCAAA;ELoyHV;EK3yHM;IAOI,gCAAA;ELuyHV;EK9yHM;IAOI,+BAAA;EL0yHV;EKjzHM;IAOI,gCAAA;EL6yHV;EKpzHM;IAOI,+BAAA;ELgzHV;EKvzHM;IAOI,4BAAA;ELmzHV;EK1zHM;IAOI,qBAAA;ELszHV;EK7zHM;IAOI,6BAAA;ELyzHV;EKh0HM;IAOI,4BAAA;EL4zHV;EKn0HM;IAOI,2BAAA;EL+zHV;EKt0HM;IAOI,4BAAA;ELk0HV;EKz0HM;IAOI,2BAAA;ELq0HV;EK50HM;IAOI,2BAAA;IAAA,0BAAA;ELy0HV;EKh1HM;IAOI,mCAAA;IAAA,kCAAA;EL60HV;EKp1HM;IAOI,kCAAA;IAAA,iCAAA;ELi1HV;EKx1HM;IAOI,iCAAA;IAAA,gCAAA;ELq1HV;EK51HM;IAOI,kCAAA;IAAA,iCAAA;ELy1HV;EKh2HM;IAOI,iCAAA;IAAA,gCAAA;EL61HV;EKp2HM;IAOI,yBAAA;IAAA,4BAAA;ELi2HV;EKx2HM;IAOI,iCAAA;IAAA,oCAAA;ELq2HV;EK52HM;IAOI,gCAAA;IAAA,mCAAA;ELy2HV;EKh3HM;IAOI,+BAAA;IAAA,kCAAA;EL62HV;EKp3HM;IAOI,gCAAA;IAAA,mCAAA;ELi3HV;EKx3HM;IAOI,+BAAA;IAAA,kCAAA;ELq3HV;EK53HM;IAOI,yBAAA;ELw3HV;EK/3HM;IAOI,iCAAA;EL23HV;EKl4HM;IAOI,gCAAA;EL83HV;EKr4HM;IAOI,+BAAA;ELi4HV;EKx4HM;IAOI,gCAAA;ELo4HV;EK34HM;IAOI,+BAAA;ELu4HV;EK94HM;IAOI,2BAAA;EL04HV;EKj5HM;IAOI,mCAAA;EL64HV;EKp5HM;IAOI,kCAAA;ELg5HV;EKv5HM;IAOI,iCAAA;ELm5HV;EK15HM;IAOI,kCAAA;ELs5HV;EK75HM;IAOI,iCAAA;ELy5HV;EKh6HM;IAOI,4BAAA;EL45HV;EKn6HM;IAOI,oCAAA;EL+5HV;EKt6HM;IAOI,mCAAA;ELk6HV;EKz6HM;IAOI,kCAAA;ELq6HV;EK56HM;IAOI,mCAAA;ELw6HV;EK/6HM;IAOI,kCAAA;EL26HV;EKl7HM;IAOI,0BAAA;EL86HV;EKr7HM;IAOI,kCAAA;ELi7HV;EKx7HM;IAOI,iCAAA;ELo7HV;EK37HM;IAOI,gCAAA;ELu7HV;EK97HM;IAOI,iCAAA;EL07HV;EKj8HM;IAOI,gCAAA;EL67HV;AACF;AMj+HA;ED4BQ;IAOI,0BAAA;ELk8HV;EKz8HM;IAOI,gCAAA;ELq8HV;EK58HM;IAOI,yBAAA;ELw8HV;EK/8HM;IAOI,wBAAA;EL28HV;EKl9HM;IAOI,+BAAA;EL88HV;EKr9HM;IAOI,yBAAA;ELi9HV;EKx9HM;IAOI,6BAAA;ELo9HV;EK39HM;IAOI,8BAAA;ELu9HV;EK99HM;IAOI,wBAAA;EL09HV;EKj+HM;IAOI,+BAAA;EL69HV;EKp+HM;IAOI,wBAAA;ELg+HV;AACF", "file": "boosted-grid.css", "sourcesContent": ["@mixin bsBanner($file) {\n  /*!\n   * Boosted #{$file} v5.3.7 (https://boosted.orange.com/)\n   * Copyright 2014-2025 The Boosted Authors\n   * Copyright 2014-2025 Orange SA\n   * Licensed under MIT (https://github.com/Orange-OpenSource/Orange-Boosted-Bootstrap/blob/main/LICENSE)\n   * This a fork of Bootstrap: Initial license below\n   * Bootstrap #{$file} v5.3.7 (https://getbootstrap.com/)\n   * Copyright 2011-2025 The Bootstrap Authors\n   * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n   */\n}\n", "// Container widths\n//\n// Set the container width, and override it for fixed navbars in media queries.\n\n@if $enable-container-classes {\n  // Single container class with breakpoint max-widths\n  .container,\n  // 100% wide container at all breakpoints\n  .container-fluid {\n    @include make-container();\n  }\n\n  // Boosted mod: `.container-fluid` is not full width in Boosted\n  .container-fluid {\n    @include make-container-fluid-margin();\n  }\n  // End mod\n\n  // Responsive containers that are 100% wide until a breakpoint\n  @each $breakpoint, $container-max-width in $container-max-widths {\n    .container-#{$breakpoint} {\n      @extend .container-fluid;\n    }\n\n    @include media-breakpoint-up($breakpoint, $grid-breakpoints) {\n      %responsive-container-#{$breakpoint} {\n        max-width: $container-max-width;\n      }\n\n      // Extend each breakpoint which is smaller or equal to the current breakpoint\n      $extend-breakpoint: true;\n\n      @each $name, $width in $grid-breakpoints {\n        @if ($extend-breakpoint) {\n          .container#{breakpoint-infix($name, $grid-breakpoints)} {\n            @extend %responsive-container-#{$breakpoint};\n          }\n\n          // Once the current breakpoint is reached, stop extending\n          @if ($breakpoint == $name) {\n            $extend-breakpoint: false;\n          }\n        }\n      }\n    }\n  }\n}\n", "// Container mixins\n\n// scss-docs-start container-mixins\n@mixin make-container($gutter: $container-padding-x) {\n  --#{$prefix}gutter-x: #{$gutter};\n  --#{$prefix}gutter-y: 0;\n  width: 100%;\n  padding-right: calc(var(--#{$prefix}gutter-x) * .25); // stylelint-disable-line function-disallowed-list\n  padding-left: calc(var(--#{$prefix}gutter-x) * .25); // stylelint-disable-line function-disallowed-list\n  margin-right: auto;\n  margin-left: auto;\n\n  // Boosted mod: gutter depends on breakpoint\n  // @note Needs both interpolation and parenthesis to prevent stylelint-scss/dimension-no-non-numeric-values to fail\n  @include media-breakpoint-up($grid-gutter-breakpoint) {\n    --#{$prefix}gutter-x: #{($gutter * 2)};\n  }\n  // End mod\n}\n\n// Boosted mod: fluid containers aren't full width → include margins\n@mixin make-container-fluid-margin() {\n  @each $breakpoint, $container-margin in $container-fluid-margin {\n    @include media-breakpoint-up($breakpoint) {\n      max-width: subtract(100vw, $container-margin * 2);\n    }\n  }\n}\n// End mod\n// scss-docs-end container-mixins\n", "/*!\n * Boosted Grid v5.3.7 (https://boosted.orange.com/)\n * Copyright 2014-2025 The Boosted Authors\n * Copyright 2014-2025 Orange SA\n * Licensed under MIT (https://github.com/Orange-OpenSource/Orange-Boosted-Bootstrap/blob/main/LICENSE)\n * This a fork of Bootstrap: Initial license below\n * Bootstrap Grid v5.3.7 (https://getbootstrap.com/)\n * Copyright 2011-2025 The Bootstrap Authors\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n */\n.container,\n.container-fluid,\n.container-xxl,\n.container-xl,\n.container-lg,\n.container-md,\n.container-sm,\n.container-xs {\n  --bs-gutter-x: 1.25rem;\n  --bs-gutter-y: 0;\n  width: 100%;\n  padding-right: calc(var(--bs-gutter-x) * 0.25);\n  padding-left: calc(var(--bs-gutter-x) * 0.25);\n  margin-right: auto;\n  margin-left: auto;\n}\n@media (min-width: 768px) {\n  .container,\n  .container-fluid,\n  .container-xxl,\n  .container-xl,\n  .container-lg,\n  .container-md,\n  .container-sm,\n  .container-xs {\n    --bs-gutter-x: 2.5rem;\n  }\n}\n\n.container-fluid, .container-xxl, .container-xl, .container-lg, .container-md, .container-sm, .container-xs {\n  max-width: calc(100vw - 8px);\n}\n@media (min-width: 480px) {\n  .container-fluid, .container-xxl, .container-xl, .container-lg, .container-md, .container-sm, .container-xs {\n    max-width: calc(100vw - 12px);\n  }\n}\n@media (min-width: 768px) {\n  .container-fluid, .container-xxl, .container-xl, .container-lg, .container-md, .container-sm, .container-xs {\n    max-width: calc(100vw - 24px);\n  }\n}\n@media (min-width: 1024px) {\n  .container-fluid, .container-xxl, .container-xl, .container-lg, .container-md, .container-sm, .container-xs {\n    max-width: calc(100vw - 64px);\n  }\n}\n@media (min-width: 1280px) {\n  .container-fluid, .container-xxl, .container-xl, .container-lg, .container-md, .container-sm, .container-xs {\n    max-width: calc(100vw - 80px);\n  }\n}\n@media (min-width: 1440px) {\n  .container-fluid, .container-xxl, .container-xl, .container-lg, .container-md, .container-sm, .container-xs {\n    max-width: calc(100vw - 120px);\n  }\n}\n\n.container {\n  max-width: 312px;\n}\n\n@media (min-width: 480px) {\n  .container-sm, .container {\n    max-width: 468px;\n  }\n}\n@media (min-width: 768px) {\n  .container-md, .container-sm, .container {\n    max-width: 744px;\n  }\n}\n@media (min-width: 1024px) {\n  .container-lg, .container-md, .container-sm, .container {\n    max-width: 960px;\n  }\n}\n@media (min-width: 1280px) {\n  .container-xl, .container-lg, .container-md, .container-sm, .container {\n    max-width: 1200px;\n  }\n}\n@media (min-width: 1440px) {\n  .container-xxl, .container-xl, .container-lg, .container-md, .container-sm, .container {\n    max-width: 1320px;\n  }\n}\n:root {\n  --bs-breakpoint-xs: 0;\n  --bs-breakpoint-sm: 480px;\n  --bs-breakpoint-md: 768px;\n  --bs-breakpoint-lg: 1024px;\n  --bs-breakpoint-xl: 1280px;\n  --bs-breakpoint-xxl: 1440px;\n}\n\n.row {\n  --bs-gutter-x: 0.625rem;\n  --bs-gutter-y: 0;\n  display: flex;\n  flex-wrap: wrap;\n  margin-top: calc(-1 * var(--bs-gutter-y));\n  margin-right: calc(-0.5 * var(--bs-gutter-x));\n  margin-left: calc(-0.5 * var(--bs-gutter-x));\n}\n@media (min-width: 768px) {\n  .row {\n    --bs-gutter-x: 1.25rem;\n  }\n}\n.row > * {\n  box-sizing: border-box;\n  flex-shrink: 0;\n  width: 100%;\n  max-width: 100%;\n  padding-right: calc(var(--bs-gutter-x) * 0.5);\n  padding-left: calc(var(--bs-gutter-x) * 0.5);\n  margin-top: var(--bs-gutter-y);\n}\n\n.col {\n  flex: 1 0 0;\n}\n\n.row-cols-auto > * {\n  flex: 0 0 auto;\n  width: auto;\n}\n\n.row-cols-1 > * {\n  flex: 0 0 auto;\n  width: 100%;\n}\n\n.row-cols-2 > * {\n  flex: 0 0 auto;\n  width: 50%;\n}\n\n.row-cols-3 > * {\n  flex: 0 0 auto;\n  width: 33.33333333%;\n}\n\n.row-cols-4 > * {\n  flex: 0 0 auto;\n  width: 25%;\n}\n\n.row-cols-5 > * {\n  flex: 0 0 auto;\n  width: 20%;\n}\n\n.row-cols-6 > * {\n  flex: 0 0 auto;\n  width: 16.66666667%;\n}\n\n.col-auto {\n  flex: 0 0 auto;\n  width: auto;\n}\n\n.col-1 {\n  flex: 0 0 auto;\n  width: 8.33333333%;\n}\n\n.col-2 {\n  flex: 0 0 auto;\n  width: 16.66666667%;\n}\n\n.col-3 {\n  flex: 0 0 auto;\n  width: 25%;\n}\n\n.col-4 {\n  flex: 0 0 auto;\n  width: 33.33333333%;\n}\n\n.col-5 {\n  flex: 0 0 auto;\n  width: 41.66666667%;\n}\n\n.col-6 {\n  flex: 0 0 auto;\n  width: 50%;\n}\n\n.col-7 {\n  flex: 0 0 auto;\n  width: 58.33333333%;\n}\n\n.col-8 {\n  flex: 0 0 auto;\n  width: 66.66666667%;\n}\n\n.col-9 {\n  flex: 0 0 auto;\n  width: 75%;\n}\n\n.col-10 {\n  flex: 0 0 auto;\n  width: 83.33333333%;\n}\n\n.col-11 {\n  flex: 0 0 auto;\n  width: 91.66666667%;\n}\n\n.col-12 {\n  flex: 0 0 auto;\n  width: 100%;\n}\n\n.offset-1 {\n  margin-left: 8.33333333%;\n}\n\n.offset-2 {\n  margin-left: 16.66666667%;\n}\n\n.offset-3 {\n  margin-left: 25%;\n}\n\n.offset-4 {\n  margin-left: 33.33333333%;\n}\n\n.offset-5 {\n  margin-left: 41.66666667%;\n}\n\n.offset-6 {\n  margin-left: 50%;\n}\n\n.offset-7 {\n  margin-left: 58.33333333%;\n}\n\n.offset-8 {\n  margin-left: 66.66666667%;\n}\n\n.offset-9 {\n  margin-left: 75%;\n}\n\n.offset-10 {\n  margin-left: 83.33333333%;\n}\n\n.offset-11 {\n  margin-left: 91.66666667%;\n}\n\n.g-0,\n.gx-0 {\n  --bs-gutter-x: 0;\n}\n\n.g-0,\n.gy-0 {\n  --bs-gutter-y: 0;\n}\n\n.g-1,\n.gx-1 {\n  --bs-gutter-x: 0.3125rem;\n}\n\n.g-1,\n.gy-1 {\n  --bs-gutter-y: 0.3125rem;\n}\n\n.g-2,\n.gx-2 {\n  --bs-gutter-x: 0.625rem;\n}\n\n.g-2,\n.gy-2 {\n  --bs-gutter-y: 0.625rem;\n}\n\n.g-3,\n.gx-3 {\n  --bs-gutter-x: 1.25rem;\n}\n\n.g-3,\n.gy-3 {\n  --bs-gutter-y: 1.25rem;\n}\n\n.g-4,\n.gx-4 {\n  --bs-gutter-x: 1.875rem;\n}\n\n.g-4,\n.gy-4 {\n  --bs-gutter-y: 1.875rem;\n}\n\n.g-5,\n.gx-5 {\n  --bs-gutter-x: 3.75rem;\n}\n\n.g-5,\n.gy-5 {\n  --bs-gutter-y: 3.75rem;\n}\n\n@media (min-width: 480px) {\n  .col-sm {\n    flex: 1 0 0;\n  }\n  .row-cols-sm-auto > * {\n    flex: 0 0 auto;\n    width: auto;\n  }\n  .row-cols-sm-1 > * {\n    flex: 0 0 auto;\n    width: 100%;\n  }\n  .row-cols-sm-2 > * {\n    flex: 0 0 auto;\n    width: 50%;\n  }\n  .row-cols-sm-3 > * {\n    flex: 0 0 auto;\n    width: 33.33333333%;\n  }\n  .row-cols-sm-4 > * {\n    flex: 0 0 auto;\n    width: 25%;\n  }\n  .row-cols-sm-5 > * {\n    flex: 0 0 auto;\n    width: 20%;\n  }\n  .row-cols-sm-6 > * {\n    flex: 0 0 auto;\n    width: 16.66666667%;\n  }\n  .col-sm-auto {\n    flex: 0 0 auto;\n    width: auto;\n  }\n  .col-sm-1 {\n    flex: 0 0 auto;\n    width: 8.33333333%;\n  }\n  .col-sm-2 {\n    flex: 0 0 auto;\n    width: 16.66666667%;\n  }\n  .col-sm-3 {\n    flex: 0 0 auto;\n    width: 25%;\n  }\n  .col-sm-4 {\n    flex: 0 0 auto;\n    width: 33.33333333%;\n  }\n  .col-sm-5 {\n    flex: 0 0 auto;\n    width: 41.66666667%;\n  }\n  .col-sm-6 {\n    flex: 0 0 auto;\n    width: 50%;\n  }\n  .col-sm-7 {\n    flex: 0 0 auto;\n    width: 58.33333333%;\n  }\n  .col-sm-8 {\n    flex: 0 0 auto;\n    width: 66.66666667%;\n  }\n  .col-sm-9 {\n    flex: 0 0 auto;\n    width: 75%;\n  }\n  .col-sm-10 {\n    flex: 0 0 auto;\n    width: 83.33333333%;\n  }\n  .col-sm-11 {\n    flex: 0 0 auto;\n    width: 91.66666667%;\n  }\n  .col-sm-12 {\n    flex: 0 0 auto;\n    width: 100%;\n  }\n  .offset-sm-0 {\n    margin-left: 0;\n  }\n  .offset-sm-1 {\n    margin-left: 8.33333333%;\n  }\n  .offset-sm-2 {\n    margin-left: 16.66666667%;\n  }\n  .offset-sm-3 {\n    margin-left: 25%;\n  }\n  .offset-sm-4 {\n    margin-left: 33.33333333%;\n  }\n  .offset-sm-5 {\n    margin-left: 41.66666667%;\n  }\n  .offset-sm-6 {\n    margin-left: 50%;\n  }\n  .offset-sm-7 {\n    margin-left: 58.33333333%;\n  }\n  .offset-sm-8 {\n    margin-left: 66.66666667%;\n  }\n  .offset-sm-9 {\n    margin-left: 75%;\n  }\n  .offset-sm-10 {\n    margin-left: 83.33333333%;\n  }\n  .offset-sm-11 {\n    margin-left: 91.66666667%;\n  }\n  .g-sm-0,\n  .gx-sm-0 {\n    --bs-gutter-x: 0;\n  }\n  .g-sm-0,\n  .gy-sm-0 {\n    --bs-gutter-y: 0;\n  }\n  .g-sm-1,\n  .gx-sm-1 {\n    --bs-gutter-x: 0.3125rem;\n  }\n  .g-sm-1,\n  .gy-sm-1 {\n    --bs-gutter-y: 0.3125rem;\n  }\n  .g-sm-2,\n  .gx-sm-2 {\n    --bs-gutter-x: 0.625rem;\n  }\n  .g-sm-2,\n  .gy-sm-2 {\n    --bs-gutter-y: 0.625rem;\n  }\n  .g-sm-3,\n  .gx-sm-3 {\n    --bs-gutter-x: 1.25rem;\n  }\n  .g-sm-3,\n  .gy-sm-3 {\n    --bs-gutter-y: 1.25rem;\n  }\n  .g-sm-4,\n  .gx-sm-4 {\n    --bs-gutter-x: 1.875rem;\n  }\n  .g-sm-4,\n  .gy-sm-4 {\n    --bs-gutter-y: 1.875rem;\n  }\n  .g-sm-5,\n  .gx-sm-5 {\n    --bs-gutter-x: 3.75rem;\n  }\n  .g-sm-5,\n  .gy-sm-5 {\n    --bs-gutter-y: 3.75rem;\n  }\n}\n@media (min-width: 768px) {\n  .col-md {\n    flex: 1 0 0;\n  }\n  .row-cols-md-auto > * {\n    flex: 0 0 auto;\n    width: auto;\n  }\n  .row-cols-md-1 > * {\n    flex: 0 0 auto;\n    width: 100%;\n  }\n  .row-cols-md-2 > * {\n    flex: 0 0 auto;\n    width: 50%;\n  }\n  .row-cols-md-3 > * {\n    flex: 0 0 auto;\n    width: 33.33333333%;\n  }\n  .row-cols-md-4 > * {\n    flex: 0 0 auto;\n    width: 25%;\n  }\n  .row-cols-md-5 > * {\n    flex: 0 0 auto;\n    width: 20%;\n  }\n  .row-cols-md-6 > * {\n    flex: 0 0 auto;\n    width: 16.66666667%;\n  }\n  .col-md-auto {\n    flex: 0 0 auto;\n    width: auto;\n  }\n  .col-md-1 {\n    flex: 0 0 auto;\n    width: 8.33333333%;\n  }\n  .col-md-2 {\n    flex: 0 0 auto;\n    width: 16.66666667%;\n  }\n  .col-md-3 {\n    flex: 0 0 auto;\n    width: 25%;\n  }\n  .col-md-4 {\n    flex: 0 0 auto;\n    width: 33.33333333%;\n  }\n  .col-md-5 {\n    flex: 0 0 auto;\n    width: 41.66666667%;\n  }\n  .col-md-6 {\n    flex: 0 0 auto;\n    width: 50%;\n  }\n  .col-md-7 {\n    flex: 0 0 auto;\n    width: 58.33333333%;\n  }\n  .col-md-8 {\n    flex: 0 0 auto;\n    width: 66.66666667%;\n  }\n  .col-md-9 {\n    flex: 0 0 auto;\n    width: 75%;\n  }\n  .col-md-10 {\n    flex: 0 0 auto;\n    width: 83.33333333%;\n  }\n  .col-md-11 {\n    flex: 0 0 auto;\n    width: 91.66666667%;\n  }\n  .col-md-12 {\n    flex: 0 0 auto;\n    width: 100%;\n  }\n  .offset-md-0 {\n    margin-left: 0;\n  }\n  .offset-md-1 {\n    margin-left: 8.33333333%;\n  }\n  .offset-md-2 {\n    margin-left: 16.66666667%;\n  }\n  .offset-md-3 {\n    margin-left: 25%;\n  }\n  .offset-md-4 {\n    margin-left: 33.33333333%;\n  }\n  .offset-md-5 {\n    margin-left: 41.66666667%;\n  }\n  .offset-md-6 {\n    margin-left: 50%;\n  }\n  .offset-md-7 {\n    margin-left: 58.33333333%;\n  }\n  .offset-md-8 {\n    margin-left: 66.66666667%;\n  }\n  .offset-md-9 {\n    margin-left: 75%;\n  }\n  .offset-md-10 {\n    margin-left: 83.33333333%;\n  }\n  .offset-md-11 {\n    margin-left: 91.66666667%;\n  }\n  .g-md-0,\n  .gx-md-0 {\n    --bs-gutter-x: 0;\n  }\n  .g-md-0,\n  .gy-md-0 {\n    --bs-gutter-y: 0;\n  }\n  .g-md-1,\n  .gx-md-1 {\n    --bs-gutter-x: 0.3125rem;\n  }\n  .g-md-1,\n  .gy-md-1 {\n    --bs-gutter-y: 0.3125rem;\n  }\n  .g-md-2,\n  .gx-md-2 {\n    --bs-gutter-x: 0.625rem;\n  }\n  .g-md-2,\n  .gy-md-2 {\n    --bs-gutter-y: 0.625rem;\n  }\n  .g-md-3,\n  .gx-md-3 {\n    --bs-gutter-x: 1.25rem;\n  }\n  .g-md-3,\n  .gy-md-3 {\n    --bs-gutter-y: 1.25rem;\n  }\n  .g-md-4,\n  .gx-md-4 {\n    --bs-gutter-x: 1.875rem;\n  }\n  .g-md-4,\n  .gy-md-4 {\n    --bs-gutter-y: 1.875rem;\n  }\n  .g-md-5,\n  .gx-md-5 {\n    --bs-gutter-x: 3.75rem;\n  }\n  .g-md-5,\n  .gy-md-5 {\n    --bs-gutter-y: 3.75rem;\n  }\n}\n@media (min-width: 1024px) {\n  .col-lg {\n    flex: 1 0 0;\n  }\n  .row-cols-lg-auto > * {\n    flex: 0 0 auto;\n    width: auto;\n  }\n  .row-cols-lg-1 > * {\n    flex: 0 0 auto;\n    width: 100%;\n  }\n  .row-cols-lg-2 > * {\n    flex: 0 0 auto;\n    width: 50%;\n  }\n  .row-cols-lg-3 > * {\n    flex: 0 0 auto;\n    width: 33.33333333%;\n  }\n  .row-cols-lg-4 > * {\n    flex: 0 0 auto;\n    width: 25%;\n  }\n  .row-cols-lg-5 > * {\n    flex: 0 0 auto;\n    width: 20%;\n  }\n  .row-cols-lg-6 > * {\n    flex: 0 0 auto;\n    width: 16.66666667%;\n  }\n  .col-lg-auto {\n    flex: 0 0 auto;\n    width: auto;\n  }\n  .col-lg-1 {\n    flex: 0 0 auto;\n    width: 8.33333333%;\n  }\n  .col-lg-2 {\n    flex: 0 0 auto;\n    width: 16.66666667%;\n  }\n  .col-lg-3 {\n    flex: 0 0 auto;\n    width: 25%;\n  }\n  .col-lg-4 {\n    flex: 0 0 auto;\n    width: 33.33333333%;\n  }\n  .col-lg-5 {\n    flex: 0 0 auto;\n    width: 41.66666667%;\n  }\n  .col-lg-6 {\n    flex: 0 0 auto;\n    width: 50%;\n  }\n  .col-lg-7 {\n    flex: 0 0 auto;\n    width: 58.33333333%;\n  }\n  .col-lg-8 {\n    flex: 0 0 auto;\n    width: 66.66666667%;\n  }\n  .col-lg-9 {\n    flex: 0 0 auto;\n    width: 75%;\n  }\n  .col-lg-10 {\n    flex: 0 0 auto;\n    width: 83.33333333%;\n  }\n  .col-lg-11 {\n    flex: 0 0 auto;\n    width: 91.66666667%;\n  }\n  .col-lg-12 {\n    flex: 0 0 auto;\n    width: 100%;\n  }\n  .offset-lg-0 {\n    margin-left: 0;\n  }\n  .offset-lg-1 {\n    margin-left: 8.33333333%;\n  }\n  .offset-lg-2 {\n    margin-left: 16.66666667%;\n  }\n  .offset-lg-3 {\n    margin-left: 25%;\n  }\n  .offset-lg-4 {\n    margin-left: 33.33333333%;\n  }\n  .offset-lg-5 {\n    margin-left: 41.66666667%;\n  }\n  .offset-lg-6 {\n    margin-left: 50%;\n  }\n  .offset-lg-7 {\n    margin-left: 58.33333333%;\n  }\n  .offset-lg-8 {\n    margin-left: 66.66666667%;\n  }\n  .offset-lg-9 {\n    margin-left: 75%;\n  }\n  .offset-lg-10 {\n    margin-left: 83.33333333%;\n  }\n  .offset-lg-11 {\n    margin-left: 91.66666667%;\n  }\n  .g-lg-0,\n  .gx-lg-0 {\n    --bs-gutter-x: 0;\n  }\n  .g-lg-0,\n  .gy-lg-0 {\n    --bs-gutter-y: 0;\n  }\n  .g-lg-1,\n  .gx-lg-1 {\n    --bs-gutter-x: 0.3125rem;\n  }\n  .g-lg-1,\n  .gy-lg-1 {\n    --bs-gutter-y: 0.3125rem;\n  }\n  .g-lg-2,\n  .gx-lg-2 {\n    --bs-gutter-x: 0.625rem;\n  }\n  .g-lg-2,\n  .gy-lg-2 {\n    --bs-gutter-y: 0.625rem;\n  }\n  .g-lg-3,\n  .gx-lg-3 {\n    --bs-gutter-x: 1.25rem;\n  }\n  .g-lg-3,\n  .gy-lg-3 {\n    --bs-gutter-y: 1.25rem;\n  }\n  .g-lg-4,\n  .gx-lg-4 {\n    --bs-gutter-x: 1.875rem;\n  }\n  .g-lg-4,\n  .gy-lg-4 {\n    --bs-gutter-y: 1.875rem;\n  }\n  .g-lg-5,\n  .gx-lg-5 {\n    --bs-gutter-x: 3.75rem;\n  }\n  .g-lg-5,\n  .gy-lg-5 {\n    --bs-gutter-y: 3.75rem;\n  }\n}\n@media (min-width: 1280px) {\n  .col-xl {\n    flex: 1 0 0;\n  }\n  .row-cols-xl-auto > * {\n    flex: 0 0 auto;\n    width: auto;\n  }\n  .row-cols-xl-1 > * {\n    flex: 0 0 auto;\n    width: 100%;\n  }\n  .row-cols-xl-2 > * {\n    flex: 0 0 auto;\n    width: 50%;\n  }\n  .row-cols-xl-3 > * {\n    flex: 0 0 auto;\n    width: 33.33333333%;\n  }\n  .row-cols-xl-4 > * {\n    flex: 0 0 auto;\n    width: 25%;\n  }\n  .row-cols-xl-5 > * {\n    flex: 0 0 auto;\n    width: 20%;\n  }\n  .row-cols-xl-6 > * {\n    flex: 0 0 auto;\n    width: 16.66666667%;\n  }\n  .col-xl-auto {\n    flex: 0 0 auto;\n    width: auto;\n  }\n  .col-xl-1 {\n    flex: 0 0 auto;\n    width: 8.33333333%;\n  }\n  .col-xl-2 {\n    flex: 0 0 auto;\n    width: 16.66666667%;\n  }\n  .col-xl-3 {\n    flex: 0 0 auto;\n    width: 25%;\n  }\n  .col-xl-4 {\n    flex: 0 0 auto;\n    width: 33.33333333%;\n  }\n  .col-xl-5 {\n    flex: 0 0 auto;\n    width: 41.66666667%;\n  }\n  .col-xl-6 {\n    flex: 0 0 auto;\n    width: 50%;\n  }\n  .col-xl-7 {\n    flex: 0 0 auto;\n    width: 58.33333333%;\n  }\n  .col-xl-8 {\n    flex: 0 0 auto;\n    width: 66.66666667%;\n  }\n  .col-xl-9 {\n    flex: 0 0 auto;\n    width: 75%;\n  }\n  .col-xl-10 {\n    flex: 0 0 auto;\n    width: 83.33333333%;\n  }\n  .col-xl-11 {\n    flex: 0 0 auto;\n    width: 91.66666667%;\n  }\n  .col-xl-12 {\n    flex: 0 0 auto;\n    width: 100%;\n  }\n  .offset-xl-0 {\n    margin-left: 0;\n  }\n  .offset-xl-1 {\n    margin-left: 8.33333333%;\n  }\n  .offset-xl-2 {\n    margin-left: 16.66666667%;\n  }\n  .offset-xl-3 {\n    margin-left: 25%;\n  }\n  .offset-xl-4 {\n    margin-left: 33.33333333%;\n  }\n  .offset-xl-5 {\n    margin-left: 41.66666667%;\n  }\n  .offset-xl-6 {\n    margin-left: 50%;\n  }\n  .offset-xl-7 {\n    margin-left: 58.33333333%;\n  }\n  .offset-xl-8 {\n    margin-left: 66.66666667%;\n  }\n  .offset-xl-9 {\n    margin-left: 75%;\n  }\n  .offset-xl-10 {\n    margin-left: 83.33333333%;\n  }\n  .offset-xl-11 {\n    margin-left: 91.66666667%;\n  }\n  .g-xl-0,\n  .gx-xl-0 {\n    --bs-gutter-x: 0;\n  }\n  .g-xl-0,\n  .gy-xl-0 {\n    --bs-gutter-y: 0;\n  }\n  .g-xl-1,\n  .gx-xl-1 {\n    --bs-gutter-x: 0.3125rem;\n  }\n  .g-xl-1,\n  .gy-xl-1 {\n    --bs-gutter-y: 0.3125rem;\n  }\n  .g-xl-2,\n  .gx-xl-2 {\n    --bs-gutter-x: 0.625rem;\n  }\n  .g-xl-2,\n  .gy-xl-2 {\n    --bs-gutter-y: 0.625rem;\n  }\n  .g-xl-3,\n  .gx-xl-3 {\n    --bs-gutter-x: 1.25rem;\n  }\n  .g-xl-3,\n  .gy-xl-3 {\n    --bs-gutter-y: 1.25rem;\n  }\n  .g-xl-4,\n  .gx-xl-4 {\n    --bs-gutter-x: 1.875rem;\n  }\n  .g-xl-4,\n  .gy-xl-4 {\n    --bs-gutter-y: 1.875rem;\n  }\n  .g-xl-5,\n  .gx-xl-5 {\n    --bs-gutter-x: 3.75rem;\n  }\n  .g-xl-5,\n  .gy-xl-5 {\n    --bs-gutter-y: 3.75rem;\n  }\n}\n@media (min-width: 1440px) {\n  .col-xxl {\n    flex: 1 0 0;\n  }\n  .row-cols-xxl-auto > * {\n    flex: 0 0 auto;\n    width: auto;\n  }\n  .row-cols-xxl-1 > * {\n    flex: 0 0 auto;\n    width: 100%;\n  }\n  .row-cols-xxl-2 > * {\n    flex: 0 0 auto;\n    width: 50%;\n  }\n  .row-cols-xxl-3 > * {\n    flex: 0 0 auto;\n    width: 33.33333333%;\n  }\n  .row-cols-xxl-4 > * {\n    flex: 0 0 auto;\n    width: 25%;\n  }\n  .row-cols-xxl-5 > * {\n    flex: 0 0 auto;\n    width: 20%;\n  }\n  .row-cols-xxl-6 > * {\n    flex: 0 0 auto;\n    width: 16.66666667%;\n  }\n  .col-xxl-auto {\n    flex: 0 0 auto;\n    width: auto;\n  }\n  .col-xxl-1 {\n    flex: 0 0 auto;\n    width: 8.33333333%;\n  }\n  .col-xxl-2 {\n    flex: 0 0 auto;\n    width: 16.66666667%;\n  }\n  .col-xxl-3 {\n    flex: 0 0 auto;\n    width: 25%;\n  }\n  .col-xxl-4 {\n    flex: 0 0 auto;\n    width: 33.33333333%;\n  }\n  .col-xxl-5 {\n    flex: 0 0 auto;\n    width: 41.66666667%;\n  }\n  .col-xxl-6 {\n    flex: 0 0 auto;\n    width: 50%;\n  }\n  .col-xxl-7 {\n    flex: 0 0 auto;\n    width: 58.33333333%;\n  }\n  .col-xxl-8 {\n    flex: 0 0 auto;\n    width: 66.66666667%;\n  }\n  .col-xxl-9 {\n    flex: 0 0 auto;\n    width: 75%;\n  }\n  .col-xxl-10 {\n    flex: 0 0 auto;\n    width: 83.33333333%;\n  }\n  .col-xxl-11 {\n    flex: 0 0 auto;\n    width: 91.66666667%;\n  }\n  .col-xxl-12 {\n    flex: 0 0 auto;\n    width: 100%;\n  }\n  .offset-xxl-0 {\n    margin-left: 0;\n  }\n  .offset-xxl-1 {\n    margin-left: 8.33333333%;\n  }\n  .offset-xxl-2 {\n    margin-left: 16.66666667%;\n  }\n  .offset-xxl-3 {\n    margin-left: 25%;\n  }\n  .offset-xxl-4 {\n    margin-left: 33.33333333%;\n  }\n  .offset-xxl-5 {\n    margin-left: 41.66666667%;\n  }\n  .offset-xxl-6 {\n    margin-left: 50%;\n  }\n  .offset-xxl-7 {\n    margin-left: 58.33333333%;\n  }\n  .offset-xxl-8 {\n    margin-left: 66.66666667%;\n  }\n  .offset-xxl-9 {\n    margin-left: 75%;\n  }\n  .offset-xxl-10 {\n    margin-left: 83.33333333%;\n  }\n  .offset-xxl-11 {\n    margin-left: 91.66666667%;\n  }\n  .g-xxl-0,\n  .gx-xxl-0 {\n    --bs-gutter-x: 0;\n  }\n  .g-xxl-0,\n  .gy-xxl-0 {\n    --bs-gutter-y: 0;\n  }\n  .g-xxl-1,\n  .gx-xxl-1 {\n    --bs-gutter-x: 0.3125rem;\n  }\n  .g-xxl-1,\n  .gy-xxl-1 {\n    --bs-gutter-y: 0.3125rem;\n  }\n  .g-xxl-2,\n  .gx-xxl-2 {\n    --bs-gutter-x: 0.625rem;\n  }\n  .g-xxl-2,\n  .gy-xxl-2 {\n    --bs-gutter-y: 0.625rem;\n  }\n  .g-xxl-3,\n  .gx-xxl-3 {\n    --bs-gutter-x: 1.25rem;\n  }\n  .g-xxl-3,\n  .gy-xxl-3 {\n    --bs-gutter-y: 1.25rem;\n  }\n  .g-xxl-4,\n  .gx-xxl-4 {\n    --bs-gutter-x: 1.875rem;\n  }\n  .g-xxl-4,\n  .gy-xxl-4 {\n    --bs-gutter-y: 1.875rem;\n  }\n  .g-xxl-5,\n  .gx-xxl-5 {\n    --bs-gutter-x: 3.75rem;\n  }\n  .g-xxl-5,\n  .gy-xxl-5 {\n    --bs-gutter-y: 3.75rem;\n  }\n}\n.d-inline {\n  display: inline !important;\n}\n\n.d-inline-block {\n  display: inline-block !important;\n}\n\n.d-block {\n  display: block !important;\n}\n\n.d-grid {\n  display: grid !important;\n}\n\n.d-inline-grid {\n  display: inline-grid !important;\n}\n\n.d-table {\n  display: table !important;\n}\n\n.d-table-row {\n  display: table-row !important;\n}\n\n.d-table-cell {\n  display: table-cell !important;\n}\n\n.d-flex {\n  display: flex !important;\n}\n\n.d-inline-flex {\n  display: inline-flex !important;\n}\n\n.d-none {\n  display: none !important;\n}\n\n.flex-fill {\n  flex: 1 1 auto !important;\n}\n\n.flex-row {\n  flex-direction: row !important;\n}\n\n.flex-column {\n  flex-direction: column !important;\n}\n\n.flex-row-reverse {\n  flex-direction: row-reverse !important;\n}\n\n.flex-column-reverse {\n  flex-direction: column-reverse !important;\n}\n\n.flex-grow-0 {\n  flex-grow: 0 !important;\n}\n\n.flex-grow-1 {\n  flex-grow: 1 !important;\n}\n\n.flex-shrink-0 {\n  flex-shrink: 0 !important;\n}\n\n.flex-shrink-1 {\n  flex-shrink: 1 !important;\n}\n\n.flex-wrap {\n  flex-wrap: wrap !important;\n}\n\n.flex-nowrap {\n  flex-wrap: nowrap !important;\n}\n\n.flex-wrap-reverse {\n  flex-wrap: wrap-reverse !important;\n}\n\n.justify-content-start {\n  justify-content: flex-start !important;\n}\n\n.justify-content-end {\n  justify-content: flex-end !important;\n}\n\n.justify-content-center {\n  justify-content: center !important;\n}\n\n.justify-content-between {\n  justify-content: space-between !important;\n}\n\n.justify-content-around {\n  justify-content: space-around !important;\n}\n\n.justify-content-evenly {\n  justify-content: space-evenly !important;\n}\n\n.align-items-start {\n  align-items: flex-start !important;\n}\n\n.align-items-end {\n  align-items: flex-end !important;\n}\n\n.align-items-center {\n  align-items: center !important;\n}\n\n.align-items-baseline {\n  align-items: baseline !important;\n}\n\n.align-items-stretch {\n  align-items: stretch !important;\n}\n\n.align-content-start {\n  align-content: flex-start !important;\n}\n\n.align-content-end {\n  align-content: flex-end !important;\n}\n\n.align-content-center {\n  align-content: center !important;\n}\n\n.align-content-between {\n  align-content: space-between !important;\n}\n\n.align-content-around {\n  align-content: space-around !important;\n}\n\n.align-content-stretch {\n  align-content: stretch !important;\n}\n\n.align-self-auto {\n  align-self: auto !important;\n}\n\n.align-self-start {\n  align-self: flex-start !important;\n}\n\n.align-self-end {\n  align-self: flex-end !important;\n}\n\n.align-self-center {\n  align-self: center !important;\n}\n\n.align-self-baseline {\n  align-self: baseline !important;\n}\n\n.align-self-stretch {\n  align-self: stretch !important;\n}\n\n.order-first {\n  order: -1 !important;\n}\n\n.order-0 {\n  order: 0 !important;\n}\n\n.order-1 {\n  order: 1 !important;\n}\n\n.order-2 {\n  order: 2 !important;\n}\n\n.order-3 {\n  order: 3 !important;\n}\n\n.order-4 {\n  order: 4 !important;\n}\n\n.order-5 {\n  order: 5 !important;\n}\n\n.order-last {\n  order: 6 !important;\n}\n\n.m-0 {\n  margin: 0 !important;\n}\n\n.m-1 {\n  margin: 0.3125rem !important;\n}\n\n.m-2 {\n  margin: 0.625rem !important;\n}\n\n.m-3 {\n  margin: 1.25rem !important;\n}\n\n.m-4 {\n  margin: 1.875rem !important;\n}\n\n.m-5 {\n  margin: 3.75rem !important;\n}\n\n.m-auto {\n  margin: auto !important;\n}\n\n.mx-0 {\n  margin-right: 0 !important;\n  margin-left: 0 !important;\n}\n\n.mx-1 {\n  margin-right: 0.3125rem !important;\n  margin-left: 0.3125rem !important;\n}\n\n.mx-2 {\n  margin-right: 0.625rem !important;\n  margin-left: 0.625rem !important;\n}\n\n.mx-3 {\n  margin-right: 1.25rem !important;\n  margin-left: 1.25rem !important;\n}\n\n.mx-4 {\n  margin-right: 1.875rem !important;\n  margin-left: 1.875rem !important;\n}\n\n.mx-5 {\n  margin-right: 3.75rem !important;\n  margin-left: 3.75rem !important;\n}\n\n.mx-auto {\n  margin-right: auto !important;\n  margin-left: auto !important;\n}\n\n.my-0 {\n  margin-top: 0 !important;\n  margin-bottom: 0 !important;\n}\n\n.my-1 {\n  margin-top: 0.3125rem !important;\n  margin-bottom: 0.3125rem !important;\n}\n\n.my-2 {\n  margin-top: 0.625rem !important;\n  margin-bottom: 0.625rem !important;\n}\n\n.my-3 {\n  margin-top: 1.25rem !important;\n  margin-bottom: 1.25rem !important;\n}\n\n.my-4 {\n  margin-top: 1.875rem !important;\n  margin-bottom: 1.875rem !important;\n}\n\n.my-5 {\n  margin-top: 3.75rem !important;\n  margin-bottom: 3.75rem !important;\n}\n\n.my-auto {\n  margin-top: auto !important;\n  margin-bottom: auto !important;\n}\n\n.mt-0 {\n  margin-top: 0 !important;\n}\n\n.mt-1 {\n  margin-top: 0.3125rem !important;\n}\n\n.mt-2 {\n  margin-top: 0.625rem !important;\n}\n\n.mt-3 {\n  margin-top: 1.25rem !important;\n}\n\n.mt-4 {\n  margin-top: 1.875rem !important;\n}\n\n.mt-5 {\n  margin-top: 3.75rem !important;\n}\n\n.mt-auto {\n  margin-top: auto !important;\n}\n\n.me-0 {\n  margin-right: 0 !important;\n}\n\n.me-1 {\n  margin-right: 0.3125rem !important;\n}\n\n.me-2 {\n  margin-right: 0.625rem !important;\n}\n\n.me-3 {\n  margin-right: 1.25rem !important;\n}\n\n.me-4 {\n  margin-right: 1.875rem !important;\n}\n\n.me-5 {\n  margin-right: 3.75rem !important;\n}\n\n.me-auto {\n  margin-right: auto !important;\n}\n\n.mb-0 {\n  margin-bottom: 0 !important;\n}\n\n.mb-1 {\n  margin-bottom: 0.3125rem !important;\n}\n\n.mb-2 {\n  margin-bottom: 0.625rem !important;\n}\n\n.mb-3 {\n  margin-bottom: 1.25rem !important;\n}\n\n.mb-4 {\n  margin-bottom: 1.875rem !important;\n}\n\n.mb-5 {\n  margin-bottom: 3.75rem !important;\n}\n\n.mb-auto {\n  margin-bottom: auto !important;\n}\n\n.ms-0 {\n  margin-left: 0 !important;\n}\n\n.ms-1 {\n  margin-left: 0.3125rem !important;\n}\n\n.ms-2 {\n  margin-left: 0.625rem !important;\n}\n\n.ms-3 {\n  margin-left: 1.25rem !important;\n}\n\n.ms-4 {\n  margin-left: 1.875rem !important;\n}\n\n.ms-5 {\n  margin-left: 3.75rem !important;\n}\n\n.ms-auto {\n  margin-left: auto !important;\n}\n\n.p-0 {\n  padding: 0 !important;\n}\n\n.p-1 {\n  padding: 0.3125rem !important;\n}\n\n.p-2 {\n  padding: 0.625rem !important;\n}\n\n.p-3 {\n  padding: 1.25rem !important;\n}\n\n.p-4 {\n  padding: 1.875rem !important;\n}\n\n.p-5 {\n  padding: 3.75rem !important;\n}\n\n.px-0 {\n  padding-right: 0 !important;\n  padding-left: 0 !important;\n}\n\n.px-1 {\n  padding-right: 0.3125rem !important;\n  padding-left: 0.3125rem !important;\n}\n\n.px-2 {\n  padding-right: 0.625rem !important;\n  padding-left: 0.625rem !important;\n}\n\n.px-3 {\n  padding-right: 1.25rem !important;\n  padding-left: 1.25rem !important;\n}\n\n.px-4 {\n  padding-right: 1.875rem !important;\n  padding-left: 1.875rem !important;\n}\n\n.px-5 {\n  padding-right: 3.75rem !important;\n  padding-left: 3.75rem !important;\n}\n\n.py-0 {\n  padding-top: 0 !important;\n  padding-bottom: 0 !important;\n}\n\n.py-1 {\n  padding-top: 0.3125rem !important;\n  padding-bottom: 0.3125rem !important;\n}\n\n.py-2 {\n  padding-top: 0.625rem !important;\n  padding-bottom: 0.625rem !important;\n}\n\n.py-3 {\n  padding-top: 1.25rem !important;\n  padding-bottom: 1.25rem !important;\n}\n\n.py-4 {\n  padding-top: 1.875rem !important;\n  padding-bottom: 1.875rem !important;\n}\n\n.py-5 {\n  padding-top: 3.75rem !important;\n  padding-bottom: 3.75rem !important;\n}\n\n.pt-0 {\n  padding-top: 0 !important;\n}\n\n.pt-1 {\n  padding-top: 0.3125rem !important;\n}\n\n.pt-2 {\n  padding-top: 0.625rem !important;\n}\n\n.pt-3 {\n  padding-top: 1.25rem !important;\n}\n\n.pt-4 {\n  padding-top: 1.875rem !important;\n}\n\n.pt-5 {\n  padding-top: 3.75rem !important;\n}\n\n.pe-0 {\n  padding-right: 0 !important;\n}\n\n.pe-1 {\n  padding-right: 0.3125rem !important;\n}\n\n.pe-2 {\n  padding-right: 0.625rem !important;\n}\n\n.pe-3 {\n  padding-right: 1.25rem !important;\n}\n\n.pe-4 {\n  padding-right: 1.875rem !important;\n}\n\n.pe-5 {\n  padding-right: 3.75rem !important;\n}\n\n.pb-0 {\n  padding-bottom: 0 !important;\n}\n\n.pb-1 {\n  padding-bottom: 0.3125rem !important;\n}\n\n.pb-2 {\n  padding-bottom: 0.625rem !important;\n}\n\n.pb-3 {\n  padding-bottom: 1.25rem !important;\n}\n\n.pb-4 {\n  padding-bottom: 1.875rem !important;\n}\n\n.pb-5 {\n  padding-bottom: 3.75rem !important;\n}\n\n.ps-0 {\n  padding-left: 0 !important;\n}\n\n.ps-1 {\n  padding-left: 0.3125rem !important;\n}\n\n.ps-2 {\n  padding-left: 0.625rem !important;\n}\n\n.ps-3 {\n  padding-left: 1.25rem !important;\n}\n\n.ps-4 {\n  padding-left: 1.875rem !important;\n}\n\n.ps-5 {\n  padding-left: 3.75rem !important;\n}\n\n@media (min-width: 480px) {\n  .d-sm-inline {\n    display: inline !important;\n  }\n  .d-sm-inline-block {\n    display: inline-block !important;\n  }\n  .d-sm-block {\n    display: block !important;\n  }\n  .d-sm-grid {\n    display: grid !important;\n  }\n  .d-sm-inline-grid {\n    display: inline-grid !important;\n  }\n  .d-sm-table {\n    display: table !important;\n  }\n  .d-sm-table-row {\n    display: table-row !important;\n  }\n  .d-sm-table-cell {\n    display: table-cell !important;\n  }\n  .d-sm-flex {\n    display: flex !important;\n  }\n  .d-sm-inline-flex {\n    display: inline-flex !important;\n  }\n  .d-sm-none {\n    display: none !important;\n  }\n  .flex-sm-fill {\n    flex: 1 1 auto !important;\n  }\n  .flex-sm-row {\n    flex-direction: row !important;\n  }\n  .flex-sm-column {\n    flex-direction: column !important;\n  }\n  .flex-sm-row-reverse {\n    flex-direction: row-reverse !important;\n  }\n  .flex-sm-column-reverse {\n    flex-direction: column-reverse !important;\n  }\n  .flex-sm-grow-0 {\n    flex-grow: 0 !important;\n  }\n  .flex-sm-grow-1 {\n    flex-grow: 1 !important;\n  }\n  .flex-sm-shrink-0 {\n    flex-shrink: 0 !important;\n  }\n  .flex-sm-shrink-1 {\n    flex-shrink: 1 !important;\n  }\n  .flex-sm-wrap {\n    flex-wrap: wrap !important;\n  }\n  .flex-sm-nowrap {\n    flex-wrap: nowrap !important;\n  }\n  .flex-sm-wrap-reverse {\n    flex-wrap: wrap-reverse !important;\n  }\n  .justify-content-sm-start {\n    justify-content: flex-start !important;\n  }\n  .justify-content-sm-end {\n    justify-content: flex-end !important;\n  }\n  .justify-content-sm-center {\n    justify-content: center !important;\n  }\n  .justify-content-sm-between {\n    justify-content: space-between !important;\n  }\n  .justify-content-sm-around {\n    justify-content: space-around !important;\n  }\n  .justify-content-sm-evenly {\n    justify-content: space-evenly !important;\n  }\n  .align-items-sm-start {\n    align-items: flex-start !important;\n  }\n  .align-items-sm-end {\n    align-items: flex-end !important;\n  }\n  .align-items-sm-center {\n    align-items: center !important;\n  }\n  .align-items-sm-baseline {\n    align-items: baseline !important;\n  }\n  .align-items-sm-stretch {\n    align-items: stretch !important;\n  }\n  .align-content-sm-start {\n    align-content: flex-start !important;\n  }\n  .align-content-sm-end {\n    align-content: flex-end !important;\n  }\n  .align-content-sm-center {\n    align-content: center !important;\n  }\n  .align-content-sm-between {\n    align-content: space-between !important;\n  }\n  .align-content-sm-around {\n    align-content: space-around !important;\n  }\n  .align-content-sm-stretch {\n    align-content: stretch !important;\n  }\n  .align-self-sm-auto {\n    align-self: auto !important;\n  }\n  .align-self-sm-start {\n    align-self: flex-start !important;\n  }\n  .align-self-sm-end {\n    align-self: flex-end !important;\n  }\n  .align-self-sm-center {\n    align-self: center !important;\n  }\n  .align-self-sm-baseline {\n    align-self: baseline !important;\n  }\n  .align-self-sm-stretch {\n    align-self: stretch !important;\n  }\n  .order-sm-first {\n    order: -1 !important;\n  }\n  .order-sm-0 {\n    order: 0 !important;\n  }\n  .order-sm-1 {\n    order: 1 !important;\n  }\n  .order-sm-2 {\n    order: 2 !important;\n  }\n  .order-sm-3 {\n    order: 3 !important;\n  }\n  .order-sm-4 {\n    order: 4 !important;\n  }\n  .order-sm-5 {\n    order: 5 !important;\n  }\n  .order-sm-last {\n    order: 6 !important;\n  }\n  .m-sm-0 {\n    margin: 0 !important;\n  }\n  .m-sm-1 {\n    margin: 0.3125rem !important;\n  }\n  .m-sm-2 {\n    margin: 0.625rem !important;\n  }\n  .m-sm-3 {\n    margin: 1.25rem !important;\n  }\n  .m-sm-4 {\n    margin: 1.875rem !important;\n  }\n  .m-sm-5 {\n    margin: 3.75rem !important;\n  }\n  .m-sm-auto {\n    margin: auto !important;\n  }\n  .mx-sm-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important;\n  }\n  .mx-sm-1 {\n    margin-right: 0.3125rem !important;\n    margin-left: 0.3125rem !important;\n  }\n  .mx-sm-2 {\n    margin-right: 0.625rem !important;\n    margin-left: 0.625rem !important;\n  }\n  .mx-sm-3 {\n    margin-right: 1.25rem !important;\n    margin-left: 1.25rem !important;\n  }\n  .mx-sm-4 {\n    margin-right: 1.875rem !important;\n    margin-left: 1.875rem !important;\n  }\n  .mx-sm-5 {\n    margin-right: 3.75rem !important;\n    margin-left: 3.75rem !important;\n  }\n  .mx-sm-auto {\n    margin-right: auto !important;\n    margin-left: auto !important;\n  }\n  .my-sm-0 {\n    margin-top: 0 !important;\n    margin-bottom: 0 !important;\n  }\n  .my-sm-1 {\n    margin-top: 0.3125rem !important;\n    margin-bottom: 0.3125rem !important;\n  }\n  .my-sm-2 {\n    margin-top: 0.625rem !important;\n    margin-bottom: 0.625rem !important;\n  }\n  .my-sm-3 {\n    margin-top: 1.25rem !important;\n    margin-bottom: 1.25rem !important;\n  }\n  .my-sm-4 {\n    margin-top: 1.875rem !important;\n    margin-bottom: 1.875rem !important;\n  }\n  .my-sm-5 {\n    margin-top: 3.75rem !important;\n    margin-bottom: 3.75rem !important;\n  }\n  .my-sm-auto {\n    margin-top: auto !important;\n    margin-bottom: auto !important;\n  }\n  .mt-sm-0 {\n    margin-top: 0 !important;\n  }\n  .mt-sm-1 {\n    margin-top: 0.3125rem !important;\n  }\n  .mt-sm-2 {\n    margin-top: 0.625rem !important;\n  }\n  .mt-sm-3 {\n    margin-top: 1.25rem !important;\n  }\n  .mt-sm-4 {\n    margin-top: 1.875rem !important;\n  }\n  .mt-sm-5 {\n    margin-top: 3.75rem !important;\n  }\n  .mt-sm-auto {\n    margin-top: auto !important;\n  }\n  .me-sm-0 {\n    margin-right: 0 !important;\n  }\n  .me-sm-1 {\n    margin-right: 0.3125rem !important;\n  }\n  .me-sm-2 {\n    margin-right: 0.625rem !important;\n  }\n  .me-sm-3 {\n    margin-right: 1.25rem !important;\n  }\n  .me-sm-4 {\n    margin-right: 1.875rem !important;\n  }\n  .me-sm-5 {\n    margin-right: 3.75rem !important;\n  }\n  .me-sm-auto {\n    margin-right: auto !important;\n  }\n  .mb-sm-0 {\n    margin-bottom: 0 !important;\n  }\n  .mb-sm-1 {\n    margin-bottom: 0.3125rem !important;\n  }\n  .mb-sm-2 {\n    margin-bottom: 0.625rem !important;\n  }\n  .mb-sm-3 {\n    margin-bottom: 1.25rem !important;\n  }\n  .mb-sm-4 {\n    margin-bottom: 1.875rem !important;\n  }\n  .mb-sm-5 {\n    margin-bottom: 3.75rem !important;\n  }\n  .mb-sm-auto {\n    margin-bottom: auto !important;\n  }\n  .ms-sm-0 {\n    margin-left: 0 !important;\n  }\n  .ms-sm-1 {\n    margin-left: 0.3125rem !important;\n  }\n  .ms-sm-2 {\n    margin-left: 0.625rem !important;\n  }\n  .ms-sm-3 {\n    margin-left: 1.25rem !important;\n  }\n  .ms-sm-4 {\n    margin-left: 1.875rem !important;\n  }\n  .ms-sm-5 {\n    margin-left: 3.75rem !important;\n  }\n  .ms-sm-auto {\n    margin-left: auto !important;\n  }\n  .p-sm-0 {\n    padding: 0 !important;\n  }\n  .p-sm-1 {\n    padding: 0.3125rem !important;\n  }\n  .p-sm-2 {\n    padding: 0.625rem !important;\n  }\n  .p-sm-3 {\n    padding: 1.25rem !important;\n  }\n  .p-sm-4 {\n    padding: 1.875rem !important;\n  }\n  .p-sm-5 {\n    padding: 3.75rem !important;\n  }\n  .px-sm-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important;\n  }\n  .px-sm-1 {\n    padding-right: 0.3125rem !important;\n    padding-left: 0.3125rem !important;\n  }\n  .px-sm-2 {\n    padding-right: 0.625rem !important;\n    padding-left: 0.625rem !important;\n  }\n  .px-sm-3 {\n    padding-right: 1.25rem !important;\n    padding-left: 1.25rem !important;\n  }\n  .px-sm-4 {\n    padding-right: 1.875rem !important;\n    padding-left: 1.875rem !important;\n  }\n  .px-sm-5 {\n    padding-right: 3.75rem !important;\n    padding-left: 3.75rem !important;\n  }\n  .py-sm-0 {\n    padding-top: 0 !important;\n    padding-bottom: 0 !important;\n  }\n  .py-sm-1 {\n    padding-top: 0.3125rem !important;\n    padding-bottom: 0.3125rem !important;\n  }\n  .py-sm-2 {\n    padding-top: 0.625rem !important;\n    padding-bottom: 0.625rem !important;\n  }\n  .py-sm-3 {\n    padding-top: 1.25rem !important;\n    padding-bottom: 1.25rem !important;\n  }\n  .py-sm-4 {\n    padding-top: 1.875rem !important;\n    padding-bottom: 1.875rem !important;\n  }\n  .py-sm-5 {\n    padding-top: 3.75rem !important;\n    padding-bottom: 3.75rem !important;\n  }\n  .pt-sm-0 {\n    padding-top: 0 !important;\n  }\n  .pt-sm-1 {\n    padding-top: 0.3125rem !important;\n  }\n  .pt-sm-2 {\n    padding-top: 0.625rem !important;\n  }\n  .pt-sm-3 {\n    padding-top: 1.25rem !important;\n  }\n  .pt-sm-4 {\n    padding-top: 1.875rem !important;\n  }\n  .pt-sm-5 {\n    padding-top: 3.75rem !important;\n  }\n  .pe-sm-0 {\n    padding-right: 0 !important;\n  }\n  .pe-sm-1 {\n    padding-right: 0.3125rem !important;\n  }\n  .pe-sm-2 {\n    padding-right: 0.625rem !important;\n  }\n  .pe-sm-3 {\n    padding-right: 1.25rem !important;\n  }\n  .pe-sm-4 {\n    padding-right: 1.875rem !important;\n  }\n  .pe-sm-5 {\n    padding-right: 3.75rem !important;\n  }\n  .pb-sm-0 {\n    padding-bottom: 0 !important;\n  }\n  .pb-sm-1 {\n    padding-bottom: 0.3125rem !important;\n  }\n  .pb-sm-2 {\n    padding-bottom: 0.625rem !important;\n  }\n  .pb-sm-3 {\n    padding-bottom: 1.25rem !important;\n  }\n  .pb-sm-4 {\n    padding-bottom: 1.875rem !important;\n  }\n  .pb-sm-5 {\n    padding-bottom: 3.75rem !important;\n  }\n  .ps-sm-0 {\n    padding-left: 0 !important;\n  }\n  .ps-sm-1 {\n    padding-left: 0.3125rem !important;\n  }\n  .ps-sm-2 {\n    padding-left: 0.625rem !important;\n  }\n  .ps-sm-3 {\n    padding-left: 1.25rem !important;\n  }\n  .ps-sm-4 {\n    padding-left: 1.875rem !important;\n  }\n  .ps-sm-5 {\n    padding-left: 3.75rem !important;\n  }\n}\n@media (min-width: 768px) {\n  .d-md-inline {\n    display: inline !important;\n  }\n  .d-md-inline-block {\n    display: inline-block !important;\n  }\n  .d-md-block {\n    display: block !important;\n  }\n  .d-md-grid {\n    display: grid !important;\n  }\n  .d-md-inline-grid {\n    display: inline-grid !important;\n  }\n  .d-md-table {\n    display: table !important;\n  }\n  .d-md-table-row {\n    display: table-row !important;\n  }\n  .d-md-table-cell {\n    display: table-cell !important;\n  }\n  .d-md-flex {\n    display: flex !important;\n  }\n  .d-md-inline-flex {\n    display: inline-flex !important;\n  }\n  .d-md-none {\n    display: none !important;\n  }\n  .flex-md-fill {\n    flex: 1 1 auto !important;\n  }\n  .flex-md-row {\n    flex-direction: row !important;\n  }\n  .flex-md-column {\n    flex-direction: column !important;\n  }\n  .flex-md-row-reverse {\n    flex-direction: row-reverse !important;\n  }\n  .flex-md-column-reverse {\n    flex-direction: column-reverse !important;\n  }\n  .flex-md-grow-0 {\n    flex-grow: 0 !important;\n  }\n  .flex-md-grow-1 {\n    flex-grow: 1 !important;\n  }\n  .flex-md-shrink-0 {\n    flex-shrink: 0 !important;\n  }\n  .flex-md-shrink-1 {\n    flex-shrink: 1 !important;\n  }\n  .flex-md-wrap {\n    flex-wrap: wrap !important;\n  }\n  .flex-md-nowrap {\n    flex-wrap: nowrap !important;\n  }\n  .flex-md-wrap-reverse {\n    flex-wrap: wrap-reverse !important;\n  }\n  .justify-content-md-start {\n    justify-content: flex-start !important;\n  }\n  .justify-content-md-end {\n    justify-content: flex-end !important;\n  }\n  .justify-content-md-center {\n    justify-content: center !important;\n  }\n  .justify-content-md-between {\n    justify-content: space-between !important;\n  }\n  .justify-content-md-around {\n    justify-content: space-around !important;\n  }\n  .justify-content-md-evenly {\n    justify-content: space-evenly !important;\n  }\n  .align-items-md-start {\n    align-items: flex-start !important;\n  }\n  .align-items-md-end {\n    align-items: flex-end !important;\n  }\n  .align-items-md-center {\n    align-items: center !important;\n  }\n  .align-items-md-baseline {\n    align-items: baseline !important;\n  }\n  .align-items-md-stretch {\n    align-items: stretch !important;\n  }\n  .align-content-md-start {\n    align-content: flex-start !important;\n  }\n  .align-content-md-end {\n    align-content: flex-end !important;\n  }\n  .align-content-md-center {\n    align-content: center !important;\n  }\n  .align-content-md-between {\n    align-content: space-between !important;\n  }\n  .align-content-md-around {\n    align-content: space-around !important;\n  }\n  .align-content-md-stretch {\n    align-content: stretch !important;\n  }\n  .align-self-md-auto {\n    align-self: auto !important;\n  }\n  .align-self-md-start {\n    align-self: flex-start !important;\n  }\n  .align-self-md-end {\n    align-self: flex-end !important;\n  }\n  .align-self-md-center {\n    align-self: center !important;\n  }\n  .align-self-md-baseline {\n    align-self: baseline !important;\n  }\n  .align-self-md-stretch {\n    align-self: stretch !important;\n  }\n  .order-md-first {\n    order: -1 !important;\n  }\n  .order-md-0 {\n    order: 0 !important;\n  }\n  .order-md-1 {\n    order: 1 !important;\n  }\n  .order-md-2 {\n    order: 2 !important;\n  }\n  .order-md-3 {\n    order: 3 !important;\n  }\n  .order-md-4 {\n    order: 4 !important;\n  }\n  .order-md-5 {\n    order: 5 !important;\n  }\n  .order-md-last {\n    order: 6 !important;\n  }\n  .m-md-0 {\n    margin: 0 !important;\n  }\n  .m-md-1 {\n    margin: 0.3125rem !important;\n  }\n  .m-md-2 {\n    margin: 0.625rem !important;\n  }\n  .m-md-3 {\n    margin: 1.25rem !important;\n  }\n  .m-md-4 {\n    margin: 1.875rem !important;\n  }\n  .m-md-5 {\n    margin: 3.75rem !important;\n  }\n  .m-md-auto {\n    margin: auto !important;\n  }\n  .mx-md-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important;\n  }\n  .mx-md-1 {\n    margin-right: 0.3125rem !important;\n    margin-left: 0.3125rem !important;\n  }\n  .mx-md-2 {\n    margin-right: 0.625rem !important;\n    margin-left: 0.625rem !important;\n  }\n  .mx-md-3 {\n    margin-right: 1.25rem !important;\n    margin-left: 1.25rem !important;\n  }\n  .mx-md-4 {\n    margin-right: 1.875rem !important;\n    margin-left: 1.875rem !important;\n  }\n  .mx-md-5 {\n    margin-right: 3.75rem !important;\n    margin-left: 3.75rem !important;\n  }\n  .mx-md-auto {\n    margin-right: auto !important;\n    margin-left: auto !important;\n  }\n  .my-md-0 {\n    margin-top: 0 !important;\n    margin-bottom: 0 !important;\n  }\n  .my-md-1 {\n    margin-top: 0.3125rem !important;\n    margin-bottom: 0.3125rem !important;\n  }\n  .my-md-2 {\n    margin-top: 0.625rem !important;\n    margin-bottom: 0.625rem !important;\n  }\n  .my-md-3 {\n    margin-top: 1.25rem !important;\n    margin-bottom: 1.25rem !important;\n  }\n  .my-md-4 {\n    margin-top: 1.875rem !important;\n    margin-bottom: 1.875rem !important;\n  }\n  .my-md-5 {\n    margin-top: 3.75rem !important;\n    margin-bottom: 3.75rem !important;\n  }\n  .my-md-auto {\n    margin-top: auto !important;\n    margin-bottom: auto !important;\n  }\n  .mt-md-0 {\n    margin-top: 0 !important;\n  }\n  .mt-md-1 {\n    margin-top: 0.3125rem !important;\n  }\n  .mt-md-2 {\n    margin-top: 0.625rem !important;\n  }\n  .mt-md-3 {\n    margin-top: 1.25rem !important;\n  }\n  .mt-md-4 {\n    margin-top: 1.875rem !important;\n  }\n  .mt-md-5 {\n    margin-top: 3.75rem !important;\n  }\n  .mt-md-auto {\n    margin-top: auto !important;\n  }\n  .me-md-0 {\n    margin-right: 0 !important;\n  }\n  .me-md-1 {\n    margin-right: 0.3125rem !important;\n  }\n  .me-md-2 {\n    margin-right: 0.625rem !important;\n  }\n  .me-md-3 {\n    margin-right: 1.25rem !important;\n  }\n  .me-md-4 {\n    margin-right: 1.875rem !important;\n  }\n  .me-md-5 {\n    margin-right: 3.75rem !important;\n  }\n  .me-md-auto {\n    margin-right: auto !important;\n  }\n  .mb-md-0 {\n    margin-bottom: 0 !important;\n  }\n  .mb-md-1 {\n    margin-bottom: 0.3125rem !important;\n  }\n  .mb-md-2 {\n    margin-bottom: 0.625rem !important;\n  }\n  .mb-md-3 {\n    margin-bottom: 1.25rem !important;\n  }\n  .mb-md-4 {\n    margin-bottom: 1.875rem !important;\n  }\n  .mb-md-5 {\n    margin-bottom: 3.75rem !important;\n  }\n  .mb-md-auto {\n    margin-bottom: auto !important;\n  }\n  .ms-md-0 {\n    margin-left: 0 !important;\n  }\n  .ms-md-1 {\n    margin-left: 0.3125rem !important;\n  }\n  .ms-md-2 {\n    margin-left: 0.625rem !important;\n  }\n  .ms-md-3 {\n    margin-left: 1.25rem !important;\n  }\n  .ms-md-4 {\n    margin-left: 1.875rem !important;\n  }\n  .ms-md-5 {\n    margin-left: 3.75rem !important;\n  }\n  .ms-md-auto {\n    margin-left: auto !important;\n  }\n  .p-md-0 {\n    padding: 0 !important;\n  }\n  .p-md-1 {\n    padding: 0.3125rem !important;\n  }\n  .p-md-2 {\n    padding: 0.625rem !important;\n  }\n  .p-md-3 {\n    padding: 1.25rem !important;\n  }\n  .p-md-4 {\n    padding: 1.875rem !important;\n  }\n  .p-md-5 {\n    padding: 3.75rem !important;\n  }\n  .px-md-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important;\n  }\n  .px-md-1 {\n    padding-right: 0.3125rem !important;\n    padding-left: 0.3125rem !important;\n  }\n  .px-md-2 {\n    padding-right: 0.625rem !important;\n    padding-left: 0.625rem !important;\n  }\n  .px-md-3 {\n    padding-right: 1.25rem !important;\n    padding-left: 1.25rem !important;\n  }\n  .px-md-4 {\n    padding-right: 1.875rem !important;\n    padding-left: 1.875rem !important;\n  }\n  .px-md-5 {\n    padding-right: 3.75rem !important;\n    padding-left: 3.75rem !important;\n  }\n  .py-md-0 {\n    padding-top: 0 !important;\n    padding-bottom: 0 !important;\n  }\n  .py-md-1 {\n    padding-top: 0.3125rem !important;\n    padding-bottom: 0.3125rem !important;\n  }\n  .py-md-2 {\n    padding-top: 0.625rem !important;\n    padding-bottom: 0.625rem !important;\n  }\n  .py-md-3 {\n    padding-top: 1.25rem !important;\n    padding-bottom: 1.25rem !important;\n  }\n  .py-md-4 {\n    padding-top: 1.875rem !important;\n    padding-bottom: 1.875rem !important;\n  }\n  .py-md-5 {\n    padding-top: 3.75rem !important;\n    padding-bottom: 3.75rem !important;\n  }\n  .pt-md-0 {\n    padding-top: 0 !important;\n  }\n  .pt-md-1 {\n    padding-top: 0.3125rem !important;\n  }\n  .pt-md-2 {\n    padding-top: 0.625rem !important;\n  }\n  .pt-md-3 {\n    padding-top: 1.25rem !important;\n  }\n  .pt-md-4 {\n    padding-top: 1.875rem !important;\n  }\n  .pt-md-5 {\n    padding-top: 3.75rem !important;\n  }\n  .pe-md-0 {\n    padding-right: 0 !important;\n  }\n  .pe-md-1 {\n    padding-right: 0.3125rem !important;\n  }\n  .pe-md-2 {\n    padding-right: 0.625rem !important;\n  }\n  .pe-md-3 {\n    padding-right: 1.25rem !important;\n  }\n  .pe-md-4 {\n    padding-right: 1.875rem !important;\n  }\n  .pe-md-5 {\n    padding-right: 3.75rem !important;\n  }\n  .pb-md-0 {\n    padding-bottom: 0 !important;\n  }\n  .pb-md-1 {\n    padding-bottom: 0.3125rem !important;\n  }\n  .pb-md-2 {\n    padding-bottom: 0.625rem !important;\n  }\n  .pb-md-3 {\n    padding-bottom: 1.25rem !important;\n  }\n  .pb-md-4 {\n    padding-bottom: 1.875rem !important;\n  }\n  .pb-md-5 {\n    padding-bottom: 3.75rem !important;\n  }\n  .ps-md-0 {\n    padding-left: 0 !important;\n  }\n  .ps-md-1 {\n    padding-left: 0.3125rem !important;\n  }\n  .ps-md-2 {\n    padding-left: 0.625rem !important;\n  }\n  .ps-md-3 {\n    padding-left: 1.25rem !important;\n  }\n  .ps-md-4 {\n    padding-left: 1.875rem !important;\n  }\n  .ps-md-5 {\n    padding-left: 3.75rem !important;\n  }\n}\n@media (min-width: 1024px) {\n  .d-lg-inline {\n    display: inline !important;\n  }\n  .d-lg-inline-block {\n    display: inline-block !important;\n  }\n  .d-lg-block {\n    display: block !important;\n  }\n  .d-lg-grid {\n    display: grid !important;\n  }\n  .d-lg-inline-grid {\n    display: inline-grid !important;\n  }\n  .d-lg-table {\n    display: table !important;\n  }\n  .d-lg-table-row {\n    display: table-row !important;\n  }\n  .d-lg-table-cell {\n    display: table-cell !important;\n  }\n  .d-lg-flex {\n    display: flex !important;\n  }\n  .d-lg-inline-flex {\n    display: inline-flex !important;\n  }\n  .d-lg-none {\n    display: none !important;\n  }\n  .flex-lg-fill {\n    flex: 1 1 auto !important;\n  }\n  .flex-lg-row {\n    flex-direction: row !important;\n  }\n  .flex-lg-column {\n    flex-direction: column !important;\n  }\n  .flex-lg-row-reverse {\n    flex-direction: row-reverse !important;\n  }\n  .flex-lg-column-reverse {\n    flex-direction: column-reverse !important;\n  }\n  .flex-lg-grow-0 {\n    flex-grow: 0 !important;\n  }\n  .flex-lg-grow-1 {\n    flex-grow: 1 !important;\n  }\n  .flex-lg-shrink-0 {\n    flex-shrink: 0 !important;\n  }\n  .flex-lg-shrink-1 {\n    flex-shrink: 1 !important;\n  }\n  .flex-lg-wrap {\n    flex-wrap: wrap !important;\n  }\n  .flex-lg-nowrap {\n    flex-wrap: nowrap !important;\n  }\n  .flex-lg-wrap-reverse {\n    flex-wrap: wrap-reverse !important;\n  }\n  .justify-content-lg-start {\n    justify-content: flex-start !important;\n  }\n  .justify-content-lg-end {\n    justify-content: flex-end !important;\n  }\n  .justify-content-lg-center {\n    justify-content: center !important;\n  }\n  .justify-content-lg-between {\n    justify-content: space-between !important;\n  }\n  .justify-content-lg-around {\n    justify-content: space-around !important;\n  }\n  .justify-content-lg-evenly {\n    justify-content: space-evenly !important;\n  }\n  .align-items-lg-start {\n    align-items: flex-start !important;\n  }\n  .align-items-lg-end {\n    align-items: flex-end !important;\n  }\n  .align-items-lg-center {\n    align-items: center !important;\n  }\n  .align-items-lg-baseline {\n    align-items: baseline !important;\n  }\n  .align-items-lg-stretch {\n    align-items: stretch !important;\n  }\n  .align-content-lg-start {\n    align-content: flex-start !important;\n  }\n  .align-content-lg-end {\n    align-content: flex-end !important;\n  }\n  .align-content-lg-center {\n    align-content: center !important;\n  }\n  .align-content-lg-between {\n    align-content: space-between !important;\n  }\n  .align-content-lg-around {\n    align-content: space-around !important;\n  }\n  .align-content-lg-stretch {\n    align-content: stretch !important;\n  }\n  .align-self-lg-auto {\n    align-self: auto !important;\n  }\n  .align-self-lg-start {\n    align-self: flex-start !important;\n  }\n  .align-self-lg-end {\n    align-self: flex-end !important;\n  }\n  .align-self-lg-center {\n    align-self: center !important;\n  }\n  .align-self-lg-baseline {\n    align-self: baseline !important;\n  }\n  .align-self-lg-stretch {\n    align-self: stretch !important;\n  }\n  .order-lg-first {\n    order: -1 !important;\n  }\n  .order-lg-0 {\n    order: 0 !important;\n  }\n  .order-lg-1 {\n    order: 1 !important;\n  }\n  .order-lg-2 {\n    order: 2 !important;\n  }\n  .order-lg-3 {\n    order: 3 !important;\n  }\n  .order-lg-4 {\n    order: 4 !important;\n  }\n  .order-lg-5 {\n    order: 5 !important;\n  }\n  .order-lg-last {\n    order: 6 !important;\n  }\n  .m-lg-0 {\n    margin: 0 !important;\n  }\n  .m-lg-1 {\n    margin: 0.3125rem !important;\n  }\n  .m-lg-2 {\n    margin: 0.625rem !important;\n  }\n  .m-lg-3 {\n    margin: 1.25rem !important;\n  }\n  .m-lg-4 {\n    margin: 1.875rem !important;\n  }\n  .m-lg-5 {\n    margin: 3.75rem !important;\n  }\n  .m-lg-auto {\n    margin: auto !important;\n  }\n  .mx-lg-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important;\n  }\n  .mx-lg-1 {\n    margin-right: 0.3125rem !important;\n    margin-left: 0.3125rem !important;\n  }\n  .mx-lg-2 {\n    margin-right: 0.625rem !important;\n    margin-left: 0.625rem !important;\n  }\n  .mx-lg-3 {\n    margin-right: 1.25rem !important;\n    margin-left: 1.25rem !important;\n  }\n  .mx-lg-4 {\n    margin-right: 1.875rem !important;\n    margin-left: 1.875rem !important;\n  }\n  .mx-lg-5 {\n    margin-right: 3.75rem !important;\n    margin-left: 3.75rem !important;\n  }\n  .mx-lg-auto {\n    margin-right: auto !important;\n    margin-left: auto !important;\n  }\n  .my-lg-0 {\n    margin-top: 0 !important;\n    margin-bottom: 0 !important;\n  }\n  .my-lg-1 {\n    margin-top: 0.3125rem !important;\n    margin-bottom: 0.3125rem !important;\n  }\n  .my-lg-2 {\n    margin-top: 0.625rem !important;\n    margin-bottom: 0.625rem !important;\n  }\n  .my-lg-3 {\n    margin-top: 1.25rem !important;\n    margin-bottom: 1.25rem !important;\n  }\n  .my-lg-4 {\n    margin-top: 1.875rem !important;\n    margin-bottom: 1.875rem !important;\n  }\n  .my-lg-5 {\n    margin-top: 3.75rem !important;\n    margin-bottom: 3.75rem !important;\n  }\n  .my-lg-auto {\n    margin-top: auto !important;\n    margin-bottom: auto !important;\n  }\n  .mt-lg-0 {\n    margin-top: 0 !important;\n  }\n  .mt-lg-1 {\n    margin-top: 0.3125rem !important;\n  }\n  .mt-lg-2 {\n    margin-top: 0.625rem !important;\n  }\n  .mt-lg-3 {\n    margin-top: 1.25rem !important;\n  }\n  .mt-lg-4 {\n    margin-top: 1.875rem !important;\n  }\n  .mt-lg-5 {\n    margin-top: 3.75rem !important;\n  }\n  .mt-lg-auto {\n    margin-top: auto !important;\n  }\n  .me-lg-0 {\n    margin-right: 0 !important;\n  }\n  .me-lg-1 {\n    margin-right: 0.3125rem !important;\n  }\n  .me-lg-2 {\n    margin-right: 0.625rem !important;\n  }\n  .me-lg-3 {\n    margin-right: 1.25rem !important;\n  }\n  .me-lg-4 {\n    margin-right: 1.875rem !important;\n  }\n  .me-lg-5 {\n    margin-right: 3.75rem !important;\n  }\n  .me-lg-auto {\n    margin-right: auto !important;\n  }\n  .mb-lg-0 {\n    margin-bottom: 0 !important;\n  }\n  .mb-lg-1 {\n    margin-bottom: 0.3125rem !important;\n  }\n  .mb-lg-2 {\n    margin-bottom: 0.625rem !important;\n  }\n  .mb-lg-3 {\n    margin-bottom: 1.25rem !important;\n  }\n  .mb-lg-4 {\n    margin-bottom: 1.875rem !important;\n  }\n  .mb-lg-5 {\n    margin-bottom: 3.75rem !important;\n  }\n  .mb-lg-auto {\n    margin-bottom: auto !important;\n  }\n  .ms-lg-0 {\n    margin-left: 0 !important;\n  }\n  .ms-lg-1 {\n    margin-left: 0.3125rem !important;\n  }\n  .ms-lg-2 {\n    margin-left: 0.625rem !important;\n  }\n  .ms-lg-3 {\n    margin-left: 1.25rem !important;\n  }\n  .ms-lg-4 {\n    margin-left: 1.875rem !important;\n  }\n  .ms-lg-5 {\n    margin-left: 3.75rem !important;\n  }\n  .ms-lg-auto {\n    margin-left: auto !important;\n  }\n  .p-lg-0 {\n    padding: 0 !important;\n  }\n  .p-lg-1 {\n    padding: 0.3125rem !important;\n  }\n  .p-lg-2 {\n    padding: 0.625rem !important;\n  }\n  .p-lg-3 {\n    padding: 1.25rem !important;\n  }\n  .p-lg-4 {\n    padding: 1.875rem !important;\n  }\n  .p-lg-5 {\n    padding: 3.75rem !important;\n  }\n  .px-lg-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important;\n  }\n  .px-lg-1 {\n    padding-right: 0.3125rem !important;\n    padding-left: 0.3125rem !important;\n  }\n  .px-lg-2 {\n    padding-right: 0.625rem !important;\n    padding-left: 0.625rem !important;\n  }\n  .px-lg-3 {\n    padding-right: 1.25rem !important;\n    padding-left: 1.25rem !important;\n  }\n  .px-lg-4 {\n    padding-right: 1.875rem !important;\n    padding-left: 1.875rem !important;\n  }\n  .px-lg-5 {\n    padding-right: 3.75rem !important;\n    padding-left: 3.75rem !important;\n  }\n  .py-lg-0 {\n    padding-top: 0 !important;\n    padding-bottom: 0 !important;\n  }\n  .py-lg-1 {\n    padding-top: 0.3125rem !important;\n    padding-bottom: 0.3125rem !important;\n  }\n  .py-lg-2 {\n    padding-top: 0.625rem !important;\n    padding-bottom: 0.625rem !important;\n  }\n  .py-lg-3 {\n    padding-top: 1.25rem !important;\n    padding-bottom: 1.25rem !important;\n  }\n  .py-lg-4 {\n    padding-top: 1.875rem !important;\n    padding-bottom: 1.875rem !important;\n  }\n  .py-lg-5 {\n    padding-top: 3.75rem !important;\n    padding-bottom: 3.75rem !important;\n  }\n  .pt-lg-0 {\n    padding-top: 0 !important;\n  }\n  .pt-lg-1 {\n    padding-top: 0.3125rem !important;\n  }\n  .pt-lg-2 {\n    padding-top: 0.625rem !important;\n  }\n  .pt-lg-3 {\n    padding-top: 1.25rem !important;\n  }\n  .pt-lg-4 {\n    padding-top: 1.875rem !important;\n  }\n  .pt-lg-5 {\n    padding-top: 3.75rem !important;\n  }\n  .pe-lg-0 {\n    padding-right: 0 !important;\n  }\n  .pe-lg-1 {\n    padding-right: 0.3125rem !important;\n  }\n  .pe-lg-2 {\n    padding-right: 0.625rem !important;\n  }\n  .pe-lg-3 {\n    padding-right: 1.25rem !important;\n  }\n  .pe-lg-4 {\n    padding-right: 1.875rem !important;\n  }\n  .pe-lg-5 {\n    padding-right: 3.75rem !important;\n  }\n  .pb-lg-0 {\n    padding-bottom: 0 !important;\n  }\n  .pb-lg-1 {\n    padding-bottom: 0.3125rem !important;\n  }\n  .pb-lg-2 {\n    padding-bottom: 0.625rem !important;\n  }\n  .pb-lg-3 {\n    padding-bottom: 1.25rem !important;\n  }\n  .pb-lg-4 {\n    padding-bottom: 1.875rem !important;\n  }\n  .pb-lg-5 {\n    padding-bottom: 3.75rem !important;\n  }\n  .ps-lg-0 {\n    padding-left: 0 !important;\n  }\n  .ps-lg-1 {\n    padding-left: 0.3125rem !important;\n  }\n  .ps-lg-2 {\n    padding-left: 0.625rem !important;\n  }\n  .ps-lg-3 {\n    padding-left: 1.25rem !important;\n  }\n  .ps-lg-4 {\n    padding-left: 1.875rem !important;\n  }\n  .ps-lg-5 {\n    padding-left: 3.75rem !important;\n  }\n}\n@media (min-width: 1280px) {\n  .d-xl-inline {\n    display: inline !important;\n  }\n  .d-xl-inline-block {\n    display: inline-block !important;\n  }\n  .d-xl-block {\n    display: block !important;\n  }\n  .d-xl-grid {\n    display: grid !important;\n  }\n  .d-xl-inline-grid {\n    display: inline-grid !important;\n  }\n  .d-xl-table {\n    display: table !important;\n  }\n  .d-xl-table-row {\n    display: table-row !important;\n  }\n  .d-xl-table-cell {\n    display: table-cell !important;\n  }\n  .d-xl-flex {\n    display: flex !important;\n  }\n  .d-xl-inline-flex {\n    display: inline-flex !important;\n  }\n  .d-xl-none {\n    display: none !important;\n  }\n  .flex-xl-fill {\n    flex: 1 1 auto !important;\n  }\n  .flex-xl-row {\n    flex-direction: row !important;\n  }\n  .flex-xl-column {\n    flex-direction: column !important;\n  }\n  .flex-xl-row-reverse {\n    flex-direction: row-reverse !important;\n  }\n  .flex-xl-column-reverse {\n    flex-direction: column-reverse !important;\n  }\n  .flex-xl-grow-0 {\n    flex-grow: 0 !important;\n  }\n  .flex-xl-grow-1 {\n    flex-grow: 1 !important;\n  }\n  .flex-xl-shrink-0 {\n    flex-shrink: 0 !important;\n  }\n  .flex-xl-shrink-1 {\n    flex-shrink: 1 !important;\n  }\n  .flex-xl-wrap {\n    flex-wrap: wrap !important;\n  }\n  .flex-xl-nowrap {\n    flex-wrap: nowrap !important;\n  }\n  .flex-xl-wrap-reverse {\n    flex-wrap: wrap-reverse !important;\n  }\n  .justify-content-xl-start {\n    justify-content: flex-start !important;\n  }\n  .justify-content-xl-end {\n    justify-content: flex-end !important;\n  }\n  .justify-content-xl-center {\n    justify-content: center !important;\n  }\n  .justify-content-xl-between {\n    justify-content: space-between !important;\n  }\n  .justify-content-xl-around {\n    justify-content: space-around !important;\n  }\n  .justify-content-xl-evenly {\n    justify-content: space-evenly !important;\n  }\n  .align-items-xl-start {\n    align-items: flex-start !important;\n  }\n  .align-items-xl-end {\n    align-items: flex-end !important;\n  }\n  .align-items-xl-center {\n    align-items: center !important;\n  }\n  .align-items-xl-baseline {\n    align-items: baseline !important;\n  }\n  .align-items-xl-stretch {\n    align-items: stretch !important;\n  }\n  .align-content-xl-start {\n    align-content: flex-start !important;\n  }\n  .align-content-xl-end {\n    align-content: flex-end !important;\n  }\n  .align-content-xl-center {\n    align-content: center !important;\n  }\n  .align-content-xl-between {\n    align-content: space-between !important;\n  }\n  .align-content-xl-around {\n    align-content: space-around !important;\n  }\n  .align-content-xl-stretch {\n    align-content: stretch !important;\n  }\n  .align-self-xl-auto {\n    align-self: auto !important;\n  }\n  .align-self-xl-start {\n    align-self: flex-start !important;\n  }\n  .align-self-xl-end {\n    align-self: flex-end !important;\n  }\n  .align-self-xl-center {\n    align-self: center !important;\n  }\n  .align-self-xl-baseline {\n    align-self: baseline !important;\n  }\n  .align-self-xl-stretch {\n    align-self: stretch !important;\n  }\n  .order-xl-first {\n    order: -1 !important;\n  }\n  .order-xl-0 {\n    order: 0 !important;\n  }\n  .order-xl-1 {\n    order: 1 !important;\n  }\n  .order-xl-2 {\n    order: 2 !important;\n  }\n  .order-xl-3 {\n    order: 3 !important;\n  }\n  .order-xl-4 {\n    order: 4 !important;\n  }\n  .order-xl-5 {\n    order: 5 !important;\n  }\n  .order-xl-last {\n    order: 6 !important;\n  }\n  .m-xl-0 {\n    margin: 0 !important;\n  }\n  .m-xl-1 {\n    margin: 0.3125rem !important;\n  }\n  .m-xl-2 {\n    margin: 0.625rem !important;\n  }\n  .m-xl-3 {\n    margin: 1.25rem !important;\n  }\n  .m-xl-4 {\n    margin: 1.875rem !important;\n  }\n  .m-xl-5 {\n    margin: 3.75rem !important;\n  }\n  .m-xl-auto {\n    margin: auto !important;\n  }\n  .mx-xl-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important;\n  }\n  .mx-xl-1 {\n    margin-right: 0.3125rem !important;\n    margin-left: 0.3125rem !important;\n  }\n  .mx-xl-2 {\n    margin-right: 0.625rem !important;\n    margin-left: 0.625rem !important;\n  }\n  .mx-xl-3 {\n    margin-right: 1.25rem !important;\n    margin-left: 1.25rem !important;\n  }\n  .mx-xl-4 {\n    margin-right: 1.875rem !important;\n    margin-left: 1.875rem !important;\n  }\n  .mx-xl-5 {\n    margin-right: 3.75rem !important;\n    margin-left: 3.75rem !important;\n  }\n  .mx-xl-auto {\n    margin-right: auto !important;\n    margin-left: auto !important;\n  }\n  .my-xl-0 {\n    margin-top: 0 !important;\n    margin-bottom: 0 !important;\n  }\n  .my-xl-1 {\n    margin-top: 0.3125rem !important;\n    margin-bottom: 0.3125rem !important;\n  }\n  .my-xl-2 {\n    margin-top: 0.625rem !important;\n    margin-bottom: 0.625rem !important;\n  }\n  .my-xl-3 {\n    margin-top: 1.25rem !important;\n    margin-bottom: 1.25rem !important;\n  }\n  .my-xl-4 {\n    margin-top: 1.875rem !important;\n    margin-bottom: 1.875rem !important;\n  }\n  .my-xl-5 {\n    margin-top: 3.75rem !important;\n    margin-bottom: 3.75rem !important;\n  }\n  .my-xl-auto {\n    margin-top: auto !important;\n    margin-bottom: auto !important;\n  }\n  .mt-xl-0 {\n    margin-top: 0 !important;\n  }\n  .mt-xl-1 {\n    margin-top: 0.3125rem !important;\n  }\n  .mt-xl-2 {\n    margin-top: 0.625rem !important;\n  }\n  .mt-xl-3 {\n    margin-top: 1.25rem !important;\n  }\n  .mt-xl-4 {\n    margin-top: 1.875rem !important;\n  }\n  .mt-xl-5 {\n    margin-top: 3.75rem !important;\n  }\n  .mt-xl-auto {\n    margin-top: auto !important;\n  }\n  .me-xl-0 {\n    margin-right: 0 !important;\n  }\n  .me-xl-1 {\n    margin-right: 0.3125rem !important;\n  }\n  .me-xl-2 {\n    margin-right: 0.625rem !important;\n  }\n  .me-xl-3 {\n    margin-right: 1.25rem !important;\n  }\n  .me-xl-4 {\n    margin-right: 1.875rem !important;\n  }\n  .me-xl-5 {\n    margin-right: 3.75rem !important;\n  }\n  .me-xl-auto {\n    margin-right: auto !important;\n  }\n  .mb-xl-0 {\n    margin-bottom: 0 !important;\n  }\n  .mb-xl-1 {\n    margin-bottom: 0.3125rem !important;\n  }\n  .mb-xl-2 {\n    margin-bottom: 0.625rem !important;\n  }\n  .mb-xl-3 {\n    margin-bottom: 1.25rem !important;\n  }\n  .mb-xl-4 {\n    margin-bottom: 1.875rem !important;\n  }\n  .mb-xl-5 {\n    margin-bottom: 3.75rem !important;\n  }\n  .mb-xl-auto {\n    margin-bottom: auto !important;\n  }\n  .ms-xl-0 {\n    margin-left: 0 !important;\n  }\n  .ms-xl-1 {\n    margin-left: 0.3125rem !important;\n  }\n  .ms-xl-2 {\n    margin-left: 0.625rem !important;\n  }\n  .ms-xl-3 {\n    margin-left: 1.25rem !important;\n  }\n  .ms-xl-4 {\n    margin-left: 1.875rem !important;\n  }\n  .ms-xl-5 {\n    margin-left: 3.75rem !important;\n  }\n  .ms-xl-auto {\n    margin-left: auto !important;\n  }\n  .p-xl-0 {\n    padding: 0 !important;\n  }\n  .p-xl-1 {\n    padding: 0.3125rem !important;\n  }\n  .p-xl-2 {\n    padding: 0.625rem !important;\n  }\n  .p-xl-3 {\n    padding: 1.25rem !important;\n  }\n  .p-xl-4 {\n    padding: 1.875rem !important;\n  }\n  .p-xl-5 {\n    padding: 3.75rem !important;\n  }\n  .px-xl-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important;\n  }\n  .px-xl-1 {\n    padding-right: 0.3125rem !important;\n    padding-left: 0.3125rem !important;\n  }\n  .px-xl-2 {\n    padding-right: 0.625rem !important;\n    padding-left: 0.625rem !important;\n  }\n  .px-xl-3 {\n    padding-right: 1.25rem !important;\n    padding-left: 1.25rem !important;\n  }\n  .px-xl-4 {\n    padding-right: 1.875rem !important;\n    padding-left: 1.875rem !important;\n  }\n  .px-xl-5 {\n    padding-right: 3.75rem !important;\n    padding-left: 3.75rem !important;\n  }\n  .py-xl-0 {\n    padding-top: 0 !important;\n    padding-bottom: 0 !important;\n  }\n  .py-xl-1 {\n    padding-top: 0.3125rem !important;\n    padding-bottom: 0.3125rem !important;\n  }\n  .py-xl-2 {\n    padding-top: 0.625rem !important;\n    padding-bottom: 0.625rem !important;\n  }\n  .py-xl-3 {\n    padding-top: 1.25rem !important;\n    padding-bottom: 1.25rem !important;\n  }\n  .py-xl-4 {\n    padding-top: 1.875rem !important;\n    padding-bottom: 1.875rem !important;\n  }\n  .py-xl-5 {\n    padding-top: 3.75rem !important;\n    padding-bottom: 3.75rem !important;\n  }\n  .pt-xl-0 {\n    padding-top: 0 !important;\n  }\n  .pt-xl-1 {\n    padding-top: 0.3125rem !important;\n  }\n  .pt-xl-2 {\n    padding-top: 0.625rem !important;\n  }\n  .pt-xl-3 {\n    padding-top: 1.25rem !important;\n  }\n  .pt-xl-4 {\n    padding-top: 1.875rem !important;\n  }\n  .pt-xl-5 {\n    padding-top: 3.75rem !important;\n  }\n  .pe-xl-0 {\n    padding-right: 0 !important;\n  }\n  .pe-xl-1 {\n    padding-right: 0.3125rem !important;\n  }\n  .pe-xl-2 {\n    padding-right: 0.625rem !important;\n  }\n  .pe-xl-3 {\n    padding-right: 1.25rem !important;\n  }\n  .pe-xl-4 {\n    padding-right: 1.875rem !important;\n  }\n  .pe-xl-5 {\n    padding-right: 3.75rem !important;\n  }\n  .pb-xl-0 {\n    padding-bottom: 0 !important;\n  }\n  .pb-xl-1 {\n    padding-bottom: 0.3125rem !important;\n  }\n  .pb-xl-2 {\n    padding-bottom: 0.625rem !important;\n  }\n  .pb-xl-3 {\n    padding-bottom: 1.25rem !important;\n  }\n  .pb-xl-4 {\n    padding-bottom: 1.875rem !important;\n  }\n  .pb-xl-5 {\n    padding-bottom: 3.75rem !important;\n  }\n  .ps-xl-0 {\n    padding-left: 0 !important;\n  }\n  .ps-xl-1 {\n    padding-left: 0.3125rem !important;\n  }\n  .ps-xl-2 {\n    padding-left: 0.625rem !important;\n  }\n  .ps-xl-3 {\n    padding-left: 1.25rem !important;\n  }\n  .ps-xl-4 {\n    padding-left: 1.875rem !important;\n  }\n  .ps-xl-5 {\n    padding-left: 3.75rem !important;\n  }\n}\n@media (min-width: 1440px) {\n  .d-xxl-inline {\n    display: inline !important;\n  }\n  .d-xxl-inline-block {\n    display: inline-block !important;\n  }\n  .d-xxl-block {\n    display: block !important;\n  }\n  .d-xxl-grid {\n    display: grid !important;\n  }\n  .d-xxl-inline-grid {\n    display: inline-grid !important;\n  }\n  .d-xxl-table {\n    display: table !important;\n  }\n  .d-xxl-table-row {\n    display: table-row !important;\n  }\n  .d-xxl-table-cell {\n    display: table-cell !important;\n  }\n  .d-xxl-flex {\n    display: flex !important;\n  }\n  .d-xxl-inline-flex {\n    display: inline-flex !important;\n  }\n  .d-xxl-none {\n    display: none !important;\n  }\n  .flex-xxl-fill {\n    flex: 1 1 auto !important;\n  }\n  .flex-xxl-row {\n    flex-direction: row !important;\n  }\n  .flex-xxl-column {\n    flex-direction: column !important;\n  }\n  .flex-xxl-row-reverse {\n    flex-direction: row-reverse !important;\n  }\n  .flex-xxl-column-reverse {\n    flex-direction: column-reverse !important;\n  }\n  .flex-xxl-grow-0 {\n    flex-grow: 0 !important;\n  }\n  .flex-xxl-grow-1 {\n    flex-grow: 1 !important;\n  }\n  .flex-xxl-shrink-0 {\n    flex-shrink: 0 !important;\n  }\n  .flex-xxl-shrink-1 {\n    flex-shrink: 1 !important;\n  }\n  .flex-xxl-wrap {\n    flex-wrap: wrap !important;\n  }\n  .flex-xxl-nowrap {\n    flex-wrap: nowrap !important;\n  }\n  .flex-xxl-wrap-reverse {\n    flex-wrap: wrap-reverse !important;\n  }\n  .justify-content-xxl-start {\n    justify-content: flex-start !important;\n  }\n  .justify-content-xxl-end {\n    justify-content: flex-end !important;\n  }\n  .justify-content-xxl-center {\n    justify-content: center !important;\n  }\n  .justify-content-xxl-between {\n    justify-content: space-between !important;\n  }\n  .justify-content-xxl-around {\n    justify-content: space-around !important;\n  }\n  .justify-content-xxl-evenly {\n    justify-content: space-evenly !important;\n  }\n  .align-items-xxl-start {\n    align-items: flex-start !important;\n  }\n  .align-items-xxl-end {\n    align-items: flex-end !important;\n  }\n  .align-items-xxl-center {\n    align-items: center !important;\n  }\n  .align-items-xxl-baseline {\n    align-items: baseline !important;\n  }\n  .align-items-xxl-stretch {\n    align-items: stretch !important;\n  }\n  .align-content-xxl-start {\n    align-content: flex-start !important;\n  }\n  .align-content-xxl-end {\n    align-content: flex-end !important;\n  }\n  .align-content-xxl-center {\n    align-content: center !important;\n  }\n  .align-content-xxl-between {\n    align-content: space-between !important;\n  }\n  .align-content-xxl-around {\n    align-content: space-around !important;\n  }\n  .align-content-xxl-stretch {\n    align-content: stretch !important;\n  }\n  .align-self-xxl-auto {\n    align-self: auto !important;\n  }\n  .align-self-xxl-start {\n    align-self: flex-start !important;\n  }\n  .align-self-xxl-end {\n    align-self: flex-end !important;\n  }\n  .align-self-xxl-center {\n    align-self: center !important;\n  }\n  .align-self-xxl-baseline {\n    align-self: baseline !important;\n  }\n  .align-self-xxl-stretch {\n    align-self: stretch !important;\n  }\n  .order-xxl-first {\n    order: -1 !important;\n  }\n  .order-xxl-0 {\n    order: 0 !important;\n  }\n  .order-xxl-1 {\n    order: 1 !important;\n  }\n  .order-xxl-2 {\n    order: 2 !important;\n  }\n  .order-xxl-3 {\n    order: 3 !important;\n  }\n  .order-xxl-4 {\n    order: 4 !important;\n  }\n  .order-xxl-5 {\n    order: 5 !important;\n  }\n  .order-xxl-last {\n    order: 6 !important;\n  }\n  .m-xxl-0 {\n    margin: 0 !important;\n  }\n  .m-xxl-1 {\n    margin: 0.3125rem !important;\n  }\n  .m-xxl-2 {\n    margin: 0.625rem !important;\n  }\n  .m-xxl-3 {\n    margin: 1.25rem !important;\n  }\n  .m-xxl-4 {\n    margin: 1.875rem !important;\n  }\n  .m-xxl-5 {\n    margin: 3.75rem !important;\n  }\n  .m-xxl-auto {\n    margin: auto !important;\n  }\n  .mx-xxl-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important;\n  }\n  .mx-xxl-1 {\n    margin-right: 0.3125rem !important;\n    margin-left: 0.3125rem !important;\n  }\n  .mx-xxl-2 {\n    margin-right: 0.625rem !important;\n    margin-left: 0.625rem !important;\n  }\n  .mx-xxl-3 {\n    margin-right: 1.25rem !important;\n    margin-left: 1.25rem !important;\n  }\n  .mx-xxl-4 {\n    margin-right: 1.875rem !important;\n    margin-left: 1.875rem !important;\n  }\n  .mx-xxl-5 {\n    margin-right: 3.75rem !important;\n    margin-left: 3.75rem !important;\n  }\n  .mx-xxl-auto {\n    margin-right: auto !important;\n    margin-left: auto !important;\n  }\n  .my-xxl-0 {\n    margin-top: 0 !important;\n    margin-bottom: 0 !important;\n  }\n  .my-xxl-1 {\n    margin-top: 0.3125rem !important;\n    margin-bottom: 0.3125rem !important;\n  }\n  .my-xxl-2 {\n    margin-top: 0.625rem !important;\n    margin-bottom: 0.625rem !important;\n  }\n  .my-xxl-3 {\n    margin-top: 1.25rem !important;\n    margin-bottom: 1.25rem !important;\n  }\n  .my-xxl-4 {\n    margin-top: 1.875rem !important;\n    margin-bottom: 1.875rem !important;\n  }\n  .my-xxl-5 {\n    margin-top: 3.75rem !important;\n    margin-bottom: 3.75rem !important;\n  }\n  .my-xxl-auto {\n    margin-top: auto !important;\n    margin-bottom: auto !important;\n  }\n  .mt-xxl-0 {\n    margin-top: 0 !important;\n  }\n  .mt-xxl-1 {\n    margin-top: 0.3125rem !important;\n  }\n  .mt-xxl-2 {\n    margin-top: 0.625rem !important;\n  }\n  .mt-xxl-3 {\n    margin-top: 1.25rem !important;\n  }\n  .mt-xxl-4 {\n    margin-top: 1.875rem !important;\n  }\n  .mt-xxl-5 {\n    margin-top: 3.75rem !important;\n  }\n  .mt-xxl-auto {\n    margin-top: auto !important;\n  }\n  .me-xxl-0 {\n    margin-right: 0 !important;\n  }\n  .me-xxl-1 {\n    margin-right: 0.3125rem !important;\n  }\n  .me-xxl-2 {\n    margin-right: 0.625rem !important;\n  }\n  .me-xxl-3 {\n    margin-right: 1.25rem !important;\n  }\n  .me-xxl-4 {\n    margin-right: 1.875rem !important;\n  }\n  .me-xxl-5 {\n    margin-right: 3.75rem !important;\n  }\n  .me-xxl-auto {\n    margin-right: auto !important;\n  }\n  .mb-xxl-0 {\n    margin-bottom: 0 !important;\n  }\n  .mb-xxl-1 {\n    margin-bottom: 0.3125rem !important;\n  }\n  .mb-xxl-2 {\n    margin-bottom: 0.625rem !important;\n  }\n  .mb-xxl-3 {\n    margin-bottom: 1.25rem !important;\n  }\n  .mb-xxl-4 {\n    margin-bottom: 1.875rem !important;\n  }\n  .mb-xxl-5 {\n    margin-bottom: 3.75rem !important;\n  }\n  .mb-xxl-auto {\n    margin-bottom: auto !important;\n  }\n  .ms-xxl-0 {\n    margin-left: 0 !important;\n  }\n  .ms-xxl-1 {\n    margin-left: 0.3125rem !important;\n  }\n  .ms-xxl-2 {\n    margin-left: 0.625rem !important;\n  }\n  .ms-xxl-3 {\n    margin-left: 1.25rem !important;\n  }\n  .ms-xxl-4 {\n    margin-left: 1.875rem !important;\n  }\n  .ms-xxl-5 {\n    margin-left: 3.75rem !important;\n  }\n  .ms-xxl-auto {\n    margin-left: auto !important;\n  }\n  .p-xxl-0 {\n    padding: 0 !important;\n  }\n  .p-xxl-1 {\n    padding: 0.3125rem !important;\n  }\n  .p-xxl-2 {\n    padding: 0.625rem !important;\n  }\n  .p-xxl-3 {\n    padding: 1.25rem !important;\n  }\n  .p-xxl-4 {\n    padding: 1.875rem !important;\n  }\n  .p-xxl-5 {\n    padding: 3.75rem !important;\n  }\n  .px-xxl-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important;\n  }\n  .px-xxl-1 {\n    padding-right: 0.3125rem !important;\n    padding-left: 0.3125rem !important;\n  }\n  .px-xxl-2 {\n    padding-right: 0.625rem !important;\n    padding-left: 0.625rem !important;\n  }\n  .px-xxl-3 {\n    padding-right: 1.25rem !important;\n    padding-left: 1.25rem !important;\n  }\n  .px-xxl-4 {\n    padding-right: 1.875rem !important;\n    padding-left: 1.875rem !important;\n  }\n  .px-xxl-5 {\n    padding-right: 3.75rem !important;\n    padding-left: 3.75rem !important;\n  }\n  .py-xxl-0 {\n    padding-top: 0 !important;\n    padding-bottom: 0 !important;\n  }\n  .py-xxl-1 {\n    padding-top: 0.3125rem !important;\n    padding-bottom: 0.3125rem !important;\n  }\n  .py-xxl-2 {\n    padding-top: 0.625rem !important;\n    padding-bottom: 0.625rem !important;\n  }\n  .py-xxl-3 {\n    padding-top: 1.25rem !important;\n    padding-bottom: 1.25rem !important;\n  }\n  .py-xxl-4 {\n    padding-top: 1.875rem !important;\n    padding-bottom: 1.875rem !important;\n  }\n  .py-xxl-5 {\n    padding-top: 3.75rem !important;\n    padding-bottom: 3.75rem !important;\n  }\n  .pt-xxl-0 {\n    padding-top: 0 !important;\n  }\n  .pt-xxl-1 {\n    padding-top: 0.3125rem !important;\n  }\n  .pt-xxl-2 {\n    padding-top: 0.625rem !important;\n  }\n  .pt-xxl-3 {\n    padding-top: 1.25rem !important;\n  }\n  .pt-xxl-4 {\n    padding-top: 1.875rem !important;\n  }\n  .pt-xxl-5 {\n    padding-top: 3.75rem !important;\n  }\n  .pe-xxl-0 {\n    padding-right: 0 !important;\n  }\n  .pe-xxl-1 {\n    padding-right: 0.3125rem !important;\n  }\n  .pe-xxl-2 {\n    padding-right: 0.625rem !important;\n  }\n  .pe-xxl-3 {\n    padding-right: 1.25rem !important;\n  }\n  .pe-xxl-4 {\n    padding-right: 1.875rem !important;\n  }\n  .pe-xxl-5 {\n    padding-right: 3.75rem !important;\n  }\n  .pb-xxl-0 {\n    padding-bottom: 0 !important;\n  }\n  .pb-xxl-1 {\n    padding-bottom: 0.3125rem !important;\n  }\n  .pb-xxl-2 {\n    padding-bottom: 0.625rem !important;\n  }\n  .pb-xxl-3 {\n    padding-bottom: 1.25rem !important;\n  }\n  .pb-xxl-4 {\n    padding-bottom: 1.875rem !important;\n  }\n  .pb-xxl-5 {\n    padding-bottom: 3.75rem !important;\n  }\n  .ps-xxl-0 {\n    padding-left: 0 !important;\n  }\n  .ps-xxl-1 {\n    padding-left: 0.3125rem !important;\n  }\n  .ps-xxl-2 {\n    padding-left: 0.625rem !important;\n  }\n  .ps-xxl-3 {\n    padding-left: 1.25rem !important;\n  }\n  .ps-xxl-4 {\n    padding-left: 1.875rem !important;\n  }\n  .ps-xxl-5 {\n    padding-left: 3.75rem !important;\n  }\n}\n@media print {\n  .d-print-inline {\n    display: inline !important;\n  }\n  .d-print-inline-block {\n    display: inline-block !important;\n  }\n  .d-print-block {\n    display: block !important;\n  }\n  .d-print-grid {\n    display: grid !important;\n  }\n  .d-print-inline-grid {\n    display: inline-grid !important;\n  }\n  .d-print-table {\n    display: table !important;\n  }\n  .d-print-table-row {\n    display: table-row !important;\n  }\n  .d-print-table-cell {\n    display: table-cell !important;\n  }\n  .d-print-flex {\n    display: flex !important;\n  }\n  .d-print-inline-flex {\n    display: inline-flex !important;\n  }\n  .d-print-none {\n    display: none !important;\n  }\n}\n\n/*# sourceMappingURL=boosted-grid.css.map */\n", "// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px)\n//\n// The map defined in the `$grid-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl xxl))\n//    md\n@function breakpoint-next($name, $breakpoints: $grid-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @if not $n {\n    @error \"breakpoint `#{$name}` not found in `#{$breakpoints}`\";\n  }\n  @return if($n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $grid-breakpoints) {\n  $min: map-get($breakpoints, $name);\n  @return if($min != 0, $min, null);\n}\n\n// Maximum breakpoint width.\n// The maximum value is reduced by 0.02px to work around the limitations of\n// `min-` and `max-` prefixes and viewports with fractional widths.\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(md, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $grid-breakpoints) {\n  $max: map-get($breakpoints, $name);\n  @return if($max and $max > 0, $max - .02, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash in front.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $grid-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $grid-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media that spans multiple breakpoint widths.\n// Makes the @content apply between the min and max breakpoints\n@mixin media-breakpoint-between($lower, $upper, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($lower, $breakpoints);\n  $max: breakpoint-max($upper, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($lower, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($upper, $breakpoints) {\n      @content;\n    }\n  }\n}\n\n// Media between the breakpoint's minimum and maximum widths.\n// No minimum for the smallest breakpoint, and no maximum for the largest one.\n// Makes the @content apply only to the given breakpoint, not viewports any wider or narrower.\n@mixin media-breakpoint-only($name, $breakpoints: $grid-breakpoints) {\n  $min:  breakpoint-min($name, $breakpoints);\n  $next: breakpoint-next($name, $breakpoints);\n  $max:  breakpoint-max($next, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($name, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($next, $breakpoints) {\n      @content;\n    }\n  }\n}\n", "@import \"color-palette\";\n\n// Variables\n//\n// Variables should follow the `$component-state-property-size` formula for\n// consistent naming. Ex: $nav-link-disabled-color and $modal-content-box-shadow-xs.\n\n// Color system\n\n// scss-docs-start gray-color-variables\n$white:    $ods-white-100 !default;\n$gray-100: #fafafa !default;\n$gray-200: #f6f6f6 !default;\n$gray-300: $ods-gray-200 !default;\n$gray-400: $ods-gray-300 !default;\n$gray-500: $ods-gray-400 !default;\n$gray-600: $ods-gray-500 !default;\n$gray-700: $ods-gray-600 !default;\n$gray-800: $ods-gray-700 !default;\n$gray-900: $ods-gray-800 !default;\n$gray-950: $ods-gray-900 !default;\n$black:    $ods-black-900 !default;\n// scss-docs-end gray-color-variables\n\n// fusv-disable\n// scss-docs-start gray-colors-map\n$grays: (\n  \"100\": $gray-100,\n  \"200\": $gray-200,\n  \"300\": $gray-300,\n  \"400\": $gray-400,\n  \"500\": $gray-500,\n  \"600\": $gray-600,\n  \"700\": $gray-700,\n  \"800\": $gray-800,\n  \"900\": $gray-900,\n  \"950\": $gray-950,\n) !default;\n// scss-docs-end gray-colors-map\n// fusv-enable\n\n// Boosted mod\n// scss-docs-start brand-colors\n//// Functional colors\n$functional-green:  $ods-forest-200 !default;\n$functional-blue:   $ods-water-200 !default;\n$functional-yellow: $ods-sun-100 !default;\n$functional-red:    $ods-fire-200 !default;\n//// Supporting colors\n$supporting-blue:   $ods-blue-300 !default;\n$supporting-yellow: $ods-yellow-300 !default;\n$supporting-green:  $ods-green-300 !default;\n$supporting-purple: $ods-purple-300 !default;\n$supporting-pink:   $ods-pink-300 !default;\n$supporting-orange: $ods-orange-100 !default;\n// scss-docs-end brand-colors\n// End mod\n\n\n// scss-docs-start color-variables\n$blue:    $functional-blue !default;\n$indigo:  $supporting-purple !default;\n$purple:  $supporting-purple !default;\n$pink:    $supporting-pink !default;\n$red:     $functional-red !default;\n$orange:  $ods-orange-200 !default;\n$yellow:  $functional-yellow !default;\n$green:   $functional-green !default;\n$teal:    $supporting-green !default;\n$cyan:    $supporting-blue !default;\n// scss-docs-end color-variables\n\n// scss-docs-start colors-map\n$colors: (\n  \"blue\":       $blue,\n  \"indigo\":     $indigo,\n  \"purple\":     $purple,\n  \"pink\":       $pink,\n  \"red\":        $red,\n  \"orange\":     $orange,\n  \"yellow\":     $yellow,\n  \"green\":      $green,\n  \"teal\":       $teal,\n  \"cyan\":       $cyan,\n  \"black\":      $black,\n  \"white\":      $white,\n  \"gray\":       $gray-600,\n  \"gray-dark\":  $gray-800\n) !default;\n// scss-docs-end colors-map\n\n// The contrast ratio to reach against white, to determine if color changes from \"light\" to \"dark\". Acceptable values for WCAG 2.2 are 3, 4.5 and 7.\n// See https://www.w3.org/TR/WCAG/#contrast-minimum\n$min-contrast-ratio:   4.5 !default;\n\n// Customize the light and dark text colors for use in our YIQ color contrast function.\n$color-contrast-dark:  $black !default;\n$color-contrast-light: $white !default;\n\n// fusv-disable\n$blue-100: tint-color($blue, 80%) !default;\n$blue-200: tint-color($blue, 60%) !default;\n$blue-300: tint-color($blue, 40%) !default;\n$blue-400: tint-color($blue, 20%) !default;\n$blue-500: $blue !default;\n$blue-600: shade-color($blue, 20%) !default;\n$blue-700: shade-color($blue, 40%) !default;\n$blue-800: shade-color($blue, 60%) !default;\n$blue-900: shade-color($blue, 80%) !default;\n\n$indigo-100: $ods-purple-100 !default;\n$indigo-200: $ods-purple-200 !default;\n$indigo-300: $ods-purple-300 !default;\n$indigo-400: $ods-purple-400 !default;\n$indigo-500: $ods-purple-500 !default;\n$indigo-600: $ods-purple-600 !default;\n$indigo-700: shade-color($ods-purple-600, 20%) !default;\n$indigo-800: shade-color($ods-purple-600, 40%) !default;\n$indigo-900: shade-color($ods-purple-600, 60%) !default;\n\n$purple-100: $ods-purple-100 !default;\n$purple-200: $ods-purple-200 !default;\n$purple-300: $ods-purple-300 !default;\n$purple-400: $ods-purple-400 !default;\n$purple-500: $ods-purple-500 !default;\n$purple-600: $ods-purple-600 !default;\n$purple-700: shade-color($ods-purple-600, 20%) !default;\n$purple-800: shade-color($ods-purple-600, 40%) !default;\n$purple-900: shade-color($ods-purple-600, 60%) !default;\n\n$pink-100: $ods-pink-100 !default;\n$pink-200: $ods-pink-200 !default;\n$pink-300: $ods-pink-300 !default;\n$pink-400: $ods-pink-400 !default;\n$pink-500: $ods-pink-500 !default;\n$pink-600: $ods-pink-600 !default;\n$pink-700: shade-color($ods-pink-600, 20%) !default;\n$pink-800: shade-color($ods-pink-600, 40%) !default;\n$pink-900: shade-color($ods-pink-600, 60%) !default;\n\n$red-100: tint-color($red, 80%) !default;\n$red-200: tint-color($red, 60%) !default;\n$red-300: tint-color($red, 40%) !default;\n$red-400: tint-color($red, 20%) !default;\n$red-500: $red !default;\n$red-600: shade-color($red, 20%) !default;\n$red-700: shade-color($red, 40%) !default;\n$red-800: shade-color($red, 60%) !default;\n$red-900: shade-color($red, 80%) !default;\n\n$orange-100: tint-color($orange, 80%) !default;\n$orange-200: tint-color($orange, 60%) !default;\n$orange-300: tint-color($orange, 40%) !default;\n$orange-400: tint-color($orange, 20%) !default;\n$orange-500: $orange !default;\n$orange-600: shade-color($orange, 20%) !default;\n$orange-700: shade-color($orange, 40%) !default;\n$orange-800: shade-color($orange, 60%) !default;\n$orange-900: shade-color($orange, 80%) !default;\n\n$yellow-100: $ods-yellow-100 !default;\n$yellow-200: $ods-yellow-200 !default;\n$yellow-300: $ods-yellow-300 !default;\n$yellow-400: $ods-yellow-400 !default;\n$yellow-500: $ods-yellow-500 !default;\n$yellow-600: $ods-yellow-600 !default;\n$yellow-700: shade-color($ods-yellow-600, 20%) !default;\n$yellow-800: shade-color($ods-yellow-600, 40%) !default;\n$yellow-900: shade-color($ods-yellow-600, 60%) !default;\n\n$green-100: tint-color($green, 80%) !default;\n$green-200: tint-color($green, 60%) !default;\n$green-300: tint-color($green, 40%) !default;\n$green-400: tint-color($green, 20%) !default;\n$green-500: $green !default;\n$green-600: shade-color($green, 20%) !default;\n$green-700: shade-color($green, 40%) !default;\n$green-800: shade-color($green, 60%) !default;\n$green-900: shade-color($green, 80%) !default;\n\n$teal-100: $ods-green-100 !default;\n$teal-200: $ods-green-200 !default;\n$teal-300: $ods-green-300 !default;\n$teal-400: $ods-green-400 !default;\n$teal-500: $ods-green-500 !default;\n$teal-600: $ods-green-600 !default;\n$teal-700: shade-color($ods-green-600, 20%) !default;\n$teal-800: shade-color($ods-green-600, 40%) !default;\n$teal-900: shade-color($ods-green-600, 60%) !default;\n\n$cyan-100: $ods-blue-100 !default;\n$cyan-200: $ods-blue-200 !default;\n$cyan-300: $ods-blue-300 !default;\n$cyan-400: $ods-blue-400 !default;\n$cyan-500: $ods-blue-500 !default;\n$cyan-600: $ods-blue-600 !default;\n$cyan-700: shade-color($ods-blue-600, 20%) !default;\n$cyan-800: shade-color($ods-blue-600, 40%) !default;\n$cyan-900: shade-color($ods-blue-600, 60%) !default;\n\n$blues: (\n  \"blue-100\": $blue-100,\n  \"blue-200\": $blue-200,\n  \"blue-300\": $blue-300,\n  \"blue-400\": $blue-400,\n  \"blue-500\": $blue-500,\n  \"blue-600\": $blue-600,\n  \"blue-700\": $blue-700,\n  \"blue-800\": $blue-800,\n  \"blue-900\": $blue-900\n) !default;\n\n$indigos: (\n  \"indigo-100\": $indigo-100,\n  \"indigo-200\": $indigo-200,\n  \"indigo-300\": $indigo-300,\n  \"indigo-400\": $indigo-400,\n  \"indigo-500\": $indigo-500,\n  \"indigo-600\": $indigo-600,\n  \"indigo-700\": $indigo-700,\n  \"indigo-800\": $indigo-800,\n  \"indigo-900\": $indigo-900\n) !default;\n\n$purples: (\n  \"purple-100\": $purple-100,\n  \"purple-200\": $purple-200,\n  \"purple-300\": $purple-300,\n  \"purple-400\": $purple-400,\n  \"purple-500\": $purple-500,\n  \"purple-600\": $purple-600,\n  \"purple-700\": $purple-700,\n  \"purple-800\": $purple-800,\n  \"purple-900\": $purple-900\n) !default;\n\n$pinks: (\n  \"pink-100\": $pink-100,\n  \"pink-200\": $pink-200,\n  \"pink-300\": $pink-300,\n  \"pink-400\": $pink-400,\n  \"pink-500\": $pink-500,\n  \"pink-600\": $pink-600,\n  \"pink-700\": $pink-700,\n  \"pink-800\": $pink-800,\n  \"pink-900\": $pink-900\n) !default;\n\n$reds: (\n  \"red-100\": $red-100,\n  \"red-200\": $red-200,\n  \"red-300\": $red-300,\n  \"red-400\": $red-400,\n  \"red-500\": $red-500,\n  \"red-600\": $red-600,\n  \"red-700\": $red-700,\n  \"red-800\": $red-800,\n  \"red-900\": $red-900\n) !default;\n\n$oranges: (\n  \"orange-100\": $orange-100,\n  \"orange-200\": $orange-200,\n  \"orange-300\": $orange-300,\n  \"orange-400\": $orange-400,\n  \"orange-500\": $orange-500,\n  \"orange-600\": $orange-600,\n  \"orange-700\": $orange-700,\n  \"orange-800\": $orange-800,\n  \"orange-900\": $orange-900\n) !default;\n\n$yellows: (\n  \"yellow-100\": $yellow-100,\n  \"yellow-200\": $yellow-200,\n  \"yellow-300\": $yellow-300,\n  \"yellow-400\": $yellow-400,\n  \"yellow-500\": $yellow-500,\n  \"yellow-600\": $yellow-600,\n  \"yellow-700\": $yellow-700,\n  \"yellow-800\": $yellow-800,\n  \"yellow-900\": $yellow-900\n) !default;\n\n$greens: (\n  \"green-100\": $green-100,\n  \"green-200\": $green-200,\n  \"green-300\": $green-300,\n  \"green-400\": $green-400,\n  \"green-500\": $green-500,\n  \"green-600\": $green-600,\n  \"green-700\": $green-700,\n  \"green-800\": $green-800,\n  \"green-900\": $green-900\n) !default;\n\n$teals: (\n  \"teal-100\": $teal-100,\n  \"teal-200\": $teal-200,\n  \"teal-300\": $teal-300,\n  \"teal-400\": $teal-400,\n  \"teal-500\": $teal-500,\n  \"teal-600\": $teal-600,\n  \"teal-700\": $teal-700,\n  \"teal-800\": $teal-800,\n  \"teal-900\": $teal-900\n) !default;\n\n$cyans: (\n  \"cyan-100\": $cyan-100,\n  \"cyan-200\": $cyan-200,\n  \"cyan-300\": $cyan-300,\n  \"cyan-400\": $cyan-400,\n  \"cyan-500\": $cyan-500,\n  \"cyan-600\": $cyan-600,\n  \"cyan-700\": $cyan-700,\n  \"cyan-800\": $cyan-800,\n  \"cyan-900\": $cyan-900\n) !default;\n// fusv-enable\n\n// scss-docs-start theme-color-variables\n$primary:       $orange !default;\n$secondary:     $black !default;\n$success:       $green !default;\n$info:          $blue !default;\n$warning:       $yellow !default;\n$danger:        $red !default;\n$light:         $gray-500 !default;\n$dark:          $black !default;\n// scss-docs-end theme-color-variables\n\n// scss-docs-start theme-colors-map\n$theme-colors: (\n  \"primary\":    $primary,\n  \"secondary\":  $secondary,\n  \"success\":    $success,\n  \"info\":       $info,\n  \"warning\":    $warning,\n  \"danger\":     $danger,\n  \"light\":      $light,\n  \"dark\":       $dark\n) !default;\n// scss-docs-end theme-colors-map\n\n// scss-docs-start theme-text-variables\n$primary-text-emphasis:   $primary !default; // Boosted mod: instead of `shade-color($primary, 60%)`\n$secondary-text-emphasis: $secondary !default; // Boosted mod: instead of `shade-color($secondary, 60%)`\n$success-text-emphasis:   $success !default; // Boosted mod: instead of `shade-color($success, 60%)`\n$info-text-emphasis:      $info !default; // Boosted mod: instead of `shade-color($info, 60%)`\n$warning-text-emphasis:   $warning !default; // Boosted mod: instead of `shade-color($warning, 60%)`\n$danger-text-emphasis:    $danger !default; // Boosted mod: instead of `shade-color($danger, 60%)`\n$light-text-emphasis:     $light !default; // Boosted mod: instead of `$gray-700`\n$dark-text-emphasis:      $dark !default; // Boosted mod: instead of `$gray-700`\n// scss-docs-end theme-text-variables\n\n// scss-docs-start theme-bg-subtle-variables\n$primary-bg-subtle:       $primary !default; // Boosted mod: instead of `tint-color($primary, 80%)`\n$secondary-bg-subtle:     $secondary !default; // Boosted mod: instead of `tint-color($secondary, 80%)`\n$success-bg-subtle:       $success !default; // Boosted mod: instead of `tint-color($success, 80%)`\n$info-bg-subtle:          $info !default; // Boosted mod: instead of `tint-color($info, 80%)`\n$warning-bg-subtle:       $warning !default; // Boosted mod: instead of `tint-color($warning, 80%)`\n$danger-bg-subtle:        $danger !default; // Boosted mod: instead of `tint-color($danger, 80%)`\n$light-bg-subtle:         $light !default; // Boosted mod: instead of `mix($gray-100, $white)`\n$dark-bg-subtle:          $dark !default; // Boosted mod: instead of `$gray-400`\n// scss-docs-end theme-bg-subtle-variables\n\n// scss-docs-start theme-border-subtle-variables\n$primary-border-subtle:   $primary !default; // Boosted mod: instead of `tint-color($primary, 60%)`\n$secondary-border-subtle: $secondary !default; // Boosted mod: instead of `tint-color($secondary, 60%)`\n$success-border-subtle:   $success !default; // Boosted mod: instead of `tint-color($success, 60%)`\n$info-border-subtle:      $info !default; // Boosted mod: instead of `tint-color($info, 60%)`\n$warning-border-subtle:   $warning !default; // Boosted mod: instead of `tint-color($warning, 60%)`\n$danger-border-subtle:    $danger !default; // Boosted mod: instead of `tint-color($danger, 60%)`\n$light-border-subtle:     $light !default; // Boosted mod: instead of `$gray-200`\n$dark-border-subtle:      $dark !default; // Boosted mod: instead of `$gray-500`\n// scss-docs-end theme-border-subtle-variables\n\n// Characters which are escaped by the escape-svg function\n$escaped-characters: (\n  (\"<\", \"%3c\"),\n  (\">\", \"%3e\"),\n  (\"#\", \"%23\"),\n  (\"(\", \"%28\"),\n  (\")\", \"%29\"),\n) !default;\n\n// Boosted mod\n//// SVG as Data-URi\n$chevron-icon:          url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 9 14'><path d='M9 2 7 0 0 7l7 7 2-2-5-5 5-5z'/></svg>\") !default;\n$cross-icon:            url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30' fill='#{$black}'><path d='m15 17.121-8.132 8.132-2.121-2.12L12.879 15 4.747 6.868l2.12-2.121L15 12.879l8.132-8.132 2.12 2.121L17.122 15l8.132 8.132-2.121 2.12L15 17.123z'/></svg>\") !default;\n$cross-icon-stroke:     url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='116 116 767 767' fill='#{$black}'><path d='M817.493 676.165a49.977 49.977 0 0 1 0 70.664l-70.664 70.664a49.977 49.977 0 0 1-70.664 0L499.5 640.828 322.835 817.493a49.977 49.977 0 0 1-70.664 0l-70.664-70.664a49.977 49.977 0 0 1 0-70.664L358.172 499.5 181.507 322.835a49.977 49.977 0 0 1 0-70.664l70.664-70.664a49.977 49.977 0 0 1 70.664 0L499.5 358.172l176.665-176.665a49.977 49.977 0 0 1 70.664 0l70.664 70.664a49.977 49.977 0 0 1 0 70.664L640.828 499.5Z'/></svg>\") !default;\n$check-icon:            url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 15 12'><path fill='#{$black}' d='M13 0 5 8 2 5 0 7l5 5L15 2z'/></svg>\") !default;\n$burger-icon:           url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30' fill='#{$black}'><path d='M28 21v2H2v-2h26Zm0-7v2H2v-2h26Zm0-7v2H2V7h26Z'/></svg>\") !default;\n$burger-icon-small:     url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 25 25' fill='#{$black}'><path d='M2 19h21v-2H2v2Zm0-6h21v-2H2v2Zm0-6h21V5H2v2Z'/></svg>\") !default;\n$success-icon:          url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 125 125'><path fill='#{$success}' d='M62.5 0a62.5 62.5 0 1 0 0 125 62.5 62.5 0 0 0 0-125zm28 29.4c3.3 0 6 2.6 6 5.9a5.9 5.9 0 0 1-1.3 3.7L57.7 86a5.8 5.8 0 0 1-9.1 0L29.8 62.5c-.8-1-1.2-2.3-1.2-3.7a5.9 5.9 0 0 1 1.7-4.1l2.3-2.4a5.8 5.8 0 0 1 4.2-1.7 5.8 5.8 0 0 1 3.8 1.4L52 64.7 86.6 31a5.8 5.8 0 0 1 4-1.6z'/></svg>\") !default;\n$info-icon:             url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 125 125'><path fill='#{$info}' d='M62.5 0a62.5 62.5 0 1 0 0 125 62.5 62.5 0 0 0 0-125zm0 14.7a11 11 0 1 1 0 22 11 11 0 0 1 0-22zM47.8 44.1h25.7v46.2c0 4.7 1.3 6.5 1.8 7.2.8 1 2.3 1.5 4.8 1.6h.8v3.8H47.8v-3.7h.8c2.3-.1 4-.8 5-2 .4-.4 1-2 1-7V57c0-4.8-.6-6.6-1.2-7.3-.8-1-2.4-1.5-4.9-1.6h-.7V44z'/></svg>\") !default;\n$warning-icon:          url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='75 125 850 750'><path fill='#{$warning}' fill-rule='evenodd' d='M828.111 875H170.889A93.71 93.71 0 0 1 89.8 734.017l-.008-.005.772-1.321.036-.062 327.8-561.117h.008a93.94 93.94 0 0 1 162.182 0h.008l328.612 562.5-.009.005A93.709 93.709 0 0 1 828.111 875ZM500.5 775a47.5 47.5 0 1 1 47.507-47.5A47.5 47.5 0 0 1 500.5 775Zm47.368-424.038-15.7 258.121c-.009 17.482-14.185 24.05-31.671 24.05s-31.662-6.568-31.671-24.05l-15.74-258.716c-.057-.949-.094-1.9-.094-2.867a47.507 47.507 0 0 1 95.014 0 47.782 47.782 0 0 1-.138 3.462Z'/></svg>\") !default;\n$warning-icon-filled:   url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='75 125 850 750'><path fill='#{$warning}' d='M828.111 875H170.889A93.71 93.71 0 0 1 89.8 734.017l-.008-.005.772-1.321.036-.062 327.8-561.117h.008a93.94 93.94 0 0 1 162.182 0h.008l328.612 562.5-.009.005A93.709 93.709 0 0 1 828.111 875Z'/><path fill='#{$black}' d='M500.5 775a47.5 47.5 0 1 1 47.507-47.5A47.5 47.5 0 0 1 500.5 775Zm47.368-424.038-15.7 258.121c-.009 17.482-14.185 24.05-31.671 24.05s-31.662-6.568-31.671-24.05l-15.74-258.716c-.057-.949-.094-1.9-.094-2.867a47.507 47.507 0 0 1 95.014 0 47.782 47.782 0 0 1-.138 3.462Z'/></svg>\") !default;\n$danger-icon:           url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 140 125'><path fill='#{$danger}' d='M70.3 0c-5.8 0-10.8 3.1-13.5 7.8L2.3 101.3l-.2.2A15.6 15.6 0 0 0 15.6 125H125a15.6 15.6 0 0 0 13.5-23.5L83.8 7.8A15.6 15.6 0 0 0 70.3 0zm19.2 50a6.4 6.4 0 0 1 4.4 1.9 6.4 6.4 0 0 1 0 9L79.4 75.6l15 15a6.4 6.4 0 0 1 0 9.2 6.4 6.4 0 0 1-4.5 1.9 6.4 6.4 0 0 1-4.6-2l-15-15-15 15a6.4 6.4 0 0 1-4.6 2 6.4 6.4 0 0 1-4.6-2 6.4 6.4 0 0 1 0-9l15-15L46.8 61a6.4 6.4 0 1 1 9-9.1l14.6 14.5L84.8 52a6.4 6.4 0 0 1 4.7-1.9z'/></svg>\") !default;\n$add-icon:              url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 14 14'><path fill='currentColor' d='M14 6H8V0H6v6H0v2h6v6h2V8h6V6z'/></svg>\") !default;\n$remove-icon:           url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 14 2'><path fill='currentColor' d='M0 0h14v2H0z'/></svg>\") !default;\n$add-icon-sm:           url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 10 10'><path d='M10 4H6V0H4v4H0v2h4v4h2V6h4V4z'/></svg>\") !default;\n$remove-icon-sm:        url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 10 2'><path d='M0 0h10v2H0z'/></svg>\") !default;\n$play-icon:             url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'><path fill='currentColor' d='M12.138 16.8 3 21.6V2.4l9.138 4.8L21 12z' fill-rule='evenodd'/></svg>\") !default;\n$pause-icon:            url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'><path fill='currentColor' d='M10.2 21H3V3h7.2v18ZM21 21h-7.2V3H21v18Z' fill-rule='evenodd'/></svg>\") !default;\n$helper-icon:           url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1000 1000'><path fill='#{$info}' d='M500 75C265.277 75 75 265.279 75 500s190.277 425 425 425 425-190.279 425-425S734.721 75 500 75Zm30.8 680.633a54.149 54.149 0 0 1-37.069 14.267 56.1 56.1 0 0 1-37.95-14.085q-16.233-14.079-16.226-39.384 0-22.458 15.679-37.781t38.5-15.324q22.464 0 37.789 15.324t15.324 37.781q-.003 24.951-16.047 39.202Zm133.12-330.046a162.251 162.251 0 0 1-29.23 39.38q-16.92 16.574-60.772 55.785A248.236 248.236 0 0 0 554.5 540.18a79.146 79.146 0 0 0-10.868 15.32 75.1 75.1 0 0 0-5.529 13.9q-1.953 6.954-5.879 24.42-6.762 37.068-42.413 37.069-18.541 0-31.192-12.119t-12.647-36q0-29.945 9.262-51.863a131.346 131.346 0 0 1 24.6-38.491q15.319-16.577 41.35-39.4 22.789-19.946 32.962-30.113a101.987 101.987 0 0 0 17.105-22.632 54.714 54.714 0 0 0 6.955-27.086q0-28.517-21.213-48.119t-54.7-19.6q-39.213 0-57.743 19.783t-31.359 58.272Q401.059 423.8 367.2 423.8q-19.964 0-33.683-14.079T319.8 379.248q0-33.852 21.739-68.606t63.447-57.562q41.7-22.814 97.3-22.813 51.66 0 91.244 19.069 39.549 19.074 61.119 51.856t21.571 71.286q.004 30.297-12.297 53.109Z'/></svg>\") !default;\n\n//// SVG used several times\n$svg-as-custom-props: (\n  \"chevron\": $chevron-icon,\n  \"close\":   $cross-icon-stroke,\n  \"check\":   $check-icon,\n  \"success\": $success-icon,\n  \"error\":   $danger-icon\n) !default;\n\n//// Filters\n// see https://codepen.io/sosuke/pen/Pjoqqp\n$invert-filter:         invert(1) !default;\n// fusv-disable\n$orange-filter:         invert(46%) sepia(60%) saturate(2878%) hue-rotate(6deg) brightness(98%) contrast(104%) !default; // Deprecated in v5.3.3\n// fusv-enable\n// End mod\n\n// Options\n//\n// Quickly modify global styling by enabling or disabling optional features.\n\n$enable-caret:                true !default;\n$enable-rounded:              false !default;\n$enable-shadows:              false !default;\n$enable-gradients:            false !default;\n$enable-transitions:          true !default;\n$enable-reduced-motion:       true !default;\n$enable-smooth-scroll:        true !default;\n$enable-grid-classes:         true !default;\n$enable-container-classes:    true !default;\n$enable-cssgrid:              false !default;\n$enable-button-pointers:      true !default;\n$enable-rfs:                  false !default;\n$enable-validation-icons:     true !default;\n$enable-negative-margins:     false !default;\n$enable-deprecation-messages: false !default;\n$enable-important-utilities:  true !default;\n$enable-fixed-header:         true !default; // Boosted mod: used to apply scroll-padding-top\n\n$enable-dark-mode:            true !default;\n$color-mode-type:             data !default; // `data` or `media-query`\n\n// Prefix for :root CSS variables\n\n$variable-prefix:             bs- !default; // Deprecated in v5.2.0 for the shorter `$prefix`\n$prefix:                      $variable-prefix !default;\n// fusv-disable\n$boosted-variable-prefix:     o- !default; // Deprecated in v5.2.0 for the shorter `$prefix`\n$boosted-prefix:              $boosted-variable-prefix !default; // Deprecated in v5.3.0 for the shorter `$prefix`\n// fusv-enable\n\n// Gradient\n//\n// The gradient which is added to components if `$enable-gradients` is `true`\n// This gradient is also added to elements with `.bg-gradient`\n// scss-docs-start variable-gradient\n$gradient: linear-gradient(180deg, rgba($white, .15), rgba($white, 0)) !default;\n// scss-docs-end variable-gradient\n\n// Spacing\n//\n// Control the default styling of most Boosted elements by modifying these\n// variables. Mostly focused on spacing.\n// You can add more entries to the $spacers map, should you need more variation.\n\n// scss-docs-start spacer-variables-maps\n$spacer: 1.25rem !default; // Boosted mod\n$spacers: (\n  0: 0,\n  1: $spacer * .25,\n  2: $spacer * .5,\n  3: $spacer,\n  4: $spacer * 1.5,\n  5: $spacer * 3,\n) !default;\n// scss-docs-end spacer-variables-maps\n\n$target-size: 2.75rem !default; // Boosted mod: minimum target size (44×44px)\n\n\n// Position\n//\n// Define the edge positioning anchors of the position utilities.\n\n// scss-docs-start position-map\n$position-values: (\n  0: 0,\n  50: 50%,\n  100: 100%\n) !default;\n// scss-docs-end position-map\n\n// Body\n//\n// Settings for the `<body>` element.\n\n$body-text-align:           null !default;\n$body-color:                $black !default; // Boosted mod: instead of `$gray-900`\n$body-bg:                   $white !default;\n\n$body-secondary-color:      $gray-700 !default; // Boosted mod: instead of `rgba($body-color, .75)`\n$body-secondary-bg:         $gray-300 !default; // Boosted mod: instead of `$gray-200`\n\n$body-tertiary-color:       $gray-500 !default; // Boosted mod: instead of `rgba($body-color, .5)`\n$body-tertiary-bg:          $gray-100 !default;\n\n$body-emphasis-color:       $black !default;\n\n// Links\n//\n// Style anchor elements.\n\n$link-color:                              $black !default; // Boosted mod\n$link-decoration:                         underline !default;\n$link-shade-percentage:                   20% !default;\n$link-hover-color:                        $primary !default; // Boosted mod\n$link-hover-decoration:                   null !default;\n\n$stretched-link-pseudo-element:           after !default;\n$stretched-link-z-index:                  1 !default;\n\n// Boosted mod\n$linked-chevron-icon-width:   subtract(.5rem, 1px) !default;\n$linked-chevron-icon-height:  $spacer * .5 !default;\n$linked-chevron-transform:    rotate(.5turn) translateY(1px) !default;\n$linked-chevron-margin-left:  $spacer * .25 !default;\n// End mod\n\n// Icon links\n// scss-docs-start icon-link-variables\n$icon-link-gap:               .3125rem !default; // Boosted mod: instead of `.375rem`\n$icon-link-underline-offset:  .25em !default;\n$icon-link-icon-size:         1em !default;\n$icon-link-icon-transition:   .2s ease-in-out transform !default;\n$icon-link-icon-transform:    translate3d(.25em, 0, 0) !default;\n// scss-docs-end icon-link-variables\n\n\n// Paragraphs\n//\n// Style p element.\n\n$paragraph-margin-bottom:   1rem !default;\n\n\n// Grid breakpoints\n//\n// Define the minimum dimensions at which your layout will change,\n// adapting to different screen sizes, for use in media queries.\n\n// scss-docs-start grid-breakpoints\n$grid-breakpoints: (\n  xs: 0,\n  sm: 480px,\n  md: 768px,\n  lg: 1024px,\n  xl: 1280px,\n  xxl: 1440px\n) !default;\n// scss-docs-end grid-breakpoints\n\n@include _assert-ascending($grid-breakpoints, \"$grid-breakpoints\");\n@include _assert-starts-at-zero($grid-breakpoints, \"$grid-breakpoints\");\n\n\n// Grid containers\n//\n// Define the maximum width of `.container` for different screen sizes.\n\n// scss-docs-start container-max-widths\n$container-max-widths: (\n  xs: 312px,\n  sm: 468px,\n  md: 744px,\n  lg: 960px,\n  xl: 1200px,\n  xxl: 1320px\n) !default;\n\n// Boosted mod\n$container-fluid-margin: (\n  xs: 4px,\n  sm: 6px,\n  md: 12px,\n  lg: 32px,\n  xl: 40px,\n  xxl: 60px\n) !default;\n// End mod\n// scss-docs-end container-max-widths\n\n@include _assert-ascending($container-max-widths, \"$container-max-widths\");\n\n\n// Grid columns\n//\n// Set the number of columns and specify the width of the gutters.\n\n$grid-columns:                12 !default;\n$grid-gutter-width:           $spacer !default;\n$grid-gutter-breakpoint:      \"md\" !default; // Boosted mod: gutter depends on breakpoint\n$grid-row-columns:            6 !default;\n\n// Container padding\n\n$container-padding-x: $grid-gutter-width !default;\n\n\n// Components\n//\n// Define common padding and border radius sizes and more.\n\n// scss-docs-start border-variables\n$border-width:                .125rem !default;\n$border-widths: (\n  1: $border-width * .5,\n  2: $border-width,\n  3: $border-width * 1.5,\n  4: $border-width * 2,\n  5: $border-width * 2.5\n) !default;\n$border-style:                solid !default;\n$border-color:                $black !default; // Boosted mod: instead of `$gray-300`\n$border-color-subtle:         $gray-500 !default; // Boosted mod\n$border-color-translucent:    rgba($black, .175) !default;\n// scss-docs-end border-variables\n\n// scss-docs-start border-radius-variables\n$border-radius:               .375rem !default;\n$border-radius-sm:            .25rem !default;\n$border-radius-lg:            .5rem !default;\n$border-radius-xl:            1rem !default;\n$border-radius-xxl:           2rem !default;\n$border-radius-pill:          50rem !default;\n// scss-docs-end border-radius-variables\n// fusv-disable\n$border-radius-2xl:           $border-radius-xxl !default; // Deprecated in v5.3.0\n// fusv-enable\n\n// fusv-disable\n$outline-width:               var(--#{$prefix}border-width) !default; // Deprecated in v5.2.3\n$outline-offset:              $outline-width !default; // Deprecated in v5.2.3\n// fusv-enable\n\n// scss-docs-start focus-visible-variables\n$focus-visible-zindex:                5 !default; // Boosted mod\n\n$focus-visible-inner-width:           2px !default; // Boosted mod\n$focus-visible-inner-color:           $white !default; // Boosted mod\n\n$focus-visible-outer-width:           3px !default;  // Boosted mod\n$focus-visible-outer-offset:          $focus-visible-inner-width !default; // Boosted mod\n$focus-visible-outer-color:           $black !default; // Boosted mod\n// scss-docs-end focus-visible-variables\n\n// scss-docs-start box-shadow-variables\n$box-shadow:        null !default; // Boosted mod: instead of `0 .5rem 1rem rgba($black, .15)`\n$box-shadow-sm:     null !default; // Boosted mod: instead of `0 .125rem .25rem rgba($black, .075)`\n$box-shadow-lg:     null !default; // Boosted mod: instead of `0 1rem 3rem rgba($black, .175)`\n$box-shadow-inset:  null !default; // Boosted mod: instead of `inset 0 1px 2px rgba($black, .075)`\n// scss-docs-end box-shadow-variables\n\n$component-active-color:      $black !default;\n$component-active-bg:         $supporting-orange !default;\n$disabled-color:              var(--#{$prefix}tertiary-color) !default; // Boosted mod\n$tertiary-active-bg:          $gray-400 !default; // Boosted mod\n\n// scss-docs-start focus-ring-variables\n$focus-ring-width:      .25rem !default;\n$focus-ring-opacity:    .25 !default;\n$focus-ring-color:      rgba($primary, $focus-ring-opacity) !default;\n// Boosted mod: no `$focus-ring-blur`\n$focus-ring-box-shadow: null !default; // Boosted mod: instead of `0 0 $focus-ring-blur $focus-ring-width $focus-ring-color`\n// scss-docs-end focus-ring-variables\n\n// scss-docs-start caret-variables\n$caret-width:                 add($spacer * .25, var(--#{$prefix}border-width)) !default;\n$caret-vertical-align:        center !default;\n$caret-spacing:               $spacer * .5 !default;\n// scss-docs-end caret-variables\n\n$transition-duration: .2s !default; // Boosted mod\n$transition-timing:   ease-in-out !default; // Boosted mod\n$transition-base:     all $transition-duration $transition-timing !default;\n$transition-fade:     opacity $transition-timing linear !default;\n// scss-docs-start collapse-transition\n$transition-collapse:         height .35s ease !default;\n$transition-collapse-width:   width .35s ease !default;\n// scss-docs-end collapse-transition\n$transition-focus:    null !default; // Boosted mod\n\n// stylelint-disable function-disallowed-list\n// scss-docs-start aspect-ratios\n$aspect-ratios: (\n  \"1x1\": 100%,\n  \"4x3\": calc(3 / 4 * 100%),\n  \"16x9\": calc(9 / 16 * 100%),\n  \"21x9\": calc(9 / 21 * 100%),\n  \"9x16\": calc(16 / 9 * 100%) // Boosted mod: additional ratio for portrait videos\n) !default;\n// scss-docs-end aspect-ratios\n// stylelint-enable function-disallowed-list\n\n// Typography\n//\n// Font, line-height, and color for body text, headings, and more.\n\n// scss-docs-start font-variables\n// stylelint-disable value-keyword-case\n$font-family-sans-serif:      HelvNeueOrange#{\"/*rtl:insert:Arabic*/\"}, \"Helvetica Neue\", Helvetica, \"Noto Sans\", \"Liberation Sans\", Arial, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\" !default;\n$font-family-monospace:       SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace !default;\n// stylelint-enable value-keyword-case\n$font-family-base:            var(--#{$prefix}font-sans-serif) !default;\n$font-family-code:            var(--#{$prefix}font-monospace) !default;\n\n\n// Boosted mod\n//// Type scale & vertical rhythm completely revamped to match Orange Web Guidelines\n\n// $font-size-root affects the value of `rem`, which is used for as well font sizes, paddings and margins\n// $font-size-base affects the font size of the body text\n$font-size-root:              null !default;\n$font-size-base:              1rem !default; // Assumes the browser default, typically `16px`\n$font-size-sm:                $font-size-base * .875 !default;  // 14px\n$font-size-lg:                $font-size-base * 1.125 !default; // 18px\n$font-size-xlg:               $font-size-base * 1.25 !default;  // 20px\n\n$font-weight-lighter:         null !default;\n$font-weight-light:           null !default;\n$font-weight-normal:          400 !default;\n$font-weight-medium:          500 !default;\n$font-weight-semibold:        600 !default;\n$font-weight-bold:            700 !default;\n$font-weight-bolder:          null !default;\n\n$font-weight-base:            $font-weight-normal !default;\n\n// stylelint-disable function-disallowed-list\n$line-height-base:            calc(18 / 16) !default;\n$line-height-sm:              calc(16 / 14) !default;\n$line-height-lg:              calc(30 / 16) !default;\n// stylelint-enable function-disallowed-list\n\n$line-length-sm:              40ch !default;\n$line-length-md:              80ch !default;\n\n$letter-spacing-base:         $spacer * -.005 !default; // -0.1px\n\n$h1-font-size:                $font-size-base * 2.125 !default;   // 34px\n$h2-font-size:                $font-size-base * 1.875 !default;   // 30px\n$h3-font-size:                $font-size-base * 1.5 !default;     // 24px\n$h4-font-size:                $font-size-xlg !default;            // 20px\n$h5-font-size:                $font-size-lg !default;             // 18px\n$h6-font-size:                $font-size-base !default;           // 16px\n// scss-docs-end font-variables\n\n// scss-docs-start font-sizes\n$font-sizes: (\n  1: $h1-font-size,\n  2: $h2-font-size,\n  3: $h3-font-size,\n  4: $h4-font-size,\n  5: $h5-font-size,\n  6: $h6-font-size\n) !default;\n// scss-docs-end font-sizes\n\n// scss-docs-start letter-spacing\n$h1-spacing:                  $letter-spacing-base * 10 !default; // -1px\n$h2-spacing:                  $letter-spacing-base * 8 !default;  // -0.8px\n$mid-spacing:                 $letter-spacing-base * 6 !default;  // -0.6px\n$h3-spacing:                  $letter-spacing-base * 5 !default;  // -0.5px\n$h4-spacing:                  $letter-spacing-base * 4 !default;  // -0.4px\n$h5-spacing:                  $letter-spacing-base * 2 !default;  // -0.2px\n$h6-spacing:                  $letter-spacing-base !default;\n// scss-docs-end letter-spacing\n\n// stylelint-disable function-disallowed-list\n// scss-docs-start line-height\n$h1-line-height:              1 !default;\n$h2-line-height:              calc(32 / 30) !default;\n$h3-line-height:              calc(26 / 24) !default;\n$h4-line-height:              calc(22 / 20) !default;\n$h5-line-height:              calc(20 / 18) !default;\n$h6-line-height:              $line-height-base !default;\n// scss-docs-end line-height\n// stylelint-enable function-disallowed-list\n\n// scss-docs-start headings-variables\n$headings-margin-bottom:      $spacer !default; // Boosted mod\n$headings-font-family:        null !default;\n$headings-font-style:         null !default;\n$headings-font-weight:        700 !default;\n$headings-line-height:        $h6-line-height !default;\n$headings-color:              inherit !default;\n// scss-docs-end headings-variables\n\n// scss-docs-start display-headings\n$display1-size:               $font-size-xlg * 3 !default;        // 60px\n$display2-size:               $font-size-xlg * 2.5 !default;      // 50px\n$display3-size:               $font-size-xlg * 2 !default;        // 40px\n$display4-size:               $h1-font-size !default;             // 34px\n$display1-spacing:            $letter-spacing-base * 20 !default; // -2px\n$display2-spacing:            $letter-spacing-base * 16 !default; // -1.6px\n$display3-spacing:            $h1-spacing !default;               // -1px\n$display4-spacing:            $h1-spacing !default;               // -1px\n$display-line-height:         $h1-line-height !default;\n// scss-docs-end display-headings\n\n// scss-docs-start type-variables\n$lead-font-size:              $font-size-xlg !default;\n$lead-font-weight:            400 !default;\n$lead-line-height:            1.5 !default;\n$lead-letter-spacing:         $letter-spacing-base * 4 !default;\n\n$small-font-size:             .875rem !default;\n\n$sub-sup-font-size:           .75em !default;\n\n// fusv-disable\n$text-muted:                  var(--#{$prefix}secondary-color) !default; // Deprecated in 5.3.0\n// fusv-enable\n\n$initialism-font-size:        $small-font-size !default;\n\n$blockquote-margin-y:         $spacer !default;\n$blockquote-font-size:        $font-size-xlg !default;\n$blockquote-footer-color:     var(--#{$prefix}secondary-color) !default; // Boosted mod: instead of `$gray-600`\n$blockquote-footer-font-size: $small-font-size !default;\n$blockquote-line-height:      1.5 !default; // Boosted mod\n$blockquote-letter-spacing:   $letter-spacing-base * .25 !default; // Boosted mod\n\n$hr-margin-y:                 $spacer !default;\n$hr-color:                    inherit !default;\n\n// fusv-disable\n$hr-bg-color:                 null !default; // Deprecated in v5.2.0\n$hr-height:                   null !default; // Deprecated in v5.2.0\n// fusv-enable\n\n$hr-border-color:             null !default; // Allows for inherited colors\n$hr-border-width:             var(--#{$prefix}border-width) !default;\n$hr-opacity:                  null !default;\n\n// scss-docs-start vr-variables\n$vr-border-width:             2px !default; // Boosted mod: instead of `var(--#{$prefix}border-width)`\n// scss-docs-end vr-variables\n\n$legend-margin-bottom:        $spacer * .25 !default;\n$legend-font-size:            $font-size-xlg !default;\n$legend-font-weight:          $font-weight-bold !default;\n\n$dt-font-weight:              $font-weight-bold !default;\n\n$list-inline-padding:         $spacer * .25 !default;\n\n$mark-padding:                0 .1875em !default; // Boosted mod\n$mark-color:                  $white !default; // Boosted mod: instead of `$body-color`\n$mark-bg:                     $black !default; // Boosted mod: instead of `$yellow-100`\n// scss-docs-end type-variables\n// End mod\n\n// Tables\n//\n// Customizes the `.table` component with basic values, each used across all table variations.\n\n// scss-docs-start table-variables\n$table-cell-padding-y:                  .875rem !default; // Boosted mod\n$table-cell-padding-x:                  $spacer * .5 !default; // Boosted mod\n$table-cell-padding-y-sm:               .5625rem !default; // Boosted mod\n$table-cell-padding-x-sm:               $table-cell-padding-x !default; // Boosted mod\n\n$table-cell-icon-margin-top:            -.75rem !default; // Boosted mod\n$table-cell-icon-margin-bottom:         -.625rem !default; // Boosted mod\n$table-cell-vertical-align:             top !default;\n$table-line-height:                     1.25 !default; // Boosted mod\n\n$table-color:                           var(--#{$prefix}emphasis-color) !default;\n$table-bg:                              var(--#{$prefix}body-bg) !default;\n$table-accent-bg:                       transparent !default;\n\n$table-th-font-weight:                  null !default;\n\n$table-striped-color:                   $table-color !default;\n$table-striped-bg-factor:               .035 !default; // Boosted mod: equivalent to `$gray-200`\n$table-striped-bg:                      rgba(var(--#{$prefix}black-rgb), var(--#{$prefix}table-striped-bg-factor)) !default; // Boosted mod: instead of `rgba(var(--#{$prefix}emphasis-color-rgb), $table-striped-bg-factor)`\n$table-variant-striped-bg-factor:       .08 !default; // Boosted mod\n\n$table-active-color:                    $table-color !default;\n$table-active-bg-factor:                .135 !default; // Boosted mod\n$table-active-bg:                       rgba(var(--#{$prefix}emphasis-color-rgb), var(--#{$prefix}table-active-bg-factor)) !default; // Boosted mod: instead of `rgba(var(--#{$prefix}emphasis-color-rgb), $table-active-bg-factor)`\n$table-variant-active-bg-factor:        .4 !default; // Boosted mod\n\n$table-hover-color:                     $table-color !default;\n$table-hover-bg-factor:                 .065 !default; // Boosted mod\n$table-hover-bg:                        rgba(var(--#{$prefix}emphasis-color-rgb), var(--#{$prefix}table-hover-bg-factor)) !default; // Boosted mod: instead of `rgba(var(--#{$prefix}emphasis-color-rgb), $table-hover-bg-factor)`\n$table-variant-hover-bg-factor:         .2 !default; // Boosted mod\n\n$table-border-factor:                   .4 !default; // Boosted mod\n// stylelint-disable-next-line function-disallowed-list\n$table-border-width:                    calc(var(--#{$prefix}border-width) * .5) !default;  // Boosted mod\n$table-border-color:                    var(--#{$prefix}border-color-subtle) !default; // Boosted mod: instead of `var(--#{$prefix}border-color)`\n\n$table-striped-order:                   odd !default;\n$table-striped-columns-order:           even !default;\n\n$table-group-separator-color:           currentcolor !default;\n\n$table-caption-color:                   var(--#{$prefix}caption-color, var(--#{$prefix}emphasis-color)) !default; // Boosted mod: instead of `var(--#{$prefix}secondary-color)`\n$table-caption-padding-y:               .75rem !default; // Boosted mod\n\n$table-bg-scale:                        -60% !default;\n// scss-docs-end table-variables\n\n// scss-docs-start table-loop\n$table-variants: (\n  \"primary\":    shift-color($primary, $table-bg-scale),\n  \"secondary\":  shift-color($secondary, $table-bg-scale),\n  \"success\":    shift-color($success, $table-bg-scale),\n  \"info\":       shift-color($info, $table-bg-scale),\n  \"warning\":    shift-color($warning, $table-bg-scale),\n  \"danger\":     shift-color($danger, $table-bg-scale),\n  \"light\":      $light,\n  \"dark\":       $dark,\n) !default;\n// scss-docs-end table-loop\n\n// Buttons + Forms\n//\n// Shared variables that are reassigned to `$input-` and `$btn-` specific variables.\n\n// scss-docs-start input-btn-variables\n$input-btn-padding-y:         .5rem !default;\n$input-btn-padding-x:         1.125rem !default;\n$input-btn-font-family:       inherit !default;\n$input-btn-font-size:         $font-size-base !default;\n$input-btn-line-height:       1.25 !default;\n\n$input-btn-focus-width:       $focus-visible-outer-offset !default; // Boosted mod: instead of `$focus-ring-width`\n// Boosted mod: no `$input-btn-focus-color-opacity`\n// Boosted mod: no `$input-btn-focus-color`\n// Boosted mod: no `$input-btn-focus-blur`\n$input-btn-focus-box-shadow:  $focus-ring-box-shadow !default;\n\n$input-btn-padding-y-sm:      $spacer * .25 !default;\n$input-btn-padding-x-sm:      $spacer * .5 !default;\n$input-btn-font-size-sm:      $font-size-sm !default;\n\n$input-btn-padding-y-lg:      .8125rem !default;\n$input-btn-padding-x-lg:      $spacer !default;\n$input-btn-font-size-lg:      $font-size-lg !default;\n\n$input-btn-border-width:      var(--#{$prefix}border-width) !default;\n// scss-docs-end input-btn-variables\n\n// Buttons\n//\n// For each of Boosted's buttons, define text, background, and border color.\n\n// scss-docs-start btn-variables\n$btn-color:                   var(--#{$prefix}body-color) !default;\n$btn-hover-color:             $btn-color !default; // Boosted mod\n$btn-padding-y:               $input-btn-padding-y !default;\n$btn-padding-x:               $input-btn-padding-x !default;\n$btn-font-family:             $input-btn-font-family !default;\n$btn-font-size:               $input-btn-font-size !default;\n$btn-line-height:             $input-btn-line-height !default;\n$btn-letter-spacing:          $letter-spacing-base !default; // Boosted mod\n$btn-white-space:             null !default; // Set to `nowrap` to prevent text wrapping\n\n$btn-padding-y-sm:            $input-btn-padding-y-sm !default;\n$btn-padding-x-sm:            $input-btn-padding-x-sm !default;\n$btn-font-size-sm:            $input-btn-font-size-sm !default;\n$btn-line-height-sm:          $line-height-sm !default; // Boosted mod\n$btn-letter-spacing-sm:       $letter-spacing-base !default; // Boosted mod\n\n$btn-padding-y-lg:            $input-btn-padding-y-lg !default;\n$btn-padding-x-lg:            $input-btn-padding-x-lg !default;\n$btn-font-size-lg:            $input-btn-font-size-lg !default;\n$btn-line-height-lg:          $h5-line-height !default; // Boosted mod\n$btn-letter-spacing-lg:       $letter-spacing-base * 2 !default; // Boosted mod\n\n$btn-border-width:            $input-btn-border-width !default;\n\n$btn-default-hover-bg:        var(--#{$prefix}highlight-bg) !default; // Boosted mod\n$btn-default-hover-border:    var(--#{$prefix}border-color) !default; // Boosted mod\n$btn-default-hover-color:     var(--#{$prefix}highlight-color) !default; // Boosted mod\n$btn-default-active-bg:       $supporting-orange !default; // Boosted mod\n$btn-default-active-border:   $supporting-orange !default; // Boosted mod\n$btn-default-active-color:    $black !default; // Boosted mod\n$btn-default-disabled-bg:     var(--#{$prefix}disabled-color) !default; // Boosted mod\n$btn-default-disabled-border: var(--#{$prefix}disabled-color) !default; // Boosted mod\n$btn-default-disabled-color:  var(--#{$prefix}highlight-color) !default; // Boosted mod\n\n$btn-outline-default-hover-bg:        var(--#{$prefix}btn-color) !default; // Boosted mod\n$btn-outline-default-hover-border:    var(--#{$prefix}btn-border-color) !default; // Boosted mod\n$btn-outline-default-hover-color:     $white !default; // Boosted mod\n$btn-outline-default-active-bg:       $supporting-orange !default; // Boosted mod\n$btn-outline-default-active-border:   $supporting-orange !default; // Boosted mod\n$btn-outline-default-active-color:    $black !default; // Boosted mod\n$btn-outline-default-disabled-bg:     transparent !default; // Boosted mod\n$btn-outline-default-disabled-border: var(--#{$prefix}disabled-color) !default; // Boosted mod\n$btn-outline-default-disabled-color:  var(--#{$prefix}disabled-color) !default; // Boosted mod\n\n$btn-font-weight:             $font-weight-bold !default;\n$btn-box-shadow:              null !default;\n$btn-focus-width:             $input-btn-focus-width !default;\n$btn-focus-box-shadow:        0 0 0 $btn-focus-width $white !default;\n$btn-disabled-opacity:        1 !default;\n$btn-active-box-shadow:       null !default;\n\n$btn-link-color:              var(--#{$prefix}link-color) !default;\n$btn-link-hover-color:        var(--#{$prefix}link-hover-color) !default;\n$btn-link-disabled-color:     var(--#{$prefix}disabled-color) !default; // Boosted mod: instead of `$gray-600`\n// Boosted mod: no `$btn-link-focus-shadow-rgb`\n\n// Allows for customizing button radius independently from global border radius\n$btn-border-radius:           var(--#{$prefix}border-radius) !default;\n$btn-border-radius-sm:        var(--#{$prefix}border-radius-sm) !default;\n$btn-border-radius-lg:        var(--#{$prefix}border-radius-lg) !default;\n\n$btn-transition:              $transition-focus !default; // Boosted mod\n// scss-docs-end btn-variables\n\n// Boosted mod: icon button\n$btn-icon-padding-x:          subtract($spacer * .5, var(--#{$prefix}border-width)) !default;\n$btn-icon-padding-x-sm:       $spacer * .25 !default;\n$btn-icon-padding-x-lg:       add($spacer * .5, calc(var(--#{$prefix}border-width) * 1.5)) !default; // stylelint-disable-line function-disallowed-list\n// Boosted mod: social button\n// scss-docs-start social-buttons\n$btn-social-networks: (\n  \"facebook\": (\n    \"color\": #3b5998,\n    \"icon\": \"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'><path d='M19 6h5V0h-5c-4 0-7 3-7 7v3H8v6h4v16h6V16h5l1-6h-6V7l1-1z'></path></svg>\"\n  ),\n  \"twitter\": (\n    \"color\": #1da1f2,\n    \"icon\": \"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'><path d='M32 7a13 13 0 01-3.8 1.1 6.6 6.6 0 003-3.6c-1.4.7-2.8 1.3-4.3 1.6a6.6 6.6 0 00-11.1 6A18.6 18.6 0 012.2 5a6.6 6.6 0 002 8.9c-1 0-2-.4-3-.9v.1c0 3.2 2.4 5.9 5.4 6.5a6.6 6.6 0 01-3 0 6.6 6.6 0 006.1 4.6A13.2 13.2 0 010 27.1a18.6 18.6 0 0028.7-16.6C30 9.5 31.1 8.4 32 7z'/></svg>\"\n  ),\n  \"instagram\": (\n    \"color\": #e1306c,\n    \"icon\": \"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'><path d='M16 2.9h6.5c1.5.1 2.4.4 3 .6a5 5 0 011.8 1.2c.5.6.9 1.1 1.2 1.9.2.5.4 1.4.5 3a112.7 112.7 0 01-.5 15.8 5 5 0 01-1.2 1.9c-.6.5-1.1.9-1.9 1.2-.5.2-1.4.4-3 .5a112.7 112.7 0 01-15.8-.5 5 5 0 01-1.9-1.2 5 5 0 01-1.2-1.9c-.2-.5-.4-1.4-.5-3a112.7 112.7 0 01.5-15.8 5 5 0 011.2-1.9c.6-.5 1.1-.9 1.9-1.2C7 3.3 8 3 9.6 3l6.4-.1zM16 0H9.4C7.7.3 6.5.5 5.5.9s-2 1-2.8 1.9c-1 .9-1.5 1.8-1.9 2.8-.4 1-.6 2.2-.7 3.9a117.6 117.6 0 00.7 17c.5 1.1 1 2 1.9 3 .9.8 1.8 1.4 2.8 1.8 1 .4 2.2.6 3.9.7a117.2 117.2 0 0017-.7c1.1-.4 2-1 2.9-1.9s1.4-1.8 1.8-2.8c.4-1 .7-2.2.8-3.9a117.2 117.2 0 00-.8-17A7.8 7.8 0 0026.4.8c-1-.5-2.1-.7-3.8-.8L16 0z'/><path d='M16 7.8a8.2 8.2 0 100 16.4 8.2 8.2 0 000-16.4zm0 13.5a5.3 5.3 0 110-10.6 5.3 5.3 0 010 10.6zM26.5 7.5a2 2 0 11-3.9 0 2 2 0 013.9 0z'/></svg>\"\n  ),\n  \"youtube\": (\n    \"color\": #f00,\n    \"icon\": \"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'><path d='M31.7 9.6s-.3-2.2-1.3-3.2c-1.2-1.3-2.6-1.3-3.2-1.3-4.5-.4-11.2-.4-11.2-.4s-6.7 0-11.2.4c-.6 0-2 0-3.2 1.3C.6 7.4.3 9.6.3 9.6S0 12.2 0 14.8v2.4c0 2.6.3 5.2.3 5.2s.3 2.2 1.3 3.2c1.2 1.2 2.8 1.2 3.5 1.3 2.6.3 11 .4 11 .4s6.6 0 11.1-.4c.6 0 2 0 3.2-1.3 1-1 1.3-3.2 1.3-3.2s.3-2.6.3-5.2v-2.4c0-2.6-.3-5.2-.3-5.2zm-19 10.5v-9l8.6 4.6-8.6 4.4z'/></svg>\"\n  ),\n  \"linkedin\": (\n    \"color\": #0077b5,\n    \"icon\": \"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'><path d='M12 12h5.5v2.8h.1a6.1 6.1 0 015.5-2.8c5.8 0 6.9 3.6 6.9 8.4V30h-5.8v-8.5c0-2 0-4.7-3-4.7s-3.4 2.2-3.4 4.5V30H12V12zM2 12h6v18H2V12zm6-5a3 3 0 11-6 0 3 3 0 016 0z'/></svg>\",\n  ),\n  \"whatsapp\": (\n    \"color\": #25d366,\n    \"icon\": \"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'><path d='M27.3 4.7a15.9 15.9 0 00-25 19.1L.1 32l8.4-2.2A15.9 15.9 0 0027.3 4.7zM16 29c-2.4 0-4.7-.6-6.7-1.8l-.5-.3-5 1.3 1.3-4.8-.3-.5A13.2 13.2 0 1116.1 29zm7.2-9.8l-2.7-1.3c-.3-.1-.6-.2-1 .2l-1.2 1.5c-.2.3-.4.3-.8.1s-1.7-.6-3.2-2c-1.2-1-2-2.3-2.2-2.7s0-.6.2-.8l.6-.7.4-.6v-.7l-1.3-3c-.3-.7-.6-.6-.9-.7h-.7c-.2 0-.7.1-1.1.5C9 9.4 8 10.4 8 12.3s1.4 3.9 1.6 4.1c.2.3 2.8 4.3 6.8 6l2.3.9c.9.3 1.8.2 2.4.1.8-.1 2.4-1 2.7-1.9s.4-1.7.3-1.9l-.8-.4z'/></svg>\"\n  ),\n  \"mail\": (\n    \"color\": $supporting-orange,\n    \"icon\": \"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'><path d='M3.2 14.3c0 9.5 0 9 .2 9.5.3.8 1 1.4 1.7 1.7l12.2.1h11.5v-8.8c0-9.3 0-8.9-.2-9.3-.2-.7-.7-1.2-1.3-1.6l-.8-.3H3.2v8.7zm22.9-2.4a246.2 246.2 0 01-4.9 4.7l-.8.7-.5.6-.7.6c-.6.6-1 .9-1.3 1a4 4 0 01-1.8.5 4 4 0 01-2.4-.6 13 13 0 01-1.9-1.7l-2.4-2.4-.6-.6-1.4-1.3L6.1 12l-.5-.5V8.9l.6.5L7.9 11l1.4 1.4 1.3 1.2 1.3 1.3a195 195 0 012.6 2.4c.4.3 1 .5 1.6.4.5 0 1-.1 1.4-.4L19 16l1-1 1-1a214.7 214.7 0 012.2-2l1-1 2-2 .2-.2v2.8l-.3.3z'/></svg>\",\n    \"size\": 1.5rem\n  ),\n  \"snapchat\": (\n    \"color\": #fffc00,\n    \"icon\": \"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 26 28'><path d='M13 2c3 0 5 2 7 4v6h2l1 1-3 2v1l4 4h1l1 1-4 1-1 2h-2-1c-1 0-2 2-5 2s-4-2-5-2H5l-1-2-4-1 1-1h1l4-4v-1l-3-2 1-1h2V9 6c2-3 4-4 7-4z'/></svg>\"\n  ),\n  \"pinterest\": (\n    \"color\": red,\n    \"icon\": \"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'><path d='M16 2a14 14 0 00-5 27v-4l2-7-1-2c0-2 1-3 3-3l1 2-1 4c0 2 1 3 2 3 3 0 5-3 5-7 0-3-3-5-6-5-4 0-6 3-6 6l1 3a302 302 0 01-1 2c-2-1-3-3-3-5 0-5 3-9 9-9 5 0 9 4 9 8 0 5-3 9-7 9l-4-2v4l-2 3a14 14 0 0018-13c0-8-6-14-14-14z'/></svg>\",\n    \"size\": 1.375rem\n  ),\n  \"tiktok\": (\n    \"color\": #ff2c55,\n    \"icon\": \"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'><path d='M7.024 30.054C4.584 28.212 3 25.235 3 21.876c0-5.59 4.39-10.123 9.805-10.123.45 0 .899.031 1.345.094v5.6a4.363 4.363 0 0 0-1.361-.218c-2.477 0-4.485 2.074-4.485 4.631 0 1.809 1.003 3.374 2.467 4.137l.31.146a4.348 4.348 0 0 0 1.708.348c2.471 0 4.476-2.065 4.484-4.615V0h5.335v.704c.02.211.046.42.082.63l.08.404a7.668 7.668 0 0 0 3.306 4.769A7.22 7.22 0 0 0 30 7.665V8.83l-.199-.047-.182-.047.381.094v4.312a12.4 12.4 0 0 1-7.392-2.443v11.177c0 5.591-4.39 10.124-9.804 10.124-2.02 0-3.898-.63-5.458-1.712l-.322-.234Z'/></svg>\"\n  ),\n  \"x\": (\n    \"color\": #1da1f2,\n    \"icon\": \"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 19'><path d='m15.751 0-5.053 5.776L6.328 0H0l7.561 9.888-7.166 8.19h3.068l5.531-6.32 4.834 6.32H20l-7.883-10.42L18.817 0h-3.066ZM3.581 1.74h1.824l10.97 14.502h-1.7L3.58 1.74Z'/></svg>\"\n  )\n) !default;\n// scss-docs-end social-buttons\n// End mod\n\n// Forms\n\n// scss-docs-start form-text-variables\n$form-text-margin-top:                  .4375rem !default; // Boosted mod\n$form-text-font-size:                   $small-font-size !default;\n$form-text-font-style:                  null !default;\n$form-text-font-weight:                 $font-weight-bold !default; // Boosted mod: instead of `null`\n$form-text-line-height:                 $line-height-sm !default; // Boosted mod\n$form-text-color:                       var(--#{$prefix}secondary-color) !default;\n// scss-docs-end form-text-variables\n\n// scss-docs-start form-label-variables\n$form-label-margin-bottom:              .5rem !default; // Boosted mod\n$form-label-font-size:                  null !default;\n$form-label-font-style:                 null !default;\n$form-label-font-weight:                $font-weight-bold !default;\n$form-label-color:                      null !default;\n$form-label-disabled-color:             var(--#{$prefix}disabled-color) !default; // Boosted mod\n$form-label-required-margin-left:       .1875rem !default; // Boosted mod\n$form-label-required-color:             var(--#{$prefix}primary) !default; // Boosted mod\n// scss-docs-end form-label-variables\n\n// scss-docs-start form-helper-variables\n$form-helper-size:                      1.25rem !default; // Boosted mod\n$form-helper-color:                     var(--#{$prefix}info) !default; // Boosted mod\n$form-helper-bg:                        var(--#{$prefix}highlight-color) !default; // Boosted mod\n$form-helper-icon:                      escape-svg($helper-icon) !default; // Boosted mod\n$form-helper-label-margin-bottom:       $form-label-margin-bottom - divide(($form-helper-size - $font-size-base), 2) !default; // Boosted mod\n// scss-docs-end form-helper-variables\n\n// scss-docs-start form-input-variables\n$input-padding-y:                       $input-btn-padding-y !default;\n$input-padding-x:                       $spacer * .5 !default;\n$input-font-family:                     $input-btn-font-family !default;\n$input-font-size:                       $input-btn-font-size !default;\n$input-font-weight:                     $font-weight-bold !default;\n$input-line-height:                     $input-btn-line-height !default;\n\n$input-padding-y-sm:                    divide($input-padding-y, 2) !default; // Boosted mod\n$input-padding-x-sm:                    $input-btn-padding-x-sm !default;\n$input-font-size-sm:                    $input-btn-font-size-sm !default;\n\n$input-padding-y-lg:                    $input-btn-padding-y-lg !default;\n$input-padding-x-lg:                    $input-btn-padding-x-lg !default;\n$input-font-size-lg:                    $input-btn-font-size-lg !default;\n\n$input-bg:                              var(--#{$prefix}body-bg) !default;\n$input-disabled-color:                  var(--#{$prefix}secondary-color) !default; // Boosted mod\n$input-disabled-bg:                     var(--#{$prefix}secondary-bg) !default;\n$input-disabled-border-color:           null !default;\n\n$input-color:                           var(--#{$prefix}body-color) !default;\n$input-border-color:                    var(--#{$prefix}border-color-subtle) !default; // Boosted mod: instead of var(--#{$prefix}border-color)\n$input-border-width:                    $input-btn-border-width !default;\n$input-box-shadow:                      none !default; // Boosted mod\n\n$input-border-radius:                   var(--#{$prefix}border-radius) !default;\n$input-border-radius-sm:                var(--#{$prefix}border-radius-sm) !default;\n$input-border-radius-lg:                var(--#{$prefix}border-radius-lg) !default;\n\n$input-focus-bg:                        $input-bg !default;\n$input-focus-border-color:              currentcolor !default;\n$input-focus-color:                     $input-color !default;\n$input-focus-width:                     $input-btn-focus-width !default;\n$input-focus-box-shadow:                none !default; // Boosted mod\n\n$input-placeholder-color:               var(--#{$prefix}secondary-color) !default;\n$input-plaintext-color:                 null !default; // Boosted mod: instead of `var(--#{$prefix}body-color)`\n\n// Boosted mod: no $input-height-border\n\n$input-height-inner:                    add($input-line-height * 1em, $input-padding-y * 2) !default;\n$input-height-inner-half:               $spacer !default; // Boosted mod\n$input-height-inner-quarter:            map-get($spacers, 2) !default; // Boosted mod\n\n$input-height:                          2.5rem !default;\n$input-height-sm:                       1.875rem !default;\n$input-height-lg:                       3.125rem !default;\n$input-line-height-lg:                  $h5-line-height !default; // Boosted mod\n\n$input-transition:                      border-color $transition-duration $transition-timing, $transition-focus !default;\n\n$form-color-width:                      2.5rem !default; // Boosted mod: instead of `3rem`\n$form-color-border-color:               var(--#{$prefix}emphasis-color) !default; // Boosted mod\n$form-color-hover-bg-color:             var(--#{$prefix}emphasis-color) !default; // Boosted mod\n$form-color-disabled-bg-color:          $input-bg !default; // Boosted mod\n$form-color-disabled-border-color:      var(--#{$prefix}disabled-color) !default; // Boosted mod\n$form-color-disabled-background-swatch: var(--#{$prefix}form-color-disabled-filter) !default; // Boosted mod\n$form-color-disabled-filter:            brightness(0) invert(1) brightness(.8) !default; // Boosted mod\n// scss-docs-end form-input-variables\n\n// scss-docs-start form-check-variables\n$form-check-input-width:                  1em !default;\n$form-check-min-height:                   $font-size-base * $input-btn-line-height !default;\n$form-check-padding-start:                $form-check-input-width + .5em !default;\n$form-check-margin-bottom:                .125rem !default;\n$form-check-label-padding-top:            .4375rem !default; // Boosted mod\n$form-check-label-color:                  null !default;\n$form-check-label-cursor:                 null !default;\n$form-check-transition:                   null !default;\n$form-check-filter:                       $invert-filter !default; // Boosted mod\n\n$form-check-input-active-filter:          null !default;\n$form-check-input-active-bg-color:        $component-active-bg !default; // Boosted mod\n\n$form-check-input-bg:                     $input-bg !default;\n$form-check-input-border:                 var(--#{$prefix}border-width) solid $input-border-color !default; // Boosted mod: instead of `var(--#{$prefix}border-width) solid var(--#{$prefix}border-color)`\n$form-check-input-border-radius:          0 !default;\n$form-check-radio-border-radius:          50% !default;\n$form-check-input-focus-border:           null !default;\n$form-check-input-focus-box-shadow:       $focus-ring-box-shadow !default;\n\n$form-check-input-checked-color:          $component-active-color !default;\n$form-check-input-checked-bg-color:       $component-active-bg !default;\n$form-check-input-checked-border-color:   $form-check-input-checked-bg-color !default;\n$form-check-input-checked-bg-image:       var(--#{$prefix}check-icon) !default;\n$form-check-input-disabled-color:         $gray-900 !default; // Boosted mod\n$form-check-input-disabled-filter:        var(--#{$prefix}form-check-filter) !default; // Boosted mod\n$form-check-radio-checked-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='2' fill='#{$form-check-input-checked-color}'/></svg>\") !default;\n\n$form-check-input-indeterminate-color:          $form-check-input-checked-color !default;\n$form-check-input-indeterminate-bg-color:       $form-check-input-checked-bg-color !default;\n$form-check-input-indeterminate-border-color:   $form-check-input-indeterminate-bg-color !default;\n$form-check-input-indeterminate-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 10 3'><path fill='#{$form-check-input-indeterminate-color}' d='M0 0h10v3H0z'/></svg>\") !default;\n\n$form-check-input-disabled-opacity:        null !default;\n$form-check-label-disabled-opacity:        null !default;\n$form-check-btn-check-disabled-opacity:    null !default;\n\n$form-check-inline-margin-end:    1rem !default;\n\n// Boosted mod: Star rating\n$form-star-size:                        1.5625rem !default;\n$form-star-size-sm:                     1.25rem !default;\n$form-star-margin-between:              -.125rem !default;\n\n$form-star-rating-checked-color:        var(--#{$prefix}primary) !default;\n$form-star-rating-unchecked-color:      var(--#{$prefix}secondary-color) !default;\n$form-star-rating-hover-color:          var(--#{$prefix}highlight-bg) !default;\n$form-star-rating-disabled-color:       var(--#{$prefix}disabled-color) !default;\n\n$form-star-rating-checked-icon:         escape-svg(url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 25 25'><path fill='#{$black}' stroke='#{$black}' d='m12.5 4.523 2.016 6.227 6.542-.005-5.296 3.843 2.027 6.224L12.5 16.96l-5.289 3.852 2.027-6.224-5.296-3.843 6.542.005L12.5 4.523Z'/></svg>\")) !default;\n$form-star-rating-unchecked-icon:       escape-svg(url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 25 25'><path fill='transparent' stroke='#{$black}' d='m12.5 4.523 2.016 6.227 6.542-.005-5.296 3.843 2.027 6.224L12.5 16.96l-5.289 3.852 2.027-6.224-5.296-3.843 6.542.005L12.5 4.523Z'/></svg>\")) !default;\n$form-star-rating-sm-checked-icon:      escape-svg(url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='#{$black}' stroke='#{$black}' d='M10 3.943 11.54 8.7l4.998-.004-4.046 2.936 1.548 4.755L10 13.444l-4.04 2.943 1.548-4.755-4.046-2.936L8.46 8.7 10 3.943Z'/></svg>\")) !default;\n$form-star-rating-sm-unchecked-icon:    escape-svg(url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='transparent' stroke='#{$black}' d='M10 3.943 11.54 8.7l4.998-.004-4.046 2.936 1.548 4.755L10 13.444l-4.04 2.943 1.548-4.755-4.046-2.936L8.46 8.7 10 3.943Z'/></svg>\")) !default;\n//fusv-disable\n$form-star-focus-color:                 $black !default; // Deprecated in v5.2.3\n$form-star-focus-outline:               var(--#{$prefix}border-width) solid $form-star-focus-color !default; // Deprecated in v5.2.3\n$form-star-focus-color-dark:            $white !default; // Deprecated in v5.2.3\n$form-star-focus-outline-dark:          var(--#{$prefix}border-width) solid $form-star-focus-color-dark !default; // Deprecated in v5.2.3\n$form-star-focus-box-shadow:            $input-btn-focus-box-shadow !default; // Deprecated in v5.2.3\n//fusv-enable\n\n// End mod\n// scss-docs-end form-check-variables\n\n// scss-docs-start form-switch-variables\n// Boosted mod: no $form-switch-color\n$form-switch-width:               $spacer * 3 !default; // Boosted mod\n$form-switch-padding-start:       $form-switch-width + .625rem !default; // Boosted mod\n$form-switch-bg-image:            var(--#{$prefix}close-icon) !default;  // Boosted mod\n$form-switch-bg-position:         right .5rem top 50% !default;  // Boosted mod\n$form-switch-bg-size:             .75rem !default;  // Boosted mod\n$form-switch-bg-square-size:      add(1rem, $spacer * .5) !default;  // Boosted mod\n$form-switch-border-radius:       null !default; // Boosted mod\n$form-switch-transition:          background-position .15s ease-in-out, $transition-focus !default; // Boosted mod\n\n$form-switch-square-bg:             $black !default; // Boosted mod\n$form-switch-bg:                    $white !default; // Boosted mod\n$form-switch-border-color:          $white !default; // Boosted mod\n$form-switch-filter:                var(--#{$prefix}form-check-filter) !default; // Boosted mod\n$form-switch-focus-visible-inner:   $black !default; // Boosted mod\n$form-switch-focus-visible-outer:   $white !default; // Boosted mod\n\n// Boosted mod: no $form-switch-focus-color\n// Boosted mod: no $form-switch-focus-bg-image\n\n// Boosted mod: no $form-switch-checked-color\n$form-switch-checked-bg-image:    $form-check-input-checked-bg-image !default; // Boosted mod\n$form-switch-checked-bg-size:     add(map-get($spacers, 2), map-get($spacers, 1)) !default; // Boosted mod\n// stylelint-disable-next-line function-disallowed-list\n$form-switch-checked-bg-position: calc(var(--#{$prefix}border-width) * 3) 50% !default; // Boosted mod\n\n$form-switch-checked-square-bg:                 var(--#{$prefix}body-bg) !default; // Boosted mod\n$form-switch-checked-bg:                        $supporting-orange !default; // Boosted mod\n$form-switch-checked-border-color:              $supporting-orange !default; // Boosted mod\n$form-switch-checked-filter:                    none !default; // Boosted mod\n$form-switch-checked-focus-inner:               var(--#{$prefix}focus-visible-inner-color) !default; // Boosted mod\n$form-switch-checked-focus-outer:               var(--#{$prefix}focus-visible-outer-color) !default; // Boosted mod\n$form-switch-unchecked-invalid-border-color:    #31c3eb !default; // Boosted mod: will be rendered red when mixed with the filter\n// scss-docs-end form-switch-variables\n\n// scss-docs-start input-group-variables\n$input-group-addon-padding-y:           $input-padding-y !default;\n$input-group-addon-padding-x:           $input-padding-x !default;\n$input-group-addon-font-weight:         $input-font-weight !default;\n$input-group-addon-color:               $input-color !default; // Boosted mod: instead of `null`\n$input-group-addon-bg:                  null !default; // Boosted mod: instead of `var(--#{$prefix}tertiary-bg)`\n$input-group-addon-border-color:        null !default;\n// scss-docs-end input-group-variables\n\n// scss-docs-start form-select-variables\n$form-select-padding-y:             $input-padding-y !default;\n$form-select-padding-x:             $input-padding-x !default;\n$form-select-font-family:           $input-font-family !default;\n$form-select-font-size:             $input-font-size !default;\n$form-select-indicator-padding:     $form-select-padding-x * 3 !default; // Extra padding for background-image\n$form-select-font-weight:           $input-font-weight !default;\n$form-select-line-height:           $input-line-height !default;\n$form-select-color:                 $input-color !default;\n$form-select-bg:                    $input-bg !default;\n$form-select-disabled-color:        $input-disabled-color !default; // Boosted mod: instead of `null`\n$form-select-disabled-bg:           $input-disabled-bg !default;\n$form-select-disabled-border-color: $input-disabled-border-color !default;\n$form-select-bg-position:           right $form-select-padding-x top add(50%, 1px) !default;\n$form-select-bg-size:               .875rem 1rem !default; // In pixels because image dimensions\n$form-select-indicator:             escape-svg(url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 14 7'><path d='M7 7 0 0h14L7 7z'/></svg>\")) !default; // Boosted mod: instead of Bootstrap svg\n$form-select-disabled-indicator:    escape-svg(url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 14 7'><path fill='#{$gray-700}' d='M7 7 0 0h14L7 7z'/></svg>\")) !default; // Boosted mod\n\n$form-select-feedback-icon-padding-end: $form-select-padding-x * 2.5 + $form-select-indicator-padding !default;\n$form-select-feedback-icon-position:    center right $form-select-indicator-padding !default;\n$form-select-feedback-icon-size:        $input-height-inner-half $input-height-inner-half !default;\n\n$form-select-border-width:        $input-border-width !default;\n$form-select-border-color:        $input-border-color !default;\n$form-select-border-radius:       $input-border-radius !default;\n$form-select-box-shadow:          none !default; // Boosted mod\n\n$form-select-focus-border-color:  $input-color !default; // Boosted mod: handle a Firefox-specific visible focus rendering where we remove the border from the select box (see `.form-select` rule)\n// Boosted mod: no $form-select-focus-width\n$form-select-focus-box-shadow:    none !default; // Boosted mod\n\n$form-select-padding-y-sm:        add($input-padding-y-sm, 1px) !default; // Boosted mod\n$form-select-padding-x-sm:        $input-padding-x-sm !default;\n$form-select-font-size-sm:        $input-font-size-sm !default;\n$form-select-border-radius-sm:    $input-border-radius-sm !default;\n\n$form-select-padding-y-lg:        $spacer * .5 !default; // Boosted mod\n$form-select-padding-x-lg:        $input-padding-x-lg !default;\n$form-select-font-size-lg:        $input-font-size-lg !default;\n$form-select-border-radius-lg:    $input-border-radius-lg !default;\n\n$form-select-transition:          $input-transition !default;\n// scss-docs-end form-select-variables\n\n// scss-docs-start form-range-variables\n$form-range-track-width:          100% !default;\n$form-range-track-height:         .375rem !default; // Boosted mod: instead of `.5rem`\n$form-range-track-cursor:         pointer !default;\n$form-range-track-bg:             var(--#{$prefix}secondary-bg) !default;\n$form-range-track-filled-bg:      var(--#{$prefix}primary) !default; // Boosted mod\n$form-range-track-border-radius:  null !default; // Boosted mod: instead of `1rem`\n$form-range-track-box-shadow:     var(--#{$prefix}box-shadow-inset) !default;\n\n$form-range-thumb-width:                   1rem !default;\n$form-range-thumb-height:                  $form-range-thumb-width !default;\n$form-range-thumb-bg:                      var(--#{$prefix}body-bg) !default; // Boosted mod: instead of `$component-active-bg`\n$form-range-thumb-border:                  var(--#{$prefix}border-width) solid var(--#{$prefix}border-color) !default; // Boosted mod: instead of `0`\n$form-range-thumb-border-radius:           50% !default;\n$form-range-thumb-box-shadow:              0 .1rem .25rem rgba($black, .1) !default;\n$form-range-thumb-focus-box-shadow:        null !default; // Boosted mod\n$form-range-thumb-focus-box-shadow-width:  $input-focus-width !default; // For focus box shadow issue in Edge\n$form-range-thumb-hover-bg:                var(--#{$prefix}highlight-bg) !default; // Boosted mod\n$form-range-thumb-active-bg:               var(--#{$prefix}primary) !default; // Boosted mod: instead of `tint-color($component-active-bg, 70%)`\n$form-range-thumb-active-border:           var(--#{$prefix}primary) !default; // Boosted mod\n$form-range-thumb-disabled-bg:             var(--#{$prefix}disabled-color) !default; // Boosted mod: instead of `var(--#{$prefix}secondary-color)`\n$form-range-thumb-transition:              background-color $transition-duration $transition-timing, border-color $transition-duration $transition-timing !default; // Boosted mod: no box shadow\n// scss-docs-end form-range-variables\n\n// scss-docs-start form-file-variables\n$form-file-button-color:          $input-color !default;\n$form-file-button-bg:             $input-bg !default; // Boosted mod: instead of `var(--#{$prefix}tertiary-bg)`\n$form-file-button-hover-bg:       var(--#{$prefix}secondary-bg) !default;\n// scss-docs-end form-file-variables\n\n// Boosted mod: no floating labels\n\n// Form validation\n\n// scss-docs-start form-feedback-variables\n$form-feedback-margin-top:          $form-text-margin-top !default;\n$form-feedback-font-size:           $small-font-size !default;\n$form-feedback-font-style:          null !default;\n// fusv-disable\n$form-feedback-valid-color:         $success !default; // Boosted mod: deprecated in v5.3.0\n$form-feedback-invalid-color:       $danger !default; // Boosted mod: deprecated in v5.3.0\n// fusv-enable\n\n$form-feedback-icon-valid:          var(--#{$prefix}success-icon) !default;\n$form-feedback-icon-invalid:        var(--#{$prefix}error-icon) !default;\n$form-feedback-icon-size:           add($spacer * .25, $spacer * .5) !default; // Boosted mod\n$form-feedback-line-height:         $line-height-sm !default; // Boosted mod\n$form-feedback-color:               null !default; // Boosted mod\n// scss-docs-end form-feedback-variables\n\n// scss-docs-start form-validation-colors\n$form-valid-color:                  var(--#{$prefix}success-text-emphasis) !default; // Boosted mod: instead of `$form-feedback-valid-color`\n$form-valid-border-color:           var(--#{$prefix}success) !default; // Boosted mod: instead of `$form-feedback-valid-color`\n$form-invalid-color:                var(--#{$prefix}danger-text-emphasis) !default; // Boosted mod: instead of `$form-feedback-invalid-color`\n$form-invalid-border-color:         var(--#{$prefix}danger) !default; // Boosted mod: instead of `$form-feedback-invalid-color`\n// scss-docs-end form-validation-colors\n\n// scss-docs-start form-validation-states\n$form-validation-states: (\n  \"valid\": (\n    \"color\": var(--#{$prefix}form-valid-color),\n    \"icon\": $form-feedback-icon-valid,\n    // Boosted mod: no `tooltip-color`\n    // Boosted mod: no `tooltip-bg-color`\n    // Boosted mod: no `focus-box-shadow`\n    \"border-color\": var(--#{$prefix}form-valid-border-color),\n  ),\n  \"invalid\": (\n    \"color\": var(--#{$prefix}form-invalid-color),\n    \"icon\": $form-feedback-icon-invalid,\n    // Boosted mod: no `tooltip-color`\n    // Boosted mod: no `tooltip-bg-color`\n    // Boosted mod: no `focus-box-shadow`\n    \"border-color\": var(--#{$prefix}form-invalid-border-color),\n  )\n) !default;\n// scss-docs-end form-validation-states\n\n// Z-index master list\n//\n// Warning: Avoid customizing these values. They're used for a bird's eye view\n// of components dependent on the z-axis and are designed to all work together.\n\n// scss-docs-start zindex-stack\n$zindex-dropdown:                   1000 !default;\n$zindex-sticky:                     1020 !default;\n$zindex-fixed:                      1030 !default;\n$zindex-back-to-top:                1035 !default; // Boosted mod\n$zindex-offcanvas-backdrop:         1040 !default;\n$zindex-offcanvas:                  1045 !default;\n$zindex-modal-backdrop:             1050 !default;\n$zindex-modal:                      1055 !default;\n$zindex-popover:                    1070 !default;\n$zindex-tooltip:                    1080 !default;\n$zindex-toast:                      1090 !default;\n// scss-docs-end zindex-stack\n\n// scss-docs-start zindex-levels-map\n$zindex-levels: (\n  n1: -1,\n  0: 0,\n  1: 1,\n  2: 2,\n  3: 3\n) !default;\n// scss-docs-end zindex-levels-map\n\n\n// Navs\n\n// scss-docs-start nav-variables\n$nav-link-padding-y:                $spacer * .5 !default;\n$nav-link-padding-x:                $spacer !default;\n$nav-link-font-size:                null !default;\n$nav-link-font-weight:              $font-weight-bold !default;\n$nav-link-color:                    inherit !default; // Boosted mod: instead of `var(--#{$prefix}link-color)`\n$nav-link-hover-color:              var(--#{$prefix}link-hover-color) !default;\n$nav-link-transition:               null !default; // Boosted mod\n$nav-link-disabled-color:           var(--#{$prefix}disabled-color) !default; // Boosted mod: instead of `var(--#{$prefix}secondary-color)`\n// Boosted mod: no `$nav-link-focus-box-shadow`\n\n$nav-tabs-border-color:             var(--#{$prefix}border-color) !default;\n$nav-tabs-border-width:             var(--#{$prefix}border-width) !default;\n$nav-tabs-border-radius:            var(--#{$prefix}border-radius) !default;\n$nav-tabs-link-padding-x:           1.8125rem !default; // Boosted mod\n$nav-tabs-link-hover-color:         var(--#{$prefix}highlight-color) !default; // Boosted mod\n$nav-tabs-link-hover-bg:            var(--#{$prefix}highlight-bg) !default; // Boosted mod\n$nav-tabs-link-hover-border-color:  var(--#{$prefix}border-color) !default; // Boosted mod: instead of `var(--#{$prefix}secondary-bg) var(--#{$prefix}secondary-bg) $nav-tabs-border-color`\n$nav-tabs-link-active-color:        var(--#{$prefix}emphasis-color) !default;\n$nav-tabs-link-active-bg:           var(--#{$prefix}body-bg) !default;\n$nav-tabs-link-active-border-color: $nav-tabs-link-active-color !default; // Boosted mod: instead of `var(--#{$prefix}border-color) var(--#{$prefix}border-color) $nav-tabs-link-active-bg`\n\n$nav-pills-padding-x:               1.8125rem !default; // Boosted mod\n$nav-pills-border-radius:           var(--#{$prefix}border-radius) !default;\n$nav-pills-link-active-color:       $component-active-color !default;\n$nav-pills-link-active-bg:          $component-active-bg !default;\n\n$nav-underline-gap:                       0 !default; // Boosted mod: instead of 1rem\n$nav-underline-gap-lg:                    $spacer * .5 !default; // Boosted mod\n// stylelint-disable-next-line function-disallowed-list\n$nav-underline-border-width:              calc(var(--#{$prefix}border-width) * .5) !default; // Boosted mod: instead of `.125rem`\n$nav-underline-border-color:              var(--#{$prefix}border-color-subtle) !default; // Boosted mod\n$nav-underline-border-radius:             var(--#{$prefix}border-radius) !default; // Boosted mod\n$nav-underline-link-active-color:         var(--#{$prefix}emphasis-color) !default;\n$nav-underline-link-padding-x:            1.8125rem !default; // Boosted mod\n$nav-underline-link-hover-color:          var(--#{$prefix}link-hover-color) !default; // Boosted mod\n// stylelint-disable-next-line function-disallowed-list\n$nav-underline-link-border-width:         0 0 calc(var(--#{$prefix}nav-underline-border-width) * 4) !default; // Boosted mod\n$nav-underline-link-active-bg:            transparent !default; // Boosted mod\n$nav-underline-link-active-border-color:  var(--#{$prefix}primary) !default; // Boosted mod\n// scss-docs-end nav-variables\n\n\n// Navbar\n\n// scss-docs-start navbar-variables\n$navbar-padding-y:                    .375rem !default; // Boosted mod\n$navbar-padding-x:                    null !default;\n$navbar-font-weight:                  $font-weight-bold !default; // Boosted mod\n\n$navbar-nav-link-padding-y:           1rem !default; // Boosted mod\n$navbar-nav-link-padding-x-xs:        $spacer * .25 !default; // Boosted mod\n$navbar-nav-link-padding-x:           $spacer * .5 !default; // Boosted mod\n\n$navbar-brand-font-size:              2.1875rem !default; // Boosted mod\n// Boosted mod: no nav-link-height calculation\n$navbar-brand-padding-y:              0 !default; // Boosted mod\n$navbar-brand-margin-end:             $spacer * 1.5 !default; // Boosted mod\n\n$navbar-toggler-icon-close-bg:        $cross-icon !default; // Boosted mod\n$navbar-toggler-padding-y:            $spacer * .6 !default; // Boosted mod: same as $navbar-nav-icon-padding-y-xs\n$navbar-toggler-padding-x:            $spacer * .75 !default; // Boosted mod: same as $navbar-nav-icon-padding-x-xs\n$navbar-toggler-font-size-xs:         1.04166666rem !default; // Boosted mod\n$navbar-toggler-font-size:            1.25rem !default; // Boosted mod\n$navbar-toggler-border-radius:        $btn-border-radius !default;\n$navbar-toggler-focus-width:          null !default; // Boosted mod\n$navbar-toggler-transition:           $transition-focus !default; // Boosted mod\n\n$navbar-light-color:                  var(--#{$prefix}emphasis-color) !default; // Boosted mod: instead of `rgba(var(--#{$prefix}emphasis-color-rgb), .65)`\n$navbar-light-bg:                     var(--#{$prefix}highlight-color) !default; // Boosted mod\n$navbar-light-hover-color:            var(--#{$prefix}link-hover-color) !default; // Boosted mod: instead of `rgba(var(--#{$prefix}emphasis-color-rgb), .8)`\n$navbar-light-active-color:           var(--#{$prefix}primary) !default; // Boosted mod: instead of `rgba(var(--#{$prefix}emphasis-color-rgb), 1)`\n$navbar-light-disabled-color:         var(--#{$prefix}disabled-color) !default; // Boosted mod: instead of `rgba(var(--#{$prefix}emphasis-color-rgb), .3)`\n$navbar-light-icon-color:             var(--#{$prefix}emphasis-color) !default; // Boosted mod: instead of `rgba($body-color, .75)`\n$navbar-light-icon-hover-color:       var(--#{$prefix}link-hover-color) !default; // Boosted mod\n$navbar-light-toggler-icon-bg:        $burger-icon !default; // Boosted mod: instead of inline SVG\n$navbar-light-toggler-icon-bg-small:  $burger-icon-small !default; // Boosted mod: slightly different burger icon for small breakpoints\n$navbar-light-toggler-border-color:   null !default; // Boosted mod: instead of `rgba(var(--#{$prefix}emphasis-color-rgb), .15)`\n$navbar-light-brand-color:            $navbar-light-color !default; // Boosted mod: instead of `$navbar-light-active-color`\n$navbar-light-brand-hover-color:      $navbar-light-active-color !default;\n// scss-docs-end navbar-variables\n\n// Boosted mod: Orange navbar\n// scss-docs-start orange-navbar-variables\n$navbar-transition-duration:                $transition-duration !default;\n$navbar-transition-timing-function:         $transition-timing !default;\n$navbar-transition:                         padding-top $navbar-transition-duration $navbar-transition-timing-function, padding-bottom $navbar-transition-duration $navbar-transition-timing-function, $transition-focus !default;\n$navbar-brand-transition:                   margin $navbar-transition-duration $navbar-transition-timing-function, $transition-focus !default;\n$navbar-brand-logo-transition:              width $navbar-transition-duration $navbar-transition-timing-function, height $navbar-transition-duration $navbar-transition-timing-function !default;\n$navbar-active-transition:                  bottom $navbar-transition-duration $navbar-transition-timing-function !default;\n\n$navbar-border-width:                       calc(var(--#{$prefix}border-width) * .5) !default; // stylelint-disable-line function-disallowed-list\n$navbar-border-color:                       var(--#{$prefix}border-color-subtle) !default;\n\n$navbar-brand-margin-y-xs:                  $spacer * .5 !default;\n$navbar-brand-logo-size-xs:                 $spacer * 1.5 !default;\n$navbar-brand-font-size-xs:                 1.3125rem !default;\n$navbar-brand-letter-spacing-xs:            $letter-spacing-base * 5 !default;\n$navbar-brand-font-size-two-lined-xs:       1.0625rem !default;\n$navbar-brand-letter-spacing-two-lined-xs:  $letter-spacing-base * 4 !default;\n\n$navbar-brand-margin-y:                     $spacer * .95 !default;\n$navbar-brand-logo-size:                    $spacer * 2.5 !default;\n$navbar-brand-letter-spacing:               $letter-spacing-base * 10 !default;\n$navbar-brand-font-size-two-lined:          1.8125rem !default;\n$navbar-brand-letter-spacing-two-lined:     $letter-spacing-base * 8 !default;\n\n$navbar-icon-size-xs:                       $spacer * 1.25 !default;\n$navbar-icon-size:                          $spacer * 1.5 !default;\n\n$navbar-nav-icon-padding-y-xs:              $spacer * .6 !default;\n$navbar-nav-icon-padding-x-xs:              $spacer * .75 !default;\n$navbar-nav-icon-padding-y:                 $navbar-brand-margin-y !default;\n$navbar-nav-icon-padding-x:                 $spacer !default;\n\n$navbar-supra-link-padding-y:               $spacer * .6 !default;\n$navbar-supra-link-padding-x:               .46875rem !default;\n$navbar-supra-icon-padding-y:               $spacer * .25 !default;\n$navbar-supra-icon-padding-x:               $navbar-nav-icon-padding-x-xs !default;\n$navbar-supra-icon-size:                    $navbar-icon-size-xs !default;\n\n$navbar-minimized-brand-margin-y:           $spacer * .75 !default;\n$navbar-minimized-nav-icon-padding-y:       $navbar-minimized-brand-margin-y !default;\n$navbar-minimized-toggler-padding-y:        $navbar-minimized-brand-margin-y !default;\n\n$navbar-badge-padding-y:                    .125rem !default;\n$navbar-badge-padding-x:                    .375rem !default;\n$navbar-badge-margin-top:                   .375rem !default;\n// scss-docs-end orange-navbar-variables\n// End mod\n\n// Deprecated in v5.3.3: all `$navbar-dark-*`\n$navbar-dark-border-color:          $gray-700 !default; // Boosted mod\n$navbar-dark-color:                 $white !default; // Boosted mod: instead of `rgba($white, .55)`\n$navbar-dark-hover-color:           $supporting-orange !default; // Boosted mod: instead of `rgba($white, .75)`\n$navbar-dark-active-color:          $supporting-orange !default; // Boosted mod: instead of `$white`\n$navbar-dark-disabled-color:        $gray-700 !default; // Boosted mod: instead of `rgba($white, .25)`\n// Boosted mod: no $navbar-dark-icon-color\n// Boosted mod: no $navbar-dark-toggler-icon-bg since dark toggler are handled with filter\n$navbar-dark-toggler-border-color:  transparent !default; // Boosted mod: instead of `rgba($white, .1)`\n$navbar-dark-brand-color:           inherit !default; // Boosted mod: instead of `$navbar-dark-active-color`\n$navbar-dark-brand-hover-color:     $navbar-dark-active-color !default;\n\n\n// Dropdowns\n//\n// Dropdown menu container and contents.\n\n// scss-docs-start dropdown-variables\n$dropdown-min-width:                10rem !default;\n$dropdown-padding-x:                $spacer * .5 !default; // Boosted mod: instead of `0`\n$dropdown-padding-y:                0 !default; // Boosted mod: instead of `.5rem`\n$dropdown-spacer:                   0 !default; // Boosted mod: instead of `.125rem`\n$dropdown-font-size:                $font-size-base !default;\n$dropdown-line-height:              $line-height-base !default; // Boosted mod\n$dropdown-color:                    var(--#{$prefix}body-color) !default;\n$dropdown-bg:                       var(--#{$prefix}body-bg) !default;\n$dropdown-border-color:             var(--#{$prefix}border-color-subtle) !default; // Boosted mod: instead of `var(--#{$prefix}border-color-translucent)`\n$dropdown-border-radius:            var(--#{$prefix}border-radius) !default;\n$dropdown-border-width:             var(--#{$prefix}border-width) !default;\n$dropdown-inner-border-radius:      0 !default; // Boosted mod: instead of `calc(#{$dropdown-border-radius} - #{$dropdown-border-width})`\n$dropdown-divider-bg:               $dropdown-border-color !default;\n$dropdown-divider-margin-y:         $spacer * .25 !default; // Boosted mod: instead of `$spacer * .5`\n$dropdown-box-shadow:               var(--#{$prefix}box-shadow) !default;\n\n$dropdown-link-color:               null !default; // Boosted mod: instead of `var(--#{$prefix}body-color)`\n$dropdown-link-hover-color:         $dropdown-link-color !default;\n$dropdown-link-hover-bg:            var(--#{$prefix}secondary-bg) !default; // Boosted mod: instead of `var(--#{$prefix}tertiary-bg)`\n\n$dropdown-link-active-color:        $dropdown-link-color !default; // Boosted mod: instead of `$component-active-color`\n$dropdown-link-active-bg:           var(--#{$prefix}tertiary-active-bg) !default; // Boosted mod: instead of `$component-active-bg`\n\n$dropdown-link-disabled-color:      var(--#{$prefix}disabled-color) !default; // Boosted mod: instead of `var(--#{$prefix}tertiary-color)`\n\n$dropdown-item-padding-y:           $spacer * .5 !default; // Boosted mod: instead of `$spacer * .25`\n$dropdown-item-padding-x:           $spacer * .5 !default; // Boosted mod: instead of `$spacer`\n\n$dropdown-header-color:             null !default; // Boosted mod: instead of `$gray-600`\n$dropdown-header-padding-x:         $dropdown-item-padding-x !default;\n$dropdown-header-padding-y:         $spacer !default; // Boosted mod: instead of `$dropdown-padding-y`\n// fusv-disable\n$dropdown-header-padding:           $dropdown-header-padding-y $dropdown-header-padding-x !default; // Deprecated in v5.2.0\n// fusv-enable\n// scss-docs-end dropdown-variables\n\n// Deprecated in v5.3.3: all `$dropdown-dark-*`\n$dropdown-dark-color:               $white !default; // Boosted mod\n$dropdown-dark-bg:                  $black !default; // Boosted mod\n$dropdown-dark-border-color:        $gray-700 !default; // Boosted mod\n$dropdown-dark-divider-bg:          $dropdown-dark-border-color !default; // Boosted mod\n$dropdown-dark-box-shadow:          null !default;\n$dropdown-dark-link-color:          $dropdown-dark-color !default;\n$dropdown-dark-link-hover-color:    $white !default;\n$dropdown-dark-link-hover-bg:       $gray-700 !default; // Boosted mod\n$dropdown-dark-link-active-color:   $black !default; // Boosted mod\n$dropdown-dark-link-active-bg:      $white !default; // Boosted mod\n$dropdown-dark-link-disabled-color: $gray-700 !default; // Boosted mod\n$dropdown-dark-header-color:        $white !default; // Boosted mod\n\n// Pagination\n\n// scss-docs-start pagination-variables\n$pagination-padding-y:              null !default; // Boosted mod: instead of `.375rem`\n$pagination-padding-x:              null !default; // Boosted mod: instead of `.75rem`\n// Boosted mod: no $pagination-padding-y-sm\n// Boosted mod: no $pagination-padding-x-sm\n// Boosted mod: no $pagination-padding-y-lg\n// Boosted mod: no $pagination-padding-x-lg\n\n$pagination-font-size:              $font-size-base !default;\n\n$pagination-color:                  inherit !default; // Boosted mod: instead of `var(--#{$prefix}link-color)`\n$pagination-bg:                     transparent !default; // Boosted mod: instead of `var(--#{$prefix}body-bg)`\n$pagination-border-radius:          var(--#{$prefix}border-radius) !default;\n$pagination-border-width:           var(--#{$prefix}border-width) !default;\n$pagination-margin-y:               $spacer !default; // Boosted mod\n$pagination-margin-start:           0 !default; // Boosted mod: instead of `calc(-1 * $pagination-border-width)`\n$pagination-margin-x-first-last:    $spacer * .5 !default; // Boosted mod\n$pagination-border-color:           transparent !default; // Boosted mod: instead of `var(--#{$prefix}border-color)`\n\n// Deprecated in v5.3.3\n// fusv-disable\n$pagination-focus-color:            null !default; // Boosted mod\n$pagination-focus-bg:               null !default; // Boosted mod: instead of `var(--#{$prefix}secondary-bg)`\n$pagination-focus-box-shadow:       0 0 0 $focus-visible-inner-width var(--#{$prefix}focus-visible-inner-color) !default; // Boosted mod: no `$focus-ring-box-shadow`\n$pagination-focus-outline:          null !default; // Boosted mod\n// fusv-enable\n\n$pagination-hover-color:            var(--#{$prefix}body-color) !default; // Boosted mod: instead of `var(--#{$prefix}link-hover-color)`\n$pagination-hover-bg:               var(--#{$prefix}secondary-bg) !default; // Boosted mod: instead of `var(--#{$prefix}tertiary-bg)`\n$pagination-hover-border-color:     $pagination-hover-bg !default; // Boosted mod: instead of `var(--#{$prefix}border-color)`\n\n$pagination-active-color:           var(--#{$prefix}highlight-color) !default; // Boosted mod: instead of `$component-active-color`\n$pagination-active-bg:              var(--#{$prefix}highlight-bg) !default; // Boosted mod: instead of `$component-active-bg`\n$pagination-active-border-color:    $pagination-active-bg !default; // Boosted mod: instead of `$component-active-bg`\n\n$pagination-disabled-color:         var(--#{$prefix}disabled-color) !default; // Boosted mod: instead of `var(--#{$prefix}secondary-color)`\n$pagination-disabled-bg:            transparent !default; // Boosted mod: instead of `var(--#{$prefix}secondary-bg)`\n$pagination-disabled-border-color:  transparent !default; // Boosted mod: instead of `var(--#{$prefix}border-color)`\n\n$pagination-transition:             $transition-focus !default; // Boosted mod: no color, bg-color, border-color, box-shadow\n\n// Boosted mod: no $pagination-border-radius-sm\n// Boosted mod: no $pagination-border-radius-lg\n\n// Boosted mod\n$pagination-padding-end:            1.125rem !default;\n$pagination-icon:                   var(--#{$prefix}chevron-icon) !default;\n$pagination-icon-size:              subtract($spacer * 2, calc(var(--#{$prefix}border-width) * 2)) !default; // stylelint-disable-line function-disallowed-list\n$pagination-icon-width:             add(.5rem, 1px) !default;\n$pagination-icon-height:            subtract(1rem, 1px) !default;\n\n$pagination-active-item-bg:         $supporting-orange !default;\n$pagination-active-item-color:      $black !default;\n$pagination-active-item-border:     $pagination-active-item-bg !default;\n// End mod\n// scss-docs-end pagination-variables\n\n\n// Placeholders\n\n// scss-docs-start placeholders\n$placeholder-opacity-max:           .5 !default;\n$placeholder-opacity-min:           .2 !default;\n// scss-docs-end placeholders\n\n// Cards\n\n// scss-docs-start card-variables\n$card-spacer-top:                   $spacer * .75 !default; // Boosted mod\n$card-spacer-bottom:                $spacer !default; // Boosted mod\n// fusv-disable\n$card-spacer-y:                     $spacer !default; // Deprecated in v5.2.3\n// fusv-enable\n$card-spacer-x:                     $spacer !default;\n$card-title-spacer-y:               $spacer * .5 !default;\n$card-title-color:                  null !default;\n$card-subtitle-color:               null !default;\n$card-border-width:                 var(--#{$prefix}border-width) !default;\n$card-border-color:                 var(--#{$prefix}border-color-subtle) !default; // Boosted mod: instead of `var(--#{$prefix}border-color-translucent)`\n$card-border-radius:                var(--#{$prefix}border-radius) !default;\n$card-box-shadow:                   null !default;\n$card-inner-border-radius:          subtract($card-border-radius, $card-border-width) !default;\n$card-cap-padding-y:                $card-spacer-bottom * .5 !default; // Boosted mod\n$card-cap-padding-x:                $card-spacer-x !default;\n$card-cap-bg:                       var(--#{$prefix}highlight-bg) !default; // Boosted mod: instead of `rgba(var(--#{$prefix}body-color-rgb), .03)`\n$card-cap-color:                    var(--#{$prefix}highlight-color) !default; // Boosted mod: instead of `null`\n$card-cap-font-weight:              $font-weight-bold !default; // Boosted mod\n$card-height:                       null !default;\n$card-color:                        null !default;\n$card-bg:                           var(--#{$prefix}body-bg) !default;\n$card-img-overlay-padding:          $spacer !default;\n$card-group-margin:                 $grid-gutter-width * .5 !default;\n$card-footer-color:                 var(--#{$prefix}secondary-color) !default; // Boosted mod\n// scss-docs-end card-variables\n\n// Accordion\n\n// scss-docs-start accordion-variables\n$accordion-padding-y:                     $spacer * .5 !default; // Boosted mod\n$accordion-padding-x:                     0 !default; // Boosted mod\n$accordion-color:                         null !default; // Boosted mod: instead of `var(--#{$prefix}body-color)`\n$accordion-bg:                            transparent !default; // Boosted mod: instead of `var(--#{$prefix}body-bg)`\n// stylelint-disable-next-line function-disallowed-list\n$accordion-border-width:                  calc(var(--#{$prefix}border-width) * .5) !default; // Boosted mod\n$accordion-border-color:                  var(--#{$prefix}border-color-subtle) !default; // Boosted mod: instead of `var(--#{$prefix}border-color)`\n$accordion-border-radius:                 var(--#{$prefix}border-radius) !default;\n$accordion-inner-border-radius:           subtract($accordion-border-radius, #{$accordion-border-width}) !default;\n\n$accordion-body-padding-top:              $spacer !default; // Boosted mod\n$accordion-body-padding-end:              0 !default; // Boosted mod\n$accordion-body-padding-bottom:           $spacer * 1.5 !default; // Boosted mod\n$accordion-body-padding-start:            0 !default; // Boosted mod\n// fusv-disable\n$accordion-body-padding-y:                $spacer !default; // Deprecated in Boosted 5.2.3 to divide it in -padding<top | end | bottom |start>\n$accordion-body-padding-x:                $spacer !default; // Deprecated in Boosted 5.2.3 to divide it in -padding<top | end | bottom |start>\n// fusv-enable\n\n$accordion-button-padding-y:              $accordion-padding-y !default;\n$accordion-button-padding-x:              $accordion-padding-x !default;\n$accordion-button-color:                  null !default; // Boosted mod: instead of `var(--#{$prefix}body-color)`\n$accordion-button-bg:                     var(--#{$prefix}accordion-bg) !default;\n$accordion-transition:                    $btn-transition, border-radius .15s ease !default;\n$accordion-button-hover-bg:               var(--#{$prefix}secondary-bg) !default; // Boosted mod\n$accordion-button-active-bg:              null !default; // Boosted mod: instead of `var(--#{$prefix}primary-bg-subtle)`\n$accordion-button-active-color:           $accordion-button-color !default; // Boosted mod: instead of `var(--#{$prefix}primary-text-emphasis)`\n\n// Boosted mod: no $accordion-button-focus-border-color\n// Boosted mod: no $accordion-button-focus-box-shadow\n\n// Boosted mod: no $accordion-icon-width\n// Boosted mod: no $accordion-icon-color\n// Boosted mod: no $accordion-icon-active-color\n// Boosted mod: no $accordion-icon-transition\n$accordion-icon-transform:                scaleY(-1) !default;\n\n// Boosted mod: no $accordion-button-icon\n// Boosted mod: no $accordion-button-active-icon\n\n// Boosted mod: accordion sizes\n$accordion-button-font-size:              $h3-font-size !default;\n$accordion-button-line-height:            null !default;\n$accordion-button-font-weight:            $font-weight-bold !default;\n$accordion-button-letter-spacing:         $h3-spacing !default;\n$accordion-button-font-size-sm:           $h5-font-size !default;\n$accordion-button-line-height-sm:         $h5-line-height !default;\n$accordion-button-letter-spacing-sm:      $h5-spacing !default;\n$accordion-button-font-size-lg:           $h2-font-size !default;\n$accordion-button-line-height-lg:         calc(40 / 30) !default; // stylelint-disable-line function-disallowed-list\n$accordion-button-letter-spacing-lg:      $h2-spacing !default;\n// End mod\n// scss-docs-end accordion-variables\n\n// Tooltips\n\n// scss-docs-start tooltip-variables\n$tooltip-font-size:                 $font-size-sm !default;\n$tooltip-font-weight:               $font-weight-bold !default; // Boosted mod\n$tooltip-line-height:               $line-height-sm !default; // Boosted mod\n$tooltip-max-width:                 $spacer * 10 !default;\n$tooltip-color:                     null !default; // Boosted mod: instead of `var(--#{$prefix}body-bg)`\n$tooltip-bg:                        var(--#{$prefix}body-bg) !default; // Boosted mod: instead of `var(--#{$prefix}emphasis-color)`\n// stylelint-disable-next-line function-disallowed-list\n$tooltip-border-width:              calc(var(--#{$prefix}border-width) * .5) !default; // Boosted mod\n$tooltip-border-color:              var(--#{$prefix}emphasis-color) !default; // Boosted mod\n$tooltip-border-radius:             var(--#{$prefix}border-radius) !default;\n$tooltip-opacity:                   1 !default;\n$tooltip-padding-y:                 $spacer * .5 !default;\n$tooltip-padding-x:                 $spacer * .5 !default;\n$tooltip-margin:                    null !default; // TODO: remove this in v6\n\n$tooltip-arrow-width:               $spacer * .5 !default;\n$tooltip-arrow-height:              $tooltip-arrow-width * .5 !default;\n// fusv-disable\n$tooltip-arrow-color:               null !default; // Deprecated in Boosted 5.2.0 for CSS variables\n// fusv-enable\n// scss-docs-end tooltip-variables\n\n// Boosted mod: no form tooltips\n\n\n// Popovers\n\n// scss-docs-start popover-variables\n$popover-font-size:                 $font-size-base !default; // Boosted mod: instead of `$font-size-sm`\n$popover-line-height:               1.5 !default; // Boosted mod\n$popover-font-weight:               $font-weight-bold !default; // Boosted mod\n$popover-bg:                        var(--#{$prefix}body-bg) !default;\n$popover-max-width:                 $spacer * 19 !default; // Boosted mod: instead of `276px`\n$popover-padding-y:                 $spacer !default; // Boosted mod\n$popover-border-width:              var(--#{$prefix}border-width) !default;\n$popover-border-color:              var(--#{$prefix}border-color-subtle) !default; // Boosted mod: instead of `var(--#{$prefix}border-color-translucent)`\n$popover-border-radius:             var(--#{$prefix}border-radius-lg) !default;\n$popover-inner-border-radius:       calc(#{$popover-border-radius} - #{$popover-border-width}) !default; // stylelint-disable-line function-disallowed-list\n$popover-box-shadow:                var(--#{$prefix}box-shadow) !default;\n\n$popover-header-font-size:          $font-size-lg !default; // Boosted mod: instead of `$font-size-base`\n$popover-header-line-height:        1.11 !default; // Boosted mod\n$popover-header-bg:                 $popover-bg !default; // Boosted mod: instead of `var(--#{$prefix}secondary-bg)`\n$popover-header-color:              var(--#{$prefix}heading-color) !default; // Boosted mod: instead of `$headings-color`\n$popover-header-padding-top:        $popover-padding-y !default; // Boosted mod\n$popover-header-padding-bottom:     map-get($spacers, 2) !default; // Boosted mod\n$popover-header-padding-y:          initial !default; // Boosted mod: instead of `.5rem`\n$popover-header-padding-x:          $spacer * .9 !default; // Boosted mod: instead of `$spacer`\n\n$popover-body-color:                null !default; // Boosted mod: instead of `var(--#{$prefix}body-color)`\n$popover-body-padding-top:          0 !default; // Boosted mod\n$popover-body-padding-bottom:       $popover-padding-y !default; // Boosted mod\n$popover-body-padding-y:            initial !default; // Boosted mod: instead of `$spacer`\n$popover-body-padding-x:            $popover-header-padding-x !default; // Boosted mod: instead of `$spacer`\n\n$popover-arrow-width:               $spacer !default; // Boosted mod: instead of `1rem`\n$popover-arrow-height:              $popover-arrow-width * .5 !default; // Boosted mod: instead of `.5rem`\n// scss-docs-end popover-variables\n\n// fusv-disable\n// Deprecated in Bootstrap 5.2.0 for CSS variables\n$popover-arrow-color:               $popover-bg !default;\n$popover-arrow-outer-color:         $popover-border-color !default; // Boosted mod: instead of `var(--#{$prefix}border-color-translucent)`\n// fusv-enable\n\n// Toasts\n\n// scss-docs-start toast-variables\n$toast-max-width:                   21.875rem !default;\n$toast-padding-x:                   $spacer * .5 !default;\n$toast-padding-y:                   $spacer * .25 !default;\n$toast-font-size:                   .875rem !default;\n$toast-color:                       var(--#{$prefix}emphasis-color) !default; // Boosted mod: instead of `null` due to some `bg-dark` issue\n$toast-background-color:            rgba(var(--#{$prefix}body-bg-rgb), .85) !default;\n$toast-border-width:                var(--#{$prefix}border-width) !default;\n$toast-border-color:                var(--#{$prefix}border-color-subtle) !default; // Boosted mod: instead of `var(--#{$prefix}border-color-translucent)`\n$toast-border-radius:               var(--#{$prefix}border-radius) !default;\n$toast-box-shadow:                  var(--#{$prefix}box-shadow) !default;\n$toast-spacing:                     $container-padding-x !default;\n\n$toast-header-color:                null !default; // Boosted mod: instead of `var(--#{$prefix}secondary-color)`\n$toast-header-background-color:     rgba(var(--#{$prefix}body-bg-rgb), .85) !default;\n$toast-header-border-color:         rgba($black, .05) !default; // Boosted mod: instead of `$toast-border-color`\n// scss-docs-end toast-variables\n\n// Badges\n\n// scss-docs-start badge-variables\n$badge-font-size:                   .75em !default;\n$badge-font-weight:                 $font-weight-bold !default;\n$badge-color:                       $white !default;\n$badge-padding-y:                   .35em !default;\n$badge-padding-x:                   .65em !default;\n$badge-border-radius:               var(--#{$prefix}border-radius) !default;\n// scss-docs-end badge-variables\n\n// Modals\n\n// scss-docs-start modal-variables\n$modal-inner-padding:               $spacer * .5 $spacer !default;\n\n$modal-footer-margin-between:       $spacer * .5 !default;\n$modal-footer-padding:              $spacer * .5 subtract($spacer, $modal-footer-margin-between * .5) 0 !default; // Boosted mod\n\n$modal-dialog-margin:               $spacer * .5 !default;\n$modal-dialog-margin-y-sm-up:       $spacer * 1.5 !default;\n\n$modal-title-line-height:           $line-height-base !default;\n\n$modal-content-padding-y:           $spacer !default; // Boosted mod\n$modal-content-padding-x:           0 !default; // Boosted mod\n$modal-content-padding:             $modal-content-padding-y $modal-content-padding-x !default; // Boosted mod\n$modal-content-color:               var(--#{$prefix}body-color) !default;\n$modal-content-bg:                  var(--#{$prefix}body-bg) !default;\n$modal-content-border-color:        var(--#{$prefix}border-color-subtle) !default; // Boosted mod: instead of `var(--#{$prefix}border-color-translucent)`\n$modal-content-border-width:        var(--#{$prefix}border-width) !default;\n$modal-content-border-radius:       var(--#{$prefix}border-radius-lg) !default;\n$modal-content-inner-border-radius: var(--#{$prefix}border-radius) !default; // Boosted mod: instead of `subtract($modal-content-border-radius, $modal-content-border-width)`\n$modal-content-box-shadow-xs:       var(--#{$prefix}box-shadow-sm) !default;\n$modal-content-box-shadow-sm-up:    var(--#{$prefix}box-shadow) !default;\n\n$modal-backdrop-bg:                 $black !default;\n$modal-backdrop-opacity:            .5 !default;\n\n$modal-header-border-color:         null !default; // Boosted mod\n$modal-header-border-width:         $modal-content-border-width !default;\n$modal-header-padding-y:            0 !default;\n$modal-header-padding-x:            $spacer !default;\n$modal-header-padding:              $modal-header-padding-y $modal-header-padding-x !default; // Keep this for backwards compatibility\n\n$modal-footer-bg:                   null !default;\n$modal-footer-border-color:         null !default; // Boosted mod\n$modal-footer-border-width:         $modal-header-border-width !default;\n$modal-footer-margin-top:           $spacer * .5 !default; // Boosted mod\n$modal-footer-margin-top-sm:        $spacer * .75 !default; // Boosted mod\n\n// Boosted mod\n//// Scrollable modal\n$modal-scrollable-inner-padding:     $spacer !default;\n$modal-scrollable-inner-margin:      $spacer 0 0 !default;\n$modal-scrollable-footer-margin-top: $spacer * .5 !default;\n\n//// Modal with top image\n$modal-img-margin:                  -$modal-content-padding-y 0 $modal-content-padding-y !default; // Boosted mod\n$modal-img-btn-close-offset:        $modal-content-padding-y !default;\n// End mod\n\n$modal-sm:                          300px !default;\n$modal-md:                          460px !default;\n$modal-lg:                          700px !default;\n$modal-xl:                          940px !default;\n\n$modal-fade-transform:              translate(0, -50px) !default;\n$modal-show-transform:              none !default;\n$modal-transition:                  transform .3s ease-out !default;\n$modal-scale-transform:             scale(1.02) !default;\n// scss-docs-end modal-variables\n\n// Alerts\n//\n// Define alert colors, border radius, and padding.\n\n// scss-docs-start alert-variables\n$alert-padding-y:                   1rem !default;\n$alert-padding-x:                   $spacer !default;\n$alert-margin-bottom:               $spacer !default;\n$alert-color:                       var(--#{$prefix}body-color) !default; // Boosted mod\n$alert-border-radius:               var(--#{$prefix}border-radius) !default;\n$alert-link-font-weight:            null !default; // Boosted mod\n$alert-heading-font-weight:         $font-weight-bold !default; // Boosted mod\n$alert-border-width:                var(--#{$prefix}border-width) !default;\n\n// Boosted mod\n$alert-padding-sm:                  $spacer * .5 !default;\n$alert-icons: (\n  \"success\": var(--#{$prefix}success-icon),\n  \"info\":    escape-svg($info-icon),\n  // Create a list for this warning icon to indicate that the mask needs to be replaced by a background image\n  // Be aware that the background of the icon won't change anymore\n  // Note: `true` parameter is only used to create a list, it could be empty (e.g. `(escape-svg($warning-icon),)`)\n  \"warning\": (escape-svg($warning-icon-filled), true),\n  \"danger\":  var(--#{$prefix}error-icon)\n) !default;\n$alert-logo-size:                   add($spacer * .5, 1rem) !default;\n$alert-logo-size-sm:                add(1rem, 1px) !default;\n$alert-icon-size:                   3rem !default;\n$alert-icon-size-sm:                $alert-icon-size * .5 !default;\n$alert-icon-margin-y:               $spacer * .1 !default;\n$alert-btn-close-offset:            .5rem !default;\n$alert-btn-close-offset-sm:         $spacer * .25 !default;\n// End mod\n\n$alert-dismissible-padding-r:       $alert-padding-y * 3 !default; // 3x covers width of x plus default padding on either side\n// scss-docs-end alert-variables\n\n// Progress bars\n\n// scss-docs-start progress-variables\n$progress-height:                   $spacer !default;\n$progress-font-size:                $font-size-base !default;\n$progress-bg:                       var(--#{$prefix}tertiary-active-bg) !default; // Boosted mod: instead of `var(--#{$prefix}secondary-bg)`\n$progress-border-radius:            var(--#{$prefix}border-radius) !default;\n$progress-box-shadow:               var(--#{$prefix}box-shadow-inset) !default;\n$progress-bar-color:                var(--#{$prefix}highlight-color) !default; // Boosted mod: instead of `$white`\n$progress-bar-font-weight:          $font-weight-bold !default; // Boosted mod\n$progress-bar-text-indent:          map-get($spacers, 2) !default; // Boosted mod\n$progress-bar-bg:                   var(--#{$prefix}primary) !default; // Boosted mod: instead of `$primary`\n$progress-bar-animation-timing:     1s linear infinite !default;\n$progress-bar-transition:           width .6s ease !default;\n\n// Boosted mod\n$progress-height-sm:                $spacer * .5 !default;\n$progress-height-xs:                $spacer * .25 !default;\n// End mod\n// scss-docs-end progress-variables\n\n// List group\n\n// scss-docs-start list-group-variables\n$list-group-font-weight:              $font-weight-bold !default; // Boosted mod\n$list-group-color:                    null !default; // Boosted mod: instead of `var(--#{$prefix}body-color)`\n$list-group-bg:                       transparent !default; // Boosted mod: instead of `var(--#{$prefix}body-bg)`\n$list-group-border-color:             var(--#{$prefix}border-color-subtle) !default; // Boosted mod: instead of `var(--#{$prefix}border-color)`\n$list-group-border-width:             var(--#{$prefix}border-width) !default;\n$list-group-border-radius:            var(--#{$prefix}border-radius) !default;\n\n$list-group-divider-size:             map-get($border-widths, 1) !default;\n\n$list-group-item-padding-y:           10px !default; // Boosted mod: instead of $spacer * .5\n$list-group-item-padding-x:           13px !default; // Boosted mod: instead of $spacer\n$list-group-item-icon-size:           $spacer * .85 !default; // Boosted mod\n$list-group-item-icon-margin-x:       subtract(var(--#{$prefix}list-group-item-padding-x), 5px) !default; // Boosted mod\n\n$list-group-numbered-item-margin-end: 14px !default; // Boosted mod\n\n$list-group-hover-bg:                 var(--#{$prefix}secondary-bg) !default; // Boosted mod: instead of `var(--#{$prefix}tertiary-bg)`\n$list-group-active-color:             var(--#{$prefix}emphasis-color) !default; // Boosted mod: instead of `$component-active-color`\n$list-group-active-bg:                no-repeat linear-gradient(to right, var(--#{$prefix}primary) 4px, var(--#{$prefix}tertiary-active-bg) 4px) !default; // Boosted mod: instead of `$component-active-bg`\n$list-group-active-border-color:      $list-group-border-color !default; // Boosted mod: instead of `$list-group-active-bg`\n\n$list-group-disabled-color:           var(--#{$prefix}disabled-color) !default; // Boosted mod: instead of `var(--#{$prefix}secondary-color)`\n$list-group-disabled-bg:              $list-group-bg !default;\n\n$list-group-action-color:             var(--#{$prefix}emphasis-color) !default; // Boosted mod: instead of `var(--#{$prefix}secondary-color)`\n$list-group-action-hover-color:       var(--#{$prefix}emphasis-color) !default;\n\n$list-group-action-active-color:      $list-group-active-color !default; // Boosted mod: instead of `var(--#{$prefix}body-color)`\n$list-group-action-active-bg:         $list-group-active-bg !default; // Boosted mod: instead of `var(--#{$prefix}secondary-bg)`\n\n// Boosted mod\n$list-group-icons: (\n  \"success\": var(--#{$prefix}success-icon),\n  \"info\":    escape-svg($info-icon),\n  \"warning\": escape-svg($warning-icon),\n  \"danger\":  var(--#{$prefix}error-icon)\n) !default;\n// End mod\n// scss-docs-end list-group-variables\n\n\n// Image thumbnails\n\n// scss-docs-start thumbnail-variables\n$thumbnail-padding:                 0 !default; // Boosted mod\n$thumbnail-bg:                      var(--#{$prefix}body-bg) !default;\n$thumbnail-border-width:            var(--#{$prefix}border-width) !default;\n$thumbnail-border-color:            var(--#{$prefix}border-color-subtle) !default; // Boosted mod: instead of `var(--#{$prefix}border-color-translucent)`\n$thumbnail-border-radius:           var(--#{$prefix}border-radius) !default;\n$thumbnail-box-shadow:              var(--#{$prefix}box-shadow-sm) !default;\n// scss-docs-end thumbnail-variables\n\n// Figures\n\n// scss-docs-start figure-variables\n$figure-caption-font-size:          $small-font-size !default;\n$figure-caption-color:              var(--#{$prefix}secondary-color) !default;\n// scss-docs-end figure-variables\n\n// Boosted mod\n// Title bars\n\n// scss-docs-start title-bars-variables\n$title-bar-bg:                      var(--#{$prefix}body-bg) !default;\n$title-bar-color:                   var(--#{$prefix}body-color) !default;\n$title-bar-image-ratio:             1.8em !default;\n$title-bar-padding-y:               .3333333em !default;\n$title-bar-font-size:               $h2-font-size !default;\n$title-bar-line-height:             $display-line-height !default;\n$title-bar-letter-spacing:          $h2-spacing !default;\n$title-bar-border-width:            calc(var(--#{$prefix}border-width) * .5) !default; // stylelint-disable-line function-disallowed-list\n$title-bar-border-color:            var(--#{$prefix}border-color-subtle) !default;\n\n$title-bar-font-size-md:            $display2-size !default;\n$title-bar-letter-spacing-md:       $display2-spacing !default;\n\n$title-bar-font-size-xl:            $display1-size !default;\n$title-bar-letter-spacing-xl:       $display1-spacing !default;\n\n// fusv-disable\n$title-bar-border-color-dark:       $gray-700 !default; // Deprecated in v5.3.3\n// fusv-enable\n// scss-docs-end title-bars-variables\n// End mod\n\n\n// Breadcrumbs\n\n// scss-docs-start breadcrumb-variables\n$breadcrumb-font-size:              $font-size-sm !default;\n$breadcrumb-font-weight:            $font-weight-bold !default; // Boosted mod\n\n$breadcrumb-padding-y:              .5rem !default;\n$breadcrumb-padding-x:              0 !default;\n$breadcrumb-item-padding-x:         $spacer * .5 !default;\n$breadcrumb-margin-bottom:          1rem !default;\n$breadcrumb-color:                  var(--#{$prefix}emphasis-color) !default; // Boosted mod\n$breadcrumb-bg:                     null !default;\n$breadcrumb-divider-color:          $black !default; // Boosted mod: instead of `var(--#{$prefix}secondary-color)`\n$breadcrumb-active-color:           null !default; // Boosted mod: instead of `var(--#{$prefix}secondary-color)`\n$breadcrumb-divider:                url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 9 14' width='7' height='10'><path d='m-.4 12 2 2 7-7-7-7-2 2 5 5z'/></svg>\") !default;\n$breadcrumb-divider-filter:         none !default; // Boosted mod\n$breadcrumb-divider-flipped:        $breadcrumb-divider !default;\n$breadcrumb-border-radius:          null !default;\n// scss-docs-end breadcrumb-variables\n\n\n// Carousel\n\n// scss-docs-start carousel-variables\n$carousel-control-color:             $black !default;\n$carousel-control-width:             $spacer * 3 !default;\n$carousel-control-opacity:           null !default;\n$carousel-control-hover-opacity:     null !default;\n$carousel-control-transition:        $transition-focus !default;\n// Boosted mod: no $carousel-control-icon-filter\n\n$carousel-indicator-width:           .5rem !default;\n$carousel-indicator-height:          .5rem !default;\n$carousel-indicator-hit-area-height: $spacer * 1.5 !default;\n$carousel-indicator-spacer:          $spacer * .5 !default;\n$carousel-indicator-opacity:         null !default;\n$carousel-indicator-active-bg:       $white !default;\n$carousel-indicator-active-opacity:  null !default;\n$carousel-indicator-transition:      null !default;\n// Boosted mod\n$carousel-indicator-hover-scale:        1.5 !default;\n$carousel-indicator-active-scale:       calc(2 / 3) !default; // stylelint-disable-line function-disallowed-list\n$carousel-indicator-active-radius:      0 100% 100% 0 / 50% !default;\n$carousel-indicator-animation-duration: 5000ms !default;\n$carousel-indicator-animation-interval: var(--#{$prefix}carousel-interval, #{$carousel-indicator-animation-duration}) !default;\n$carousel-indicators-padding-y:         $spacer * .5 !default;\n$carousel-indicators-margin-bottom:     $spacer !default;\n// End mod\n\n$carousel-caption-width:             70% !default;\n$carousel-caption-color:             null !default; // Boosted mod: instead of `var(--#{$prefix}body-color)`\n$carousel-caption-bg:                var(--#{$prefix}body-bg) !default; // Boosted mod\n$carousel-caption-padding-y:         $spacer !default;\n$carousel-caption-padding-x:         $spacer !default; // Boosted mod\n$carousel-caption-spacer:            $spacer * 3 !default;\n\n$carousel-control-icon-width:        2.5rem !default;\n// Boosted mod\n$carousel-control-icon-size:         1rem 1.5rem !default;\n$carousel-control-icon-bg:           var(--#{$prefix}chevron-icon) !default;\n$carousel-control-icon-color:        $black !default; // Boosted mod\n$carousel-control-icon-active-bg:    $component-active-bg !default;\n\n$carousel-control-pause-indicators-spacing: 10px !default;\n$carousel-control-pause-icon:               $pause-icon !default;\n$carousel-control-play-icon:                $play-icon !default;\n$carousel-control-pause-button-size:        .75rem !default;\n$carousel-control-pause-icon-size:          .75rem .75rem !default;\n// End mod\n\n$carousel-transition-duration:       .6s !default;\n$carousel-transition:                transform $carousel-transition-duration $transition-timing !default; // Define transform transition first if using multiple transitions (e.g., `transform 2s ease, opacity .5s ease-out`)\n// scss-docs-end carousel-variables\n\n// Boosted mod: no dark carousel variables\n\n\n// Spinners\n\n// scss-docs-start spinner-variables\n$spinner-color:           null !default; // Boosted mod\n$spinner-width:           $spacer * 2 !default;\n$spinner-height:          $spinner-width !default;\n$spinner-vertical-align:  -.125em !default;\n// stylelint-disable-next-line function-disallowed-list\n$spinner-border-width:    calc(var(--#{$prefix}border-width) * 3) !default; // Boosted mod\n$spinner-animation-speed: .75s !default;\n\n$spinner-width-sm:        $spacer !default;\n$spinner-height-sm:       $spinner-width-sm !default;\n// stylelint-disable-next-line function-disallowed-list\n$spinner-border-width-sm: calc(var(--#{$prefix}border-width) * 2) !default; // Boosted mod\n\n$spinner-width-lg:        $spacer * 4 !default; // Boosted mod\n$spinner-height-lg:       $spinner-width-lg !default; // Boosted mod\n// stylelint-disable-next-line function-disallowed-list\n$spinner-border-width-lg: calc(var(--#{$prefix}border-width) * 4) !default; // Boosted mod\n// scss-docs-end spinner-variables\n\n\n// Close\n\n// scss-docs-start close-variables\n$btn-close-width:               $spacer !default; // Boosted mod\n$btn-close-height:              $btn-close-width !default;\n$btn-close-padding:             var(--#{$prefix}icon-spacing, #{$btn-icon-padding-x}) !default; // Boosted mod\n$btn-close-border-width:        var(--#{$prefix}border-width) !default; // Boosted mod\n$btn-close-border-color:        transparent !default; // Boosted mod\n$btn-close-color:               var(--#{$prefix}emphasis-color) !default;\n$btn-close-bg:                  var(--#{$prefix}close-icon) !default; // Boosted mod\n// Boosted mod\n// fusv-disable\n$btn-close-focus-shadow:        $btn-focus-box-shadow !default; // Deprecated in v5.3.0\n// fusv-enable\n// End mod\n\n// Boosted mod: no opacity/filter\n\n// Boosted mod\n$btn-close-hover-color:         $btn-close-color !default;\n$btn-close-active-color:        var(--#{$prefix}primary) !default;\n$btn-close-active-border-color: var(--#{$prefix}border-color-subtle) !default;\n$btn-close-disabled-color:      var(--#{$prefix}disabled-color) !default;\n\n$btn-close-icon-size:           1rem auto !default;\n$btn-close-padding-sm:          subtract($btn-icon-padding-x, $spacer * .25) !default;\n// End mod\n// scss-docs-end close-variables\n\n// Deprecated in v5.3.3: all `$btn-close-white-*`\n$btn-close-white-color:               $white !default; // Boosted mod\n$btn-close-white-bg:                  transparent !default; // Boosted mod\n$btn-close-white-border-color:        transparent !default; // Boosted mod\n$btn-close-white-hover-color:         $btn-close-white-color !default; // Boosted mod\n$btn-close-white-active-color:        $supporting-orange !default; // Boosted mod\n$btn-close-white-active-border-color: $gray-700 !default; // Boosted mod\n$btn-close-white-disabled-color:      $gray-700 !default; // Boosted mod\n\n// Offcanvas\n\n// scss-docs-start offcanvas-variables\n$offcanvas-padding-y:               $modal-inner-padding !default;\n$offcanvas-padding-x:               $modal-inner-padding !default;\n$offcanvas-horizontal-width:        400px !default;\n$offcanvas-vertical-height:         30vh !default;\n$offcanvas-transition-duration:     .3s !default;\n$offcanvas-border-color:            $modal-content-border-color !default;\n$offcanvas-border-width:            $modal-content-border-width !default;\n$offcanvas-title-line-height:       $modal-title-line-height !default;\n$offcanvas-bg-color:                $modal-content-bg !default; // Boosted mod: instead of `var(--#{$prefix}body-bg)`\n$offcanvas-color:                   $modal-content-color !default; // Boosted mod: instead of `var(--#{$prefix}body-color)`\n$offcanvas-box-shadow:              none !default; // Boosted mod\n$offcanvas-backdrop-bg:             $modal-backdrop-bg !default;\n$offcanvas-backdrop-opacity:        $modal-backdrop-opacity !default;\n// scss-docs-end offcanvas-variables\n\n// Code\n// Boosted mod\n$code-font-size:                    .875em !default;\n$code-color:                        $gray-700 !default;\n\n$kbd-padding-y:                     $spacer * .05 !default;\n$kbd-padding-x:                     $spacer * .05 !default;\n$kbd-font-size:                     $code-font-size !default;\n$kbd-color:                         var(--#{$prefix}kbd-color, $black) !default;\n$kbd-bg:                            var(--#{$prefix}kbd-bg, $gray-300) !default;\n$nested-kbd-font-weight:            null !default; // Deprecated in v5.2.0, removing in v6\n\n$pre-color:                         var(--#{$prefix}code-color) !default;\n$pre-line-height:                   1.25 !default;\n// End mod\n\n//\n// Boosted mod\n//\n\n//// Scroll margin\n$scroll-offset-top:              $spacer * 6 !default; // Matching .navbar computed height\n\n//// Back to top\n// scss-docs-start back-to-top\n$back-to-top-display-threshold:  100vh !default;\n$back-to-top-target-id:          \"top\" !default;\n$back-to-top-target-offset-top:  $scroll-offset-top !default;\n$back-to-top-offset:             $spacer * 1.5 !default;\n$back-to-top-offset-right:       $back-to-top-offset !default;\n$back-to-top-offset-bottom:      $back-to-top-offset !default;\n$back-to-top-link-offset-top:    subtract(100vh, $back-to-top-offset * 4) !default;\n$back-to-top-link-offset-top-xl: subtract(100vh, $spacer * 5) !default;\n$back-to-top-title-offset-right: add(100%, var(--#{$prefix}border-width)) !default;\n$back-to-top-title-padding:      subtract($btn-padding-y, 1px) $btn-padding-x add($btn-padding-y, 1px) !default;\n$back-to-top-title-color:        var(--#{$prefix}body-color) !default;\n$back-to-top-title-bg-color:     var(--#{$prefix}body-bg) !default;\n$back-to-top-bg:                 var(--#{$prefix}highlight-color) !default;\n$back-to-top-icon:               var(--#{$prefix}chevron-icon) !default;\n$back-to-top-icon-width:         add(.5rem, 1px) !default;\n$back-to-top-icon-height:        subtract(1rem, 1px) !default;\n// scss-docs-end back-to-top\n\n//// Stepped process\n// scss-docs-start stepped-process\n$stepped-process-font-size:   $small-font-size !default;\n$stepped-process-font-weight: $font-weight-bold !default;\n$stepped-process-max-items:   5 !default;\n$stepped-process-counter:     step !default; // Used as a counter name\n$stepped-process-bg:          var(--#{$prefix}body-bg) !default;\n\n$step-item-padding:           7px !default;\n// fusv-disable\n$step-item-padding-end:       $step-item-padding * 2 !default; // Deprecated in v5.2.0\n// fusv-enable\n$step-item-margin-end:        var(--#{$prefix}border-width) !default;\n$step-item-bg:                var(--#{$prefix}secondary) !default;\n$step-item-active-bg:         $supporting-orange !default;\n$step-item-next-bg:           var(--#{$prefix}border-color-subtle) !default;\n$step-item-shadow-size:       calc(var(--#{$prefix}border-width) * 1.5) !default; // stylelint-disable-line function-disallowed-list\n$step-item-drop-shadow:       drop-shadow($step-item-shadow-size 0 0 var(--#{$prefix}stepped-process-bg)) #{\"/* rtl:\"} drop-shadow(calc(-1 * #{$step-item-shadow-size}) 0 0 var(--#{$prefix}stepped-process-bg)) #{\"*/\"} !default; // stylelint-disable-line function-disallowed-list\n\n$step-item-arrow-width:       .8125rem !default;\n$step-item-arrow-shape:       polygon(0% 0%, 1px 0%, subtract(100%, var(--#{$prefix}border-width)) 50%, 1px 100%, 0% 100%) #{\"/* rtl:\"} polygon(100% 0%, subtract(100%, 1px) 0%, var(--#{$prefix}border-width) 50%, subtract(100%, 1px) 100%, 100% 100%) #{\"*/\"} !default; // Used in clip-path\n\n$step-link-width:             1.25ch !default; // Matches width of a single number\n$step-link-color:             var(--#{$prefix}highlight-color) !default;\n$step-link-active-color:      $black !default;\n$step-link-active-outline:    $black !default;\n$step-link-next-color:        var(--#{$prefix}link-color) !default;\n$step-link-line-height:       $line-height-sm !default;\n$step-link-marker:            counter(var(--bs-stepped-process-counter)) inspect(\"\\A0\") !default;\n$step-link-marker-lg:         counter(var(--bs-stepped-process-counter)) inspect(\".\\A0\") !default;\n$step-link-text-decoration:   $link-decoration !default;\n// scss-docs-end stepped-process\n\n\n//// Sticker\n// scss-docs-start sticker\n$sticker-color:                         $black !default;\n$sticker-background-color:              $supporting-orange !default;\n$sticker-font-weight:                   $font-weight-bold !default;\n\n$sticker-size-sm:                       $spacer * 7 !default;\n$sticker-size-md:                       $spacer * 9 !default;\n$sticker-size-lg:                       $spacer * 14 !default;\n\n// Considering @use \"sass:math\", math.sqrt(2) / 2 is approximated to 0.7071067812\n$sticker-content-max-width-sm:          $sticker-size-sm * .7071067812 !default;\n$sticker-content-max-width-md:          $sticker-size-md * .7071067812 !default;\n$sticker-content-max-width-lg:          $sticker-size-lg * .7071067812 !default;\n// scss-docs-end sticker\n\n//// Quantity selector\n// scss-docs-start quantity-selector\n$quantity-selector-width:                 7.5rem !default;\n$quantity-selector-sm-width:              5.625rem !default;\n\n$quantity-selector-btn-padding-x:         add($btn-icon-padding-x, 2px) !default;\n$quantity-selector-btn-padding-x-sm:      add($btn-icon-padding-x-sm, 2px) !default;\n$quantity-selector-btn-bg:                var(--#{$prefix}body-bg) !default;\n$quantity-selector-btn-border:            var(--#{$prefix}border-width) solid var(--#{$prefix}border-color-subtle) !default;\n\n$quantity-selector-disabled-color:        var(--#{$prefix}disabled-color) !default;\n$quantity-selector-disabled-bg:           var(--#{$prefix}body-bg) !default;\n\n$quantity-selector-icon-width:            .875rem !default;\n$quantity-selector-icon-sm-width:         .625rem !default;\n\n$quantity-selector-icon-add:              $add-icon !default;\n$quantity-selector-icon-add-sm:           $add-icon-sm !default;\n$quantity-selector-icon-add-height:       .875rem !default;\n$quantity-selector-icon-sm-add-height:    .625rem !default;\n\n$quantity-selector-icon-remove:           $remove-icon !default;\n$quantity-selector-icon-remove-sm:        $remove-icon-sm !default;\n$quantity-selector-icon-remove-height:    .125rem !default;\n$quantity-selector-icon-sm-remove-height: .125rem !default;\n\n$quantity-selector-input-max-width:       2.5rem !default;\n$quantity-selector-input-sm-max-width:    1.875rem !default;\n// scss-docs-end quantity-selector\n\n//// Footer\n// scss-docs-start footer\n$footer-color:                            var(--#{$prefix}body-color) !default;\n$footer-font-size-sm:                     $font-size-sm !default;\n$footer-line-height-sm:                   $line-height-sm !default;\n$footer-font-size-md:                     $font-size-base !default;\n$footer-line-height-md:                   $line-height-base !default;\n$footer-title-font-weight:                $font-weight-bold !default;\n$footer-letter-spacing:                   $letter-spacing-base !default;\n$footer-accordion-line-height:            $spacer * 1.45 !default;\n$footer-accordion-active-color:           var(--#{$prefix}primary) !default;\n$footer-accordion-btn-hover-bg:           null !default;\n// fusv-disable\n$footer-nav-link-font-weight:             $font-weight-bold !default; // Deprecated in v5.3.3\n// fusv-enable\n\n$footer-title-content-padding-top:        $spacer * 1.25 !default;\n$footer-title-content-padding-bottom:     $spacer * 1.45 !default;\n$footer-title-content-padding-bottom-md:  $spacer * 1.95 !default;\n$footer-title-margin-bottom:              $spacer * .85 !default;\n\n$footer-social-padding-top:               $spacer * .85 !default;\n$footer-social-padding-top-md:            $spacer * 1.5 !default;\n$footer-social-padding-bottom:            $spacer * 1.45 !default;\n$footer-social-title-margin-bottom-md:    $spacer * .1 !default;\n\n$footer-title-margin-bottom-md:           $spacer * 1.05 !default;\n$footer-nav-padding-top:                  $spacer * 1.55 !default;\n$footer-nav-list-padding-top:             $spacer * .85 !default;\n$footer-nav-list-padding-top-md:          $spacer * .05 !default;\n$footer-nav-list-padding-bottom:          $spacer * 1.3 !default;\n$footer-nav-list-padding-bottom-md:       $spacer * 1.75 !default;\n$footer-nav-list-padding-bottom-lg:       $spacer * 2 !default;\n\n$footer-service-padding-y:                $spacer !default;\n$footer-service-padding-y-md:             $spacer * 1.2 !default;\n$footer-service-link-padding-top:         $spacer * .1 !default;\n\n$footer-terms-padding-top:                $spacer * .85 !default;\n$footer-terms-padding-bottom:             $spacer * 1.35 !default;\n$footer-terms-padding-y-md:               $spacer * 1.1 !default;\n\n$footer-gap:                              $spacer * .75 !default;\n$footer-gap-xl:                           $spacer * 1.7 !default;\n// scss-docs-end footer\n\n\n// Tags\n\n// scss-docs-start tag-variables\n$tag-color:                         var(--#{$prefix}body-color) !default;\n$tag-bg:                            var(--#{$prefix}body-bg) !default;\n\n$tag-gap:                           map-get($spacers, 1) !default;\n$tag-font-shift:                    $spacer * .1 !default;\n$tag-font-weight:                   $font-weight-bold !default;\n$tag-border-width:                  var(--#{$prefix}border-width) !default;\n$tag-border-radius:                 var(--#{$prefix}border-radius-pill) !default;\n\n$tag-padding-x:                     $spacer * .65 !default;\n$tag-padding-y:                     $spacer * .45 !default;\n$tag-icon-size:                     $spacer * 1.2 !default;\n$tag-icon-margin-start:             -$spacer * .35 !default;\n$tag-close-margin-end:              -$spacer * .3 !default;\n$tag-close-margin-start:            $spacer * .2 !default;\n$tag-font-size:                     $font-size-base !default;\n\n$tag-active-color:                  var(--#{$prefix}highlight-color) !default;\n$tag-disabled-color:                var(--#{$prefix}disabled-color) !default;\n$tag-border-color:                  var(--#{$prefix}border-color-subtle) !default;\n$tag-active-decoration-color:       var(--#{$prefix}highlight-bg) !default;\n// scss-docs-end tag-variables\n\n// scss-docs-start tag-sm-variables\n$tag-padding-x-sm:                  $spacer * .4 !default;\n$tag-padding-y-sm:                  $spacer * .25 !default;\n$tag-icon-size-sm:                  $spacer !default;\n$tag-icon-margin-start-sm:          -$spacer * .1 !default;\n$tag-close-margin-end-sm:           -$spacer * .25 !default;\n$tag-close-margin-start-sm:         0 !default;\n$tag-font-size-sm:                  $font-size-sm !default;\n// scss-docs-end tag-sm-variables\n\n\n// Local navigation\n\n// scss-docs-start local-nav-variables\n$local-nav-padding-y:           $navbar-nav-link-padding-y !default;\n$local-nav-color:               null !default;\n$local-nav-bg:                  var(--#{$prefix}body-bg) !default;\n$local-nav-hover-color:         var(--#{$prefix}link-hover-color) !default;\n$local-nav-hover-bg:            var(--#{$prefix}secondary-bg) !default;\n$local-nav-active-color:        var(--#{$prefix}primary) !default;\n$local-nav-active-bg:           var(--#{$prefix}tertiary-active-bg) !default;\n$local-nav-active-marker-width: $spacer * .2 !default;\n$local-nav-border-color:        var(--#{$prefix}border-color-subtle) !default;\n$local-nav-border-width:        calc(var(--#{$prefix}border-width) * .5) !default; // stylelint-disable-line function-disallowed-list\n// scss-docs-end local-nav-variables\n// End mod\n\n@import \"variables-dark\"; // TODO: can be removed safely in v6, only here to avoid breaking changes in v5.3\n", "// Row\n//\n// Rows contain your columns.\n\n:root {\n  @each $name, $value in $grid-breakpoints {\n    --#{$prefix}breakpoint-#{$name}: #{$value};\n  }\n}\n\n@if $enable-grid-classes {\n  .row {\n    @include make-row();\n\n    > * {\n      @include make-col-ready();\n    }\n  }\n}\n\n@if $enable-cssgrid {\n  .grid {\n    display: grid;\n    grid-template-rows: repeat(var(--#{$prefix}rows, 1), 1fr);\n    grid-template-columns: repeat(var(--#{$prefix}columns, #{$grid-columns}), 1fr);\n    gap: var(--#{$prefix}gap, #{$grid-gutter-width});\n\n    @include make-cssgrid();\n  }\n}\n\n\n// Columns\n//\n// Common styles for small and large grid columns\n\n@if $enable-grid-classes {\n  @include make-grid-columns();\n}\n", "// Grid system\n//\n// Generate semantic grid columns with these mixins.\n\n@mixin make-row($gutter: $grid-gutter-width, $gutter-sm: ($gutter * .5)) {\n  --#{$prefix}gutter-x: #{$gutter-sm}; // Boosted mod: gutter depends on breakpoint\n  --#{$prefix}gutter-y: 0;\n  display: flex;\n  flex-wrap: wrap;\n  // TODO: Revisit calc order after https://github.com/react-bootstrap/react-bootstrap/issues/6039 is fixed\n  margin-top: calc(-1 * var(--#{$prefix}gutter-y)); // stylelint-disable-line function-disallowed-list\n  margin-right: calc(-.5 * var(--#{$prefix}gutter-x)); // stylelint-disable-line function-disallowed-list\n  margin-left: calc(-.5 * var(--#{$prefix}gutter-x)); // stylelint-disable-line function-disallowed-list\n\n  // Boosted mod: gutter depends on breakpoint\n  @include media-breakpoint-up($grid-gutter-breakpoint) {\n    --#{$prefix}gutter-x: #{$gutter};\n  }\n  // End mod\n}\n\n@mixin make-col-ready() {\n  // Add box sizing if only the grid is loaded\n  box-sizing: if(variable-exists(include-column-box-sizing) and $include-column-box-sizing, border-box, null);\n  // Prevent columns from becoming too narrow when at smaller grid tiers by\n  // always setting `width: 100%;`. This works because we set the width\n  // later on to override this initial width.\n  flex-shrink: 0;\n  width: 100%;\n  max-width: 100%; // Prevent `.col-auto`, `.col` (& responsive variants) from breaking out the grid\n  padding-right: calc(var(--#{$prefix}gutter-x) * .5); // stylelint-disable-line function-disallowed-list\n  padding-left: calc(var(--#{$prefix}gutter-x) * .5); // stylelint-disable-line function-disallowed-list\n  margin-top: var(--#{$prefix}gutter-y);\n}\n\n@mixin make-col($size: false, $columns: $grid-columns) {\n  @if $size {\n    flex: 0 0 auto;\n    width: percentage(divide($size, $columns));\n\n  } @else {\n    flex: 1 1 0;\n    max-width: 100%;\n  }\n}\n\n@mixin make-col-auto() {\n  flex: 0 0 auto;\n  width: auto;\n}\n\n@mixin make-col-offset($size, $columns: $grid-columns) {\n  $num: divide($size, $columns);\n  margin-left: if($num == 0, 0, percentage($num));\n}\n\n// Row columns\n//\n// Specify on a parent element(e.g., .row) to force immediate children into NN\n// number of columns. Supports wrapping to new lines, but does not do a Masonry\n// style grid.\n@mixin row-cols($count) {\n  > * {\n    flex: 0 0 auto;\n    width: percentage(divide(1, $count));\n  }\n}\n\n// Framework grid generation\n//\n// Used only by Bootstrap to generate the correct number of grid classes given\n// any value of `$grid-columns`.\n\n@mixin make-grid-columns($columns: $grid-columns, $gutter: $grid-gutter-width, $breakpoints: $grid-breakpoints) {\n  @each $breakpoint in map-keys($breakpoints) {\n    $infix: breakpoint-infix($breakpoint, $breakpoints);\n\n    @include media-breakpoint-up($breakpoint, $breakpoints) {\n      // Provide basic `.col-{bp}` classes for equal-width flexbox columns\n      .col#{$infix} {\n        flex: 1 0 0;\n      }\n\n      .row-cols#{$infix}-auto > * {\n        @include make-col-auto();\n      }\n\n      @if $grid-row-columns > 0 {\n        @for $i from 1 through $grid-row-columns {\n          .row-cols#{$infix}-#{$i} {\n            @include row-cols($i);\n          }\n        }\n      }\n\n      .col#{$infix}-auto {\n        @include make-col-auto();\n      }\n\n      @if $columns > 0 {\n        @for $i from 1 through $columns {\n          .col#{$infix}-#{$i} {\n            @include make-col($i, $columns);\n          }\n        }\n\n        // `$columns - 1` because offsetting by the width of an entire row isn't possible\n        @for $i from 0 through ($columns - 1) {\n          @if not ($infix == \"\" and $i == 0) { // Avoid emitting useless .offset-0\n            .offset#{$infix}-#{$i} {\n              @include make-col-offset($i, $columns);\n            }\n          }\n        }\n      }\n\n      // Gutters\n      //\n      // Make use of `.g-*`, `.gx-*` or `.gy-*` utilities to change spacing between the columns.\n      @each $key, $value in $gutters {\n        .g#{$infix}-#{$key},\n        .gx#{$infix}-#{$key} {\n          --#{$prefix}gutter-x: #{$value};\n        }\n\n        .g#{$infix}-#{$key},\n        .gy#{$infix}-#{$key} {\n          --#{$prefix}gutter-y: #{$value};\n        }\n      }\n    }\n  }\n}\n\n@mixin make-cssgrid($columns: $grid-columns, $breakpoints: $grid-breakpoints) {\n  @each $breakpoint in map-keys($breakpoints) {\n    $infix: breakpoint-infix($breakpoint, $breakpoints);\n\n    @include media-breakpoint-up($breakpoint, $breakpoints) {\n      @if $columns > 0 {\n        @for $i from 1 through $columns {\n          .g-col#{$infix}-#{$i} {\n            grid-column: auto / span $i;\n          }\n        }\n\n        // Start with `1` because `0` is an invalid value.\n        // Ends with `$columns - 1` because offsetting by the width of an entire row isn't possible.\n        @for $i from 1 through ($columns - 1) {\n          .g-start#{$infix}-#{$i} {\n            grid-column-start: $i;\n          }\n        }\n      }\n    }\n  }\n}\n", "// Utility generator\n// Used to generate utilities & print utilities\n@mixin generate-utility($utility, $infix: \"\", $is-rfs-media-query: false) {\n  $values: map-get($utility, values);\n\n  // If the values are a list or string, convert it into a map\n  @if type-of($values) == \"string\" or type-of(nth($values, 1)) != \"list\" {\n    $values: zip($values, $values);\n  }\n\n  @each $key, $value in $values {\n    $properties: map-get($utility, property);\n\n    // Multiple properties are possible, for example with vertical or horizontal margins or paddings\n    @if type-of($properties) == \"string\" {\n      $properties: append((), $properties);\n    }\n\n    // Use custom class if present\n    $property-class: if(map-has-key($utility, class), map-get($utility, class), nth($properties, 1));\n    $property-class: if($property-class == null, \"\", $property-class);\n\n    // Use custom CSS variable name if present, otherwise default to `class`\n    $css-variable-name: if(map-has-key($utility, css-variable-name), map-get($utility, css-variable-name), map-get($utility, class));\n\n    // State params to generate pseudo-classes\n    $state: if(map-has-key($utility, state), map-get($utility, state), ());\n\n    $infix: if($property-class == \"\" and str-slice($infix, 1, 1) == \"-\", str-slice($infix, 2), $infix);\n\n    // Don't prefix if value key is null (e.g. with shadow class)\n    $property-class-modifier: if($key, if($property-class == \"\" and $infix == \"\", \"\", \"-\") + $key, \"\");\n\n    @if map-get($utility, rfs) {\n      // Inside the media query\n      @if $is-rfs-media-query {\n        $val: rfs-value($value);\n\n        // Do not render anything if fluid and non fluid values are the same\n        $value: if($val == rfs-fluid-value($value), null, $val);\n      }\n      @else {\n        $value: rfs-fluid-value($value);\n      }\n    }\n\n    $is-css-var: map-get($utility, css-var);\n    $is-local-vars: map-get($utility, local-vars);\n    $is-rtl: map-get($utility, rtl);\n\n    @if $value != null {\n      @if $is-rtl == false {\n        /* rtl:begin:remove */\n      }\n\n      @if $is-css-var {\n        .#{$property-class + $infix + $property-class-modifier} {\n          --#{$prefix}#{$css-variable-name}: #{$value};\n        }\n\n        @each $pseudo in $state {\n          .#{$property-class + $infix + $property-class-modifier}-#{$pseudo}:#{$pseudo} {\n            --#{$prefix}#{$css-variable-name}: #{$value};\n          }\n        }\n      } @else {\n        .#{$property-class + $infix + $property-class-modifier} {\n          @each $property in $properties {\n            @if $is-local-vars {\n              @each $local-var, $variable in $is-local-vars {\n                --#{$prefix}#{$local-var}: #{$variable};\n              }\n            }\n            #{$property}: $value if($enable-important-utilities, !important, null);\n          }\n        }\n\n        @each $pseudo in $state {\n          .#{$property-class + $infix + $property-class-modifier}-#{$pseudo}:#{$pseudo} {\n            @each $property in $properties {\n              @if $is-local-vars {\n                @each $local-var, $variable in $is-local-vars {\n                  --#{$prefix}#{$local-var}: #{$variable};\n                }\n              }\n              #{$property}: $value if($enable-important-utilities, !important, null);\n            }\n          }\n        }\n      }\n\n      @if $is-rtl == false {\n        /* rtl:end:remove */\n      }\n    }\n  }\n}\n", "// Loop over each breakpoint\n@each $breakpoint in map-keys($grid-breakpoints) {\n\n  // Generate media query if needed\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    // Loop over each utility property\n    @each $key, $utility in $utilities {\n      // The utility can be disabled with `false`, thus check if the utility is a map first\n      // Only proceed if responsive media queries are enabled or if it's the base media query\n      @if type-of($utility) == \"map\" and (map-get($utility, responsive) or $infix == \"\") {\n        @include generate-utility($utility, $infix);\n      }\n    }\n  }\n}\n\n// RFS rescaling\n@media (min-width: $rfs-mq-value) {\n  @each $breakpoint in map-keys($grid-breakpoints) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    @if (map-get($grid-breakpoints, $breakpoint) < $rfs-breakpoint) {\n      // Loop over each utility property\n      @each $key, $utility in $utilities {\n        // The utility can be disabled with `false`, thus check if the utility is a map first\n        // Only proceed if responsive media queries are enabled or if it's the base media query\n        @if type-of($utility) == \"map\" and map-get($utility, rfs) and (map-get($utility, responsive) or $infix == \"\") {\n          @include generate-utility($utility, $infix, true);\n        }\n      }\n    }\n  }\n}\n\n\n// Print utilities\n@media print {\n  @each $key, $utility in $utilities {\n    // The utility can be disabled with `false`, thus check if the utility is a map first\n    // Then check if the utility needs print styles\n    @if type-of($utility) == \"map\" and map-get($utility, print) == true {\n      @include generate-utility($utility, \"-print\");\n    }\n  }\n}\n"]}