# Système d'agents collaboratifs (Contextualisation + Exécution)

Ce document décrit l'architecture, le flux de données, les formats d'échange, des exemples d'utilisation et les points d'extension futurs du système de deux agents pour le tool calling dans Dinootoo Companion.

## Architecture

- Agent de Contextualisation (ContextAgent)
  - Rôle: analyser la requête utilisateur et produire un contexte JSON structuré exploitable par l'agent d'exécution
  - Prompt utilisé: `CONTEXT_BUILDER_SYSTEM_PROMPT` (src/ToolCalling.ts)
  - Sortie: objet JSON conforme au schéma (intent, acceptance_criteria, info_gaps, search_plan, files_to_read, tool_plan, risks, success_check, next_step, history)

- Agent d'Exécution (ExecutionAgent)
  - Rôle: choisir la prochaine action à réaliser à partir du contexte JSON
  - Prompt utilisé: `AGENT_TOOLCALLING_SYSTEM_PROMPT` (src/ToolCalling.ts)
  - Sorties possibles:
    1. Réponse finale: `{ "final_answer": "..." }`
    2. Appel d'outil (simulation): `{ "call": { "name": "t_grep|t_read_file|t_edit_file", "arguments": { justification: "...", ... } } }`

- Orchestrateur (ChatViewProvider en mode Agent)
  - Prépare les entrées (requête utilisateur, snippets, outils disponibles)
  - Demande un contexte initial à l'Agent de Contextualisation
  - Boucle de collaboration: Exécution → Résumé → Contexte mis à jour → Exécution...
  - Affiche dans la webview soit la réponse finale, soit la liste d'outils qui auraient été utilisés (mode développement)

## Flux de données

1. Entrée utilisateur (texte) + extraits de code sélectionnés → ChatViewProvider
2. ChatViewProvider → ContextAgent.buildInitialContext({ user_query, code_snippets_meta, available_tools })
3. ContextAgent → JSON de contexte (intent, plans, critères, etc.)
4. ChatViewProvider → ExecutionAgent.decideNextAction(context)
5. ExecutionAgent →
   - soit `{ final_answer }` (cas terminal)
   - soit `{ call: { name, arguments.justification } }` (simulation)
6. ChatViewProvider →
   - En cas de call: affichage du plan (simulation) et mise à jour du contexte via ContextAgent.refineContext(...)
   - En cas de final: affichage de la réponse

## Formats d'échange

- Contexte JSON (extrait abrégé):
```
{
  "intent": "...",
  "acceptance_criteria": ["..."],
  "info_gaps": ["..."],
  "search_plan": [{ "query": "...", "path": "..." }],
  "files_to_read": [{ "path": "...", "start": 1, "end": 200 }],
  "tool_plan": [{ "tool": "t_grep", "why": "..." }],
  "risks": ["..."],
  "success_check": ["..."],
  "next_step": "...",
  "history": [{ "tool": "t_grep", "justification": "...", "summary": "..." }]
}
```

- Décision d'exécution (simulation):
```
{"call": {"name": "t_grep", "arguments": {"pattern": "...", "path": "src/", "justification": "..."}}}
```

- Réponse finale:
```
{"final_answer": "texte"}
```

## Exemples d'utilisation

- L'utilisateur demande: « Où est construit le prompt système ? »
- ContextAgent propose un plan: grep sur `ToolCalling.ts`, lecture de sections, etc.
- ExecutionAgent (simulation) renvoie: `t_grep` avec justification.
- L'orchestrateur affiche dans la webview: « Plan d'outillage suggéré (simulation) » + bullets des outils choisis.

## Points d'extension futurs

- Exécution réelle des outils: brancher t_grep, t_read_file, t_edit_file (respecter politique de sûreté)
- Persistance des contextes/itérations (mémoires par conversation)
- Tests automatiques: exécuter grep/read avant d'autoriser edit
- Contrôles UX: afficher l'historique d'actions et les critères de succès
- Observabilité: logs détaillés des décisions et contextes

## Contraintes actuelles

- Les outils ne sont PAS exécutés. On affiche uniquement le nom + justification.
- Les prompts système sont centralisés dans `src/ToolCalling.ts`.
- Le flux est limité à un petit nombre d'itérations (3) pour rester réactif.

