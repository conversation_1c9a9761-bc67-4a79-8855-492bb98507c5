{"name": "<PERSON><PERSON><PERSON>-companion", "displayName": "<PERSON><PERSON><PERSON>-companion", "description": "Une extension conçue pour exploiter la puissance de Dinootoo à travers votre éditeur de code favoris.", "version": "0.0.1", "engines": {"vscode": "^1.103.0"}, "categories": ["Other"], "activationEvents": [], "main": "./out/extension.js", "contributes": {"commands": [{"command": "din<PERSON>oo-companion.sendT<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "Envoyer au chat <PERSON>"}, {"command": "dinootoo-companion.inlineGenerateCode", "title": "Editer a<PERSON><PERSON>"}, {"command": "dinootoo-companion.applyProposedInlineCode", "title": "Accepter", "icon": "$(check)"}, {"command": "dinootoo-companion.discardProposedInlineCode", "title": "<PERSON><PERSON><PERSON>", "icon": "$(close)"}], "menus": {"editor/context": [{"when": "editorHasSelection", "command": "din<PERSON>oo-companion.sendT<PERSON><PERSON><PERSON><PERSON><PERSON>", "group": "navigation"}, {"command": "dinootoo-companion.inlineGenerateCode", "group": "navigation@1"}], "editor/title": [{"when": "resourceScheme == dinootoo-companion-proposed", "command": "dinootoo-companion.applyProposedInlineCode", "group": "navigation@1"}, {"when": "resourceScheme == dinootoo-companion-proposed", "command": "dinootoo-companion.discardProposedInlineCode", "group": "navigation@2"}]}, "viewsContainers": {"activitybar": [{"id": "<PERSON><PERSON><PERSON>-companion-sidebar", "title": "Dinootoo Companion", "icon": "media/assets/icon_code_snippet.svg"}]}, "views": {"dinootoo-companion-sidebar": [{"id": "dinootoo-companion-chat", "name": "Cha<PERSON>", "type": "webview"}]}}, "scripts": {"vscode:prepublish": "npm run copy:boosted && npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src", "test": "vscode-test --extensionDevelopmentPath=. --extensionTestsPath=./out/test/runTest.js", "copy:boosted": "node scripts/copy-boosted.js"}, "devDependencies": {"@types/mocha": "^10.0.10", "@types/node": "22.x", "@types/vscode": "^1.103.0", "@typescript-eslint/eslint-plugin": "^8.39.0", "@typescript-eslint/parser": "^8.39.0", "@vscode/test-cli": "^0.0.11", "@vscode/test-electron": "^2.5.2", "eslint": "^9.32.0", "typescript": "^5.9.2"}, "dependencies": {"boosted": "^5.3.7"}}