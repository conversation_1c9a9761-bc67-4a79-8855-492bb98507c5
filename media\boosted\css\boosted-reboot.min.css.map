{"version": 3, "sources": ["..\\..\\scss\\mixins\\_banner.scss", "..\\..\\scss\\_root.scss", "dist\\css\\boosted-reboot.css", "..\\..\\scss\\vendor\\_rfs.scss", "..\\..\\scss\\mixins\\_color-mode.scss", "..\\..\\scss\\_reboot.scss", "..\\..\\scss\\mixins\\_breakpoints.scss", "..\\..\\scss\\mixins\\_focus.scss"], "names": [], "mappings": "AACE;;;;;;;;;ACAF,MCUA,gBDRE,MAAA,qBACA,iBAAA,kBAMF,MCOA,sBDLE,aAAA,MASE,UAAA,QAAA,YAAA,QAAA,YAAA,QAAA,UAAA,QAAA,SAAA,QAAA,YAAA,QAAA,YAAA,KAAA,WAAA,QAAA,UAAA,QAAA,UAAA,QAAA,WAAA,KAAA,WAAA,KAAA,UAAA,KAAA,eAAA,QAIA,cAAA,QAAA,cAAA,QAAA,cAAA,KAAA,cAAA,KAAA,cAAA,KAAA,cAAA,KAAA,cAAA,KAAA,cAAA,QAAA,cAAA,KAAA,cAAA,QAIA,aAAA,QAAA,eAAA,KAAA,aAAA,QAAA,UAAA,QAAA,aAAA,KAAA,YAAA,QAAA,WAAA,KAAA,UAAA,KAIA,iBAAA,GAAA,CAAA,GAAA,CAAA,EAAA,mBAAA,CAAA,CAAA,CAAA,CAAA,EAAA,iBAAA,EAAA,CAAA,GAAA,CAAA,GAAA,cAAA,EAAA,CAAA,GAAA,CAAA,IAAA,iBAAA,GAAA,CAAA,GAAA,CAAA,EAAA,gBAAA,GAAA,CAAA,EAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,CAAA,IAAA,cAAA,CAAA,CAAA,CAAA,CAAA,EAIA,2BAAA,QAAA,6BAAA,KAAA,2BAAA,QAAA,wBAAA,QAAA,2BAAA,KAAA,0BAAA,QAAA,yBAAA,KAAA,wBAAA,KAIA,uBAAA,QAAA,yBAAA,KAAA,uBAAA,QAAA,oBAAA,QAAA,uBAAA,KAAA,sBAAA,QAAA,qBAAA,KAAA,oBAAA,KAIA,2BAAA,QAAA,6BAAA,KAAA,2BAAA,QAAA,wBAAA,QAAA,2BAAA,KAAA,0BAAA,QAAA,yBAAA,KAAA,wBAAA,KAGF,eAAA,GAAA,CAAA,GAAA,CAAA,IACA,eAAA,CAAA,CAAA,CAAA,CAAA,EAIE,kBAAA,iJAAA,gBAAA,oiBAAA,gBAAA,8JAAA,kBAAA,uZAAA,gBAAA,giBAQF,qBAAA,mCAAA,CAAA,gBAAA,CAAA,SAAA,CAAA,WAAA,CAAA,iBAAA,CAAA,KAAA,CAAA,UAAA,CAAA,mBAAA,CAAA,gBAAA,CAAA,iBAAA,CAAA,mBACA,oBAAA,cAAA,CAAA,KAAA,CAAA,MAAA,CAAA,QAAA,CAAA,iBAAA,CAAA,aAAA,CAAA,UACA,cAAA,2EAOA,sBAAA,0BEyNI,oBAAA,KFvNJ,sBAAA,IACA,sBAAA,MAKA,gBAAA,KACA,oBAAA,CAAA,CAAA,CAAA,CAAA,EACA,aAAA,KACA,iBAAA,GAAA,CAAA,GAAA,CAAA,IAEA,oBAAA,KACA,wBAAA,CAAA,CAAA,CAAA,CAAA,EAEA,qBAAA,KACA,yBAAA,GAAA,CAAA,GAAA,CAAA,IACA,kBAAA,KACA,sBAAA,GAAA,CAAA,GAAA,CAAA,IAEA,oBAAA,KACA,wBAAA,GAAA,CAAA,GAAA,CAAA,IACA,iBAAA,QACA,qBAAA,GAAA,CAAA,GAAA,CAAA,IAGA,mBAAA,QAEA,gBAAA,KACA,oBAAA,CAAA,CAAA,CAAA,CAAA,EACA,qBAAA,UAEA,sBAAA,QACA,0BAAA,GAAA,CAAA,GAAA,CAAA,EAMA,gBAAA,KACA,qBAAA,KACA,kBAAA,KACA,oBAAA,yBACA,wBAAA,KAGA,kBAAA,SACA,kBAAA,MACA,kBAAA,KACA,yBAAA,KACA,8BAAA,qBAEA,mBAAA,SACA,sBAAA,QACA,sBAAA,OACA,sBAAA,KACA,uBAAA,KACA,uBAAA,4BACA,wBAAA,MAGA,gBAAA,EACA,mBAAA,EACA,mBAAA,EACA,sBAAA,EAEA,+BAAA,KACA,+BAAA,KAIA,sBAAA,QACA,wBAAA,KACA,sBAAA,wBAIA,sBAAA,gCACA,6BAAA,kBACA,wBAAA,+BACA,+BAAA,iBAGA,uBAAA,UACA,qCAAA,KACA,gCAAA,cAAA,UAAA,gBACA,2BAAA,oIACA,oCAAA,kJACA,2BAAA,KACA,gDAAA,QAIA,4BAAA,MACA,2BAAA,MACA,6BAAA,MAGA,+BAAA,KG1JE,qBHgKA,aAAA,KAGA,gBAAA,KACA,oBAAA,GAAA,CAAA,GAAA,CAAA,IACA,aAAA,QACA,iBAAA,EAAA,CAAA,EAAA,CAAA,GAEA,oBAAA,KACA,wBAAA,GAAA,CAAA,GAAA,CAAA,IAEA,qBAAA,KACA,yBAAA,GAAA,CAAA,GAAA,CAAA,IACA,kBAAA,KACA,sBAAA,EAAA,CAAA,EAAA,CAAA,GAEA,oBAAA,KACA,wBAAA,GAAA,CAAA,GAAA,CAAA,IACA,iBAAA,KACA,qBAAA,CAAA,CAAA,CAAA,CAAA,EAIE,aAAA,QAAA,eAAA,KAAA,aAAA,KAAA,UAAA,KAAA,aAAA,KAAA,YAAA,QAAA,WAAA,KAAA,UAAA,KAIA,iBAAA,GAAA,CAAA,GAAA,CAAA,EAAA,mBAAA,GAAA,CAAA,GAAA,CAAA,IAAA,iBAAA,GAAA,CAAA,GAAA,CAAA,IAAA,cAAA,GAAA,CAAA,GAAA,CAAA,IAAA,iBAAA,GAAA,CAAA,GAAA,CAAA,EAAA,gBAAA,GAAA,CAAA,EAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,CAAA,IAAA,cAAA,CAAA,CAAA,CAAA,CAAA,EAKA,2BAAA,QAAA,6BAAA,KAAA,2BAAA,KAAA,wBAAA,KAAA,2BAAA,KAAA,0BAAA,QAAA,yBAAA,KAAA,wBAAA,KAIA,uBAAA,QAAA,yBAAA,KAAA,uBAAA,KAAA,oBAAA,KAAA,uBAAA,KAAA,sBAAA,QAAA,qBAAA,KAAA,oBAAA,KAIA,2BAAA,QAAA,6BAAA,KAAA,2BAAA,KAAA,wBAAA,KAAA,2BAAA,KAAA,0BAAA,QAAA,yBAAA,KAAA,wBAAA,KAKA,kBAAA,oZAAA,gBAAA,giBAIF,mBAAA,QAEA,gBAAA,KACA,sBAAA,QACA,oBAAA,GAAA,CAAA,GAAA,CAAA,IACA,0BAAA,GAAA,CAAA,GAAA,CAAA,EAEA,gBAAA,KACA,qBAAA,KACA,kBAAA,KACA,oBAAA,yBACA,wBAAA,KAEA,kBAAA,KACA,yBAAA,KACA,8BAAA,0BAEA,+BAAA,KACA,+BAAA,KAEA,sBAAA,wBAEA,sBAAA,gCACA,6BAAA,kBACA,wBAAA,+BACA,+BAAA,iBACA,uBAAA,KACA,qCAAA,KACA,2BAAA,kJACA,oCAAA,kJACA,gCAAA,cAAA,UAAA,gBACA,2BAAA,QACA,gDAAA,iBAIA,4BAAA,KACA,2BAAA,MACA,6BAAA,EAGA,+BAAA,UI1PJ,EH8OA,QADA,SG1OE,WAAA,WASF,MAKI,mBAAA,QC+BA,0BDpCJ,MAQM,mBAAA,QAWF,8CAnBJ,MAoBM,gBAAA,QAgBN,KACE,SAAA,SACA,OAAA,EACA,YAAA,2BACA,eAAA,KF4NI,UAAA,yBE1NJ,YAAA,2BACA,YAAA,2BAEA,WAAA,0BAGA,eAAA,WACA,iBAAA,kBACA,yBAAA,KACA,4BAAA,YACA,uBAAA,qBACA,eAAA,cAgBF,OE9FE,UAAA,QACA,QAAA,IAAA,MAAA,oCACA,eAAA,IACA,WAAA,EAAA,EAAA,EAAA,IAAA,oCLoSF,0IGrMA,0IAEE,QAAA,YACA,WAAA,KAGF,4GACE,QAAA,YACA,WAAA,KAUF,GACE,OAAA,QAAA,EACA,MAAA,QACA,OAAA,EACA,WAAA,uBAAA,MHmME,GADA,GACJ,GGxLA,GHsLA,GACA,GGtLE,WAAA,EACA,cAAA,QFwJI,UAAA,KEpJJ,YAAA,IACA,YAAA,MACA,MAAA,wBAGA,eAAA,WACA,uBAAA,YACA,wBAAA,UACA,eAAA,mBAGF,GFyIM,UAAA,QEtIJ,YAAA,IAGA,eAAA,SAGF,GHoLA,GCpDM,UAAA,SE5HJ,YAAA,aAGA,eAAA,UAeF,EACE,WAAA,EACA,cAAA,KAUF,YACE,wBAAA,UAAA,OAAA,gBAAA,UAAA,OACA,OAAA,KACA,iCAAA,KAAA,yBAAA,KAMF,QACE,cAAA,KACA,WAAA,OACA,YAAA,QAMF,GH2JA,GGzJE,aAAA,KH+JF,GG5JA,GH2JA,GGxJE,WAAA,EACA,cAAA,KAGF,MH4JA,MACA,MAFA,MGvJE,cAAA,EAKF,GACE,gBAAA,OAMF,WACE,MAAA,kBACA,eAAA,OAEA,cACE,MAAA,QAIJ,cAAgB,MAAA,0BAEhB,iBAAmB,MAAA,yBAKnB,WACE,MAAA,kBACA,eAAA,SAEA,cACE,MAAA,QAIJ,cAAgB,MAAA,0BAEhB,iBAAmB,MAAA,yBAInB,GACE,YAAA,IAKF,GACE,cAAA,MACA,YAAA,EAMF,WACE,OAAA,EAAA,EAAA,KAQF,EH6IA,GACA,OG3IE,YAAA,IAQF,MFjBM,UAAA,QEoBJ,YAAA,IACA,YAAA,aAOF,KACE,QAAA,EAAA,QACA,MAAA,0BACA,iBAAA,uBASF,IH6HA,IG3HE,SAAA,SF1CI,UAAA,ME4CJ,YAAA,EACA,eAAA,SAGF,IAAM,OAAA,OACN,IAAM,IAAA,MAKN,EACE,MAAA,wDACA,gBAAA,UAEA,QACE,oBAAA,+BAWF,2BAAA,iCAEE,MAAA,QACA,gBAAA,KH0HJ,KACA,IAFA,IAGA,KGrHA,IAKE,YAAA,yBFrFI,UAAA,IE6FN,IACE,QAAA,MACA,WAAA,EACA,cAAA,KACA,SAAA,KFjGI,UAAA,OEmGJ,YAAA,KACA,MAAA,qBAGA,SFvGI,UAAA,QEyGF,MAAA,QACA,WAAA,OHgHJ,KG5GA,IF9GM,UAAA,OEiHJ,WAAA,OACA,YAAA,aACA,MAAA,qBACA,UAAA,WH8GF,OG3GE,MACE,MAAA,QAIJ,IACE,QAAA,SAAA,SF7HI,UAAA,OE+HJ,MAAA,yBACA,iBAAA,sBAGA,QACE,QAAA,EFpIE,UAAA,IE+IN,OACE,OAAA,EAAA,EAAA,KAMF,IHgGA,IG9FE,eAAA,OAaF,MACE,sBAAA,OACA,qBAAA,aACA,aAAA,IACA,gBAAA,SAGF,QACE,YAAA,OACA,eAAA,OF9KI,UAAA,SEgLJ,YAAA,IACA,MAAA,iDACA,WAAA,KAGA,eAAA,UACA,uBAAA,YACA,wBAAA,UACA,eAAA,mBAOF,GAEE,WAAA,QACA,WAAA,qBHmFF,MAGA,GAFA,MAGA,GGpFA,MHkFA,GG5EE,aAAA,QACA,aAAA,MACA,aAAA,EAQF,MACE,QAAA,aACA,YAAA,IAMF,OAEE,cAAA,EAWF,kDACE,QAAA,EACA,WAAA,KHkEF,OG7DA,MH+DA,SADA,OAEA,SG3DE,OAAA,EACA,YAAA,QFvPI,UAAA,QEyPJ,YAAA,QAGA,eAAA,QACA,WAAA,KAIF,OH2DA,OGzDE,eAAA,KAKF,cACE,OAAA,QAGF,OAGE,UAAA,OAGA,gBACE,QAAA,EAOJ,0IACE,QAAA,eHqDF,cACA,aACA,cG/CA,OAIE,mBAAA,OH+CF,6BACA,4BACA,6BG9CI,sBACE,OAAA,QAON,mBACE,QAAA,EACA,aAAA,KAKF,SACE,OAAA,SAUF,SACE,UAAA,EACA,QAAA,EACA,OAAA,EACA,OAAA,EAQF,OACE,MAAA,KACA,MAAA,KACA,QAAA,EACA,cAAA,SFpVI,UAAA,QEsVJ,YAAA,IACA,YAAA,QAEA,SACE,MAAA,KHmCJ,kCG5BA,uCH2BA,mCADA,+BAGA,oCAJA,6BAKA,mCGvBE,QAAA,EAGF,4BACE,OAAA,KASF,cACE,mBAAA,UACA,eAAA,KAmBF,4BACE,mBAAA,KAKF,+BACE,QAAA,EAOF,6BACE,KAAA,QACA,mBAAA,OAFF,uBACE,KAAA,QACA,mBAAA,OAKF,OACE,QAAA,aAKF,OACE,OAAA,EAOF,QACE,QAAA,UACA,OAAA,QAQF,SACE,eAAA,SAQF,SACE,QAAA", "sourcesContent": ["@mixin bsBanner($file) {\n  /*!\n   * Boosted #{$file} v5.3.7 (https://boosted.orange.com/)\n   * Copyright 2014-2025 The Boosted Authors\n   * Copyright 2014-2025 Orange SA\n   * Licensed under MIT (https://github.com/Orange-OpenSource/Orange-Boosted-Bootstrap/blob/main/LICENSE)\n   * This a fork of Bootstrap: Initial license below\n   * Bootstrap #{$file} v5.3.7 (https://getbootstrap.com/)\n   * Copyright 2011-2025 The Bootstrap Authors\n   * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n   */\n}\n", "// Boosted mod\n:root,\n[data-bs-theme] {\n  color: var(--#{$prefix}body-color);\n  background-color: var(--#{$prefix}body-bg);\n}\n\n// Note that some of the following variables in `:root, [data-bs-theme=\"light\"]` could be extracted into `:root` only selector since they are not modified by other color modes!\n// End mod\n\n:root,\n[data-bs-theme=\"light\"] {\n  color-scheme: light; // Boosted mod\n\n  // Note: Custom variable values only support SassScript inside `#{}`.\n\n  // Colors\n  //\n  // Generate palettes for full colors, grays, and theme colors.\n\n  @each $color, $value in $colors {\n    --#{$prefix}#{$color}: #{$value};\n  }\n\n  @each $color, $value in $grays {\n    --#{$prefix}gray-#{$color}: #{$value};\n  }\n\n  @each $color, $value in $theme-colors {\n    --#{$prefix}#{$color}: #{$value};\n  }\n\n  @each $color, $value in $theme-colors-rgb {\n    --#{$prefix}#{$color}-rgb: #{$value};\n  }\n\n  @each $color, $value in $theme-colors-text {\n    --#{$prefix}#{$color}-text-emphasis: #{$value};\n  }\n\n  @each $color, $value in $theme-colors-bg-subtle {\n    --#{$prefix}#{$color}-bg-subtle: #{$value};\n  }\n\n  @each $color, $value in $theme-colors-border-subtle {\n    --#{$prefix}#{$color}-border-subtle: #{$value};\n  }\n\n  --#{$prefix}white-rgb: #{to-rgb($white)};\n  --#{$prefix}black-rgb: #{to-rgb($black)};\n\n  // Boosted mod\n  @each $icon, $svg in $svg-as-custom-props {\n    --#{$prefix}#{$icon}-icon: #{escape-svg($svg)};\n  }\n  // End mod\n\n  // Fonts\n\n  // Note: Use `inspect` for lists so that quoted items keep the quotes.\n  // See https://github.com/sass/sass/issues/2383#issuecomment-336349172\n  --#{$prefix}font-sans-serif: #{inspect($font-family-sans-serif)};\n  --#{$prefix}font-monospace: #{inspect($font-family-monospace)};\n  --#{$prefix}gradient: #{$gradient};\n\n  // Root and body\n  // scss-docs-start root-body-variables\n  @if $font-size-root != null {\n    --#{$prefix}root-font-size: #{$font-size-root};\n  }\n  --#{$prefix}body-font-family: #{inspect($font-family-base)};\n  @include rfs($font-size-base, --#{$prefix}body-font-size);\n  --#{$prefix}body-font-weight: #{$font-weight-base};\n  --#{$prefix}body-line-height: #{$line-height-base};\n  @if $body-text-align != null {\n    --#{$prefix}body-text-align: #{$body-text-align};\n  }\n\n  --#{$prefix}body-color: #{$body-color};\n  --#{$prefix}body-color-rgb: #{to-rgb($body-color)};\n  --#{$prefix}body-bg: #{$body-bg};\n  --#{$prefix}body-bg-rgb: #{to-rgb($body-bg)};\n\n  --#{$prefix}emphasis-color: #{$body-emphasis-color};\n  --#{$prefix}emphasis-color-rgb: #{to-rgb($body-emphasis-color)};\n\n  --#{$prefix}secondary-color: #{$body-secondary-color};\n  --#{$prefix}secondary-color-rgb: #{to-rgb($body-secondary-color)};\n  --#{$prefix}secondary-bg: #{$body-secondary-bg};\n  --#{$prefix}secondary-bg-rgb: #{to-rgb($body-secondary-bg)};\n\n  --#{$prefix}tertiary-color: #{$body-tertiary-color};\n  --#{$prefix}tertiary-color-rgb: #{to-rgb($body-tertiary-color)};\n  --#{$prefix}tertiary-bg: #{$body-tertiary-bg};\n  --#{$prefix}tertiary-bg-rgb: #{to-rgb($body-tertiary-bg)};\n  // scss-docs-end root-body-variables\n\n  --#{$prefix}heading-color: #{$headings-color};\n\n  --#{$prefix}link-color: #{$link-color};\n  --#{$prefix}link-color-rgb: #{to-rgb($link-color)};\n  --#{$prefix}link-decoration: #{$link-decoration};\n\n  --#{$prefix}link-hover-color: #{$link-hover-color};\n  --#{$prefix}link-hover-color-rgb: #{to-rgb($link-hover-color)};\n\n  @if $link-hover-decoration != null {\n    --#{$prefix}link-hover-decoration: #{$link-hover-decoration};\n  }\n\n  --#{$prefix}code-color: #{$code-color};\n  --#{$prefix}highlight-color: #{$mark-color};\n  --#{$prefix}highlight-bg: #{$mark-bg};\n  --#{$prefix}disabled-color: #{$disabled-color}; // Boosted mod\n  --#{$prefix}tertiary-active-bg: #{$tertiary-active-bg}; // Boosted mod\n\n  // scss-docs-start root-border-var\n  --#{$prefix}border-width: #{$border-width};\n  --#{$prefix}border-style: #{$border-style};\n  --#{$prefix}border-color: #{$border-color};\n  --#{$prefix}border-color-subtle: #{$border-color-subtle}; // Boosted mod\n  --#{$prefix}border-color-translucent: #{$border-color-translucent};\n\n  --#{$prefix}border-radius: #{$border-radius};\n  --#{$prefix}border-radius-sm: #{$border-radius-sm};\n  --#{$prefix}border-radius-lg: #{$border-radius-lg};\n  --#{$prefix}border-radius-xl: #{$border-radius-xl};\n  --#{$prefix}border-radius-xxl: #{$border-radius-xxl};\n  --#{$prefix}border-radius-2xl: var(--#{$prefix}border-radius-xxl); // Deprecated in v5.3.0 for consistency\n  --#{$prefix}border-radius-pill: #{$border-radius-pill};\n  // scss-docs-end root-border-var\n\n  --#{$prefix}box-shadow: #{$box-shadow};\n  --#{$prefix}box-shadow-sm: #{$box-shadow-sm};\n  --#{$prefix}box-shadow-lg: #{$box-shadow-lg};\n  --#{$prefix}box-shadow-inset: #{$box-shadow-inset};\n\n  --#{$prefix}focus-visible-inner-color: #{$focus-visible-inner-color}; // Boosted mod\n  --#{$prefix}focus-visible-outer-color: #{$focus-visible-outer-color}; // Boosted mod\n\n  // Focus styles\n  // scss-docs-start root-focus-variables\n  --#{$prefix}focus-ring-width: #{$focus-ring-width};\n  --#{$prefix}focus-ring-opacity: #{$focus-ring-opacity};\n  --#{$prefix}focus-ring-color: #{$focus-ring-color};\n  // scss-docs-end root-focus-variables\n\n  // scss-docs-start root-form-validation-variables\n  --#{$prefix}form-valid-color: #{$form-valid-color};\n  --#{$prefix}form-valid-border-color: #{$form-valid-border-color};\n  --#{$prefix}form-invalid-color: #{$form-invalid-color};\n  --#{$prefix}form-invalid-border-color: #{$form-invalid-border-color};\n  // scss-docs-end root-form-validation-variables\n\n  --#{$prefix}form-check-filter: #{$form-check-filter}; // Boosted mod\n  --#{$prefix}form-check-input-disabled-color: #{$form-check-input-disabled-color}; // Boosted mod\n  --#{$prefix}form-color-disabled-filter: #{$form-color-disabled-filter}; // Boosted mod\n  --#{$prefix}form-select-indicator: #{$form-select-indicator}; // Boosted mod\n  --#{$prefix}form-select-disabled-indicator: #{$form-select-disabled-indicator}; // Boosted mod\n  --#{$prefix}form-switch-square-bg: #{$form-switch-square-bg}; // Boosted mod\n  --#{$prefix}form-switch-unchecked-invalid-border-color: #{$form-switch-unchecked-invalid-border-color}; // Boosted mod\n\n  // Boosted mod\n  // Table-specific styles\n  --#{$prefix}table-active-bg-factor: #{$table-active-bg-factor};\n  --#{$prefix}table-hover-bg-factor: #{$table-hover-bg-factor};\n  --#{$prefix}table-striped-bg-factor: #{$table-striped-bg-factor};\n\n  // Breadcrumb-specific styles\n  --#{$prefix}breadcrumb-divider-filter: #{$breadcrumb-divider-filter};\n  // End mod\n}\n\n@if $enable-dark-mode {\n  @include color-mode(dark, true) {\n    color-scheme: dark;\n\n    // scss-docs-start root-dark-mode-vars\n    --#{$prefix}body-color: #{$body-color-dark};\n    --#{$prefix}body-color-rgb: #{to-rgb($body-color-dark)};\n    --#{$prefix}body-bg: #{$body-bg-dark};\n    --#{$prefix}body-bg-rgb: #{to-rgb($body-bg-dark)};\n\n    --#{$prefix}emphasis-color: #{$body-emphasis-color-dark};\n    --#{$prefix}emphasis-color-rgb: #{to-rgb($body-emphasis-color-dark)};\n\n    --#{$prefix}secondary-color: #{$body-secondary-color-dark};\n    --#{$prefix}secondary-color-rgb: #{to-rgb($body-secondary-color-dark)};\n    --#{$prefix}secondary-bg: #{$body-secondary-bg-dark};\n    --#{$prefix}secondary-bg-rgb: #{to-rgb($body-secondary-bg-dark)};\n\n    --#{$prefix}tertiary-color: #{$body-tertiary-color-dark};\n    --#{$prefix}tertiary-color-rgb: #{to-rgb($body-tertiary-color-dark)};\n    --#{$prefix}tertiary-bg: #{$body-tertiary-bg-dark};\n    --#{$prefix}tertiary-bg-rgb: #{to-rgb($body-tertiary-bg-dark)};\n\n    // Boosted mod\n    @each $color, $value in $theme-colors-dark {\n      --#{$prefix}#{$color}: #{$value};\n    }\n\n    @each $color, $value in $theme-colors-rgb-dark {\n      --#{$prefix}#{$color}-rgb: #{$value};\n    }\n    // End mod\n\n    @each $color, $value in $theme-colors-text-dark {\n      --#{$prefix}#{$color}-text-emphasis: #{$value};\n    }\n\n    @each $color, $value in $theme-colors-bg-subtle-dark {\n      --#{$prefix}#{$color}-bg-subtle: #{$value};\n    }\n\n    @each $color, $value in $theme-colors-border-subtle-dark {\n      --#{$prefix}#{$color}-border-subtle: #{$value};\n    }\n\n    // Boosted mod\n    @each $icon, $svg in $svg-as-custom-props-dark {\n      --#{$prefix}#{$icon}-icon: #{escape-svg($svg)};\n    }\n    // End mod\n\n    --#{$prefix}heading-color: #{$headings-color-dark};\n\n    --#{$prefix}link-color: #{$link-color-dark};\n    --#{$prefix}link-hover-color: #{$link-hover-color-dark};\n    --#{$prefix}link-color-rgb: #{to-rgb($link-color-dark)};\n    --#{$prefix}link-hover-color-rgb: #{to-rgb($link-hover-color-dark)};\n\n    --#{$prefix}code-color: #{$code-color-dark};\n    --#{$prefix}highlight-color: #{$mark-color-dark};\n    --#{$prefix}highlight-bg: #{$mark-bg-dark};\n    --#{$prefix}disabled-color: #{$disabled-color-dark}; // Boosted mod\n    --#{$prefix}tertiary-active-bg: #{$tertiary-active-bg-dark}; // Boosted mod\n\n    --#{$prefix}border-color: #{$border-color-dark};\n    --#{$prefix}border-color-subtle: #{$border-color-subtle-dark}; // Boosted mod\n    --#{$prefix}border-color-translucent: #{$border-color-translucent-dark};\n\n    --#{$prefix}focus-visible-inner-color: #{$focus-visible-inner-color-dark}; // Boosted mod\n    --#{$prefix}focus-visible-outer-color: #{$focus-visible-outer-color-dark}; // Boosted mod\n\n    --#{$prefix}focus-ring-color: #{$focus-ring-color-dark}; // Boosted mod\n\n    --#{$prefix}form-valid-color: #{$form-valid-color-dark};\n    --#{$prefix}form-valid-border-color: #{$form-valid-border-color-dark};\n    --#{$prefix}form-invalid-color: #{$form-invalid-color-dark};\n    --#{$prefix}form-invalid-border-color: #{$form-invalid-border-color-dark};\n    --#{$prefix}form-check-filter: #{$form-check-filter-dark}; // Boosted mod\n    --#{$prefix}form-check-input-disabled-color: #{$form-check-input-disabled-color-dark}; // Boosted mod\n    --#{$prefix}form-select-indicator: #{$form-select-indicator-dark}; // Boosted mod\n    --#{$prefix}form-select-disabled-indicator: #{$form-select-disabled-indicator-dark}; // Boosted mod\n    --#{$prefix}form-color-disabled-filter: #{$form-color-disabled-filter-dark};\n    --#{$prefix}form-switch-square-bg: #{$form-switch-square-bg-dark}; // Boosted mod\n    --#{$prefix}form-switch-unchecked-invalid-border-color: #{$form-switch-unchecked-invalid-border-color-dark}; // Boosted mod\n\n    // Boosted mod\n    // Table-specific styles\n    --#{$prefix}table-active-bg-factor: #{$table-active-bg-factor-dark};\n    --#{$prefix}table-hover-bg-factor: #{$table-hover-bg-factor-dark};\n    --#{$prefix}table-striped-bg-factor: #{$table-striped-bg-factor-dark};\n\n    // Breadcrumb-specific styles\n    --#{$prefix}breadcrumb-divider-filter: #{$breadcrumb-divider-filter-dark};\n    // End mod\n    // scss-docs-end root-dark-mode-vars\n  }\n}\n", "/*!\n * Boosted Reboot v5.3.7 (https://boosted.orange.com/)\n * Copyright 2014-2025 The Boosted Authors\n * Copyright 2014-2025 Orange SA\n * Licensed under MIT (https://github.com/Orange-OpenSource/Orange-Boosted-Bootstrap/blob/main/LICENSE)\n * This a fork of Bootstrap: Initial license below\n * Bootstrap Reboot v5.3.7 (https://getbootstrap.com/)\n * Copyright 2011-2025 The Bootstrap Authors\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n */\n:root,\n[data-bs-theme] {\n  color: var(--bs-body-color);\n  background-color: var(--bs-body-bg);\n}\n\n:root,\n[data-bs-theme=light] {\n  color-scheme: light;\n  --bs-blue: #4170d8;\n  --bs-indigo: #a885d8;\n  --bs-purple: #a885d8;\n  --bs-pink: #ffb4e6;\n  --bs-red: #cd3c14;\n  --bs-orange: #f16e00;\n  --bs-yellow: #fc0;\n  --bs-green: #228722;\n  --bs-teal: #50be87;\n  --bs-cyan: #4bb4e6;\n  --bs-black: #000;\n  --bs-white: #fff;\n  --bs-gray: #999;\n  --bs-gray-dark: #595959;\n  --bs-gray-100: #fafafa;\n  --bs-gray-200: #f6f6f6;\n  --bs-gray-300: #eee;\n  --bs-gray-400: #ddd;\n  --bs-gray-500: #ccc;\n  --bs-gray-600: #999;\n  --bs-gray-700: #666;\n  --bs-gray-800: #595959;\n  --bs-gray-900: #333;\n  --bs-gray-950: #141414;\n  --bs-primary: #f16e00;\n  --bs-secondary: #000;\n  --bs-success: #228722;\n  --bs-info: #4170d8;\n  --bs-warning: #fc0;\n  --bs-danger: #cd3c14;\n  --bs-light: #ccc;\n  --bs-dark: #000;\n  --bs-primary-rgb: 241, 110, 0;\n  --bs-secondary-rgb: 0, 0, 0;\n  --bs-success-rgb: 34, 135, 34;\n  --bs-info-rgb: 65, 112, 216;\n  --bs-warning-rgb: 255, 204, 0;\n  --bs-danger-rgb: 205, 60, 20;\n  --bs-light-rgb: 204, 204, 204;\n  --bs-dark-rgb: 0, 0, 0;\n  --bs-primary-text-emphasis: #f16e00;\n  --bs-secondary-text-emphasis: #000;\n  --bs-success-text-emphasis: #228722;\n  --bs-info-text-emphasis: #4170d8;\n  --bs-warning-text-emphasis: #fc0;\n  --bs-danger-text-emphasis: #cd3c14;\n  --bs-light-text-emphasis: #ccc;\n  --bs-dark-text-emphasis: #000;\n  --bs-primary-bg-subtle: #f16e00;\n  --bs-secondary-bg-subtle: #000;\n  --bs-success-bg-subtle: #228722;\n  --bs-info-bg-subtle: #4170d8;\n  --bs-warning-bg-subtle: #fc0;\n  --bs-danger-bg-subtle: #cd3c14;\n  --bs-light-bg-subtle: #ccc;\n  --bs-dark-bg-subtle: #000;\n  --bs-primary-border-subtle: #f16e00;\n  --bs-secondary-border-subtle: #000;\n  --bs-success-border-subtle: #228722;\n  --bs-info-border-subtle: #4170d8;\n  --bs-warning-border-subtle: #fc0;\n  --bs-danger-border-subtle: #cd3c14;\n  --bs-light-border-subtle: #ccc;\n  --bs-dark-border-subtle: #000;\n  --bs-white-rgb: 255, 255, 255;\n  --bs-black-rgb: 0, 0, 0;\n  --bs-chevron-icon: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 9 14'%3e%3cpath d='M9 2 7 0 0 7l7 7 2-2-5-5 5-5z'/%3e%3c/svg%3e\");\n  --bs-close-icon: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='116 116 767 767' fill='%23000'%3e%3cpath d='M817.493 676.165a49.977 49.977 0 0 1 0 70.664l-70.664 70.664a49.977 49.977 0 0 1-70.664 0L499.5 640.828 322.835 817.493a49.977 49.977 0 0 1-70.664 0l-70.664-70.664a49.977 49.977 0 0 1 0-70.664L358.172 499.5 181.507 322.835a49.977 49.977 0 0 1 0-70.664l70.664-70.664a49.977 49.977 0 0 1 70.664 0L499.5 358.172l176.665-176.665a49.977 49.977 0 0 1 70.664 0l70.664 70.664a49.977 49.977 0 0 1 0 70.664L640.828 499.5Z'/%3e%3c/svg%3e\");\n  --bs-check-icon: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 15 12'%3e%3cpath fill='%23000' d='M13 0 5 8 2 5 0 7l5 5L15 2z'/%3e%3c/svg%3e\");\n  --bs-success-icon: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 125 125'%3e%3cpath fill='%23228722' d='M62.5 0a62.5 62.5 0 1 0 0 125 62.5 62.5 0 0 0 0-125zm28 29.4c3.3 0 6 2.6 6 5.9a5.9 5.9 0 0 1-1.3 3.7L57.7 86a5.8 5.8 0 0 1-9.1 0L29.8 62.5c-.8-1-1.2-2.3-1.2-3.7a5.9 5.9 0 0 1 1.7-4.1l2.3-2.4a5.8 5.8 0 0 1 4.2-1.7 5.8 5.8 0 0 1 3.8 1.4L52 64.7 86.6 31a5.8 5.8 0 0 1 4-1.6z'/%3e%3c/svg%3e\");\n  --bs-error-icon: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 140 125'%3e%3cpath fill='%23cd3c14' d='M70.3 0c-5.8 0-10.8 3.1-13.5 7.8L2.3 101.3l-.2.2A15.6 15.6 0 0 0 15.6 125H125a15.6 15.6 0 0 0 13.5-23.5L83.8 7.8A15.6 15.6 0 0 0 70.3 0zm19.2 50a6.4 6.4 0 0 1 4.4 1.9 6.4 6.4 0 0 1 0 9L79.4 75.6l15 15a6.4 6.4 0 0 1 0 9.2 6.4 6.4 0 0 1-4.5 1.9 6.4 6.4 0 0 1-4.6-2l-15-15-15 15a6.4 6.4 0 0 1-4.6 2 6.4 6.4 0 0 1-4.6-2 6.4 6.4 0 0 1 0-9l15-15L46.8 61a6.4 6.4 0 1 1 9-9.1l14.6 14.5L84.8 52a6.4 6.4 0 0 1 4.7-1.9z'/%3e%3c/svg%3e\");\n  --bs-font-sans-serif: HelvNeueOrange/*rtl:insert:Arabic*/, \"Helvetica Neue\", Helvetica, \"Noto Sans\", \"Liberation Sans\", Arial, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n  --bs-font-monospace: SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace;\n  --bs-gradient: linear-gradient(180deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));\n  --bs-body-font-family: var(--bs-font-sans-serif);\n  --bs-body-font-size: 1rem;\n  --bs-body-font-weight: 400;\n  --bs-body-line-height: 1.125;\n  --bs-body-color: #000;\n  --bs-body-color-rgb: 0, 0, 0;\n  --bs-body-bg: #fff;\n  --bs-body-bg-rgb: 255, 255, 255;\n  --bs-emphasis-color: #000;\n  --bs-emphasis-color-rgb: 0, 0, 0;\n  --bs-secondary-color: #666;\n  --bs-secondary-color-rgb: 102, 102, 102;\n  --bs-secondary-bg: #eee;\n  --bs-secondary-bg-rgb: 238, 238, 238;\n  --bs-tertiary-color: #ccc;\n  --bs-tertiary-color-rgb: 204, 204, 204;\n  --bs-tertiary-bg: #fafafa;\n  --bs-tertiary-bg-rgb: 250, 250, 250;\n  --bs-heading-color: inherit;\n  --bs-link-color: #000;\n  --bs-link-color-rgb: 0, 0, 0;\n  --bs-link-decoration: underline;\n  --bs-link-hover-color: #f16e00;\n  --bs-link-hover-color-rgb: 241, 110, 0;\n  --bs-code-color: #666;\n  --bs-highlight-color: #fff;\n  --bs-highlight-bg: #000;\n  --bs-disabled-color: var(--bs-tertiary-color);\n  --bs-tertiary-active-bg: #ddd;\n  --bs-border-width: 0.125rem;\n  --bs-border-style: solid;\n  --bs-border-color: #000;\n  --bs-border-color-subtle: #ccc;\n  --bs-border-color-translucent: rgba(0, 0, 0, 0.175);\n  --bs-border-radius: 0.375rem;\n  --bs-border-radius-sm: 0.25rem;\n  --bs-border-radius-lg: 0.5rem;\n  --bs-border-radius-xl: 1rem;\n  --bs-border-radius-xxl: 2rem;\n  --bs-border-radius-2xl: var(--bs-border-radius-xxl);\n  --bs-border-radius-pill: 50rem;\n  --bs-box-shadow: ;\n  --bs-box-shadow-sm: ;\n  --bs-box-shadow-lg: ;\n  --bs-box-shadow-inset: ;\n  --bs-focus-visible-inner-color: #fff;\n  --bs-focus-visible-outer-color: #000;\n  --bs-focus-ring-width: 0.25rem;\n  --bs-focus-ring-opacity: 0.25;\n  --bs-focus-ring-color: rgba(241, 110, 0, 0.25);\n  --bs-form-valid-color: var(--bs-success-text-emphasis);\n  --bs-form-valid-border-color: var(--bs-success);\n  --bs-form-invalid-color: var(--bs-danger-text-emphasis);\n  --bs-form-invalid-border-color: var(--bs-danger);\n  --bs-form-check-filter: invert(1);\n  --bs-form-check-input-disabled-color: #333;\n  --bs-form-color-disabled-filter: brightness(0) invert(1) brightness(0.8);\n  --bs-form-select-indicator: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 14 7'%3e%3cpath d='M7 7 0 0h14L7 7z'/%3e%3c/svg%3e\");\n  --bs-form-select-disabled-indicator: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 14 7'%3e%3cpath fill='%23666' d='M7 7 0 0h14L7 7z'/%3e%3c/svg%3e\");\n  --bs-form-switch-square-bg: #000;\n  --bs-form-switch-unchecked-invalid-border-color: #31c3eb;\n  --bs-table-active-bg-factor: 0.135;\n  --bs-table-hover-bg-factor: 0.065;\n  --bs-table-striped-bg-factor: 0.035;\n  --bs-breadcrumb-divider-filter: none;\n}\n\n[data-bs-theme=dark] {\n  color-scheme: dark;\n  --bs-body-color: #fff;\n  --bs-body-color-rgb: 255, 255, 255;\n  --bs-body-bg: #141414;\n  --bs-body-bg-rgb: 20, 20, 20;\n  --bs-emphasis-color: #fff;\n  --bs-emphasis-color-rgb: 255, 255, 255;\n  --bs-secondary-color: #999;\n  --bs-secondary-color-rgb: 153, 153, 153;\n  --bs-secondary-bg: #333;\n  --bs-secondary-bg-rgb: 51, 51, 51;\n  --bs-tertiary-color: #666;\n  --bs-tertiary-color-rgb: 102, 102, 102;\n  --bs-tertiary-bg: #000;\n  --bs-tertiary-bg-rgb: 0, 0, 0;\n  --bs-primary: #ff7900;\n  --bs-secondary: #fff;\n  --bs-success: #6c6;\n  --bs-info: #69f;\n  --bs-warning: #fc0;\n  --bs-danger: #ff4d4d;\n  --bs-light: #ccc;\n  --bs-dark: #000;\n  --bs-primary-rgb: 255, 121, 0;\n  --bs-secondary-rgb: 255, 255, 255;\n  --bs-success-rgb: 102, 204, 102;\n  --bs-info-rgb: 102, 153, 255;\n  --bs-warning-rgb: 255, 204, 0;\n  --bs-danger-rgb: 255, 77, 77;\n  --bs-light-rgb: 204, 204, 204;\n  --bs-dark-rgb: 0, 0, 0;\n  --bs-primary-text-emphasis: #ff7900;\n  --bs-secondary-text-emphasis: #fff;\n  --bs-success-text-emphasis: #6c6;\n  --bs-info-text-emphasis: #69f;\n  --bs-warning-text-emphasis: #fc0;\n  --bs-danger-text-emphasis: #ff4d4d;\n  --bs-light-text-emphasis: #ccc;\n  --bs-dark-text-emphasis: #000;\n  --bs-primary-bg-subtle: #ff7900;\n  --bs-secondary-bg-subtle: #fff;\n  --bs-success-bg-subtle: #6c6;\n  --bs-info-bg-subtle: #69f;\n  --bs-warning-bg-subtle: #fc0;\n  --bs-danger-bg-subtle: #ff4d4d;\n  --bs-light-bg-subtle: #ccc;\n  --bs-dark-bg-subtle: #000;\n  --bs-primary-border-subtle: #ff7900;\n  --bs-secondary-border-subtle: #fff;\n  --bs-success-border-subtle: #6c6;\n  --bs-info-border-subtle: #69f;\n  --bs-warning-border-subtle: #fc0;\n  --bs-danger-border-subtle: #ff4d4d;\n  --bs-light-border-subtle: #ccc;\n  --bs-dark-border-subtle: #000;\n  --bs-success-icon: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 125 125'%3e%3cpath fill='%236c6' d='M62.5 0a62.5 62.5 0 1 0 0 125 62.5 62.5 0 0 0 0-125zm28 29.4c3.3 0 6 2.6 6 5.9a5.9 5.9 0 0 1-1.3 3.7L57.7 86a5.8 5.8 0 0 1-9.1 0L29.8 62.5c-.8-1-1.2-2.3-1.2-3.7a5.9 5.9 0 0 1 1.7-4.1l2.3-2.4a5.8 5.8 0 0 1 4.2-1.7 5.8 5.8 0 0 1 3.8 1.4L52 64.7 86.6 31a5.8 5.8 0 0 1 4-1.6z'/%3e%3c/svg%3e\");\n  --bs-error-icon: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 140 125'%3e%3cpath fill='%23ff4d4d' d='M70.3 0c-5.8 0-10.8 3.1-13.5 7.8L2.3 101.3l-.2.2A15.6 15.6 0 0 0 15.6 125H125a15.6 15.6 0 0 0 13.5-23.5L83.8 7.8A15.6 15.6 0 0 0 70.3 0zm19.2 50a6.4 6.4 0 0 1 4.4 1.9 6.4 6.4 0 0 1 0 9L79.4 75.6l15 15a6.4 6.4 0 0 1 0 9.2 6.4 6.4 0 0 1-4.5 1.9 6.4 6.4 0 0 1-4.6-2l-15-15-15 15a6.4 6.4 0 0 1-4.6 2 6.4 6.4 0 0 1-4.6-2 6.4 6.4 0 0 1 0-9l15-15L46.8 61a6.4 6.4 0 1 1 9-9.1l14.6 14.5L84.8 52a6.4 6.4 0 0 1 4.7-1.9z'/%3e%3c/svg%3e\");\n  --bs-heading-color: inherit;\n  --bs-link-color: #fff;\n  --bs-link-hover-color: #ff7900;\n  --bs-link-color-rgb: 255, 255, 255;\n  --bs-link-hover-color-rgb: 255, 121, 0;\n  --bs-code-color: #999;\n  --bs-highlight-color: #000;\n  --bs-highlight-bg: #fff;\n  --bs-disabled-color: var(--bs-tertiary-color);\n  --bs-tertiary-active-bg: #666;\n  --bs-border-color: #fff;\n  --bs-border-color-subtle: #666;\n  --bs-border-color-translucent: rgba(255, 255, 255, 0.15);\n  --bs-focus-visible-inner-color: #000;\n  --bs-focus-visible-outer-color: #fff;\n  --bs-focus-ring-color: rgba(255, 121, 0, 0.25);\n  --bs-form-valid-color: var(--bs-success-text-emphasis);\n  --bs-form-valid-border-color: var(--bs-success);\n  --bs-form-invalid-color: var(--bs-danger-text-emphasis);\n  --bs-form-invalid-border-color: var(--bs-danger);\n  --bs-form-check-filter: none;\n  --bs-form-check-input-disabled-color: #666;\n  --bs-form-select-indicator: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 14 7'%3e%3cpath fill='%23fff' d='M7 7 0 0h14L7 7z'/%3e%3c/svg%3e\");\n  --bs-form-select-disabled-indicator: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 14 7'%3e%3cpath fill='%23999' d='M7 7 0 0h14L7 7z'/%3e%3c/svg%3e\");\n  --bs-form-color-disabled-filter: brightness(0) invert(1) brightness(0.4);\n  --bs-form-switch-square-bg: #141414;\n  --bs-form-switch-unchecked-invalid-border-color: var(--bs-danger);\n  --bs-table-active-bg-factor: 0.35;\n  --bs-table-hover-bg-factor: 0.135;\n  --bs-table-striped-bg-factor: 1;\n  --bs-breadcrumb-divider-filter: invert(1);\n}\n\n*,\n*::before,\n*::after {\n  box-sizing: border-box;\n}\n\n:root {\n  scroll-padding-top: 3.75rem;\n}\n@media (min-width: 1024px) {\n  :root {\n    scroll-padding-top: 7.5rem;\n  }\n}\n@media (prefers-reduced-motion: no-preference) {\n  :root {\n    scroll-behavior: smooth;\n  }\n}\n\nbody {\n  position: relative;\n  margin: 0;\n  font-family: var(--bs-body-font-family);\n  font-synthesis: none;\n  font-size: var(--bs-body-font-size);\n  font-weight: var(--bs-body-font-weight);\n  line-height: var(--bs-body-line-height);\n  text-align: var(--bs-body-text-align);\n  /* rtl:remove */\n  letter-spacing: -0.00625rem;\n  background-color: var(--bs-body-bg);\n  -webkit-text-size-adjust: 100%;\n  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);\n  -webkit-font-smoothing: subpixel-antialiased;\n  text-rendering: optimizespeed;\n}\n\n:focus {\n  isolation: isolate;\n  outline: 3px solid var(--bs-focus-visible-outer-color);\n  outline-offset: 2px;\n  box-shadow: 0 0 0 2px var(--bs-focus-visible-inner-color);\n}\n\n.js-focus-visible :focus:not([data-focus-visible-added]):not(.focus-ring):not(.form-select:invalid):not(.form-control[type=file]:invalid),\n.js-focus-visible .focus:not([data-focus-visible-added]):not(.focus-ring):not(.form-select:invalid):not(.form-control[type=file]:invalid) {\n  outline: 0 !important;\n  box-shadow: none;\n}\n\n:focus:not(:focus-visible):not(.focus-ring):not(.form-select:invalid):not(.form-control[type=file]:invalid) {\n  outline: 0 !important;\n  box-shadow: none;\n}\n\nhr {\n  margin: 1.25rem 0;\n  color: inherit;\n  border: 0;\n  border-top: var(--bs-border-width) solid;\n}\n\nh4,\nh5,\nh6, h2,\nh3, h1 {\n  margin-top: 0;\n  margin-bottom: 1.25rem;\n  font-size: 1rem;\n  font-weight: 700;\n  line-height: 1.125;\n  color: var(--bs-heading-color);\n  /* rtl:remove */\n  letter-spacing: -0.00625rem;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  text-rendering: optimizelegibility;\n}\n\nh1 {\n  font-size: 1.25rem;\n  line-height: 1.1;\n  /* rtl:remove */\n  letter-spacing: -0.025rem;\n}\n\nh2,\nh3 {\n  font-size: 1.125rem;\n  line-height: 1.1111111111;\n  /* rtl:remove */\n  letter-spacing: -0.0125rem;\n}\n\np {\n  margin-top: 0;\n  margin-bottom: 1rem;\n}\n\nabbr[title] {\n  -webkit-text-decoration: underline dotted;\n  text-decoration: underline dotted;\n  cursor: help;\n  -webkit-text-decoration-skip-ink: none;\n  text-decoration-skip-ink: none;\n}\n\naddress {\n  margin-bottom: 1rem;\n  font-style: normal;\n  line-height: inherit;\n}\n\nol,\nul {\n  padding-left: 2rem;\n}\n\nol,\nul,\ndl {\n  margin-top: 0;\n  margin-bottom: 1rem;\n}\n\nol ol,\nul ul,\nol ul,\nul ol {\n  margin-bottom: 0;\n}\n\nul {\n  list-style-type: square;\n}\n\nli::marker {\n  color: var(--bs-primary);\n  vertical-align: middle;\n}\nol li::marker {\n  color: inherit;\n}\n\nli li::marker {\n  color: var(--bs-secondary-color);\n}\n\nli li li::marker {\n  color: var(--bs-tertiary-color);\n}\n\nli::before {\n  color: var(--bs-primary);\n  vertical-align: text-top;\n}\nol li::before {\n  color: inherit;\n}\n\nli li::before {\n  color: var(--bs-secondary-color);\n}\n\nli li li::before {\n  color: var(--bs-tertiary-color);\n}\n\ndt {\n  font-weight: 700;\n}\n\ndd {\n  margin-bottom: 0.5rem;\n  margin-left: 0;\n}\n\nblockquote {\n  margin: 0 0 1rem;\n}\n\nb,\nem,\nstrong {\n  font-weight: 700;\n}\n\nsmall {\n  font-size: 0.875rem;\n  font-weight: 400;\n  line-height: 1.1428571429;\n}\n\nmark {\n  padding: 0 0.1875em;\n  color: var(--bs-highlight-color);\n  background-color: var(--bs-highlight-bg);\n}\n\nsub,\nsup {\n  position: relative;\n  font-size: 0.75em;\n  line-height: 0;\n  vertical-align: baseline;\n}\n\nsub {\n  bottom: -0.25em;\n}\n\nsup {\n  top: -0.5em;\n}\n\na {\n  color: rgba(var(--bs-link-color-rgb), var(--bs-link-opacity, 1));\n  text-decoration: underline;\n}\na:hover {\n  --bs-link-color-rgb: var(--bs-link-hover-color-rgb);\n}\n\na:not([href]):not([class]), a:not([href]):not([class]):hover {\n  color: inherit;\n  text-decoration: none;\n}\n\nvar,\npre,\ncode,\nkbd,\nsamp {\n  font-family: var(--bs-font-monospace);\n  font-size: 1em;\n}\n\npre {\n  display: block;\n  margin-top: 0;\n  margin-bottom: 1rem;\n  overflow: auto;\n  font-size: 0.875em;\n  line-height: 1.25;\n  color: var(--bs-code-color);\n}\npre code {\n  font-size: inherit;\n  color: inherit;\n  word-break: normal;\n}\n\nvar,\ncode {\n  font-size: 0.875em;\n  font-style: normal;\n  line-height: 1.1428571429;\n  color: var(--bs-code-color);\n  word-wrap: break-word;\n}\na > var,\na > code {\n  color: inherit;\n}\n\nkbd {\n  padding: 0.0625rem 0.0625rem;\n  font-size: 0.875em;\n  color: var(--bs-kbd-color, #000);\n  background-color: var(--bs-kbd-bg, #eee);\n}\nkbd kbd {\n  padding: 0;\n  font-size: 1em;\n}\n\nfigure {\n  margin: 0 0 1rem;\n}\n\nimg,\nsvg {\n  vertical-align: middle;\n}\n\ntable {\n  font-feature-settings: \"tnum\";\n  font-variant-numeric: tabular-nums;\n  caption-side: top;\n  border-collapse: collapse;\n}\n\ncaption {\n  padding-top: 0.75rem;\n  padding-bottom: 0.75rem;\n  font-size: 2.125rem;\n  font-weight: 700;\n  color: var(--bs-caption-color, var(--bs-emphasis-color));\n  text-align: left;\n  /* rtl:remove */\n  letter-spacing: -0.0625rem;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  text-rendering: optimizelegibility;\n}\n\nth {\n  text-align: inherit;\n  text-align: -webkit-match-parent;\n}\n\nthead,\ntbody,\ntfoot,\ntr,\ntd,\nth {\n  border-color: inherit;\n  border-style: solid;\n  border-width: 0;\n}\n\nlabel {\n  display: inline-block;\n  font-weight: 700;\n}\n\nbutton {\n  border-radius: 0;\n}\n\nbutton:focus:not(:focus-visible):not(.focus-ring) {\n  outline: 0;\n  box-shadow: none;\n}\n\ninput,\nbutton,\nselect,\noptgroup,\ntextarea {\n  margin: 0;\n  font-family: inherit;\n  font-size: inherit;\n  line-height: inherit;\n  /* rtl:remove */\n  letter-spacing: inherit;\n  box-shadow: none;\n}\n\nbutton,\nselect {\n  text-transform: none;\n}\n\n[role=button] {\n  cursor: pointer;\n}\n\nselect {\n  word-wrap: normal;\n}\nselect:disabled {\n  opacity: 1;\n}\n\n[list]:not([type=date]):not([type=datetime-local]):not([type=month]):not([type=week]):not([type=time])::-webkit-calendar-picker-indicator {\n  display: none !important;\n}\n\nbutton,\n[type=button],\n[type=reset],\n[type=submit] {\n  -webkit-appearance: button;\n}\nbutton:not(:disabled),\n[type=button]:not(:disabled),\n[type=reset]:not(:disabled),\n[type=submit]:not(:disabled) {\n  cursor: pointer;\n}\n\n::-moz-focus-inner {\n  padding: 0;\n  border-style: none;\n}\n\ntextarea {\n  resize: vertical;\n}\n\nfieldset {\n  min-width: 0;\n  padding: 0;\n  margin: 0;\n  border: 0;\n}\n\nlegend {\n  float: left;\n  width: 100%;\n  padding: 0;\n  margin-bottom: 0.3125rem;\n  font-size: 1.25rem;\n  font-weight: 700;\n  line-height: inherit;\n}\nlegend + * {\n  clear: left;\n}\n\n::-webkit-datetime-edit-fields-wrapper,\n::-webkit-datetime-edit-text,\n::-webkit-datetime-edit-minute,\n::-webkit-datetime-edit-hour-field,\n::-webkit-datetime-edit-day-field,\n::-webkit-datetime-edit-month-field,\n::-webkit-datetime-edit-year-field {\n  padding: 0;\n}\n\n::-webkit-inner-spin-button {\n  height: auto;\n}\n\n[type=search] {\n  -webkit-appearance: textfield;\n  outline-offset: -2px;\n}\n\n/* rtl:raw:\n[type=\"tel\"],\n[type=\"url\"],\n[type=\"email\"],\n[type=\"number\"] {\n  direction: ltr;\n}\n*/\n::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n\n::-webkit-color-swatch-wrapper {\n  padding: 0;\n}\n\n::-webkit-file-upload-button {\n  font: inherit;\n  -webkit-appearance: button;\n}\n\n::file-selector-button {\n  font: inherit;\n  -webkit-appearance: button;\n}\n\noutput {\n  display: inline-block;\n}\n\niframe {\n  border: 0;\n}\n\nsummary {\n  display: list-item;\n  cursor: pointer;\n}\n\nprogress {\n  vertical-align: baseline;\n}\n\n[hidden] {\n  display: none !important;\n}\n\n/*# sourceMappingURL=boosted-reboot.css.map */", "// stylelint-disable scss/dimension-no-non-numeric-values\n\n// SCSS RFS mixin\n//\n// Automated responsive values for font sizes, paddings, margins and much more\n//\n// Licensed under MIT (https://github.com/twbs/rfs/blob/main/LICENSE)\n\n// Configuration\n\n// Base value\n$rfs-base-value: 1.25rem !default;\n$rfs-unit: rem !default;\n\n@if $rfs-unit != rem and $rfs-unit != px {\n  @error \"`#{$rfs-unit}` is not a valid unit for $rfs-unit. Use `px` or `rem`.\";\n}\n\n// Breakpoint at where values start decreasing if screen width is smaller\n$rfs-breakpoint: 1200px !default;\n$rfs-breakpoint-unit: px !default;\n\n@if $rfs-breakpoint-unit != px and $rfs-breakpoint-unit != em and $rfs-breakpoint-unit != rem {\n  @error \"`#{$rfs-breakpoint-unit}` is not a valid unit for $rfs-breakpoint-unit. Use `px`, `em` or `rem`.\";\n}\n\n// Resize values based on screen height and width\n$rfs-two-dimensional: false !default;\n\n// Factor of decrease\n$rfs-factor: 10 !default;\n\n@if type-of($rfs-factor) != number or $rfs-factor <= 1 {\n  @error \"`#{$rfs-factor}` is not a valid  $rfs-factor, it must be greater than 1.\";\n}\n\n// Mode. Possibilities: \"min-media-query\", \"max-media-query\"\n$rfs-mode: min-media-query !default;\n\n// Generate enable or disable classes. Possibilities: false, \"enable\" or \"disable\"\n$rfs-class: false !default;\n\n// 1 rem = $rfs-rem-value px\n$rfs-rem-value: 16 !default;\n\n// Safari iframe resize bug: https://github.com/twbs/rfs/issues/14\n$rfs-safari-iframe-resize-bug-fix: false !default;\n\n// Disable RFS by setting $enable-rfs to false\n$enable-rfs: true !default;\n\n// Cache $rfs-base-value unit\n$rfs-base-value-unit: unit($rfs-base-value);\n\n@function divide($dividend, $divisor, $precision: 10) {\n  $sign: if($dividend > 0 and $divisor > 0 or $dividend < 0 and $divisor < 0, 1, -1);\n  $dividend: abs($dividend);\n  $divisor: abs($divisor);\n  @if $dividend == 0 {\n    @return 0;\n  }\n  @if $divisor == 0 {\n    @error \"Cannot divide by 0\";\n  }\n  $remainder: $dividend;\n  $result: 0;\n  $factor: 10;\n  @while ($remainder > 0 and $precision >= 0) {\n    $quotient: 0;\n    @while ($remainder >= $divisor) {\n      $remainder: $remainder - $divisor;\n      $quotient: $quotient + 1;\n    }\n    $result: $result * 10 + $quotient;\n    $factor: $factor * .1;\n    $remainder: $remainder * 10;\n    $precision: $precision - 1;\n    @if ($precision < 0 and $remainder >= $divisor * 5) {\n      $result: $result + 1;\n    }\n  }\n  $result: $result * $factor * $sign;\n  $dividend-unit: unit($dividend);\n  $divisor-unit: unit($divisor);\n  $unit-map: (\n    \"px\": 1px,\n    \"rem\": 1rem,\n    \"em\": 1em,\n    \"%\": 1%\n  );\n  @if ($dividend-unit != $divisor-unit and map-has-key($unit-map, $dividend-unit)) {\n    $result: $result * map-get($unit-map, $dividend-unit);\n  }\n  @return $result;\n}\n\n// Remove px-unit from $rfs-base-value for calculations\n@if $rfs-base-value-unit == px {\n  $rfs-base-value: divide($rfs-base-value, $rfs-base-value * 0 + 1);\n}\n@else if $rfs-base-value-unit == rem {\n  $rfs-base-value: divide($rfs-base-value, divide($rfs-base-value * 0 + 1, $rfs-rem-value));\n}\n\n// Cache $rfs-breakpoint unit to prevent multiple calls\n$rfs-breakpoint-unit-cache: unit($rfs-breakpoint);\n\n// Remove unit from $rfs-breakpoint for calculations\n@if $rfs-breakpoint-unit-cache == px {\n  $rfs-breakpoint: divide($rfs-breakpoint, $rfs-breakpoint * 0 + 1);\n}\n@else if $rfs-breakpoint-unit-cache == rem or $rfs-breakpoint-unit-cache == \"em\" {\n  $rfs-breakpoint: divide($rfs-breakpoint, divide($rfs-breakpoint * 0 + 1, $rfs-rem-value));\n}\n\n// Calculate the media query value\n$rfs-mq-value: if($rfs-breakpoint-unit == px, #{$rfs-breakpoint}px, #{divide($rfs-breakpoint, $rfs-rem-value)}#{$rfs-breakpoint-unit});\n$rfs-mq-property-width: if($rfs-mode == max-media-query, max-width, min-width);\n$rfs-mq-property-height: if($rfs-mode == max-media-query, max-height, min-height);\n\n// Internal mixin used to determine which media query needs to be used\n@mixin _rfs-media-query {\n  @if $rfs-two-dimensional {\n    @if $rfs-mode == max-media-query {\n      @media (#{$rfs-mq-property-width}: #{$rfs-mq-value}), (#{$rfs-mq-property-height}: #{$rfs-mq-value}) {\n        @content;\n      }\n    }\n    @else {\n      @media (#{$rfs-mq-property-width}: #{$rfs-mq-value}) and (#{$rfs-mq-property-height}: #{$rfs-mq-value}) {\n        @content;\n      }\n    }\n  }\n  @else {\n    @media (#{$rfs-mq-property-width}: #{$rfs-mq-value}) {\n      @content;\n    }\n  }\n}\n\n// Internal mixin that adds disable classes to the selector if needed.\n@mixin _rfs-rule {\n  @if $rfs-class == disable and $rfs-mode == max-media-query {\n    // Adding an extra class increases specificity, which prevents the media query to override the property\n    &,\n    .disable-rfs &,\n    &.disable-rfs {\n      @content;\n    }\n  }\n  @else if $rfs-class == enable and $rfs-mode == min-media-query {\n    .enable-rfs &,\n    &.enable-rfs {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Internal mixin that adds enable classes to the selector if needed.\n@mixin _rfs-media-query-rule {\n\n  @if $rfs-class == enable {\n    @if $rfs-mode == min-media-query {\n      @content;\n    }\n\n    @include _rfs-media-query () {\n      .enable-rfs &,\n      &.enable-rfs {\n        @content;\n      }\n    }\n  }\n  @else {\n    @if $rfs-class == disable and $rfs-mode == min-media-query {\n      .disable-rfs &,\n      &.disable-rfs {\n        @content;\n      }\n    }\n    @include _rfs-media-query () {\n      @content;\n    }\n  }\n}\n\n// Helper function to get the formatted non-responsive value\n@function rfs-value($values) {\n  // Convert to list\n  $values: if(type-of($values) != list, ($values,), $values);\n\n  $val: \"\";\n\n  // Loop over each value and calculate value\n  @each $value in $values {\n    @if $value == 0 {\n      $val: $val + \" 0\";\n    }\n    @else {\n      // Cache $value unit\n      $unit: if(type-of($value) == \"number\", unit($value), false);\n\n      @if $unit == px {\n        // Convert to rem if needed\n        $val: $val + \" \" + if($rfs-unit == rem, #{divide($value, $value * 0 + $rfs-rem-value)}rem, $value);\n      }\n      @else if $unit == rem {\n        // Convert to px if needed\n        $val: $val + \" \" + if($rfs-unit == px, #{divide($value, $value * 0 + 1) * $rfs-rem-value}px, $value);\n      } @else {\n        // If $value isn't a number (like inherit) or $value has a unit (not px or rem, like 1.5em) or $ is 0, just print the value\n        $val: $val + \" \" + $value;\n      }\n    }\n  }\n\n  // Remove first space\n  @return unquote(str-slice($val, 2));\n}\n\n// Helper function to get the responsive value calculated by RFS\n@function rfs-fluid-value($values) {\n  // Convert to list\n  $values: if(type-of($values) != list, ($values,), $values);\n\n  $val: \"\";\n\n  // Loop over each value and calculate value\n  @each $value in $values {\n    @if $value == 0 {\n      $val: $val + \" 0\";\n    } @else {\n      // Cache $value unit\n      $unit: if(type-of($value) == \"number\", unit($value), false);\n\n      // If $value isn't a number (like inherit) or $value has a unit (not px or rem, like 1.5em) or $ is 0, just print the value\n      @if not $unit or $unit != px and $unit != rem {\n        $val: $val + \" \" + $value;\n      } @else {\n        // Remove unit from $value for calculations\n        $value: divide($value, $value * 0 + if($unit == px, 1, divide(1, $rfs-rem-value)));\n\n        // Only add the media query if the value is greater than the minimum value\n        @if abs($value) <= $rfs-base-value or not $enable-rfs {\n          $val: $val + \" \" + if($rfs-unit == rem, #{divide($value, $rfs-rem-value)}rem, #{$value}px);\n        }\n        @else {\n          // Calculate the minimum value\n          $value-min: $rfs-base-value + divide(abs($value) - $rfs-base-value, $rfs-factor);\n\n          // Calculate difference between $value and the minimum value\n          $value-diff: abs($value) - $value-min;\n\n          // Base value formatting\n          $min-width: if($rfs-unit == rem, #{divide($value-min, $rfs-rem-value)}rem, #{$value-min}px);\n\n          // Use negative value if needed\n          $min-width: if($value < 0, -$min-width, $min-width);\n\n          // Use `vmin` if two-dimensional is enabled\n          $variable-unit: if($rfs-two-dimensional, vmin, vw);\n\n          // Calculate the variable width between 0 and $rfs-breakpoint\n          $variable-width: #{divide($value-diff * 100, $rfs-breakpoint)}#{$variable-unit};\n\n          // Return the calculated value\n          $val: $val + \" calc(\" + $min-width + if($value < 0, \" - \", \" + \") + $variable-width + \")\";\n        }\n      }\n    }\n  }\n\n  // Remove first space\n  @return unquote(str-slice($val, 2));\n}\n\n// RFS mixin\n@mixin rfs($values, $property: font-size) {\n  @if $values != null {\n    $val: rfs-value($values);\n    $fluid-val: rfs-fluid-value($values);\n\n    // Do not print the media query if responsive & non-responsive values are the same\n    @if $val == $fluid-val {\n      #{$property}: $val;\n    }\n    @else {\n      @include _rfs-rule () {\n        #{$property}: if($rfs-mode == max-media-query, $val, $fluid-val);\n\n        // Include safari iframe resize fix if needed\n        min-width: if($rfs-safari-iframe-resize-bug-fix, (0 * 1vw), null);\n      }\n\n      @include _rfs-media-query-rule () {\n        #{$property}: if($rfs-mode == max-media-query, $fluid-val, $val);\n      }\n    }\n  }\n}\n\n// Shorthand helper mixins\n@mixin font-size($value) {\n  @include rfs($value);\n}\n\n@mixin padding($value) {\n  @include rfs($value, padding);\n}\n\n@mixin padding-top($value) {\n  @include rfs($value, padding-top);\n}\n\n@mixin padding-right($value) {\n  @include rfs($value, padding-right);\n}\n\n@mixin padding-bottom($value) {\n  @include rfs($value, padding-bottom);\n}\n\n@mixin padding-left($value) {\n  @include rfs($value, padding-left);\n}\n\n@mixin margin($value) {\n  @include rfs($value, margin);\n}\n\n@mixin margin-top($value) {\n  @include rfs($value, margin-top);\n}\n\n@mixin margin-right($value) {\n  @include rfs($value, margin-right);\n}\n\n@mixin margin-bottom($value) {\n  @include rfs($value, margin-bottom);\n}\n\n@mixin margin-left($value) {\n  @include rfs($value, margin-left);\n}\n", "// scss-docs-start color-mode-mixin\n@mixin color-mode($mode: light, $root: false) {\n  @if $color-mode-type == \"media-query\" {\n    @if $root == true {\n      @media (prefers-color-scheme: $mode) {\n        :root {\n          @content;\n        }\n      }\n    } @else {\n      @media (prefers-color-scheme: $mode) {\n        @content;\n      }\n    }\n  } @else {\n    [data-bs-theme=\"#{$mode}\"] {\n      @content;\n    }\n  }\n}\n// scss-docs-end color-mode-mixin\n", "// stylelint-disable declaration-no-important, selector-no-qualifying-type, property-no-vendor-prefix\n\n\n// Reboot\n//\n// Normalization of HTML elements, manually forked from Normalize.css to remove\n// styles targeting irrelevant browsers while applying new styles.\n//\n// Normalize is licensed MIT. https://github.com/necolas/normalize.css\n\n\n// Document\n//\n// Change from `box-sizing: content-box` so that `width` is not affected by `padding` or `border`.\n\n*,\n*::before,\n*::after {\n  box-sizing: border-box;\n}\n\n\n// Root\n//\n// Ability to the value of the root font sizes, affecting the value of `rem`.\n// null by default, thus nothing is generated.\n\n:root {\n  // Boosted mod: Improve focus visibility when fixed/sticky header is used\n  // See https://caniuse.com/?search=scroll-padding\n  // scss-docs-start scroll-offset\n  @if $enable-fixed-header {\n    scroll-padding-top: $scroll-offset-top * .5;\n\n    @include media-breakpoint-up(lg) {\n      scroll-padding-top: $scroll-offset-top;\n    }\n  }\n  // scss-docs-end scroll-offset\n  // End mod\n\n  @if $font-size-root != null {\n    @include font-size(var(--#{$prefix}root-font-size));\n  }\n\n  @if $enable-smooth-scroll {\n    @media (prefers-reduced-motion: no-preference) {\n      scroll-behavior: smooth;\n    }\n  }\n}\n\n\n// Body\n//\n// 1. Remove the margin in all browsers.\n// 2. As a best practice, apply a default `background-color`.\n// 3. Prevent adjustments of font size after orientation changes in iOS.\n// 4. Change the default tap highlight to be completely transparent in iOS.\n// 5. Prevent faux-bold/italic\n//    See https://developer.mozilla.org/fr/docs/Web/CSS/font-synthesis\n\n// scss-docs-start reboot-body-rules\nbody {\n  position: relative; // Boosted mod: required for back-to-top component\n  margin: 0; // 1\n  font-family: var(--#{$prefix}body-font-family);\n  font-synthesis: none; // Boosted mod // 5\n  @include font-size(var(--#{$prefix}body-font-size));\n  font-weight: var(--#{$prefix}body-font-weight);\n  line-height: var(--#{$prefix}body-line-height);\n  // Boosted mod: no color\n  text-align: var(--#{$prefix}body-text-align);\n\n  /* rtl:remove */\n  letter-spacing: $letter-spacing-base; // Boosted mod\n  background-color: var(--#{$prefix}body-bg); // 2\n  -webkit-text-size-adjust: 100%; // 3\n  -webkit-tap-highlight-color: rgba($black, 0); // 4\n  -webkit-font-smoothing: subpixel-antialiased; // Boosted mod\n  text-rendering: optimizespeed; // Boosted mod\n}\n// scss-docs-end reboot-body-rules\n\n\n// Boosted mod: focus state\n//\n// 1. Default focus state\n// 2. Using the :focus-visible polyfill to hide outline defensively\n//    See https://github.com/WICG/focus-visible\n//    Note 1: this rule is not applied for our focus ring helper which\n//    handles itself outline and box shadow\n//    Note 2: this rule is also not applied for invalid select and invalid input files to ensure\n//    sufficient contrast between select elements on error focused vs. non focused\n// 3. Using the :focus-visible pseudo-class if supported by the browser\n// scss-docs-start focus-visibility\n:focus {\n  @include focus-visible(); // 1\n}\n\n.js-focus-visible :focus:not([data-focus-visible-added]):not(.focus-ring):not(.form-select:invalid):not(.form-control[type=\"file\"]:invalid),\n.js-focus-visible .focus:not([data-focus-visible-added]):not(.focus-ring):not(.form-select:invalid):not(.form-control[type=\"file\"]:invalid) { // 2\n  outline: 0 !important;\n  box-shadow: none;\n}\n\n:focus:not(:focus-visible):not(.focus-ring):not(.form-select:invalid):not(.form-control[type=\"file\"]:invalid) { // 3\n  outline: 0 !important;\n  box-shadow: none;\n}\n// scss-docs-end focus-visibility\n// End mod\n\n\n// Content grouping\n//\n// 1. Reset Firefox's gray color\n\nhr {\n  margin: $hr-margin-y 0;\n  color: $hr-color; // 1\n  border: 0;\n  border-top: $hr-border-width solid $hr-border-color;\n  opacity: $hr-opacity;\n}\n\n\n// Typography\n//\n// 1. Remove top margins from headings\n//    By default, `<h1>`-`<h6>` all receive top and bottom margins. We nuke the top\n//    margin for easier control within type scales as it avoids margin collapsing.\n\n%heading {\n  margin-top: 0; // 1\n  margin-bottom: $headings-margin-bottom;\n  @include font-size($font-size-base);  // Boosted mod\n  font-family: $headings-font-family;\n  font-style: $headings-font-style;\n  font-weight: $headings-font-weight;\n  line-height: $headings-line-height;\n  color: var(--#{$prefix}heading-color);\n\n  /* rtl:remove */\n  letter-spacing: $letter-spacing-base; // Boosted mod\n  -webkit-font-smoothing: antialiased;  // Boosted mod\n  -moz-osx-font-smoothing: grayscale;   // Boosted mod\n  text-rendering: optimizelegibility;   // Boosted mod\n}\n\nh1 {\n  @extend %heading;\n  @include font-size($h4-font-size);\n  line-height: $h4-line-height;\n\n  /* rtl:remove */\n  letter-spacing: $h4-spacing;\n}\n\nh2,\nh3 {\n  @extend %heading;\n  @include font-size($h5-font-size);\n  line-height: $h5-line-height;\n\n  /* rtl:remove */\n  letter-spacing: $h5-spacing;\n}\n\nh4,\nh5,\nh6 {\n  @extend %heading;\n}\n\n\n// Reset margins on paragraphs\n//\n// Similarly, the top margin on `<p>`s get reset. However, we also reset the\n// bottom margin to use `rem` units instead of `em`.\n\np {\n  margin-top: 0;\n  margin-bottom: $paragraph-margin-bottom;\n}\n\n\n// Abbreviations\n//\n// 1. Add the correct text decoration in Chrome, Edge, Opera, and Safari.\n// 2. Add explicit cursor to indicate changed behavior.\n// 3. Prevent the text-decoration to be skipped.\n\nabbr[title] {\n  text-decoration: underline dotted; // 1\n  cursor: help; // 2\n  text-decoration-skip-ink: none; // 3\n}\n\n\n// Address\n\naddress {\n  margin-bottom: 1rem;\n  font-style: normal;\n  line-height: inherit;\n}\n\n\n// Lists\n\nol,\nul {\n  padding-left: 2rem;\n}\n\nol,\nul,\ndl {\n  margin-top: 0;\n  margin-bottom: 1rem;\n}\n\nol ol,\nul ul,\nol ul,\nul ol {\n  margin-bottom: 0;\n}\n\n// Boosted mod\n// Orange square list-style\nul {\n  list-style-type: square;\n}\n\n// Future-proof markers' color\n// See https://developer.mozilla.org/fr/docs/Web/CSS/::marker\n// stylelint-disable selector-max-type\nli::marker {\n  color: var(--#{$prefix}primary);\n  vertical-align: middle;\n\n  ol & {\n    color: inherit;\n  }\n}\n\nli li::marker { color: var(--#{$prefix}secondary-color); }\n\nli li li::marker { color: var(--#{$prefix}tertiary-color); }\n\n// Bullet-proof markers' color\n// @todo To remove when ::marker support is OK\n// See https://caniuse.com/#search=%3A%3Amarker\nli::before {\n  color: var(--#{$prefix}primary);\n  vertical-align: text-top;\n\n  ol & {\n    color: inherit;\n  }\n}\n\nli li::before { color: var(--#{$prefix}secondary-color); }\n\nli li li::before { color: var(--#{$prefix}tertiary-color); }\n// stylelint-enable selector-max-type\n// End mod\n\ndt {\n  font-weight: $dt-font-weight;\n}\n\n// 1. Undo browser default\n\ndd {\n  margin-bottom: .5rem;\n  margin-left: 0; // 1\n}\n\n\n// Blockquote\n\nblockquote {\n  margin: 0 0 1rem;\n}\n\n\n// Strong\n//\n// Add the correct font weight in Chrome, Edge, and Safari\n\nb,\nem, // Boosted mod\nstrong {\n  font-weight: $font-weight-bold; // Boosted mod: ensure 700\n}\n\n\n// Small\n//\n// Add the correct font size in all browsers\n\nsmall {\n  @include font-size($small-font-size);\n  // Boosted mod\n  font-weight: $font-weight-normal;\n  line-height: $line-height-sm;\n  // End mod\n}\n\n\n// Mark\n\nmark {\n  padding: $mark-padding;\n  color: var(--#{$prefix}highlight-color);\n  background-color: var(--#{$prefix}highlight-bg);\n}\n\n\n// Sub and Sup\n//\n// Prevent `sub` and `sup` elements from affecting the line height in\n// all browsers.\n\nsub,\nsup {\n  position: relative;\n  @include font-size($sub-sup-font-size);\n  line-height: 0;\n  vertical-align: baseline;\n}\n\nsub { bottom: -.25em; }\nsup { top: -.5em; }\n\n\n// Links\n\na {\n  color: rgba(var(--#{$prefix}link-color-rgb), var(--#{$prefix}link-opacity, 1));\n  text-decoration: $link-decoration;\n\n  &:hover {\n    --#{$prefix}link-color-rgb: var(--#{$prefix}link-hover-color-rgb);\n    text-decoration: $link-hover-decoration;\n  }\n}\n\n// And undo these styles for placeholder links/named anchors (without href).\n// It would be more straightforward to just use a[href] in previous block, but that\n// causes specificity issues in many other styles that are too complex to fix.\n// See https://github.com/twbs/bootstrap/issues/19402\n\na:not([href]):not([class]) {\n  &,\n  &:hover {\n    color: inherit;\n    text-decoration: none;\n  }\n}\n\n\n// Code\n\nvar, // Boosted mod\npre,\ncode,\nkbd,\nsamp {\n  font-family: $font-family-code;\n  @include font-size(1em); // Correct the odd `em` font sizing in all browsers.\n}\n\n// 1. Remove browser default top margin\n// 2. Reset browser default of `1em` to use `rem`s\n// 3. Don't allow content to break outside\n\npre {\n  display: block;\n  margin-top: 0; // 1\n  margin-bottom: 1rem; // 2\n  overflow: auto; // 3\n  @include font-size($code-font-size);\n  line-height: $pre-line-height; // Boosted mod\n  color: $pre-color;\n\n  // Account for some code outputs that place code tags in pre tags\n  code {\n    @include font-size(inherit);\n    color: inherit;\n    word-break: normal;\n  }\n}\n\nvar, // Boosted mod\ncode {\n  @include font-size($code-font-size);\n  font-style: normal; // Boosted mod: <var> is italic in all browsers\n  line-height: $line-height-sm; // Boosted mod\n  color: var(--#{$prefix}code-color);\n  word-wrap: break-word;\n\n  // Streamline the style when inside anchors to avoid broken underline and more\n  a > & {\n    color: inherit;\n  }\n}\n\nkbd {\n  padding: $kbd-padding-y $kbd-padding-x;\n  @include font-size($kbd-font-size);\n  color: $kbd-color;\n  background-color: $kbd-bg;\n  @include border-radius($border-radius-sm);\n\n  kbd {\n    padding: 0;\n    @include font-size(1em);\n    font-weight: $nested-kbd-font-weight;\n  }\n}\n\n\n// Figures\n//\n// Apply a consistent margin strategy (matches our type styles).\n\nfigure {\n  margin: 0 0 1rem;\n}\n\n\n// Images and content\n\nimg,\nsvg {\n  vertical-align: middle;\n}\n\n\n// Tables\n//\n// 1. Prevent double borders\n// 2. Ensure horizontal alignment in table when using numbers\n//    See https://x.com/wesbos/status/932644812582522880\n//    See https://caniuse.com/#feat=font-variant-numeric\n//    See https://caniuse.com/#feat=font-feature\n//    See https://helpx.adobe.com/fonts/using/open-type-syntax.html#tnum\n\ntable {\n  font-feature-settings: \"tnum\";      // Boosted mod: 2\n  font-variant-numeric: tabular-nums; // Boosted mod: 2\n  caption-side: top;                  // Boosted mod\n  border-collapse: collapse;\n}\n\ncaption {\n  padding-top: $table-caption-padding-y;\n  padding-bottom: $table-caption-padding-y;\n  @include font-size($h1-font-size);    // Boosted mod\n  font-weight: $font-weight-bold;       // Boosted mod\n  color: $table-caption-color;\n  text-align: left;\n\n  /* rtl:remove */\n  letter-spacing: $h1-spacing;          // Boosted mod\n  -webkit-font-smoothing: antialiased;  // Boosted mod\n  -moz-osx-font-smoothing: grayscale;   // Boosted mod\n  text-rendering: optimizelegibility;   // Boosted mod\n}\n\n// 1. Removes font-weight bold by inheriting\n// 2. Matches default `<td>` alignment by inheriting `text-align`.\n// 3. Fix alignment for Safari\n\nth {\n  font-weight: $table-th-font-weight; // 1\n  text-align: inherit; // 2\n  text-align: -webkit-match-parent; // 3\n}\n\nthead,\ntbody,\ntfoot,\ntr,\ntd,\nth {\n  border-color: inherit;\n  border-style: solid;\n  border-width: 0;\n}\n\n\n// Forms\n//\n// 1. Allow labels to use `margin` for spacing.\n\nlabel {\n  display: inline-block; // 1\n  font-weight: $form-label-font-weight; // Boosted mod\n}\n\n// Remove the default `border-radius` that macOS Chrome adds.\n// See https://github.com/twbs/bootstrap/issues/24093\n\nbutton {\n  // stylelint-disable-next-line property-disallowed-list\n  border-radius: 0;\n}\n\n// Explicitly remove focus outline in Chromium when it shouldn't be\n// visible (e.g. as result of mouse click or touch tap). It already\n// should be doing this automatically, but seems to currently be\n// confused and applies its very visible two-tone outline anyway.\n//\n// This rule is not applied with the focus ring utility which handles\n// itself outline and box shadow\n\nbutton:focus:not(:focus-visible):not(.focus-ring) {\n  outline: 0;\n  box-shadow: none; // Bosted mod\n}\n\n// 1. Remove the margin in Firefox and Safari\n\ninput,\nbutton,\nselect,\noptgroup,\ntextarea {\n  margin: 0; // 1\n  font-family: inherit;\n  @include font-size(inherit);\n  line-height: inherit;\n\n  /* rtl:remove */\n  letter-spacing: inherit; // Boosted mod\n  box-shadow: none; // Boosted mod: prevent native validation styles to apply\n}\n\n// Remove the inheritance of text transform in Firefox\nbutton,\nselect {\n  text-transform: none;\n}\n// Set the cursor for non-`<button>` buttons\n//\n// Details at https://github.com/twbs/bootstrap/pull/30562\n[role=\"button\"] {\n  cursor: pointer;\n}\n\nselect {\n  // Remove the inheritance of word-wrap in Safari.\n  // See https://github.com/twbs/bootstrap/issues/24990\n  word-wrap: normal;\n\n  // Undo the opacity change from Chrome\n  &:disabled {\n    opacity: 1;\n  }\n}\n\n// Remove the dropdown arrow only from text type inputs built with datalists in Chrome.\n// See https://stackoverflow.com/a/54997118\n\n[list]:not([type=\"date\"]):not([type=\"datetime-local\"]):not([type=\"month\"]):not([type=\"week\"]):not([type=\"time\"])::-webkit-calendar-picker-indicator {\n  display: none !important;\n}\n\n// 1. Prevent a WebKit bug where (2) destroys native `audio` and `video`\n//    controls in Android 4.\n// 2. Correct the inability to style clickable types in iOS and Safari.\n// 3. Opinionated: add \"hand\" cursor to non-disabled button elements.\n\nbutton,\n[type=\"button\"], // 1\n[type=\"reset\"],\n[type=\"submit\"] {\n  -webkit-appearance: button; // 2\n\n  @if $enable-button-pointers {\n    &:not(:disabled) {\n      cursor: pointer; // 3\n    }\n  }\n}\n\n// Remove inner border and padding from Firefox, but don't restore the outline like Normalize.\n\n::-moz-focus-inner {\n  padding: 0;\n  border-style: none;\n}\n\n// 1. Textareas should really only resize vertically so they don't break their (horizontal) containers.\n\ntextarea {\n  resize: vertical; // 1\n}\n\n// 1. Browsers set a default `min-width: min-content;` on fieldsets,\n//    unlike e.g. `<div>`s, which have `min-width: 0;` by default.\n//    So we reset that to ensure fieldsets behave more like a standard block element.\n//    See https://github.com/twbs/bootstrap/issues/12359\n//    and https://html.spec.whatwg.org/multipage/#the-fieldset-and-legend-elements\n// 2. Reset the default outline behavior of fieldsets so they don't affect page layout.\n\nfieldset {\n  min-width: 0; // 1\n  padding: 0; // 2\n  margin: 0; // 2\n  border: 0; // 2\n}\n\n// 1. By using `float: left`, the legend will behave like a block element.\n//    This way the border of a fieldset wraps around the legend if present.\n// 2. Fix wrapping bug.\n//    See https://github.com/twbs/bootstrap/issues/29712\n\nlegend {\n  float: left; // 1\n  width: 100%;\n  padding: 0;\n  margin-bottom: $legend-margin-bottom;\n  @include font-size($legend-font-size);\n  font-weight: $legend-font-weight;\n  line-height: inherit;\n\n  + * {\n    clear: left; // 2\n  }\n}\n\n// Fix height of inputs with a type of datetime-local, date, month, week, or time\n// See https://github.com/twbs/bootstrap/issues/18842\n\n::-webkit-datetime-edit-fields-wrapper,\n::-webkit-datetime-edit-text,\n::-webkit-datetime-edit-minute,\n::-webkit-datetime-edit-hour-field,\n::-webkit-datetime-edit-day-field,\n::-webkit-datetime-edit-month-field,\n::-webkit-datetime-edit-year-field {\n  padding: 0;\n}\n\n::-webkit-inner-spin-button {\n  height: auto;\n}\n\n// 1. This overrides the extra rounded corners on search inputs in iOS so that our\n//    `.form-control` class can properly style them. Note that this cannot simply\n//    be added to `.form-control` as it's not specific enough. For details, see\n//    https://github.com/twbs/bootstrap/issues/11586.\n// 2. Correct the outline style in Safari.\n\n[type=\"search\"] {\n  -webkit-appearance: textfield; // 1\n  outline-offset: -#{$focus-visible-outer-offset}; // 2 // Boosted mod\n}\n\n// 1. A few input types should stay LTR\n// See https://rtlstyling.com/posts/rtl-styling#form-inputs\n// 2. RTL only output\n// See https://rtlcss.com/learn/usage-guide/control-directives/#raw\n\n/* rtl:raw:\n[type=\"tel\"],\n[type=\"url\"],\n[type=\"email\"],\n[type=\"number\"] {\n  direction: ltr;\n}\n*/\n\n// Remove the inner padding in Chrome and Safari on macOS.\n\n::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n\n// Remove padding around color pickers in webkit browsers\n\n::-webkit-color-swatch-wrapper {\n  padding: 0;\n}\n\n\n// 1. Inherit font family and line height for file input buttons\n// 2. Correct the inability to style clickable types in iOS and Safari.\n\n::file-selector-button {\n  font: inherit; // 1\n  -webkit-appearance: button; // 2\n}\n\n// Correct element displays\n\noutput {\n  display: inline-block;\n}\n\n// Remove border from iframe\n\niframe {\n  border: 0;\n}\n\n// Summary\n//\n// 1. Add the correct display in all browsers\n\nsummary {\n  display: list-item; // 1\n  cursor: pointer;\n}\n\n\n// Progress\n//\n// Add the correct vertical alignment in Chrome, Firefox, and Opera.\n\nprogress {\n  vertical-align: baseline;\n}\n\n\n// Hidden attribute\n//\n// Always hide an element with the `hidden` HTML attribute.\n\n[hidden] {\n  display: none !important;\n}\n", "// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px)\n//\n// The map defined in the `$grid-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl xxl))\n//    md\n@function breakpoint-next($name, $breakpoints: $grid-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @if not $n {\n    @error \"breakpoint `#{$name}` not found in `#{$breakpoints}`\";\n  }\n  @return if($n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $grid-breakpoints) {\n  $min: map-get($breakpoints, $name);\n  @return if($min != 0, $min, null);\n}\n\n// Maximum breakpoint width.\n// The maximum value is reduced by 0.02px to work around the limitations of\n// `min-` and `max-` prefixes and viewports with fractional widths.\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(md, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $grid-breakpoints) {\n  $max: map-get($breakpoints, $name);\n  @return if($max and $max > 0, $max - .02, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash in front.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $grid-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $grid-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media that spans multiple breakpoint widths.\n// Makes the @content apply between the min and max breakpoints\n@mixin media-breakpoint-between($lower, $upper, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($lower, $breakpoints);\n  $max: breakpoint-max($upper, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($lower, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($upper, $breakpoints) {\n      @content;\n    }\n  }\n}\n\n// Media between the breakpoint's minimum and maximum widths.\n// No minimum for the smallest breakpoint, and no maximum for the largest one.\n// Makes the @content apply only to the given breakpoint, not viewports any wider or narrower.\n@mixin media-breakpoint-only($name, $breakpoints: $grid-breakpoints) {\n  $min:  breakpoint-min($name, $breakpoints);\n  $next: breakpoint-next($name, $breakpoints);\n  $max:  breakpoint-max($next, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($name, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($next, $breakpoints) {\n      @content;\n    }\n  }\n}\n", "// scss-docs-start focus-visible\n@mixin focus-visible($color: var(--#{$prefix}focus-visible-outer-color), $width: $focus-visible-outer-width, $offset: $focus-visible-outer-offset, $box-width: $focus-visible-inner-width, $box-color: var(--#{$prefix}focus-visible-inner-color)) {\n  isolation: isolate;\n  outline: $width solid $color;\n  outline-offset: $offset;\n  box-shadow: 0 0 0 $box-width $box-color;\n  @include transition($transition-focus);\n}\n// scss-docs-end focus-visible\n"]}