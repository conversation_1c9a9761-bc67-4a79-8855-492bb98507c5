# Tests unitaires des outils t_grep, t_read_file, t_edit_file

Ce document décrit la suite de tests qui valide le bon fonctionnement des trois outils réels utilisés par les agents (ExecutionAgent) : t_grep, t_read_file et t_edit_file.

Objectifs de la suite:
- Vérifier que les outils exécutent les opérations attendues sur le workspace VS Code
- Garantir la compatibilité avec les signatures définies dans `src/ToolCalling.ts`
- S’assurer de la robustesse (cas d’erreur) et de la politique de sécurité (grep → read → edit)

---

## Comment exécuter les tests

Prérequis:
- Node.js et npm installés

Commande:

```
npm test
```

Le runner utilise l’environnement de test d’extensions VS Code (via `@vscode/test-electron`). À l’exécution, un workspace temporaire est initialisé et alimenté avec des fichiers de test.

---

## Emplacement et structure

- Fichier principal de tests: `src/test/tools.test.ts`
- Runner Mocha/Glob: `src/test/suite/index.ts`
- Lanceur VS Code tests: `src/test/runTest.ts`

Organisation des tests dans `tools.test.ts`:
- Initialisation: création d’un dossier temporaire et enregistrement comme workspace actif
- Seed: écriture de quelques fichiers (a.ts, b.txt, edit_me.txt)
- Tests par outil: assertions ciblées pour chaque comportement attendu
- Nettoyage: suppression des fichiers temporaires

---

## Couverture des tests

### 1) t_grep
- Recherche d’une fonction exportée dans `a.ts` via une regex
- Vérifie au moins une correspondance et que `a.ts` figure dans les résultats

### 2) t_read_file
- Lecture d’une plage de lignes précise (ligne 1 → 1) et vérification du contenu
- Lecture complète d’un fichier texte et vérification du nombre de lignes/signatures de retour

### 3) t_edit_file
- Création d’un nouveau fichier absent et vérification de son contenu
- Remplacement ancré: suppression du contenu entre `HEADER` et `FOOTER` grâce au placeholder `// ... existing code ...`
- Gestion d’erreur: rejet d’un patch sans aucun ancrage (message d’erreur attendu)

---

## Critères de validation

Les tests valident explicitement les points suivants:
1. Tous les tests passent sans erreur (aucun échec)
2. Les outils respectent leurs signatures de retour:
   - GrepResult: `{ matches: { file, line, text }[] }`
   - ReadFileResult: `{ file, start_line, end_line, total_lines, content }`
   - EditFileResult: `{ file, created, changed, message? }`
3. Comportements attendus:
   - t_grep trouve correctement les patterns regex
   - t_read_file lit les plages ou le fichier complet et renvoie les métadonnées attendues
   - t_edit_file crée des fichiers au besoin et applique les patches ancrés
4. Gestion d’erreur: t_edit_file rejette les patches sans ancrage avec un message adéquat

---

## Points techniques notables

- Le runner Mocha utilise l’API moderne de `glob` (Promise) dans `src/test/suite/index.ts`.
- `t_read_file` normalise le comptage des lignes en ignorant une éventuelle ligne vide terminale due au newline final.
- Les tests écrivent et lisent les fichiers sous le "workspace root" effectif du runner VS Code pour rester alignés avec le comportement réel des outils.

---

## Dépannage

- Si `npm test` échoue avec des erreurs de compilation TypeScript liées aux types externes, vérifiez `tsconfig.json` (`skipLibCheck` activé, types `node`, `mocha`, `vscode`).
- Si le test de création de fichier échoue, s’assurer que le nom cible est unique et que la lecture se fait bien depuis le workspace root courant.
- Les avertissements ESLint n’empêchent pas l’exécution des tests, mais peuvent être corrigés en local via `eslint --fix` si souhaité.

---

## Étendre la suite de tests

- Ajouter de nouveaux cas de test dans `src/test/tools.test.ts` ou créer de nouveaux fichiers `*.test.ts` sous `src/test/`.
- Exemples d’extensions utiles:
  - t_grep: patterns multi-lignes, vérification d’exclusions (node_modules, .git, etc.)
  - t_read_file: plages inverses (erreur `start > end`), grands fichiers
  - t_edit_file: plusieurs ancres, insertion/remplacement conditionnel, messages d’erreur spécifiques

Contribuer en respectant la politique de sécurité: privilégier la séquence grep → read → edit et des patches ancrés.

