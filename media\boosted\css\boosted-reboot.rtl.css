/*!
 * Boosted Reboot v5.3.7 (https://boosted.orange.com/)
 * Copyright 2014-2025 The Boosted Authors
 * Copyright 2014-2025 Orange SA
 * Licensed under MIT (https://github.com/Orange-OpenSource/Orange-Boosted-Bootstrap/blob/main/LICENSE)
 * This a fork of Bootstrap: Initial license below
 * Bootstrap Reboot v5.3.7 (https://getbootstrap.com/)
 * Copyright 2011-2025 The Bootstrap Authors
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
 */
:root,
[data-bs-theme] {
  color: var(--bs-body-color);
  background-color: var(--bs-body-bg);
}

:root,
[data-bs-theme=light] {
  color-scheme: light;
  --bs-blue: #4170d8;
  --bs-indigo: #a885d8;
  --bs-purple: #a885d8;
  --bs-pink: #ffb4e6;
  --bs-red: #cd3c14;
  --bs-orange: #f16e00;
  --bs-yellow: #fc0;
  --bs-green: #228722;
  --bs-teal: #50be87;
  --bs-cyan: #4bb4e6;
  --bs-black: #000;
  --bs-white: #fff;
  --bs-gray: #999;
  --bs-gray-dark: #595959;
  --bs-gray-100: #fafafa;
  --bs-gray-200: #f6f6f6;
  --bs-gray-300: #eee;
  --bs-gray-400: #ddd;
  --bs-gray-500: #ccc;
  --bs-gray-600: #999;
  --bs-gray-700: #666;
  --bs-gray-800: #595959;
  --bs-gray-900: #333;
  --bs-gray-950: #141414;
  --bs-primary: #f16e00;
  --bs-secondary: #000;
  --bs-success: #228722;
  --bs-info: #4170d8;
  --bs-warning: #fc0;
  --bs-danger: #cd3c14;
  --bs-light: #ccc;
  --bs-dark: #000;
  --bs-primary-rgb: 241, 110, 0;
  --bs-secondary-rgb: 0, 0, 0;
  --bs-success-rgb: 34, 135, 34;
  --bs-info-rgb: 65, 112, 216;
  --bs-warning-rgb: 255, 204, 0;
  --bs-danger-rgb: 205, 60, 20;
  --bs-light-rgb: 204, 204, 204;
  --bs-dark-rgb: 0, 0, 0;
  --bs-primary-text-emphasis: #f16e00;
  --bs-secondary-text-emphasis: #000;
  --bs-success-text-emphasis: #228722;
  --bs-info-text-emphasis: #4170d8;
  --bs-warning-text-emphasis: #fc0;
  --bs-danger-text-emphasis: #cd3c14;
  --bs-light-text-emphasis: #ccc;
  --bs-dark-text-emphasis: #000;
  --bs-primary-bg-subtle: #f16e00;
  --bs-secondary-bg-subtle: #000;
  --bs-success-bg-subtle: #228722;
  --bs-info-bg-subtle: #4170d8;
  --bs-warning-bg-subtle: #fc0;
  --bs-danger-bg-subtle: #cd3c14;
  --bs-light-bg-subtle: #ccc;
  --bs-dark-bg-subtle: #000;
  --bs-primary-border-subtle: #f16e00;
  --bs-secondary-border-subtle: #000;
  --bs-success-border-subtle: #228722;
  --bs-info-border-subtle: #4170d8;
  --bs-warning-border-subtle: #fc0;
  --bs-danger-border-subtle: #cd3c14;
  --bs-light-border-subtle: #ccc;
  --bs-dark-border-subtle: #000;
  --bs-white-rgb: 255, 255, 255;
  --bs-black-rgb: 0, 0, 0;
  --bs-chevron-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 9 14'%3e%3cpath d='M9 2 7 0 0 7l7 7 2-2-5-5 5-5z'/%3e%3c/svg%3e");
  --bs-close-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='116 116 767 767' fill='%23000'%3e%3cpath d='M817.493 676.165a49.977 49.977 0 0 1 0 70.664l-70.664 70.664a49.977 49.977 0 0 1-70.664 0L499.5 640.828 322.835 817.493a49.977 49.977 0 0 1-70.664 0l-70.664-70.664a49.977 49.977 0 0 1 0-70.664L358.172 499.5 181.507 322.835a49.977 49.977 0 0 1 0-70.664l70.664-70.664a49.977 49.977 0 0 1 70.664 0L499.5 358.172l176.665-176.665a49.977 49.977 0 0 1 70.664 0l70.664 70.664a49.977 49.977 0 0 1 0 70.664L640.828 499.5Z'/%3e%3c/svg%3e");
  --bs-check-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 15 12'%3e%3cpath fill='%23000' d='M13 0 5 8 2 5 0 7l5 5L15 2z'/%3e%3c/svg%3e");
  --bs-success-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 125 125'%3e%3cpath fill='%23228722' d='M62.5 0a62.5 62.5 0 1 0 0 125 62.5 62.5 0 0 0 0-125zm28 29.4c3.3 0 6 2.6 6 5.9a5.9 5.9 0 0 1-1.3 3.7L57.7 86a5.8 5.8 0 0 1-9.1 0L29.8 62.5c-.8-1-1.2-2.3-1.2-3.7a5.9 5.9 0 0 1 1.7-4.1l2.3-2.4a5.8 5.8 0 0 1 4.2-1.7 5.8 5.8 0 0 1 3.8 1.4L52 64.7 86.6 31a5.8 5.8 0 0 1 4-1.6z'/%3e%3c/svg%3e");
  --bs-error-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 140 125'%3e%3cpath fill='%23cd3c14' d='M70.3 0c-5.8 0-10.8 3.1-13.5 7.8L2.3 101.3l-.2.2A15.6 15.6 0 0 0 15.6 125H125a15.6 15.6 0 0 0 13.5-23.5L83.8 7.8A15.6 15.6 0 0 0 70.3 0zm19.2 50a6.4 6.4 0 0 1 4.4 1.9 6.4 6.4 0 0 1 0 9L79.4 75.6l15 15a6.4 6.4 0 0 1 0 9.2 6.4 6.4 0 0 1-4.5 1.9 6.4 6.4 0 0 1-4.6-2l-15-15-15 15a6.4 6.4 0 0 1-4.6 2 6.4 6.4 0 0 1-4.6-2 6.4 6.4 0 0 1 0-9l15-15L46.8 61a6.4 6.4 0 1 1 9-9.1l14.6 14.5L84.8 52a6.4 6.4 0 0 1 4.7-1.9z'/%3e%3c/svg%3e");
  --bs-font-sans-serif: HelvNeueOrangeArabic, "Helvetica Neue", Helvetica, "Noto Sans", "Liberation Sans", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --bs-font-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  --bs-gradient: linear-gradient(180deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));
  --bs-body-font-family: var(--bs-font-sans-serif);
  --bs-body-font-size: 1rem;
  --bs-body-font-weight: 400;
  --bs-body-line-height: 1.125;
  --bs-body-color: #000;
  --bs-body-color-rgb: 0, 0, 0;
  --bs-body-bg: #fff;
  --bs-body-bg-rgb: 255, 255, 255;
  --bs-emphasis-color: #000;
  --bs-emphasis-color-rgb: 0, 0, 0;
  --bs-secondary-color: #666;
  --bs-secondary-color-rgb: 102, 102, 102;
  --bs-secondary-bg: #eee;
  --bs-secondary-bg-rgb: 238, 238, 238;
  --bs-tertiary-color: #ccc;
  --bs-tertiary-color-rgb: 204, 204, 204;
  --bs-tertiary-bg: #fafafa;
  --bs-tertiary-bg-rgb: 250, 250, 250;
  --bs-heading-color: inherit;
  --bs-link-color: #000;
  --bs-link-color-rgb: 0, 0, 0;
  --bs-link-decoration: underline;
  --bs-link-hover-color: #f16e00;
  --bs-link-hover-color-rgb: 241, 110, 0;
  --bs-code-color: #666;
  --bs-highlight-color: #fff;
  --bs-highlight-bg: #000;
  --bs-disabled-color: var(--bs-tertiary-color);
  --bs-tertiary-active-bg: #ddd;
  --bs-border-width: 0.125rem;
  --bs-border-style: solid;
  --bs-border-color: #000;
  --bs-border-color-subtle: #ccc;
  --bs-border-color-translucent: rgba(0, 0, 0, 0.175);
  --bs-border-radius: 0.375rem;
  --bs-border-radius-sm: 0.25rem;
  --bs-border-radius-lg: 0.5rem;
  --bs-border-radius-xl: 1rem;
  --bs-border-radius-xxl: 2rem;
  --bs-border-radius-2xl: var(--bs-border-radius-xxl);
  --bs-border-radius-pill: 50rem;
  --bs-box-shadow: ;
  --bs-box-shadow-sm: ;
  --bs-box-shadow-lg: ;
  --bs-box-shadow-inset: ;
  --bs-focus-visible-inner-color: #fff;
  --bs-focus-visible-outer-color: #000;
  --bs-focus-ring-width: 0.25rem;
  --bs-focus-ring-opacity: 0.25;
  --bs-focus-ring-color: rgba(241, 110, 0, 0.25);
  --bs-form-valid-color: var(--bs-success-text-emphasis);
  --bs-form-valid-border-color: var(--bs-success);
  --bs-form-invalid-color: var(--bs-danger-text-emphasis);
  --bs-form-invalid-border-color: var(--bs-danger);
  --bs-form-check-filter: invert(1);
  --bs-form-check-input-disabled-color: #333;
  --bs-form-color-disabled-filter: brightness(0) invert(1) brightness(0.8);
  --bs-form-select-indicator: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 14 7'%3e%3cpath d='M7 7 0 0h14L7 7z'/%3e%3c/svg%3e");
  --bs-form-select-disabled-indicator: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 14 7'%3e%3cpath fill='%23666' d='M7 7 0 0h14L7 7z'/%3e%3c/svg%3e");
  --bs-form-switch-square-bg: #000;
  --bs-form-switch-unchecked-invalid-border-color: #31c3eb;
  --bs-table-active-bg-factor: 0.135;
  --bs-table-hover-bg-factor: 0.065;
  --bs-table-striped-bg-factor: 0.035;
  --bs-breadcrumb-divider-filter: none;
}

[data-bs-theme=dark] {
  color-scheme: dark;
  --bs-body-color: #fff;
  --bs-body-color-rgb: 255, 255, 255;
  --bs-body-bg: #141414;
  --bs-body-bg-rgb: 20, 20, 20;
  --bs-emphasis-color: #fff;
  --bs-emphasis-color-rgb: 255, 255, 255;
  --bs-secondary-color: #999;
  --bs-secondary-color-rgb: 153, 153, 153;
  --bs-secondary-bg: #333;
  --bs-secondary-bg-rgb: 51, 51, 51;
  --bs-tertiary-color: #666;
  --bs-tertiary-color-rgb: 102, 102, 102;
  --bs-tertiary-bg: #000;
  --bs-tertiary-bg-rgb: 0, 0, 0;
  --bs-primary: #ff7900;
  --bs-secondary: #fff;
  --bs-success: #6c6;
  --bs-info: #69f;
  --bs-warning: #fc0;
  --bs-danger: #ff4d4d;
  --bs-light: #ccc;
  --bs-dark: #000;
  --bs-primary-rgb: 255, 121, 0;
  --bs-secondary-rgb: 255, 255, 255;
  --bs-success-rgb: 102, 204, 102;
  --bs-info-rgb: 102, 153, 255;
  --bs-warning-rgb: 255, 204, 0;
  --bs-danger-rgb: 255, 77, 77;
  --bs-light-rgb: 204, 204, 204;
  --bs-dark-rgb: 0, 0, 0;
  --bs-primary-text-emphasis: #ff7900;
  --bs-secondary-text-emphasis: #fff;
  --bs-success-text-emphasis: #6c6;
  --bs-info-text-emphasis: #69f;
  --bs-warning-text-emphasis: #fc0;
  --bs-danger-text-emphasis: #ff4d4d;
  --bs-light-text-emphasis: #ccc;
  --bs-dark-text-emphasis: #000;
  --bs-primary-bg-subtle: #ff7900;
  --bs-secondary-bg-subtle: #fff;
  --bs-success-bg-subtle: #6c6;
  --bs-info-bg-subtle: #69f;
  --bs-warning-bg-subtle: #fc0;
  --bs-danger-bg-subtle: #ff4d4d;
  --bs-light-bg-subtle: #ccc;
  --bs-dark-bg-subtle: #000;
  --bs-primary-border-subtle: #ff7900;
  --bs-secondary-border-subtle: #fff;
  --bs-success-border-subtle: #6c6;
  --bs-info-border-subtle: #69f;
  --bs-warning-border-subtle: #fc0;
  --bs-danger-border-subtle: #ff4d4d;
  --bs-light-border-subtle: #ccc;
  --bs-dark-border-subtle: #000;
  --bs-success-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 125 125'%3e%3cpath fill='%236c6' d='M62.5 0a62.5 62.5 0 1 0 0 125 62.5 62.5 0 0 0 0-125zm28 29.4c3.3 0 6 2.6 6 5.9a5.9 5.9 0 0 1-1.3 3.7L57.7 86a5.8 5.8 0 0 1-9.1 0L29.8 62.5c-.8-1-1.2-2.3-1.2-3.7a5.9 5.9 0 0 1 1.7-4.1l2.3-2.4a5.8 5.8 0 0 1 4.2-1.7 5.8 5.8 0 0 1 3.8 1.4L52 64.7 86.6 31a5.8 5.8 0 0 1 4-1.6z'/%3e%3c/svg%3e");
  --bs-error-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 140 125'%3e%3cpath fill='%23ff4d4d' d='M70.3 0c-5.8 0-10.8 3.1-13.5 7.8L2.3 101.3l-.2.2A15.6 15.6 0 0 0 15.6 125H125a15.6 15.6 0 0 0 13.5-23.5L83.8 7.8A15.6 15.6 0 0 0 70.3 0zm19.2 50a6.4 6.4 0 0 1 4.4 1.9 6.4 6.4 0 0 1 0 9L79.4 75.6l15 15a6.4 6.4 0 0 1 0 9.2 6.4 6.4 0 0 1-4.5 1.9 6.4 6.4 0 0 1-4.6-2l-15-15-15 15a6.4 6.4 0 0 1-4.6 2 6.4 6.4 0 0 1-4.6-2 6.4 6.4 0 0 1 0-9l15-15L46.8 61a6.4 6.4 0 1 1 9-9.1l14.6 14.5L84.8 52a6.4 6.4 0 0 1 4.7-1.9z'/%3e%3c/svg%3e");
  --bs-heading-color: inherit;
  --bs-link-color: #fff;
  --bs-link-hover-color: #ff7900;
  --bs-link-color-rgb: 255, 255, 255;
  --bs-link-hover-color-rgb: 255, 121, 0;
  --bs-code-color: #999;
  --bs-highlight-color: #000;
  --bs-highlight-bg: #fff;
  --bs-disabled-color: var(--bs-tertiary-color);
  --bs-tertiary-active-bg: #666;
  --bs-border-color: #fff;
  --bs-border-color-subtle: #666;
  --bs-border-color-translucent: rgba(255, 255, 255, 0.15);
  --bs-focus-visible-inner-color: #000;
  --bs-focus-visible-outer-color: #fff;
  --bs-focus-ring-color: rgba(255, 121, 0, 0.25);
  --bs-form-valid-color: var(--bs-success-text-emphasis);
  --bs-form-valid-border-color: var(--bs-success);
  --bs-form-invalid-color: var(--bs-danger-text-emphasis);
  --bs-form-invalid-border-color: var(--bs-danger);
  --bs-form-check-filter: none;
  --bs-form-check-input-disabled-color: #666;
  --bs-form-select-indicator: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 14 7'%3e%3cpath fill='%23fff' d='M7 7 0 0h14L7 7z'/%3e%3c/svg%3e");
  --bs-form-select-disabled-indicator: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 14 7'%3e%3cpath fill='%23999' d='M7 7 0 0h14L7 7z'/%3e%3c/svg%3e");
  --bs-form-color-disabled-filter: brightness(0) invert(1) brightness(0.4);
  --bs-form-switch-square-bg: #141414;
  --bs-form-switch-unchecked-invalid-border-color: var(--bs-danger);
  --bs-table-active-bg-factor: 0.35;
  --bs-table-hover-bg-factor: 0.135;
  --bs-table-striped-bg-factor: 1;
  --bs-breadcrumb-divider-filter: invert(1);
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

:root {
  scroll-padding-top: 3.75rem;
}
@media (min-width: 1024px) {
  :root {
    scroll-padding-top: 7.5rem;
  }
}
@media (prefers-reduced-motion: no-preference) {
  :root {
    scroll-behavior: smooth;
  }
}

body {
  position: relative;
  margin: 0;
  font-family: var(--bs-body-font-family);
  font-synthesis: none;
  font-size: var(--bs-body-font-size);
  font-weight: var(--bs-body-font-weight);
  line-height: var(--bs-body-line-height);
  text-align: var(--bs-body-text-align);
  background-color: var(--bs-body-bg);
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  -webkit-font-smoothing: subpixel-antialiased;
  text-rendering: optimizespeed;
}

:focus {
  isolation: isolate;
  outline: 3px solid var(--bs-focus-visible-outer-color);
  outline-offset: 2px;
  box-shadow: 0 0 0 2px var(--bs-focus-visible-inner-color);
}

.js-focus-visible :focus:not([data-focus-visible-added]):not(.focus-ring):not(.form-select:invalid):not(.form-control[type=file]:invalid),
.js-focus-visible .focus:not([data-focus-visible-added]):not(.focus-ring):not(.form-select:invalid):not(.form-control[type=file]:invalid) {
  outline: 0 !important;
  box-shadow: none;
}

:focus:not(:focus-visible):not(.focus-ring):not(.form-select:invalid):not(.form-control[type=file]:invalid) {
  outline: 0 !important;
  box-shadow: none;
}

hr {
  margin: 1.25rem 0;
  color: inherit;
  border: 0;
  border-top: var(--bs-border-width) solid;
}

h4,
h5,
h6, h2,
h3, h1 {
  margin-top: 0;
  margin-bottom: 1.25rem;
  font-size: 1rem;
  font-weight: 700;
  line-height: 1.125;
  color: var(--bs-heading-color);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizelegibility;
}

h1 {
  font-size: 1.25rem;
  line-height: 1.1;
}

h2,
h3 {
  font-size: 1.125rem;
  line-height: 1.1111111111;
}

p {
  margin-top: 0;
  margin-bottom: 1rem;
}

abbr[title] {
  -webkit-text-decoration: underline dotted;
  text-decoration: underline dotted;
  cursor: help;
  -webkit-text-decoration-skip-ink: none;
  text-decoration-skip-ink: none;
}

address {
  margin-bottom: 1rem;
  font-style: normal;
  line-height: inherit;
}

ol,
ul {
  padding-right: 2rem;
}

ol,
ul,
dl {
  margin-top: 0;
  margin-bottom: 1rem;
}

ol ol,
ul ul,
ol ul,
ul ol {
  margin-bottom: 0;
}

ul {
  list-style-type: square;
}

li::marker {
  color: var(--bs-primary);
  vertical-align: middle;
}
ol li::marker {
  color: inherit;
}

li li::marker {
  color: var(--bs-secondary-color);
}

li li li::marker {
  color: var(--bs-tertiary-color);
}

li::before {
  color: var(--bs-primary);
  vertical-align: text-top;
}
ol li::before {
  color: inherit;
}

li li::before {
  color: var(--bs-secondary-color);
}

li li li::before {
  color: var(--bs-tertiary-color);
}

dt {
  font-weight: 700;
}

dd {
  margin-bottom: 0.5rem;
  margin-right: 0;
}

blockquote {
  margin: 0 0 1rem;
}

b,
em,
strong {
  font-weight: 700;
}

small {
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.1428571429;
}

mark {
  padding: 0 0.1875em;
  color: var(--bs-highlight-color);
  background-color: var(--bs-highlight-bg);
}

sub,
sup {
  position: relative;
  font-size: 0.75em;
  line-height: 0;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

a {
  color: rgba(var(--bs-link-color-rgb), var(--bs-link-opacity, 1));
  text-decoration: underline;
}
a:hover {
  --bs-link-color-rgb: var(--bs-link-hover-color-rgb);
}

a:not([href]):not([class]), a:not([href]):not([class]):hover {
  color: inherit;
  text-decoration: none;
}

var,
pre,
code,
kbd,
samp {
  font-family: var(--bs-font-monospace);
  font-size: 1em;
}

pre {
  display: block;
  margin-top: 0;
  margin-bottom: 1rem;
  overflow: auto;
  font-size: 0.875em;
  line-height: 1.25;
  color: var(--bs-code-color);
}
pre code {
  font-size: inherit;
  color: inherit;
  word-break: normal;
}

var,
code {
  font-size: 0.875em;
  font-style: normal;
  line-height: 1.1428571429;
  color: var(--bs-code-color);
  word-wrap: break-word;
}
a > var,
a > code {
  color: inherit;
}

kbd {
  padding: 0.0625rem 0.0625rem;
  font-size: 0.875em;
  color: var(--bs-kbd-color, #000);
  background-color: var(--bs-kbd-bg, #eee);
}
kbd kbd {
  padding: 0;
  font-size: 1em;
}

figure {
  margin: 0 0 1rem;
}

img,
svg {
  vertical-align: middle;
}

table {
  font-feature-settings: "tnum";
  font-variant-numeric: tabular-nums;
  caption-side: top;
  border-collapse: collapse;
}

caption {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  font-size: 2.125rem;
  font-weight: 700;
  color: var(--bs-caption-color, var(--bs-emphasis-color));
  text-align: right;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizelegibility;
}

th {
  text-align: inherit;
  text-align: -webkit-match-parent;
}

thead,
tbody,
tfoot,
tr,
td,
th {
  border-color: inherit;
  border-style: solid;
  border-width: 0;
}

label {
  display: inline-block;
  font-weight: 700;
}

button {
  border-radius: 0;
}

button:focus:not(:focus-visible):not(.focus-ring) {
  outline: 0;
  box-shadow: none;
}

input,
button,
select,
optgroup,
textarea {
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  box-shadow: none;
}

button,
select {
  text-transform: none;
}

[role=button] {
  cursor: pointer;
}

select {
  word-wrap: normal;
}
select:disabled {
  opacity: 1;
}

[list]:not([type=date]):not([type=datetime-local]):not([type=month]):not([type=week]):not([type=time])::-webkit-calendar-picker-indicator {
  display: none !important;
}

button,
[type=button],
[type=reset],
[type=submit] {
  -webkit-appearance: button;
}
button:not(:disabled),
[type=button]:not(:disabled),
[type=reset]:not(:disabled),
[type=submit]:not(:disabled) {
  cursor: pointer;
}

::-moz-focus-inner {
  padding: 0;
  border-style: none;
}

textarea {
  resize: vertical;
}

fieldset {
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0;
}

legend {
  float: right;
  width: 100%;
  padding: 0;
  margin-bottom: 0.3125rem;
  font-size: 1.25rem;
  font-weight: 700;
  line-height: inherit;
}
legend + * {
  clear: right;
}

::-webkit-datetime-edit-fields-wrapper,
::-webkit-datetime-edit-text,
::-webkit-datetime-edit-minute,
::-webkit-datetime-edit-hour-field,
::-webkit-datetime-edit-day-field,
::-webkit-datetime-edit-month-field,
::-webkit-datetime-edit-year-field {
  padding: 0;
}

::-webkit-inner-spin-button {
  height: auto;
}

[type=search] {
  -webkit-appearance: textfield;
  outline-offset: -2px;
}

[type="tel"],
[type="url"],
[type="email"],
[type="number"] {
  direction: ltr;
}
::-webkit-search-decoration {
  -webkit-appearance: none;
}

::-webkit-color-swatch-wrapper {
  padding: 0;
}

::-webkit-file-upload-button {
  font: inherit;
  -webkit-appearance: button;
}

::file-selector-button {
  font: inherit;
  -webkit-appearance: button;
}

output {
  display: inline-block;
}

iframe {
  border: 0;
}

summary {
  display: list-item;
  cursor: pointer;
}

progress {
  vertical-align: baseline;
}

[hidden] {
  display: none !important;
}
/*# sourceMappingURL=boosted-reboot.rtl.css.map */