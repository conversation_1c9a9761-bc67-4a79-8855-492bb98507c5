/*!
  * Boosted v5.3.7 (https://boosted.orange.com/)
  * Copyright 2015-2025 The Boosted Authors
  * Copyright 2015-2025 Orange
  * Licensed under MIT (https://github.com/orange-opensource/orange-boosted-bootstrap/blob/main/LICENSE)
  * This a fork of Bootstrap : Initial license below
  * Bootstrap v5.3.7 (https://boosted.orange.com/)
  * Copyright 2011-2025 The Boosted Authors (https://github.com/Orange-OpenSource/Orange-Boosted-Bootstrap/graphs/contributors)
  * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
  */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).boosted=e()}(this,(function(){"use strict";const t=new Map,e={set(e,i,n){t.has(e)||t.set(e,new Map);const s=t.get(e);s.has(i)||0===s.size?s.set(i,n):console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(s.keys())[0]}.`)},get:(e,i)=>t.has(e)&&t.get(e).get(i)||null,remove(e,i){if(!t.has(e))return;const n=t.get(e);n.delete(i),0===n.size&&t.delete(e)}},i="transitionend",n=t=>(t&&window.CSS&&window.CSS.escape&&(t=t.replace(/#([^\s"#']+)/g,((t,e)=>`#${CSS.escape(e)}`))),t),s=t=>{t.dispatchEvent(new Event(i))},o=t=>!(!t||"object"!=typeof t)&&(void 0!==t.jquery&&(t=t[0]),void 0!==t.nodeType),r=t=>o(t)?t.jquery?t[0]:t:"string"==typeof t&&t.length>0?document.querySelector(n(t)):null,a=t=>{if(!o(t)||0===t.getClientRects().length)return!1;const e="visible"===getComputedStyle(t).getPropertyValue("visibility"),i=t.closest("details:not([open])");if(!i)return e;if(i!==t){const e=t.closest("summary");if(e&&e.parentNode!==i)return!1;if(null===e)return!1}return e},l=t=>!t||t.nodeType!==Node.ELEMENT_NODE||!!t.classList.contains("disabled")||(void 0!==t.disabled?t.disabled:t.hasAttribute("disabled")&&"false"!==t.getAttribute("disabled")),c=t=>{if(!document.documentElement.attachShadow)return null;if("function"==typeof t.getRootNode){const e=t.getRootNode();return e instanceof ShadowRoot?e:null}return t instanceof ShadowRoot?t:t.parentNode?c(t.parentNode):null},u=()=>{},d=t=>{t.offsetHeight},h=()=>window.jQuery&&!document.body.hasAttribute("data-bs-no-jquery")?window.jQuery:null,f=[],p=()=>"rtl"===document.documentElement.dir,m=t=>{var e;e=()=>{const e=h();if(e){const i=t.NAME,n=e.fn[i];e.fn[i]=t.jQueryInterface,e.fn[i].Constructor=t,e.fn[i].noConflict=()=>(e.fn[i]=n,t.jQueryInterface)}},"loading"===document.readyState?(f.length||document.addEventListener("DOMContentLoaded",(()=>{for(const t of f)t()})),f.push(e)):e()},g=(t,e=[],i=t)=>"function"==typeof t?t.call(...e):i,_=(t,e,n=!0)=>{if(!n)return void g(t);const o=(t=>{if(!t)return 0;let{transitionDuration:e,transitionDelay:i}=window.getComputedStyle(t);const n=Number.parseFloat(e),s=Number.parseFloat(i);return n||s?(e=e.split(",")[0],i=i.split(",")[0],1e3*(Number.parseFloat(e)+Number.parseFloat(i))):0})(e)+5;let r=!1;const a=({target:n})=>{n===e&&(r=!0,e.removeEventListener(i,a),g(t))};e.addEventListener(i,a),setTimeout((()=>{r||s(e)}),o)},b=(t,e,i,n)=>{const s=t.length;let o=t.indexOf(e);return-1===o?!i&&n?t[s-1]:t[0]:(o+=i?1:-1,n&&(o=(o+s)%s),t[Math.max(0,Math.min(o,s-1))])},v=/[^.]*(?=\..*)\.|.*/,y=/\..*/,w=/::\d+$/,E={};let A=1;const C={mouseenter:"mouseover",mouseleave:"mouseout"},T=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]);function O(t,e){return e&&`${e}::${A++}`||t.uidEvent||A++}function L(t){const e=O(t);return t.uidEvent=e,E[e]=E[e]||{},E[e]}function x(t,e,i=null){return Object.values(t).find((t=>t.callable===e&&t.delegationSelector===i))}function k(t,e,i){const n="string"==typeof e,s=n?i:e||i;let o=D(t);return T.has(o)||(o=t),[n,s,o]}function S(t,e,i,n,s){if("string"!=typeof e||!t)return;let[o,r,a]=k(e,i,n);if(e in C){const t=t=>function(e){if(!e.relatedTarget||e.relatedTarget!==e.delegateTarget&&!e.delegateTarget.contains(e.relatedTarget))return t.call(this,e)};r=t(r)}const l=L(t),c=l[a]||(l[a]={}),u=x(c,r,o?i:null);if(u)return void(u.oneOff=u.oneOff&&s);const d=O(r,e.replace(v,"")),h=o?function(t,e,i){return function n(s){const o=t.querySelectorAll(e);for(let{target:r}=s;r&&r!==this;r=r.parentNode)for(const a of o)if(a===r)return I(s,{delegateTarget:r}),n.oneOff&&P.off(t,s.type,e,i),i.apply(r,[s])}}(t,i,r):function(t,e){return function i(n){return I(n,{delegateTarget:t}),i.oneOff&&P.off(t,n.type,e),e.apply(t,[n])}}(t,r);h.delegationSelector=o?i:null,h.callable=r,h.oneOff=s,h.uidEvent=d,c[d]=h,t.addEventListener(a,h,o)}function N(t,e,i,n,s){const o=x(e[i],n,s);o&&(t.removeEventListener(i,o,Boolean(s)),delete e[i][o.uidEvent])}function $(t,e,i,n){const s=e[i]||{};for(const[o,r]of Object.entries(s))o.includes(n)&&N(t,e,i,r.callable,r.delegationSelector)}function D(t){return t=t.replace(y,""),C[t]||t}const P={on(t,e,i,n){S(t,e,i,n,!1)},one(t,e,i,n){S(t,e,i,n,!0)},off(t,e,i,n){if("string"!=typeof e||!t)return;const[s,o,r]=k(e,i,n),a=r!==e,l=L(t),c=l[r]||{},u=e.startsWith(".");if(void 0===o){if(u)for(const i of Object.keys(l))$(t,l,i,e.slice(1));for(const[i,n]of Object.entries(c)){const s=i.replace(w,"");a&&!e.includes(s)||N(t,l,r,n.callable,n.delegationSelector)}}else{if(!Object.keys(c).length)return;N(t,l,r,o,s?i:null)}},trigger(t,e,i){if("string"!=typeof e||!t)return null;const n=h();let s=null,o=!0,r=!0,a=!1;e!==D(e)&&n&&(s=n.Event(e,i),n(t).trigger(s),o=!s.isPropagationStopped(),r=!s.isImmediatePropagationStopped(),a=s.isDefaultPrevented());const l=I(new Event(e,{bubbles:o,cancelable:!0}),i);return a&&l.preventDefault(),r&&t.dispatchEvent(l),l.defaultPrevented&&s&&s.preventDefault(),l}};function I(t,e={}){for(const[i,n]of Object.entries(e))try{t[i]=n}catch(e){Object.defineProperty(t,i,{configurable:!0,get:()=>n})}return t}function M(t){if("true"===t)return!0;if("false"===t)return!1;if(t===Number(t).toString())return Number(t);if(""===t||"null"===t)return null;if("string"!=typeof t)return t;try{return JSON.parse(decodeURIComponent(t))}catch(e){return t}}function j(t){return t.replace(/[A-Z]/g,(t=>`-${t.toLowerCase()}`))}const B={setDataAttribute(t,e,i){t.setAttribute(`data-bs-${j(e)}`,i)},removeDataAttribute(t,e){t.removeAttribute(`data-bs-${j(e)}`)},getDataAttributes(t){if(!t)return{};const e={},i=Object.keys(t.dataset).filter((t=>t.startsWith("bs")&&!t.startsWith("bsConfig")));for(const n of i){let i=n.replace(/^bs/,"");i=i.charAt(0).toLowerCase()+i.slice(1),e[i]=M(t.dataset[n])}return e},getDataAttribute:(t,e)=>M(t.getAttribute(`data-bs-${j(e)}`))};class F{static get Default(){return{}}static get DefaultType(){return{}}static get NAME(){throw new Error('You have to implement the static method "NAME", for each component!')}_getConfig(t){return t=this._mergeConfigObj(t),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}_configAfterMerge(t){return t}_mergeConfigObj(t,e){const i=o(e)?B.getDataAttribute(e,"config"):{};return{...this.constructor.Default,..."object"==typeof i?i:{},...o(e)?B.getDataAttributes(e):{},..."object"==typeof t?t:{}}}_typeCheckConfig(t,e=this.constructor.DefaultType){for(const[n,s]of Object.entries(e)){const e=t[n],r=o(e)?"element":null==(i=e)?`${i}`:Object.prototype.toString.call(i).match(/\s([a-z]+)/i)[1].toLowerCase();if(!new RegExp(s).test(r))throw new TypeError(`${this.constructor.NAME.toUpperCase()}: Option "${n}" provided type "${r}" but expected type "${s}".`)}var i}}class H extends F{constructor(t,i){super(),(t=r(t))&&(this._element=t,this._config=this._getConfig(i),e.set(this._element,this.constructor.DATA_KEY,this))}dispose(){e.remove(this._element,this.constructor.DATA_KEY),P.off(this._element,this.constructor.EVENT_KEY);for(const t of Object.getOwnPropertyNames(this))this[t]=null}_queueCallback(t,e,i=!0){_(t,e,i)}_getConfig(t){return t=this._mergeConfigObj(t,this._element),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}static getInstance(t){return e.get(r(t),this.DATA_KEY)}static getOrCreateInstance(t,e={}){return this.getInstance(t)||new this(t,"object"==typeof e?e:null)}static get VERSION(){return"5.3.7"}static get DATA_KEY(){return`bs.${this.NAME}`}static get EVENT_KEY(){return`.${this.DATA_KEY}`}static eventName(t){return`${t}${this.EVENT_KEY}`}}const q=t=>{let e=t.getAttribute("data-bs-target");if(!e||"#"===e){let i=t.getAttribute("href");if(!i||!i.includes("#")&&!i.startsWith("."))return null;i.includes("#")&&!i.startsWith("#")&&(i=`#${i.split("#")[1]}`),e=i&&"#"!==i?i.trim():null}return e?e.split(",").map((t=>n(t))).join(","):null},W={find:(t,e=document.documentElement)=>[].concat(...Element.prototype.querySelectorAll.call(e,t)),findOne:(t,e=document.documentElement)=>Element.prototype.querySelector.call(e,t),children:(t,e)=>[].concat(...t.children).filter((t=>t.matches(e))),parents(t,e){const i=[];let n=t.parentNode.closest(e);for(;n;)i.push(n),n=n.parentNode.closest(e);return i},prev(t,e){let i=t.previousElementSibling;for(;i;){if(i.matches(e))return[i];i=i.previousElementSibling}return[]},next(t,e){let i=t.nextElementSibling;for(;i;){if(i.matches(e))return[i];i=i.nextElementSibling}return[]},focusableChildren(t){const e=["a","button","input","textarea","select","details","[tabindex]",'[contenteditable="true"]'].map((t=>`${t}:not([tabindex^="-"])`)).join(",");return this.find(e,t).filter((t=>!l(t)&&a(t)))},getSelectorFromElement(t){const e=q(t);return e&&W.findOne(e)?e:null},getElementFromSelector(t){const e=q(t);return e?W.findOne(e):null},getMultipleElementsFromSelector(t){const e=q(t);return e?W.find(e):[]}},z=(t,e="hide")=>{const i=`click.dismiss${t.EVENT_KEY}`,n=t.NAME;P.on(document,i,`[data-bs-dismiss="${n}"]`,(function(i){if(["A","AREA"].includes(this.tagName)&&i.preventDefault(),l(this))return;const s=W.getElementFromSelector(this)||this.closest(`.${n}`);t.getOrCreateInstance(s)[e]()}))},R=".bs.alert",V=`close${R}`,U=`closed${R}`;class K extends H{static get NAME(){return"alert"}close(){if(P.trigger(this._element,V).defaultPrevented)return;this._element.classList.remove("show");const t=this._element.classList.contains("fade");this._queueCallback((()=>this._destroyElement()),this._element,t)}_destroyElement(){this._element.remove(),P.trigger(this._element,U),this.dispose()}static jQueryInterface(t){return this.each((function(){const e=K.getOrCreateInstance(this);if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t](this)}}))}}z(K,"close"),m(K);const Q='[data-bs-toggle="button"]';class Y extends H{static get NAME(){return"button"}toggle(){this._element.setAttribute("aria-pressed",this._element.classList.toggle("active"))}static jQueryInterface(t){return this.each((function(){const e=Y.getOrCreateInstance(this);"toggle"===t&&e[t]()}))}}P.on(document,"click.bs.button.data-api",Q,(t=>{t.preventDefault();const e=t.target.closest(Q);Y.getOrCreateInstance(e).toggle()})),m(Y);const X=".bs.swipe",G=`touchstart${X}`,J=`touchmove${X}`,Z=`touchend${X}`,tt=`pointerdown${X}`,et=`pointerup${X}`,it={endCallback:null,leftCallback:null,rightCallback:null},nt={endCallback:"(function|null)",leftCallback:"(function|null)",rightCallback:"(function|null)"};class st extends F{constructor(t,e){super(),this._element=t,t&&st.isSupported()&&(this._config=this._getConfig(e),this._deltaX=0,this._supportPointerEvents=Boolean(window.PointerEvent),this._initEvents())}static get Default(){return it}static get DefaultType(){return nt}static get NAME(){return"swipe"}dispose(){P.off(this._element,X)}_start(t){this._supportPointerEvents?this._eventIsPointerPenTouch(t)&&(this._deltaX=t.clientX):this._deltaX=t.touches[0].clientX}_end(t){this._eventIsPointerPenTouch(t)&&(this._deltaX=t.clientX-this._deltaX),this._handleSwipe(),g(this._config.endCallback)}_move(t){this._deltaX=t.touches&&t.touches.length>1?0:t.touches[0].clientX-this._deltaX}_handleSwipe(){const t=Math.abs(this._deltaX);if(t<=40)return;const e=t/this._deltaX;this._deltaX=0,e&&g(e>0?this._config.rightCallback:this._config.leftCallback)}_initEvents(){this._supportPointerEvents?(P.on(this._element,tt,(t=>this._start(t))),P.on(this._element,et,(t=>this._end(t))),this._element.classList.add("pointer-event")):(P.on(this._element,G,(t=>this._start(t))),P.on(this._element,J,(t=>this._move(t))),P.on(this._element,Z,(t=>this._end(t))))}_eventIsPointerPenTouch(t){return this._supportPointerEvents&&("pen"===t.pointerType||"touch"===t.pointerType)}static isSupported(){return"ontouchstart"in document.documentElement||navigator.maxTouchPoints>0}}const ot=".bs.carousel",rt=".data-api",at="ArrowLeft",lt="ArrowRight",ct="next",ut="prev",dt="left",ht="right",ft=`slide${ot}`,pt=`slid${ot}`,mt=`keydown${ot}`,gt=`mouseenter${ot}`,_t=`mouseleave${ot}`,bt=`dragstart${ot}`,vt=`load${ot}${rt}`,yt=`click${ot}${rt}`,wt="carousel",Et="active",At="is-paused",Ct="is-done",Tt="pause",Ot="play",Lt=".active",xt=".carousel-item",kt=Lt+xt,St=".carousel-control-play-pause",Nt="data-bs-target",$t="data-bs-play-text",Dt="data-bs-pause-text",Pt="Play Carousel",It="Pause Carousel",Mt={[at]:ht,[lt]:dt},jt={interval:5e3,keyboard:!0,pause:"hover",ride:!1,touch:!0,wrap:!0},Bt={interval:"(number|boolean)",keyboard:"boolean",pause:"(string|boolean)",ride:"(boolean|string)",touch:"boolean",wrap:"boolean"};class Ft extends H{constructor(t,e){super(t,e),this._interval=null,this._activeElement=null,this._isSliding=!1,this.touchTimeout=null,this._swipeHelper=null,this._indicatorsElement=W.findOne(".carousel-indicators",this._element),this._playPauseButton=W.findOne(`${St}[${Nt}="#${this._element.id}"]`),this._addEventListeners(),this._config.ride===wt?this.cycle():this._indicatorsElement&&this._element.classList.add(At)}static get Default(){return jt}static get DefaultType(){return Bt}static get NAME(){return"carousel"}next(){this._slide(ct)}nextWhenVisible(){!document.hidden&&a(this._element)&&this.next()}prev(){this._slide(ut)}pause(){this._indicatorsElement&&this._element.classList.add(At),null!==this._playPauseButton&&this._playPauseButton.classList.contains(Tt)&&(this._playPauseButton.classList.remove(Tt),this._playPauseButton.classList.add(Ot),this._playPauseButton.getAttribute($t)?(this._playPauseButton.setAttribute("title",this._playPauseButton.getAttribute($t)),this._playPauseButton.querySelector("span.visually-hidden").innerHTML=this._playPauseButton.getAttribute($t)):(this._playPauseButton.setAttribute("title",Pt),this._playPauseButton.querySelector("span.visually-hidden").innerHTML=Pt),this._stayPaused=!0),this._isSliding&&s(this._element),this._clearInterval()}cycle(){this._indicatorsElement&&this._element.classList.remove(At),null!==this._playPauseButton&&this._playPauseButton.classList.contains(Ot)&&(this._playPauseButton.classList.remove(Ot),this._playPauseButton.classList.add(Tt),this._playPauseButton.getAttribute(Dt)?(this._playPauseButton.setAttribute("title",this._playPauseButton.getAttribute(Dt)),this._playPauseButton.querySelector("span.visually-hidden").innerHTML=this._playPauseButton.getAttribute(Dt)):(this._playPauseButton.setAttribute("title",It),this._playPauseButton.querySelector("span.visually-hidden").innerHTML=It),this._stayPaused=!1),this._clearInterval(),this._updateInterval(),this._interval=setInterval((()=>this.nextWhenVisible()),this._config.interval)}_maybeEnableCycle(){this._config.ride&&(this._isSliding?P.one(this._element,pt,(()=>this.cycle())):this.cycle())}to(t){this._indicatorsElement&&this._element.classList.remove(Ct);const e=this._getItems();if(t>e.length-1||t<0)return;if(this._isSliding)return void P.one(this._element,pt,(()=>this.to(t)));const i=this._getItemIndex(this._getActive());if(i===t)return;const n=t>i?ct:ut;this._slide(n,e[t])}dispose(){this._swipeHelper&&this._swipeHelper.dispose(),super.dispose()}_configAfterMerge(t){return t.defaultInterval=t.interval,t}_addEventListeners(){this._config.keyboard&&P.on(this._element,mt,(t=>this._keydown(t))),"hover"===this._config.pause&&(P.on(this._element,gt,(()=>this.pause())),P.on(this._element,_t,(()=>this._maybeEnableCycle()))),this._config.touch&&st.isSupported()&&this._addTouchEventListeners()}_addTouchEventListeners(){for(const t of W.find(".carousel-item img",this._element))P.on(t,bt,(t=>t.preventDefault()));const t={leftCallback:()=>this._slide(this._directionToOrder(dt)),rightCallback:()=>this._slide(this._directionToOrder(ht)),endCallback:()=>{"hover"===this._config.pause&&(this.pause(),this.touchTimeout&&clearTimeout(this.touchTimeout),this.touchTimeout=setTimeout((()=>this._maybeEnableCycle()),500+this._config.interval))}};this._swipeHelper=new st(this._element,t)}_keydown(t){if(/input|textarea/i.test(t.target.tagName))return;const e=Mt[t.key];e&&(t.preventDefault(),this._slide(this._directionToOrder(e)))}_disableControl(t){"BUTTON"===t.nodeName?t.disabled=!0:(t.setAttribute("aria-disabled",!0),t.setAttribute("tabindex","-1"))}_enableControl(t){"BUTTON"===t.nodeName?t.disabled=!1:(t.removeAttribute("aria-disabled"),t.removeAttribute("tabindex"))}_getItemIndex(t){return this._getItems().indexOf(t)}_setActiveIndicatorElement(t){if(!this._indicatorsElement)return;const e=W.findOne(Lt,this._indicatorsElement);e.classList.remove(Et),e.removeAttribute("aria-current");const i=W.findOne(`[data-bs-slide-to="${t}"]`,this._indicatorsElement);i&&(i.classList.add(Et),i.setAttribute("aria-current","true"))}_updateInterval(){const t=this._activeElement||this._getActive();if(!t)return;const e=Number.parseInt(t.getAttribute("data-bs-interval"),10);if(this._config.interval=e||this._config.defaultInterval,this._indicatorsElement&&this._config.interval!==jt.interval){const e=this._getItemIndex(t);W.findOne(`:nth-child(${e+1})`,this._indicatorsElement).style.setProperty("--bs-carousel-interval",`${this._config.interval}ms`)}}_slide(t,e=null){if(this._isSliding)return;const i=this._getActive(),n=t===ct;if(!this._config.wrap){const e=t===ut,s=this._getItemIndex(i),o=this._getItems().length-1;if(e&&0===s||n&&s===o)return n&&this._indicatorsElement&&!this._element.hasAttribute("data-bs-slide")&&this._element.classList.add(Ct),i;this._indicatorsElement&&this._element.classList.remove(Ct)}const s=e||b(this._getItems(),i,n,this._config.wrap);if(s===i)return;const o=this._getItemIndex(s),r=e=>P.trigger(this._element,e,{relatedTarget:s,direction:this._orderToDirection(t),from:this._getItemIndex(i),to:o});if(r(ft).defaultPrevented)return;if(!i||!s)return;const a=Boolean(this._interval);if(this.pause(),this._isSliding=!0,this._setActiveIndicatorElement(o),this._activeElement=s,!this._config.wrap){const t=W.findOne(".carousel-control-prev",this._element),e=W.findOne(".carousel-control-next",this._element);this._enableControl(t),this._enableControl(e),0===o?this._disableControl(t):o===this._getItems().length-1&&this._disableControl(e)}const l=n?"carousel-item-start":"carousel-item-end",c=n?"carousel-item-next":"carousel-item-prev";s.classList.add(c),d(s),i.classList.add(l),s.classList.add(l),this._queueCallback((()=>{s.classList.remove(l,c),s.classList.add(Et),i.classList.remove(Et,c,l),this._isSliding=!1,r(pt)}),i,this._isAnimated()),a&&this.cycle()}_isAnimated(){return this._element.classList.contains("slide")}_getActive(){return W.findOne(kt,this._element)}_getItems(){return W.find(xt,this._element)}_clearInterval(){this._interval&&(clearInterval(this._interval),this._interval=null)}_directionToOrder(t){return p()?t===dt?ut:ct:t===dt?ct:ut}_orderToDirection(t){return p()?t===ut?dt:ht:t===ut?ht:dt}static PauseCarousel(t){const e=t.target,i=e.getAttribute(Nt),n=Ft.getOrCreateInstance(document.querySelector(i));e.classList.contains(Tt)?n.pause():n.cycle()}static jQueryInterface(t){return this.each((function(){const e=Ft.getOrCreateInstance(this,t);if("number"!=typeof t){if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t]()}}else e.to(t)}))}}P.on(document,yt,"[data-bs-slide], [data-bs-slide-to]",(function(t){const e=W.getElementFromSelector(this);if(!e||!e.classList.contains(wt))return;t.preventDefault();const i=Ft.getOrCreateInstance(e),n=this.getAttribute("data-bs-slide-to");return n?(i.to(n),void i._maybeEnableCycle()):"next"===B.getDataAttribute(this,"slide")?(i.next(),void i._maybeEnableCycle()):(i.prev(),void i._maybeEnableCycle())})),P.on(document,yt,St,Ft.PauseCarousel),P.on(window,vt,(()=>{const t=W.find('[data-bs-ride="carousel"]');for(const e of t)Ft.getOrCreateInstance(e)})),m(Ft);const Ht=".bs.collapse",qt=`show${Ht}`,Wt=`shown${Ht}`,zt=`hide${Ht}`,Rt=`hidden${Ht}`,Vt=`click${Ht}.data-api`,Ut="show",Kt="collapse",Qt="collapsing",Yt=`:scope .${Kt} .${Kt}`,Xt='[data-bs-toggle="collapse"]',Gt={parent:null,toggle:!0},Jt={parent:"(null|element)",toggle:"boolean"};class Zt extends H{constructor(t,e){super(t,e),this._isTransitioning=!1,this._triggerArray=[];const i=W.find(Xt);for(const t of i){const e=W.getSelectorFromElement(t),i=W.find(e).filter((t=>t===this._element));null!==e&&i.length&&this._triggerArray.push(t)}this._initializeChildren(),this._config.parent||this._addAriaAndCollapsedClass(this._triggerArray,this._isShown()),this._config.toggle&&this.toggle()}static get Default(){return Gt}static get DefaultType(){return Jt}static get NAME(){return"collapse"}toggle(){this._isShown()?this.hide():this.show()}show(){if(this._isTransitioning||this._isShown())return;let t=[];if(this._config.parent&&(t=this._getFirstLevelChildren(".collapse.show, .collapse.collapsing").filter((t=>t!==this._element)).map((t=>Zt.getOrCreateInstance(t,{toggle:!1})))),t.length&&t[0]._isTransitioning)return;if(P.trigger(this._element,qt).defaultPrevented)return;for(const e of t)e.hide();const e=this._getDimension();this._element.classList.remove(Kt),this._element.classList.add(Qt),this._element.style[e]=0,this._addAriaAndCollapsedClass(this._triggerArray,!0),this._isTransitioning=!0;const i=`scroll${e[0].toUpperCase()+e.slice(1)}`;this._queueCallback((()=>{this._isTransitioning=!1,this._element.classList.remove(Qt),this._element.classList.add(Kt,Ut),this._element.style[e]="",P.trigger(this._element,Wt)}),this._element,!0),this._element.style[e]=`${this._element[i]}px`}hide(){if(this._isTransitioning||!this._isShown())return;if(P.trigger(this._element,zt).defaultPrevented)return;const t=this._getDimension();this._element.style[t]=`${this._element.getBoundingClientRect()[t]}px`,d(this._element),this._element.classList.add(Qt),this._element.classList.remove(Kt,Ut),this._isTransitioning=!0,this._element.style[t]="",this._queueCallback((()=>{this._isTransitioning=!1,this._element.classList.remove(Qt),this._element.classList.add(Kt);for(const t of this._triggerArray){const e=W.getElementFromSelector(t);e&&!this._isShown(e)&&this._addAriaAndCollapsedClass([t],!1)}P.trigger(this._element,Rt)}),this._element,!0)}_isShown(t=this._element){return t.classList.contains(Ut)}_configAfterMerge(t){return t.toggle=Boolean(t.toggle),t.parent=r(t.parent),t}_getDimension(){return this._element.classList.contains("collapse-horizontal")?"width":"height"}_initializeChildren(){if(!this._config.parent)return;const t=this._getFirstLevelChildren(Xt);for(const e of t){const t=W.getElementFromSelector(e);t&&this._addAriaAndCollapsedClass([e],this._isShown(t))}}_getFirstLevelChildren(t){const e=W.find(Yt,this._config.parent);return W.find(t,this._config.parent).filter((t=>!e.includes(t)))}_addAriaAndCollapsedClass(t,e){if(t.length)for(const i of t)i.classList.toggle("collapsed",!e),i.setAttribute("aria-expanded",e)}static jQueryInterface(t){const e={};return"string"==typeof t&&/show|hide/.test(t)&&(e.toggle=!1),this.each((function(){const i=Zt.getOrCreateInstance(this,e);if("string"==typeof t){if(void 0===i[t])throw new TypeError(`No method named "${t}"`);i[t]()}}))}}P.on(document,Vt,Xt,(function(t){("A"===t.target.tagName||t.delegateTarget&&"A"===t.delegateTarget.tagName)&&t.preventDefault();for(const t of W.getMultipleElementsFromSelector(this))Zt.getOrCreateInstance(t,{toggle:!1}).toggle()})),m(Zt);var te="top",ee="bottom",ie="right",ne="left",se="auto",oe=[te,ee,ie,ne],re="start",ae="end",le="clippingParents",ce="viewport",ue="popper",de="reference",he=oe.reduce((function(t,e){return t.concat([e+"-"+re,e+"-"+ae])}),[]),fe=[].concat(oe,[se]).reduce((function(t,e){return t.concat([e,e+"-"+re,e+"-"+ae])}),[]),pe="beforeRead",me="read",ge="afterRead",_e="beforeMain",be="main",ve="afterMain",ye="beforeWrite",we="write",Ee="afterWrite",Ae=[pe,me,ge,_e,be,ve,ye,we,Ee];function Ce(t){return t?(t.nodeName||"").toLowerCase():null}function Te(t){if(null==t)return window;if("[object Window]"!==t.toString()){var e=t.ownerDocument;return e&&e.defaultView||window}return t}function Oe(t){return t instanceof Te(t).Element||t instanceof Element}function Le(t){return t instanceof Te(t).HTMLElement||t instanceof HTMLElement}function xe(t){return"undefined"!=typeof ShadowRoot&&(t instanceof Te(t).ShadowRoot||t instanceof ShadowRoot)}const ke={name:"applyStyles",enabled:!0,phase:"write",fn:function(t){var e=t.state;Object.keys(e.elements).forEach((function(t){var i=e.styles[t]||{},n=e.attributes[t]||{},s=e.elements[t];Le(s)&&Ce(s)&&(Object.assign(s.style,i),Object.keys(n).forEach((function(t){var e=n[t];!1===e?s.removeAttribute(t):s.setAttribute(t,!0===e?"":e)})))}))},effect:function(t){var e=t.state,i={popper:{position:e.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(e.elements.popper.style,i.popper),e.styles=i,e.elements.arrow&&Object.assign(e.elements.arrow.style,i.arrow),function(){Object.keys(e.elements).forEach((function(t){var n=e.elements[t],s=e.attributes[t]||{},o=Object.keys(e.styles.hasOwnProperty(t)?e.styles[t]:i[t]).reduce((function(t,e){return t[e]="",t}),{});Le(n)&&Ce(n)&&(Object.assign(n.style,o),Object.keys(s).forEach((function(t){n.removeAttribute(t)})))}))}},requires:["computeStyles"]};function Se(t){return t.split("-")[0]}var Ne=Math.max,$e=Math.min,De=Math.round;function Pe(){var t=navigator.userAgentData;return null!=t&&t.brands&&Array.isArray(t.brands)?t.brands.map((function(t){return t.brand+"/"+t.version})).join(" "):navigator.userAgent}function Ie(){return!/^((?!chrome|android).)*safari/i.test(Pe())}function Me(t,e,i){void 0===e&&(e=!1),void 0===i&&(i=!1);var n=t.getBoundingClientRect(),s=1,o=1;e&&Le(t)&&(s=t.offsetWidth>0&&De(n.width)/t.offsetWidth||1,o=t.offsetHeight>0&&De(n.height)/t.offsetHeight||1);var r=(Oe(t)?Te(t):window).visualViewport,a=!Ie()&&i,l=(n.left+(a&&r?r.offsetLeft:0))/s,c=(n.top+(a&&r?r.offsetTop:0))/o,u=n.width/s,d=n.height/o;return{width:u,height:d,top:c,right:l+u,bottom:c+d,left:l,x:l,y:c}}function je(t){var e=Me(t),i=t.offsetWidth,n=t.offsetHeight;return Math.abs(e.width-i)<=1&&(i=e.width),Math.abs(e.height-n)<=1&&(n=e.height),{x:t.offsetLeft,y:t.offsetTop,width:i,height:n}}function Be(t,e){var i=e.getRootNode&&e.getRootNode();if(t.contains(e))return!0;if(i&&xe(i)){var n=e;do{if(n&&t.isSameNode(n))return!0;n=n.parentNode||n.host}while(n)}return!1}function Fe(t){return Te(t).getComputedStyle(t)}function He(t){return["table","td","th"].indexOf(Ce(t))>=0}function qe(t){return((Oe(t)?t.ownerDocument:t.document)||window.document).documentElement}function We(t){return"html"===Ce(t)?t:t.assignedSlot||t.parentNode||(xe(t)?t.host:null)||qe(t)}function ze(t){return Le(t)&&"fixed"!==Fe(t).position?t.offsetParent:null}function Re(t){for(var e=Te(t),i=ze(t);i&&He(i)&&"static"===Fe(i).position;)i=ze(i);return i&&("html"===Ce(i)||"body"===Ce(i)&&"static"===Fe(i).position)?e:i||function(t){var e=/firefox/i.test(Pe());if(/Trident/i.test(Pe())&&Le(t)&&"fixed"===Fe(t).position)return null;var i=We(t);for(xe(i)&&(i=i.host);Le(i)&&["html","body"].indexOf(Ce(i))<0;){var n=Fe(i);if("none"!==n.transform||"none"!==n.perspective||"paint"===n.contain||-1!==["transform","perspective"].indexOf(n.willChange)||e&&"filter"===n.willChange||e&&n.filter&&"none"!==n.filter)return i;i=i.parentNode}return null}(t)||e}function Ve(t){return["top","bottom"].indexOf(t)>=0?"x":"y"}function Ue(t,e,i){return Ne(t,$e(e,i))}function Ke(t){return Object.assign({},{top:0,right:0,bottom:0,left:0},t)}function Qe(t,e){return e.reduce((function(e,i){return e[i]=t,e}),{})}const Ye={name:"arrow",enabled:!0,phase:"main",fn:function(t){var e,i=t.state,n=t.name,s=t.options,o=i.elements.arrow,r=i.modifiersData.popperOffsets,a=Se(i.placement),l=Ve(a),c=[ne,ie].indexOf(a)>=0?"height":"width";if(o&&r){var u=function(t,e){return Ke("number"!=typeof(t="function"==typeof t?t(Object.assign({},e.rects,{placement:e.placement})):t)?t:Qe(t,oe))}(s.padding,i),d=je(o),h="y"===l?te:ne,f="y"===l?ee:ie,p=i.rects.reference[c]+i.rects.reference[l]-r[l]-i.rects.popper[c],m=r[l]-i.rects.reference[l],g=Re(o),_=g?"y"===l?g.clientHeight||0:g.clientWidth||0:0,b=p/2-m/2,v=u[h],y=_-d[c]-u[f],w=_/2-d[c]/2+b,E=Ue(v,w,y),A=l;i.modifiersData[n]=((e={})[A]=E,e.centerOffset=E-w,e)}},effect:function(t){var e=t.state,i=t.options.element,n=void 0===i?"[data-popper-arrow]":i;null!=n&&("string"!=typeof n||(n=e.elements.popper.querySelector(n)))&&Be(e.elements.popper,n)&&(e.elements.arrow=n)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function Xe(t){return t.split("-")[1]}var Ge={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Je(t){var e,i=t.popper,n=t.popperRect,s=t.placement,o=t.variation,r=t.offsets,a=t.position,l=t.gpuAcceleration,c=t.adaptive,u=t.roundOffsets,d=t.isFixed,h=r.x,f=void 0===h?0:h,p=r.y,m=void 0===p?0:p,g="function"==typeof u?u({x:f,y:m}):{x:f,y:m};f=g.x,m=g.y;var _=r.hasOwnProperty("x"),b=r.hasOwnProperty("y"),v=ne,y=te,w=window;if(c){var E=Re(i),A="clientHeight",C="clientWidth";E===Te(i)&&"static"!==Fe(E=qe(i)).position&&"absolute"===a&&(A="scrollHeight",C="scrollWidth"),(s===te||(s===ne||s===ie)&&o===ae)&&(y=ee,m-=(d&&E===w&&w.visualViewport?w.visualViewport.height:E[A])-n.height,m*=l?1:-1),s!==ne&&(s!==te&&s!==ee||o!==ae)||(v=ie,f-=(d&&E===w&&w.visualViewport?w.visualViewport.width:E[C])-n.width,f*=l?1:-1)}var T,O=Object.assign({position:a},c&&Ge),L=!0===u?function(t,e){var i=t.x,n=t.y,s=e.devicePixelRatio||1;return{x:De(i*s)/s||0,y:De(n*s)/s||0}}({x:f,y:m},Te(i)):{x:f,y:m};return f=L.x,m=L.y,l?Object.assign({},O,((T={})[y]=b?"0":"",T[v]=_?"0":"",T.transform=(w.devicePixelRatio||1)<=1?"translate("+f+"px, "+m+"px)":"translate3d("+f+"px, "+m+"px, 0)",T)):Object.assign({},O,((e={})[y]=b?m+"px":"",e[v]=_?f+"px":"",e.transform="",e))}const Ze={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(t){var e=t.state,i=t.options,n=i.gpuAcceleration,s=void 0===n||n,o=i.adaptive,r=void 0===o||o,a=i.roundOffsets,l=void 0===a||a,c={placement:Se(e.placement),variation:Xe(e.placement),popper:e.elements.popper,popperRect:e.rects.popper,gpuAcceleration:s,isFixed:"fixed"===e.options.strategy};null!=e.modifiersData.popperOffsets&&(e.styles.popper=Object.assign({},e.styles.popper,Je(Object.assign({},c,{offsets:e.modifiersData.popperOffsets,position:e.options.strategy,adaptive:r,roundOffsets:l})))),null!=e.modifiersData.arrow&&(e.styles.arrow=Object.assign({},e.styles.arrow,Je(Object.assign({},c,{offsets:e.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:l})))),e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-placement":e.placement})},data:{}};var ti={passive:!0};const ei={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(t){var e=t.state,i=t.instance,n=t.options,s=n.scroll,o=void 0===s||s,r=n.resize,a=void 0===r||r,l=Te(e.elements.popper),c=[].concat(e.scrollParents.reference,e.scrollParents.popper);return o&&c.forEach((function(t){t.addEventListener("scroll",i.update,ti)})),a&&l.addEventListener("resize",i.update,ti),function(){o&&c.forEach((function(t){t.removeEventListener("scroll",i.update,ti)})),a&&l.removeEventListener("resize",i.update,ti)}},data:{}};var ii={left:"right",right:"left",bottom:"top",top:"bottom"};function ni(t){return t.replace(/left|right|bottom|top/g,(function(t){return ii[t]}))}var si={start:"end",end:"start"};function oi(t){return t.replace(/start|end/g,(function(t){return si[t]}))}function ri(t){var e=Te(t);return{scrollLeft:e.pageXOffset,scrollTop:e.pageYOffset}}function ai(t){return Me(qe(t)).left+ri(t).scrollLeft}function li(t){var e=Fe(t),i=e.overflow,n=e.overflowX,s=e.overflowY;return/auto|scroll|overlay|hidden/.test(i+s+n)}function ci(t){return["html","body","#document"].indexOf(Ce(t))>=0?t.ownerDocument.body:Le(t)&&li(t)?t:ci(We(t))}function ui(t,e){var i;void 0===e&&(e=[]);var n=ci(t),s=n===(null==(i=t.ownerDocument)?void 0:i.body),o=Te(n),r=s?[o].concat(o.visualViewport||[],li(n)?n:[]):n,a=e.concat(r);return s?a:a.concat(ui(We(r)))}function di(t){return Object.assign({},t,{left:t.x,top:t.y,right:t.x+t.width,bottom:t.y+t.height})}function hi(t,e,i){return e===ce?di(function(t,e){var i=Te(t),n=qe(t),s=i.visualViewport,o=n.clientWidth,r=n.clientHeight,a=0,l=0;if(s){o=s.width,r=s.height;var c=Ie();(c||!c&&"fixed"===e)&&(a=s.offsetLeft,l=s.offsetTop)}return{width:o,height:r,x:a+ai(t),y:l}}(t,i)):Oe(e)?function(t,e){var i=Me(t,!1,"fixed"===e);return i.top=i.top+t.clientTop,i.left=i.left+t.clientLeft,i.bottom=i.top+t.clientHeight,i.right=i.left+t.clientWidth,i.width=t.clientWidth,i.height=t.clientHeight,i.x=i.left,i.y=i.top,i}(e,i):di(function(t){var e,i=qe(t),n=ri(t),s=null==(e=t.ownerDocument)?void 0:e.body,o=Ne(i.scrollWidth,i.clientWidth,s?s.scrollWidth:0,s?s.clientWidth:0),r=Ne(i.scrollHeight,i.clientHeight,s?s.scrollHeight:0,s?s.clientHeight:0),a=-n.scrollLeft+ai(t),l=-n.scrollTop;return"rtl"===Fe(s||i).direction&&(a+=Ne(i.clientWidth,s?s.clientWidth:0)-o),{width:o,height:r,x:a,y:l}}(qe(t)))}function fi(t){var e,i=t.reference,n=t.element,s=t.placement,o=s?Se(s):null,r=s?Xe(s):null,a=i.x+i.width/2-n.width/2,l=i.y+i.height/2-n.height/2;switch(o){case te:e={x:a,y:i.y-n.height};break;case ee:e={x:a,y:i.y+i.height};break;case ie:e={x:i.x+i.width,y:l};break;case ne:e={x:i.x-n.width,y:l};break;default:e={x:i.x,y:i.y}}var c=o?Ve(o):null;if(null!=c){var u="y"===c?"height":"width";switch(r){case re:e[c]=e[c]-(i[u]/2-n[u]/2);break;case ae:e[c]=e[c]+(i[u]/2-n[u]/2)}}return e}function pi(t,e){void 0===e&&(e={});var i=e,n=i.placement,s=void 0===n?t.placement:n,o=i.strategy,r=void 0===o?t.strategy:o,a=i.boundary,l=void 0===a?le:a,c=i.rootBoundary,u=void 0===c?ce:c,d=i.elementContext,h=void 0===d?ue:d,f=i.altBoundary,p=void 0!==f&&f,m=i.padding,g=void 0===m?0:m,_=Ke("number"!=typeof g?g:Qe(g,oe)),b=h===ue?de:ue,v=t.rects.popper,y=t.elements[p?b:h],w=function(t,e,i,n){var s="clippingParents"===e?function(t){var e=ui(We(t)),i=["absolute","fixed"].indexOf(Fe(t).position)>=0&&Le(t)?Re(t):t;return Oe(i)?e.filter((function(t){return Oe(t)&&Be(t,i)&&"body"!==Ce(t)})):[]}(t):[].concat(e),o=[].concat(s,[i]),r=o[0],a=o.reduce((function(e,i){var s=hi(t,i,n);return e.top=Ne(s.top,e.top),e.right=$e(s.right,e.right),e.bottom=$e(s.bottom,e.bottom),e.left=Ne(s.left,e.left),e}),hi(t,r,n));return a.width=a.right-a.left,a.height=a.bottom-a.top,a.x=a.left,a.y=a.top,a}(Oe(y)?y:y.contextElement||qe(t.elements.popper),l,u,r),E=Me(t.elements.reference),A=fi({reference:E,element:v,placement:s}),C=di(Object.assign({},v,A)),T=h===ue?C:E,O={top:w.top-T.top+_.top,bottom:T.bottom-w.bottom+_.bottom,left:w.left-T.left+_.left,right:T.right-w.right+_.right},L=t.modifiersData.offset;if(h===ue&&L){var x=L[s];Object.keys(O).forEach((function(t){var e=[ie,ee].indexOf(t)>=0?1:-1,i=[te,ee].indexOf(t)>=0?"y":"x";O[t]+=x[i]*e}))}return O}function mi(t,e){void 0===e&&(e={});var i=e,n=i.placement,s=i.boundary,o=i.rootBoundary,r=i.padding,a=i.flipVariations,l=i.allowedAutoPlacements,c=void 0===l?fe:l,u=Xe(n),d=u?a?he:he.filter((function(t){return Xe(t)===u})):oe,h=d.filter((function(t){return c.indexOf(t)>=0}));0===h.length&&(h=d);var f=h.reduce((function(e,i){return e[i]=pi(t,{placement:i,boundary:s,rootBoundary:o,padding:r})[Se(i)],e}),{});return Object.keys(f).sort((function(t,e){return f[t]-f[e]}))}const gi={name:"flip",enabled:!0,phase:"main",fn:function(t){var e=t.state,i=t.options,n=t.name;if(!e.modifiersData[n]._skip){for(var s=i.mainAxis,o=void 0===s||s,r=i.altAxis,a=void 0===r||r,l=i.fallbackPlacements,c=i.padding,u=i.boundary,d=i.rootBoundary,h=i.altBoundary,f=i.flipVariations,p=void 0===f||f,m=i.allowedAutoPlacements,g=e.options.placement,_=Se(g),b=l||(_!==g&&p?function(t){if(Se(t)===se)return[];var e=ni(t);return[oi(t),e,oi(e)]}(g):[ni(g)]),v=[g].concat(b).reduce((function(t,i){return t.concat(Se(i)===se?mi(e,{placement:i,boundary:u,rootBoundary:d,padding:c,flipVariations:p,allowedAutoPlacements:m}):i)}),[]),y=e.rects.reference,w=e.rects.popper,E=new Map,A=!0,C=v[0],T=0;T<v.length;T++){var O=v[T],L=Se(O),x=Xe(O)===re,k=[te,ee].indexOf(L)>=0,S=k?"width":"height",N=pi(e,{placement:O,boundary:u,rootBoundary:d,altBoundary:h,padding:c}),$=k?x?ie:ne:x?ee:te;y[S]>w[S]&&($=ni($));var D=ni($),P=[];if(o&&P.push(N[L]<=0),a&&P.push(N[$]<=0,N[D]<=0),P.every((function(t){return t}))){C=O,A=!1;break}E.set(O,P)}if(A)for(var I=function(t){var e=v.find((function(e){var i=E.get(e);if(i)return i.slice(0,t).every((function(t){return t}))}));if(e)return C=e,"break"},M=p?3:1;M>0&&"break"!==I(M);M--);e.placement!==C&&(e.modifiersData[n]._skip=!0,e.placement=C,e.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function _i(t,e,i){return void 0===i&&(i={x:0,y:0}),{top:t.top-e.height-i.y,right:t.right-e.width+i.x,bottom:t.bottom-e.height+i.y,left:t.left-e.width-i.x}}function bi(t){return[te,ie,ee,ne].some((function(e){return t[e]>=0}))}const vi={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(t){var e=t.state,i=t.name,n=e.rects.reference,s=e.rects.popper,o=e.modifiersData.preventOverflow,r=pi(e,{elementContext:"reference"}),a=pi(e,{altBoundary:!0}),l=_i(r,n),c=_i(a,s,o),u=bi(l),d=bi(c);e.modifiersData[i]={referenceClippingOffsets:l,popperEscapeOffsets:c,isReferenceHidden:u,hasPopperEscaped:d},e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-reference-hidden":u,"data-popper-escaped":d})}},yi={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(t){var e=t.state,i=t.options,n=t.name,s=i.offset,o=void 0===s?[0,0]:s,r=fe.reduce((function(t,i){return t[i]=function(t,e,i){var n=Se(t),s=[ne,te].indexOf(n)>=0?-1:1,o="function"==typeof i?i(Object.assign({},e,{placement:t})):i,r=o[0],a=o[1];return r=r||0,a=(a||0)*s,[ne,ie].indexOf(n)>=0?{x:a,y:r}:{x:r,y:a}}(i,e.rects,o),t}),{}),a=r[e.placement],l=a.x,c=a.y;null!=e.modifiersData.popperOffsets&&(e.modifiersData.popperOffsets.x+=l,e.modifiersData.popperOffsets.y+=c),e.modifiersData[n]=r}},wi={name:"popperOffsets",enabled:!0,phase:"read",fn:function(t){var e=t.state,i=t.name;e.modifiersData[i]=fi({reference:e.rects.reference,element:e.rects.popper,placement:e.placement})},data:{}},Ei={name:"preventOverflow",enabled:!0,phase:"main",fn:function(t){var e=t.state,i=t.options,n=t.name,s=i.mainAxis,o=void 0===s||s,r=i.altAxis,a=void 0!==r&&r,l=i.boundary,c=i.rootBoundary,u=i.altBoundary,d=i.padding,h=i.tether,f=void 0===h||h,p=i.tetherOffset,m=void 0===p?0:p,g=pi(e,{boundary:l,rootBoundary:c,padding:d,altBoundary:u}),_=Se(e.placement),b=Xe(e.placement),v=!b,y=Ve(_),w="x"===y?"y":"x",E=e.modifiersData.popperOffsets,A=e.rects.reference,C=e.rects.popper,T="function"==typeof m?m(Object.assign({},e.rects,{placement:e.placement})):m,O="number"==typeof T?{mainAxis:T,altAxis:T}:Object.assign({mainAxis:0,altAxis:0},T),L=e.modifiersData.offset?e.modifiersData.offset[e.placement]:null,x={x:0,y:0};if(E){if(o){var k,S="y"===y?te:ne,N="y"===y?ee:ie,$="y"===y?"height":"width",D=E[y],P=D+g[S],I=D-g[N],M=f?-C[$]/2:0,j=b===re?A[$]:C[$],B=b===re?-C[$]:-A[$],F=e.elements.arrow,H=f&&F?je(F):{width:0,height:0},q=e.modifiersData["arrow#persistent"]?e.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},W=q[S],z=q[N],R=Ue(0,A[$],H[$]),V=v?A[$]/2-M-R-W-O.mainAxis:j-R-W-O.mainAxis,U=v?-A[$]/2+M+R+z+O.mainAxis:B+R+z+O.mainAxis,K=e.elements.arrow&&Re(e.elements.arrow),Q=K?"y"===y?K.clientTop||0:K.clientLeft||0:0,Y=null!=(k=null==L?void 0:L[y])?k:0,X=D+U-Y,G=Ue(f?$e(P,D+V-Y-Q):P,D,f?Ne(I,X):I);E[y]=G,x[y]=G-D}if(a){var J,Z="x"===y?te:ne,tt="x"===y?ee:ie,et=E[w],it="y"===w?"height":"width",nt=et+g[Z],st=et-g[tt],ot=-1!==[te,ne].indexOf(_),rt=null!=(J=null==L?void 0:L[w])?J:0,at=ot?nt:et-A[it]-C[it]-rt+O.altAxis,lt=ot?et+A[it]+C[it]-rt-O.altAxis:st,ct=f&&ot?function(t,e,i){var n=Ue(t,e,i);return n>i?i:n}(at,et,lt):Ue(f?at:nt,et,f?lt:st);E[w]=ct,x[w]=ct-et}e.modifiersData[n]=x}},requiresIfExists:["offset"]};function Ai(t,e,i){void 0===i&&(i=!1);var n,s,o=Le(e),r=Le(e)&&function(t){var e=t.getBoundingClientRect(),i=De(e.width)/t.offsetWidth||1,n=De(e.height)/t.offsetHeight||1;return 1!==i||1!==n}(e),a=qe(e),l=Me(t,r,i),c={scrollLeft:0,scrollTop:0},u={x:0,y:0};return(o||!o&&!i)&&(("body"!==Ce(e)||li(a))&&(c=(n=e)!==Te(n)&&Le(n)?{scrollLeft:(s=n).scrollLeft,scrollTop:s.scrollTop}:ri(n)),Le(e)?((u=Me(e,!0)).x+=e.clientLeft,u.y+=e.clientTop):a&&(u.x=ai(a))),{x:l.left+c.scrollLeft-u.x,y:l.top+c.scrollTop-u.y,width:l.width,height:l.height}}function Ci(t){var e=new Map,i=new Set,n=[];function s(t){i.add(t.name),[].concat(t.requires||[],t.requiresIfExists||[]).forEach((function(t){if(!i.has(t)){var n=e.get(t);n&&s(n)}})),n.push(t)}return t.forEach((function(t){e.set(t.name,t)})),t.forEach((function(t){i.has(t.name)||s(t)})),n}var Ti={placement:"bottom",modifiers:[],strategy:"absolute"};function Oi(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];return!e.some((function(t){return!(t&&"function"==typeof t.getBoundingClientRect)}))}function Li(t){void 0===t&&(t={});var e=t,i=e.defaultModifiers,n=void 0===i?[]:i,s=e.defaultOptions,o=void 0===s?Ti:s;return function(t,e,i){void 0===i&&(i=o);var s,r,a={placement:"bottom",orderedModifiers:[],options:Object.assign({},Ti,o),modifiersData:{},elements:{reference:t,popper:e},attributes:{},styles:{}},l=[],c=!1,u={state:a,setOptions:function(i){var s="function"==typeof i?i(a.options):i;d(),a.options=Object.assign({},o,a.options,s),a.scrollParents={reference:Oe(t)?ui(t):t.contextElement?ui(t.contextElement):[],popper:ui(e)};var r,c,h=function(t){var e=Ci(t);return Ae.reduce((function(t,i){return t.concat(e.filter((function(t){return t.phase===i})))}),[])}((r=[].concat(n,a.options.modifiers),c=r.reduce((function(t,e){var i=t[e.name];return t[e.name]=i?Object.assign({},i,e,{options:Object.assign({},i.options,e.options),data:Object.assign({},i.data,e.data)}):e,t}),{}),Object.keys(c).map((function(t){return c[t]}))));return a.orderedModifiers=h.filter((function(t){return t.enabled})),a.orderedModifiers.forEach((function(t){var e=t.name,i=t.options,n=void 0===i?{}:i,s=t.effect;if("function"==typeof s){var o=s({state:a,name:e,instance:u,options:n});l.push(o||function(){})}})),u.update()},forceUpdate:function(){if(!c){var t=a.elements,e=t.reference,i=t.popper;if(Oi(e,i)){a.rects={reference:Ai(e,Re(i),"fixed"===a.options.strategy),popper:je(i)},a.reset=!1,a.placement=a.options.placement,a.orderedModifiers.forEach((function(t){return a.modifiersData[t.name]=Object.assign({},t.data)}));for(var n=0;n<a.orderedModifiers.length;n++)if(!0!==a.reset){var s=a.orderedModifiers[n],o=s.fn,r=s.options,l=void 0===r?{}:r,d=s.name;"function"==typeof o&&(a=o({state:a,options:l,name:d,instance:u})||a)}else a.reset=!1,n=-1}}},update:(s=function(){return new Promise((function(t){u.forceUpdate(),t(a)}))},function(){return r||(r=new Promise((function(t){Promise.resolve().then((function(){r=void 0,t(s())}))}))),r}),destroy:function(){d(),c=!0}};if(!Oi(t,e))return u;function d(){l.forEach((function(t){return t()})),l=[]}return u.setOptions(i).then((function(t){!c&&i.onFirstUpdate&&i.onFirstUpdate(t)})),u}}var xi=Li(),ki=Li({defaultModifiers:[ei,wi,Ze,ke]}),Si=Li({defaultModifiers:[ei,wi,Ze,ke,yi,gi,Ei,Ye,vi]});const Ni=Object.freeze(Object.defineProperty({__proto__:null,afterMain:ve,afterRead:ge,afterWrite:Ee,applyStyles:ke,arrow:Ye,auto:se,basePlacements:oe,beforeMain:_e,beforeRead:pe,beforeWrite:ye,bottom:ee,clippingParents:le,computeStyles:Ze,createPopper:Si,createPopperBase:xi,createPopperLite:ki,detectOverflow:pi,end:ae,eventListeners:ei,flip:gi,hide:vi,left:ne,main:be,modifierPhases:Ae,offset:yi,placements:fe,popper:ue,popperGenerator:Li,popperOffsets:wi,preventOverflow:Ei,read:me,reference:de,right:ie,start:re,top:te,variationPlacements:he,viewport:ce,write:we},Symbol.toStringTag,{value:"Module"})),$i="dropdown",Di=".bs.dropdown",Pi=".data-api",Ii="ArrowUp",Mi="ArrowDown",ji=`hide${Di}`,Bi=`hidden${Di}`,Fi=`show${Di}`,Hi=`shown${Di}`,qi=`click${Di}${Pi}`,Wi=`keydown${Di}${Pi}`,zi=`keyup${Di}${Pi}`,Ri="show",Vi='[data-bs-toggle="dropdown"]:not(.disabled):not(:disabled)',Ui=`${Vi}.${Ri}`,Ki=".dropdown-menu",Qi=p()?"top-end":"top-start",Yi=p()?"top-start":"top-end",Xi=p()?"bottom-end":"bottom-start",Gi=p()?"bottom-start":"bottom-end",Ji=p()?"left-start":"right-start",Zi=p()?"right-start":"left-start",tn={autoClose:!0,boundary:"clippingParents",display:"dynamic",offset:[0,0],popperConfig:null,reference:"toggle"},en={autoClose:"(boolean|string)",boundary:"(string|element)",display:"string",offset:"(array|string|function)",popperConfig:"(null|object|function)",reference:"(string|element|object)"};class nn extends H{constructor(t,e){super(t,e),this._popper=null,this._parent=this._element.parentNode,this._menu=W.next(this._element,Ki)[0]||W.prev(this._element,Ki)[0]||W.findOne(Ki,this._parent),this._inNavbar=this._detectNavbar()}static get Default(){return tn}static get DefaultType(){return en}static get NAME(){return $i}toggle(){return this._isShown()?this.hide():this.show()}show(){if(l(this._element)||this._isShown())return;const t={relatedTarget:this._element};if(!P.trigger(this._element,Fi,t).defaultPrevented){if(this._createPopper(),"ontouchstart"in document.documentElement&&!this._parent.closest(".navbar-nav"))for(const t of[].concat(...document.body.children))P.on(t,"mouseover",u);this._element.focus(),this._element.setAttribute("aria-expanded",!0),this._menu.classList.add(Ri),this._element.classList.add(Ri),P.trigger(this._element,Hi,t)}}hide(){if(l(this._element)||!this._isShown())return;const t={relatedTarget:this._element};this._completeHide(t)}dispose(){this._popper&&this._popper.destroy(),super.dispose()}update(){this._inNavbar=this._detectNavbar(),this._popper&&this._popper.update()}_completeHide(t){if(!P.trigger(this._element,ji,t).defaultPrevented){if("ontouchstart"in document.documentElement)for(const t of[].concat(...document.body.children))P.off(t,"mouseover",u);this._popper&&this._popper.destroy(),this._menu.classList.remove(Ri),this._element.classList.remove(Ri),this._element.setAttribute("aria-expanded","false"),B.removeDataAttribute(this._menu,"popper"),P.trigger(this._element,Bi,t),this._element.focus()}}_getConfig(t){if("object"==typeof(t=super._getConfig(t)).reference&&!o(t.reference)&&"function"!=typeof t.reference.getBoundingClientRect)throw new TypeError(`${$i.toUpperCase()}: Option "reference" provided type "object" without a required "getBoundingClientRect" method.`);return t}_createPopper(){if(void 0===Ni)throw new TypeError("Bootstrap's dropdowns require Popper (https://popper.js.org/docs/v2/)");let t=this._element;"parent"===this._config.reference?t=this._parent:o(this._config.reference)?t=r(this._config.reference):"object"==typeof this._config.reference&&(t=this._config.reference);const e=this._getPopperConfig();this._popper=Si(t,this._menu,e)}_isShown(){return this._menu.classList.contains(Ri)}_getPlacement(){const t=this._parent;if(t.classList.contains("dropend"))return Ji;if(t.classList.contains("dropstart"))return Zi;if(t.classList.contains("dropup-center"))return"top";if(t.classList.contains("dropdown-center"))return"bottom";const e="end"===getComputedStyle(this._menu).getPropertyValue("--bs-position").trim();return t.classList.contains("dropup")?e?Yi:Qi:e?Gi:Xi}_detectNavbar(){return null!==this._element.closest(".navbar")}_getOffset(){const{offset:t}=this._config;return"string"==typeof t?t.split(",").map((t=>Number.parseInt(t,10))):"function"==typeof t?e=>t(e,this._element):t}_getPopperConfig(){const t={placement:this._getPlacement(),modifiers:[{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"offset",options:{offset:this._getOffset()}}]};return(this._inNavbar||"static"===this._config.display)&&(B.setDataAttribute(this._menu,"popper","static"),t.modifiers=[{name:"applyStyles",enabled:!1}]),{...t,...g(this._config.popperConfig,[void 0,t])}}_selectMenuItem({key:t,target:e}){const i=W.find(".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",this._menu).filter((t=>a(t)));i.length&&b(i,e,t===Mi,!i.includes(e)).focus()}static jQueryInterface(t){return this.each((function(){const e=nn.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t]()}}))}static clearMenus(t){if(2===t.button||"keyup"===t.type&&"Tab"!==t.key)return;const e=W.find(Ui);for(const i of e){const e=nn.getInstance(i);if(!e||!1===e._config.autoClose)continue;const n=t.composedPath(),s=n.includes(e._menu);if(n.includes(e._element)||"inside"===e._config.autoClose&&!s||"outside"===e._config.autoClose&&s)continue;if(e._menu.contains(t.target)&&("keyup"===t.type&&"Tab"===t.key||/input|select|option|textarea|form/i.test(t.target.tagName)))continue;const o={relatedTarget:e._element};"click"===t.type&&(o.clickEvent=t),e._completeHide(o)}}static dataApiKeydownHandler(t){const e=/input|textarea/i.test(t.target.tagName),i="Escape"===t.key,n=[Ii,Mi].includes(t.key);if(!n&&!i)return;if(e&&!i)return;t.preventDefault();const s=this.matches(Vi)?this:W.prev(this,Vi)[0]||W.next(this,Vi)[0]||W.findOne(Vi,t.delegateTarget.parentNode),o=nn.getOrCreateInstance(s);if(n)return t.stopPropagation(),o.show(),void o._selectMenuItem(t);o._isShown()&&(t.stopPropagation(),o.hide(),s.focus())}}P.on(document,Wi,Vi,nn.dataApiKeydownHandler),P.on(document,Wi,Ki,nn.dataApiKeydownHandler),P.on(document,qi,nn.clearMenus),P.on(document,zi,nn.clearMenus),P.on(document,qi,Vi,(function(t){t.preventDefault(),nn.getOrCreateInstance(this).toggle()})),m(nn);const sn="backdrop",on="show",rn=`mousedown.bs.${sn}`,an={className:"modal-backdrop",clickCallback:null,isAnimated:!1,isVisible:!0,rootElement:"body"},ln={className:"string",clickCallback:"(function|null)",isAnimated:"boolean",isVisible:"boolean",rootElement:"(element|string)"};class cn extends F{constructor(t){super(),this._config=this._getConfig(t),this._isAppended=!1,this._element=null}static get Default(){return an}static get DefaultType(){return ln}static get NAME(){return sn}show(t){if(!this._config.isVisible)return void g(t);this._append();const e=this._getElement();this._config.isAnimated&&d(e),e.classList.add(on),this._emulateAnimation((()=>{g(t)}))}hide(t){this._config.isVisible?(this._getElement().classList.remove(on),this._emulateAnimation((()=>{this.dispose(),g(t)}))):g(t)}dispose(){this._isAppended&&(P.off(this._element,rn),this._element.remove(),this._isAppended=!1)}_getElement(){if(!this._element){const t=document.createElement("div");t.className=this._config.className,this._config.isAnimated&&t.classList.add("fade"),this._element=t}return this._element}_configAfterMerge(t){return t.rootElement=r(t.rootElement),t}_append(){if(this._isAppended)return;const t=this._getElement();this._config.rootElement.append(t),P.on(t,rn,(()=>{g(this._config.clickCallback)})),this._isAppended=!0}_emulateAnimation(t){_(t,this._getElement(),this._config.isAnimated)}}const un=".bs.focustrap",dn=`focusin${un}`,hn=`keydown.tab${un}`,fn="backward",pn={autofocus:!0,trapElement:null},mn={autofocus:"boolean",trapElement:"element"};class gn extends F{constructor(t){super(),this._config=this._getConfig(t),this._isActive=!1,this._lastTabNavDirection=null}static get Default(){return pn}static get DefaultType(){return mn}static get NAME(){return"focustrap"}activate(){this._isActive||(this._config.autofocus&&this._config.trapElement.focus(),P.off(document,un),P.on(document,dn,(t=>this._handleFocusin(t))),P.on(document,hn,(t=>this._handleKeydown(t))),this._isActive=!0)}deactivate(){this._isActive&&(this._isActive=!1,P.off(document,un))}_handleFocusin(t){const{trapElement:e}=this._config;if(t.target===document||t.target===e||e.contains(t.target))return;const i=W.focusableChildren(e);0===i.length?e.focus():this._lastTabNavDirection===fn?i[i.length-1].focus():i[0].focus()}_handleKeydown(t){"Tab"===t.key&&(this._lastTabNavDirection=t.shiftKey?fn:"forward")}}const _n=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",bn=".sticky-top",vn="padding-right",yn="margin-right";class wn{constructor(){this._element=document.body}getWidth(){const t=document.documentElement.clientWidth;return Math.abs(window.innerWidth-t)}hide(){const t=this.getWidth();this._disableOverFlow(),this._setElementAttributes(this._element,vn,(e=>e+t)),this._setElementAttributes(_n,vn,(e=>e+t)),this._setElementAttributes(bn,yn,(e=>e-t))}reset(){this._resetElementAttributes(this._element,"overflow"),this._resetElementAttributes(this._element,vn),this._resetElementAttributes(_n,vn),this._resetElementAttributes(bn,yn)}isOverflowing(){return this.getWidth()>0}_disableOverFlow(){this._saveInitialAttribute(this._element,"overflow"),this._element.style.overflow="hidden"}_setElementAttributes(t,e,i){const n=this.getWidth();this._applyManipulationCallback(t,(t=>{if(t!==this._element&&window.innerWidth>t.clientWidth+n)return;this._saveInitialAttribute(t,e);const s=window.getComputedStyle(t).getPropertyValue(e);t.style.setProperty(e,`${i(Number.parseFloat(s))}px`)}))}_saveInitialAttribute(t,e){const i=t.style.getPropertyValue(e);i&&B.setDataAttribute(t,e,i)}_resetElementAttributes(t,e){this._applyManipulationCallback(t,(t=>{const i=B.getDataAttribute(t,e);null!==i?(B.removeDataAttribute(t,e),t.style.setProperty(e,i)):t.style.removeProperty(e)}))}_applyManipulationCallback(t,e){if(o(t))e(t);else for(const i of W.find(t,this._element))e(i)}}const En=".bs.modal",An=`hide${En}`,Cn=`hidePrevented${En}`,Tn=`hidden${En}`,On=`show${En}`,Ln=`shown${En}`,xn=`resize${En}`,kn=`click.dismiss${En}`,Sn=`mousedown.dismiss${En}`,Nn=`keydown.dismiss${En}`,$n=`click${En}.data-api`,Dn="modal-open",Pn="show",In="modal-static",Mn={backdrop:!0,focus:!0,keyboard:!0},jn={backdrop:"(boolean|string)",focus:"boolean",keyboard:"boolean"};class Bn extends H{constructor(t,e){super(t,e),this._dialog=W.findOne(".modal-dialog",this._element),this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._isShown=!1,this._isTransitioning=!1,this._scrollBar=new wn,this._addEventListeners()}static get Default(){return Mn}static get DefaultType(){return jn}static get NAME(){return"modal"}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){this._isShown||this._isTransitioning||P.trigger(this._element,On,{relatedTarget:t}).defaultPrevented||(this._isShown=!0,this._isTransitioning=!0,this._scrollBar.hide(),document.body.classList.add(Dn),this._adjustDialog(),this._backdrop.show((()=>this._showElement(t))))}hide(){this._isShown&&!this._isTransitioning&&(P.trigger(this._element,An).defaultPrevented||(this._isShown=!1,this._isTransitioning=!0,this._focustrap.deactivate(),this._element.classList.remove(Pn),this._queueCallback((()=>this._hideModal()),this._element,this._isAnimated())))}dispose(){P.off(window,En),P.off(this._dialog,En),this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}handleUpdate(){this._adjustDialog()}_initializeBackDrop(){return new cn({isVisible:Boolean(this._config.backdrop),isAnimated:this._isAnimated()})}_initializeFocusTrap(){return new gn({trapElement:this._element})}_showElement(t){document.body.contains(this._element)||document.body.append(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.scrollTop=0;const e=W.findOne(".modal-body",this._dialog);e&&(e.scrollTop=0),d(this._element),this._element.classList.add(Pn),this._queueCallback((()=>{this._config.focus&&this._focustrap.activate(),this._isTransitioning=!1,P.trigger(this._element,Ln,{relatedTarget:t})}),this._dialog,this._isAnimated())}_addEventListeners(){P.on(this._element,Nn,(t=>{"Escape"===t.key&&(this._config.keyboard?this.hide():this._triggerBackdropTransition())})),P.on(window,xn,(()=>{this._isShown&&!this._isTransitioning&&this._adjustDialog()})),P.on(this._element,Sn,(t=>{P.one(this._element,kn,(e=>{this._element===t.target&&this._element===e.target&&("static"!==this._config.backdrop?this._config.backdrop&&this.hide():this._triggerBackdropTransition())}))}))}_hideModal(){this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._backdrop.hide((()=>{document.body.classList.remove(Dn),this._resetAdjustments(),this._scrollBar.reset(),P.trigger(this._element,Tn)}))}_isAnimated(){return this._element.classList.contains("fade")}_triggerBackdropTransition(){if(P.trigger(this._element,Cn).defaultPrevented)return;const t=this._element.scrollHeight>document.documentElement.clientHeight,e=this._element.style.overflowY;"hidden"===e||this._element.classList.contains(In)||(t||(this._element.style.overflowY="hidden"),this._element.classList.add(In),this._queueCallback((()=>{this._element.classList.remove(In),this._queueCallback((()=>{this._element.style.overflowY=e}),this._dialog)}),this._dialog),this._element.focus())}_adjustDialog(){const t=this._element.scrollHeight>document.documentElement.clientHeight,e=this._scrollBar.getWidth(),i=e>0;if(i&&!t){const t=p()?"paddingLeft":"paddingRight";this._element.style[t]=`${e}px`}if(!i&&t){const t=p()?"paddingRight":"paddingLeft";this._element.style[t]=`${e}px`}}_resetAdjustments(){this._element.style.paddingLeft="",this._element.style.paddingRight=""}static jQueryInterface(t,e){return this.each((function(){const i=Bn.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===i[t])throw new TypeError(`No method named "${t}"`);i[t](e)}}))}}P.on(document,$n,'[data-bs-toggle="modal"]',(function(t){const e=W.getElementFromSelector(this);["A","AREA"].includes(this.tagName)&&t.preventDefault(),P.one(e,On,(t=>{t.defaultPrevented||P.one(e,Tn,(()=>{a(this)&&this.focus()}))}));const i=W.findOne(".modal.show");i&&Bn.getInstance(i).hide(),Bn.getOrCreateInstance(e).toggle(this)})),z(Bn),m(Bn);const Fn=".bs.offcanvas",Hn=".data-api",qn=`load${Fn}${Hn}`,Wn="show",zn="showing",Rn="hiding",Vn=".offcanvas.show",Un=`show${Fn}`,Kn=`shown${Fn}`,Qn=`hide${Fn}`,Yn=`hidePrevented${Fn}`,Xn=`hidden${Fn}`,Gn=`resize${Fn}`,Jn=`click${Fn}${Hn}`,Zn=`keydown.dismiss${Fn}`,ts={backdrop:!0,keyboard:!0,scroll:!1},es={backdrop:"(boolean|string)",keyboard:"boolean",scroll:"boolean"};class is extends H{constructor(t,e){super(t,e),this._isShown=!1,this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._addEventListeners()}static get Default(){return ts}static get DefaultType(){return es}static get NAME(){return"offcanvas"}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){this._isShown||P.trigger(this._element,Un,{relatedTarget:t}).defaultPrevented||(this._isShown=!0,this._backdrop.show(),this._config.scroll||(new wn).hide(),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.classList.add(zn),this._queueCallback((()=>{this._config.scroll&&!this._config.backdrop||this._focustrap.activate(),this._element.classList.add(Wn),this._element.classList.remove(zn),P.trigger(this._element,Kn,{relatedTarget:t})}),this._element,!0))}hide(){this._isShown&&(P.trigger(this._element,Qn).defaultPrevented||(this._focustrap.deactivate(),this._element.blur(),this._isShown=!1,this._element.classList.add(Rn),this._backdrop.hide(),this._queueCallback((()=>{this._element.classList.remove(Wn,Rn),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._config.scroll||(new wn).reset(),P.trigger(this._element,Xn)}),this._element,!0)))}dispose(){this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}_initializeBackDrop(){const t=Boolean(this._config.backdrop);return new cn({className:"offcanvas-backdrop",isVisible:t,isAnimated:!0,rootElement:this._element.parentNode,clickCallback:t?()=>{"static"!==this._config.backdrop?this.hide():P.trigger(this._element,Yn)}:null})}_initializeFocusTrap(){return new gn({trapElement:this._element})}_addEventListeners(){P.on(this._element,Zn,(t=>{"Escape"===t.key&&(this._config.keyboard?this.hide():P.trigger(this._element,Yn))}))}static jQueryInterface(t){return this.each((function(){const e=is.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t](this)}}))}}P.on(document,Jn,'[data-bs-toggle="offcanvas"]',(function(t){const e=W.getElementFromSelector(this);if(["A","AREA"].includes(this.tagName)&&t.preventDefault(),l(this))return;P.one(e,Xn,(()=>{a(this)&&this.focus()}));const i=W.findOne(Vn);i&&i!==e&&is.getInstance(i).hide(),is.getOrCreateInstance(e).toggle(this)})),P.on(window,qn,(()=>{for(const t of W.find(Vn))is.getOrCreateInstance(t).show()})),P.on(window,Gn,(()=>{for(const t of W.find("[aria-modal][class*=show][class*=offcanvas-]"))"fixed"!==getComputedStyle(t).position&&is.getOrCreateInstance(t).hide()})),z(is),m(is);const ns=".bs.orangenavbar",ss=".data-api",os=`scroll${ns}${ss}`,rs=`load${ns}${ss}`,as="header.sticky-top";class ls extends H{static get NAME(){return"orangenavbar"}static enableMinimizing(t){window.scrollY>0?t.classList.add("header-minimized"):t.classList.remove("header-minimized")}static jQueryInterface(t){return this.each((function(){const e=ls.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t]()}}))}}P.on(window,os,(()=>{for(const t of W.find(as))ls.enableMinimizing(t)})),P.on(window,rs,(()=>{for(const t of W.find(as))ls.enableMinimizing(t)})),m(ls);const cs={"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],dd:[],div:[],dl:[],dt:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},us=new Set(["background","cite","href","itemtype","longdesc","poster","src","xlink:href"]),ds=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i,hs=(t,e)=>{const i=t.nodeName.toLowerCase();return e.includes(i)?!us.has(i)||Boolean(ds.test(t.nodeValue)):e.filter((t=>t instanceof RegExp)).some((t=>t.test(i)))},fs={allowList:cs,content:{},extraClass:"",html:!1,sanitize:!0,sanitizeFn:null,template:"<div></div>"},ps={allowList:"object",content:"object",extraClass:"(string|function)",html:"boolean",sanitize:"boolean",sanitizeFn:"(null|function)",template:"string"},ms={selector:"(string|element)",entry:"(string|element|function|null)"};class gs extends F{constructor(t){super(),this._config=this._getConfig(t)}static get Default(){return fs}static get DefaultType(){return ps}static get NAME(){return"TemplateFactory"}getContent(){return Object.values(this._config.content).map((t=>this._resolvePossibleFunction(t))).filter(Boolean)}hasContent(){return this.getContent().length>0}changeContent(t){return this._checkContent(t),this._config.content={...this._config.content,...t},this}toHtml(){const t=document.createElement("div");t.innerHTML=this._maybeSanitize(this._config.template);for(const[e,i]of Object.entries(this._config.content))this._setContent(t,i,e);const e=t.children[0],i=this._resolvePossibleFunction(this._config.extraClass);return i&&e.classList.add(...i.split(" ")),e}_typeCheckConfig(t){super._typeCheckConfig(t),this._checkContent(t.content)}_checkContent(t){for(const[e,i]of Object.entries(t))super._typeCheckConfig({selector:e,entry:i},ms)}_setContent(t,e,i){const n=W.findOne(i,t);n&&((e=this._resolvePossibleFunction(e))?o(e)?this._putElementInTemplate(r(e),n):this._config.html?n.innerHTML=this._maybeSanitize(e):n.textContent=e:n.remove())}_maybeSanitize(t){return this._config.sanitize?function(t,e,i){if(!t.length)return t;if(i&&"function"==typeof i)return i(t);const n=(new window.DOMParser).parseFromString(t,"text/html"),s=[].concat(...n.body.querySelectorAll("*"));for(const t of s){const i=t.nodeName.toLowerCase();if(!Object.keys(e).includes(i)){t.remove();continue}const n=[].concat(...t.attributes),s=[].concat(e["*"]||[],e[i]||[]);for(const e of n)hs(e,s)||t.removeAttribute(e.nodeName)}return n.body.innerHTML}(t,this._config.allowList,this._config.sanitizeFn):t}_resolvePossibleFunction(t){return g(t,[void 0,this])}_putElementInTemplate(t,e){if(this._config.html)return e.innerHTML="",void e.append(t);e.textContent=t.textContent}}const _s=new Set(["sanitize","allowList","sanitizeFn"]),bs="fade",vs="show",ys=".tooltip-inner",ws=".modal",Es="hide.bs.modal",As="hover",Cs="focus",Ts="click",Os={AUTO:"auto",TOP:"top",RIGHT:p()?"left":"right",BOTTOM:"bottom",LEFT:p()?"right":"left"},Ls={allowList:cs,animation:!0,boundary:"clippingParents",container:!1,customClass:"",delay:0,fallbackPlacements:["top","right","bottom","left"],html:!1,offset:[0,10],placement:"top",popperConfig:null,sanitize:!0,sanitizeFn:null,selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',title:"",trigger:"hover focus"},xs={allowList:"object",animation:"boolean",boundary:"(string|element)",container:"(string|element|boolean)",customClass:"(string|function)",delay:"(number|object)",fallbackPlacements:"array",html:"boolean",offset:"(array|string|function)",placement:"(string|function)",popperConfig:"(null|object|function)",sanitize:"boolean",sanitizeFn:"(null|function)",selector:"(string|boolean)",template:"string",title:"(string|element|function)",trigger:"string"};class ks extends H{constructor(t,e){if(void 0===Ni)throw new TypeError("Bootstrap's tooltips require Popper (https://popper.js.org/docs/v2/)");super(t,e),this._isEnabled=!0,this._timeout=0,this._isHovered=null,this._activeTrigger={},this._popper=null,this._templateFactory=null,this._newContent=null,this.tip=null,this._setListeners(),this._config.selector||this._fixTitle()}static get Default(){return Ls}static get DefaultType(){return xs}static get NAME(){return"tooltip"}enable(){this._isEnabled=!0}disable(){this._isEnabled=!1}toggleEnabled(){this._isEnabled=!this._isEnabled}toggle(){this._isEnabled&&(this._isShown()?this._leave():this._enter())}dispose(){clearTimeout(this._timeout),P.off(this._element.closest(ws),Es,this._hideModalHandler),this._element.getAttribute("data-bs-original-title")&&this._element.setAttribute("title",this._element.getAttribute("data-bs-original-title")),this._disposePopper(),super.dispose()}show(){if("none"===this._element.style.display)throw new Error("Please use show on visible elements");if(!this._isWithContent()||!this._isEnabled)return;const t=P.trigger(this._element,this.constructor.eventName("show")),e=(c(this._element)||this._element.ownerDocument.documentElement).contains(this._element);if(t.defaultPrevented||!e)return;this._disposePopper();const i=this._getTipElement();this._element.setAttribute("aria-describedby",i.getAttribute("id"));const{container:n}=this._config;if(this._element.ownerDocument.documentElement.contains(this.tip)||(n.append(i),P.trigger(this._element,this.constructor.eventName("inserted"))),this._popper=this._createPopper(i),i.classList.add(vs),"ontouchstart"in document.documentElement)for(const t of[].concat(...document.body.children))P.on(t,"mouseover",u);this._queueCallback((()=>{P.trigger(this._element,this.constructor.eventName("shown")),!1===this._isHovered&&this._leave(),this._isHovered=!1}),this.tip,this._isAnimated())}hide(){if(this._isShown()&&!P.trigger(this._element,this.constructor.eventName("hide")).defaultPrevented){if(this._getTipElement().classList.remove(vs),"ontouchstart"in document.documentElement)for(const t of[].concat(...document.body.children))P.off(t,"mouseover",u);this._activeTrigger[Ts]=!1,this._activeTrigger[Cs]=!1,this._activeTrigger[As]=!1,this._isHovered=null,this._queueCallback((()=>{this._isWithActiveTrigger()||(this._isHovered||this._disposePopper(),this._element.removeAttribute("aria-describedby"),P.trigger(this._element,this.constructor.eventName("hidden")))}),this.tip,this._isAnimated())}}update(){this._popper&&this._popper.update()}_isWithContent(){return Boolean(this._getTitle())}_getTipElement(){return this.tip||(this.tip=this._createTipElement(this._newContent||this._getContentForTemplate())),this.tip}_createTipElement(t){const e=this._getTemplateFactory(t).toHtml();if(!e)return null;e.classList.remove(bs,vs),e.classList.add(`bs-${this.constructor.NAME}-auto`);const i=(t=>{do{t+=Math.floor(1e6*Math.random())}while(document.getElementById(t));return t})(this.constructor.NAME).toString();return e.setAttribute("id",i),this._isAnimated()&&e.classList.add(bs),e}setContent(t){this._newContent=t,this._isShown()&&(this._disposePopper(),this.show())}_getTemplateFactory(t){return this._templateFactory?this._templateFactory.changeContent(t):this._templateFactory=new gs({...this._config,content:t,extraClass:this._resolvePossibleFunction(this._config.customClass)}),this._templateFactory}_getContentForTemplate(){return{[ys]:this._getTitle()}}_getTitle(){return this._resolvePossibleFunction(this._config.title)||this._element.getAttribute("data-bs-original-title")}_initializeOnDelegatedTarget(t){return this.constructor.getOrCreateInstance(t.delegateTarget,this._getDelegateConfig())}_isAnimated(){return this._config.animation||this.tip&&this.tip.classList.contains(bs)}_isShown(){return this.tip&&this.tip.classList.contains(vs)}_createPopper(t){const e=g(this._config.placement,[this,t,this._element]),i=Os[e.toUpperCase()];return Si(this._element,t,this._getPopperConfig(i))}_getOffset(){const{offset:t}=this._config;return"string"==typeof t?t.split(",").map((t=>Number.parseInt(t,10))):"function"==typeof t?e=>t(e,this._element):t}_resolvePossibleFunction(t){return g(t,[this._element,this._element])}_getPopperConfig(t){const e={placement:t,modifiers:[{name:"flip",options:{fallbackPlacements:this._config.fallbackPlacements}},{name:"offset",options:{offset:this._getOffset()}},{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"arrow",options:{element:`.${this.constructor.NAME}-arrow`}},{name:"preSetPlacement",enabled:!0,phase:"beforeMain",fn:t=>{this._getTipElement().setAttribute("data-popper-placement",t.state.placement)}}]};return{...e,...g(this._config.popperConfig,[void 0,e])}}_setListeners(){const t=this._config.trigger.split(" ");for(const e of t)if("click"===e)P.on(this._element,this.constructor.eventName("click"),this._config.selector,(t=>{const e=this._initializeOnDelegatedTarget(t);e._activeTrigger[Ts]=!(e._isShown()&&e._activeTrigger[Ts]),e.toggle()}));else if("manual"!==e){const t=e===As?this.constructor.eventName("mouseenter"):this.constructor.eventName("focusin"),i=e===As?this.constructor.eventName("mouseleave"):this.constructor.eventName("focusout");P.on(this._element,t,this._config.selector,(t=>{const e=this._initializeOnDelegatedTarget(t);e._activeTrigger["focusin"===t.type?Cs:As]=!0,e._enter()})),P.on(this._element,i,this._config.selector,(t=>{const e=this._initializeOnDelegatedTarget(t);e._activeTrigger["focusout"===t.type?Cs:As]=e._element.contains(t.relatedTarget),e._leave()}))}this._hideModalHandler=()=>{this._element&&this.hide()},P.on(this._element.closest(ws),Es,this._hideModalHandler)}_fixTitle(){const t=this._element.getAttribute("title");t&&(this._element.getAttribute("aria-label")||this._element.textContent.trim()||this._element.setAttribute("aria-label",t),this._element.setAttribute("data-bs-original-title",t),this._element.removeAttribute("title"))}_enter(){this._isShown()||this._isHovered?this._isHovered=!0:(this._isHovered=!0,this._setTimeout((()=>{this._isHovered&&this.show()}),this._config.delay.show))}_leave(){this._isWithActiveTrigger()||(this._isHovered=!1,this._setTimeout((()=>{this._isHovered||this.hide()}),this._config.delay.hide))}_setTimeout(t,e){clearTimeout(this._timeout),this._timeout=setTimeout(t,e)}_isWithActiveTrigger(){return Object.values(this._activeTrigger).includes(!0)}_getConfig(t){const e=B.getDataAttributes(this._element);for(const t of Object.keys(e))_s.has(t)&&delete e[t];return t={...e,..."object"==typeof t&&t?t:{}},t=this._mergeConfigObj(t),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}_configAfterMerge(t){return t.container=!1===t.container?document.body:r(t.container),"number"==typeof t.delay&&(t.delay={show:t.delay,hide:t.delay}),"number"==typeof t.title&&(t.title=t.title.toString()),"number"==typeof t.content&&(t.content=t.content.toString()),t}_getDelegateConfig(){const t={};for(const[e,i]of Object.entries(this._config))this.constructor.Default[e]!==i&&(t[e]=i);return t.selector=!1,t.trigger="manual",t}_disposePopper(){this._popper&&(this._popper.destroy(),this._popper=null),this.tip&&(this.tip.remove(),this.tip=null)}static jQueryInterface(t){return this.each((function(){const e=ks.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t]()}}))}}m(ks);const Ss=".popover-header",Ns=".popover-body",$s={...ks.Default,content:"",offset:[0,15],placement:"right",template:'<div class="popover" role="tooltip"><div class="popover-arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>',trigger:"click"},Ds={...ks.DefaultType,content:"(null|string|element|function)"};class Ps extends ks{static get Default(){return $s}static get DefaultType(){return Ds}static get NAME(){return"popover"}_isWithContent(){return this._getTitle()||this._getContent()}_getContentForTemplate(){return{[Ss]:this._getTitle(),[Ns]:this._getContent()}}_getContent(){return this._resolvePossibleFunction(this._config.content)}static jQueryInterface(t){return this.each((function(){const e=Ps.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t]()}}))}}m(Ps);const Is=".bs.quantityselector",Ms=".data-api",js=`load${Is}${Ms}`,Bs=`change${Is}${Ms}`,Fs=`click${Is}${Ms}`,Hs='[data-bs-step="up"]',qs='[data-bs-step="down"]',Ws='[data-bs-step="counter"]',zs=".quantity-selector";class Rs extends H{static get NAME(){return"quantityselector"}ValueOnLoad(t){const e=t.querySelector(Ws),i=t.querySelector(Hs),n=t.querySelector(qs),s=e.getAttribute("min"),o=e.getAttribute("max"),r=Number(e.getAttribute("step"));Number(e.value)-r<s&&n.setAttribute("disabled",""),Number(e.value)+r>o&&i.setAttribute("disabled","")}static StepUp(t){const e=t.target.closest(zs).querySelector(Ws),i=e.getAttribute("max"),n=Number(e.getAttribute("step")),s=Number(e.getAttribute("data-bs-round")),o=new Event("change");Number(e.value)<i&&(e.value=(Number(e.value)+n).toFixed(s).toString()),e.dispatchEvent(o)}static StepDown(t){const e=t.target.closest(zs).querySelector(Ws),i=e.getAttribute("min"),n=Number(e.getAttribute("step")),s=Number(e.getAttribute("data-bs-round")),o=new Event("change");Number(e.value)>i&&(e.value=(Number(e.value)-n).toFixed(s).toString()),e.dispatchEvent(o)}static CheckIfDisabledOnChange(t){const e=t.target.closest(zs),i=e.querySelector(Ws),n=e.querySelector(Hs),s=e.querySelector(qs),o=i.getAttribute("min"),r=i.getAttribute("max"),a=Number(i.getAttribute("step"));n.removeAttribute("disabled",""),s.removeAttribute("disabled",""),Number(i.value)-a<o&&s.setAttribute("disabled",""),Number(i.value)+a>r&&n.setAttribute("disabled","")}static jQueryInterface(t){return this.each((function(){const e=Rs.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t]()}}))}}P.on(document,Bs,Ws,Rs.CheckIfDisabledOnChange),P.on(document,Fs,Hs,Rs.StepUp),P.on(document,Fs,qs,Rs.StepDown),P.on(window,js,(()=>{for(const t of W.find(zs))Rs.getOrCreateInstance(t).ValueOnLoad(t)})),m(Rs);const Vs=".bs.scrollspy",Us=`activate${Vs}`,Ks=`click${Vs}`,Qs=`load${Vs}.data-api`,Ys="active",Xs="[href]",Gs=".nav-link",Js=`${Gs}, .nav-item > ${Gs}, .list-group-item`,Zs={offset:null,rootMargin:"0px 0px -25%",smoothScroll:!1,target:null,threshold:[.1,.5,1]},to={offset:"(number|null)",rootMargin:"string",smoothScroll:"boolean",target:"element",threshold:"array"};class eo extends H{constructor(t,e){super(t,e),this._targetLinks=new Map,this._observableSections=new Map,this._rootElement="visible"===getComputedStyle(this._element).overflowY?null:this._element,this._activeTarget=null,this._observer=null,this._previousScrollData={visibleEntryTop:0,parentScrollTop:0},this.refresh()}static get Default(){return Zs}static get DefaultType(){return to}static get NAME(){return"scrollspy"}refresh(){this._initializeTargetsAndObservables(),this._maybeEnableSmoothScroll(),this._observer?this._observer.disconnect():this._observer=this._getNewObserver();for(const t of this._observableSections.values())this._observer.observe(t)}dispose(){this._observer.disconnect(),super.dispose()}_configAfterMerge(t){return t.target=r(t.target)||document.body,t.rootMargin=t.offset?`${t.offset}px 0px -30%`:t.rootMargin,"string"==typeof t.threshold&&(t.threshold=t.threshold.split(",").map((t=>Number.parseFloat(t)))),t}_maybeEnableSmoothScroll(){this._config.smoothScroll&&(P.off(this._config.target,Ks),P.on(this._config.target,Ks,Xs,(t=>{const e=this._observableSections.get(t.target.hash);if(e){t.preventDefault();const i=this._rootElement||window,n=e.offsetTop-this._element.offsetTop;if(i.scrollTo)return void i.scrollTo({top:n,behavior:"smooth"});i.scrollTop=n}})))}_getNewObserver(){const t={root:this._rootElement,threshold:this._config.threshold,rootMargin:this._config.rootMargin};return new IntersectionObserver((t=>this._observerCallback(t)),t)}_observerCallback(t){const e=t=>this._targetLinks.get(`#${t.target.id}`),i=t=>{this._previousScrollData.visibleEntryTop=t.target.offsetTop,this._process(e(t))},n=(this._rootElement||document.documentElement).scrollTop,s=n>=this._previousScrollData.parentScrollTop;this._previousScrollData.parentScrollTop=n;for(const o of t){if(!o.isIntersecting){this._activeTarget=null,this._clearActiveClass(e(o));continue}const t=o.target.offsetTop>=this._previousScrollData.visibleEntryTop;if(s&&t){if(i(o),!n)return}else s||t||i(o)}}_initializeTargetsAndObservables(){this._targetLinks=new Map,this._observableSections=new Map;const t=W.find(Xs,this._config.target);for(const e of t){if(!e.hash||l(e))continue;const t=W.findOne(decodeURI(e.hash),this._element);a(t)&&(this._targetLinks.set(decodeURI(e.hash),e),this._observableSections.set(e.hash,t))}}_process(t){this._activeTarget!==t&&(this._clearActiveClass(this._config.target),this._activeTarget=t,t.classList.add(Ys),this._activateParents(t),P.trigger(this._element,Us,{relatedTarget:t}))}_activateParents(t){if(t.classList.contains("dropdown-item"))W.findOne(".dropdown-toggle",t.closest(".dropdown")).classList.add(Ys);else for(const e of W.parents(t,".nav, .list-group"))for(const t of W.prev(e,Js))t.classList.add(Ys)}_clearActiveClass(t){t.classList.remove(Ys);const e=W.find(`${Xs}.${Ys}`,t);for(const t of e)t.classList.remove(Ys)}static jQueryInterface(t){return this.each((function(){const e=eo.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t]()}}))}}P.on(window,Qs,(()=>{for(const t of W.find('[data-bs-spy="scroll"]'))eo.getOrCreateInstance(t)})),m(eo);const io=".bs.tab",no=`hide${io}`,so=`hidden${io}`,oo=`show${io}`,ro=`shown${io}`,ao=`click${io}`,lo=`keydown${io}`,co=`load${io}`,uo="ArrowLeft",ho="ArrowRight",fo="ArrowUp",po="ArrowDown",mo="Home",go="End",_o="active",bo="fade",vo="show",yo=".dropdown-toggle",wo=`:not(${yo})`,Eo='[data-bs-toggle="tab"], [data-bs-toggle="pill"], [data-bs-toggle="list"]',Ao=`.nav-link${wo}, .list-group-item${wo}, [role="tab"]${wo}, ${Eo}`,Co=`.${_o}[data-bs-toggle="tab"], .${_o}[data-bs-toggle="pill"], .${_o}[data-bs-toggle="list"]`;class To extends H{constructor(t){super(t),this._parent=this._element.closest('.list-group, .nav, [role="tablist"]'),this._parent&&(this._setInitialAttributes(this._parent,this._getChildren()),P.on(this._element,lo,(t=>this._keydown(t))))}static get NAME(){return"tab"}show(){const t=this._element;if(this._elemIsActive(t))return;const e=this._getActiveElem(),i=e?P.trigger(e,no,{relatedTarget:t}):null;P.trigger(t,oo,{relatedTarget:e}).defaultPrevented||i&&i.defaultPrevented||(this._deactivate(e,t),this._activate(t,e))}_activate(t,e){t&&(t.classList.add(_o),this._activate(W.getElementFromSelector(t)),this._queueCallback((()=>{"tab"===t.getAttribute("role")?(t.removeAttribute("tabindex"),t.setAttribute("aria-selected",!0),this._toggleDropDown(t,!0),P.trigger(t,ro,{relatedTarget:e})):t.classList.add(vo)}),t,t.classList.contains(bo)))}_deactivate(t,e){t&&(t.classList.remove(_o),t.blur(),this._deactivate(W.getElementFromSelector(t)),this._queueCallback((()=>{"tab"===t.getAttribute("role")?(t.setAttribute("aria-selected",!1),t.setAttribute("tabindex","-1"),this._toggleDropDown(t,!1),P.trigger(t,so,{relatedTarget:e})):t.classList.remove(vo)}),t,t.classList.contains(bo)))}_keydown(t){if(![uo,ho,fo,po,mo,go].includes(t.key))return;t.stopPropagation(),t.preventDefault();const e=this._getChildren().filter((t=>!l(t)));let i;if([mo,go].includes(t.key))i=e[t.key===mo?0:e.length-1];else{const n=[ho,po].includes(t.key);i=b(e,t.target,n,!0)}i&&(i.focus({preventScroll:!0}),To.getOrCreateInstance(i).show())}_getChildren(){return W.find(Ao,this._parent)}_getActiveElem(){return this._getChildren().find((t=>this._elemIsActive(t)))||null}_setInitialAttributes(t,e){this._setAttributeIfNotExists(t,"role","tablist");for(const t of e)this._setInitialAttributesOnChild(t)}_setInitialAttributesOnChild(t){t=this._getInnerElement(t);const e=this._elemIsActive(t),i=this._getOuterElement(t);t.setAttribute("aria-selected",e),i!==t&&this._setAttributeIfNotExists(i,"role","presentation"),e||t.setAttribute("tabindex","-1"),this._setAttributeIfNotExists(t,"role","tab"),this._setInitialAttributesOnTargetPanel(t)}_setInitialAttributesOnTargetPanel(t){const e=W.getElementFromSelector(t);e&&(this._setAttributeIfNotExists(e,"role","tabpanel"),t.id&&this._setAttributeIfNotExists(e,"aria-labelledby",`${t.id}`))}_toggleDropDown(t,e){const i=this._getOuterElement(t);if(!i.classList.contains("dropdown"))return;const n=(t,n)=>{const s=W.findOne(t,i);s&&s.classList.toggle(n,e)};n(yo,_o),n(".dropdown-menu",vo),i.setAttribute("aria-expanded",e)}_setAttributeIfNotExists(t,e,i){t.hasAttribute(e)||t.setAttribute(e,i)}_elemIsActive(t){return t.classList.contains(_o)}_getInnerElement(t){return t.matches(Ao)?t:W.findOne(Ao,t)}_getOuterElement(t){return t.closest(".nav-item, .list-group-item")||t}static jQueryInterface(t){return this.each((function(){const e=To.getOrCreateInstance(this);if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t]()}}))}}P.on(document,ao,Eo,(function(t){["A","AREA"].includes(this.tagName)&&t.preventDefault(),l(this)||To.getOrCreateInstance(this).show()})),P.on(window,co,(()=>{for(const t of W.find(Co))To.getOrCreateInstance(t)})),m(To);const Oo=".bs.toast",Lo=`mouseover${Oo}`,xo=`mouseout${Oo}`,ko=`focusin${Oo}`,So=`focusout${Oo}`,No=`hide${Oo}`,$o=`hidden${Oo}`,Do=`show${Oo}`,Po=`shown${Oo}`,Io="hide",Mo="show",jo="showing",Bo={animation:"boolean",autohide:"boolean",delay:"number"},Fo={animation:!0,autohide:!0,delay:5e3};class Ho extends H{constructor(t,e){super(t,e),this._timeout=null,this._hasMouseInteraction=!1,this._hasKeyboardInteraction=!1,this._setListeners()}static get Default(){return Fo}static get DefaultType(){return Bo}static get NAME(){return"toast"}show(){P.trigger(this._element,Do).defaultPrevented||(this._clearTimeout(),this._config.animation&&this._element.classList.add("fade"),this._element.classList.remove(Io),d(this._element),this._element.classList.add(Mo,jo),this._queueCallback((()=>{this._element.classList.remove(jo),P.trigger(this._element,Po),this._maybeScheduleHide()}),this._element,this._config.animation))}hide(){this.isShown()&&(P.trigger(this._element,No).defaultPrevented||(this._element.classList.add(jo),this._queueCallback((()=>{this._element.classList.add(Io),this._element.classList.remove(jo,Mo),P.trigger(this._element,$o)}),this._element,this._config.animation)))}dispose(){this._clearTimeout(),this.isShown()&&this._element.classList.remove(Mo),super.dispose()}isShown(){return this._element.classList.contains(Mo)}_maybeScheduleHide(){this._config.autohide&&(this._hasMouseInteraction||this._hasKeyboardInteraction||(this._timeout=setTimeout((()=>{this.hide()}),this._config.delay)))}_onInteraction(t,e){switch(t.type){case"mouseover":case"mouseout":this._hasMouseInteraction=e;break;case"focusin":case"focusout":this._hasKeyboardInteraction=e}if(e)return void this._clearTimeout();const i=t.relatedTarget;this._element===i||this._element.contains(i)||this._maybeScheduleHide()}_setListeners(){P.on(this._element,Lo,(t=>this._onInteraction(t,!0))),P.on(this._element,xo,(t=>this._onInteraction(t,!1))),P.on(this._element,ko,(t=>this._onInteraction(t,!0))),P.on(this._element,So,(t=>this._onInteraction(t,!1)))}_clearTimeout(){clearTimeout(this._timeout),this._timeout=null}static jQueryInterface(t){return this.each((function(){const e=Ho.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t](this)}}))}}var qo;return z(Ho),m(Ho),qo=function(){function t(t){var e=!0,i=!1,n=null,s={text:!0,search:!0,url:!0,tel:!0,email:!0,password:!0,number:!0,date:!0,month:!0,week:!0,time:!0,datetime:!0,"datetime-local":!0};function o(t){return!!(t&&t!==document&&"HTML"!==t.nodeName&&"BODY"!==t.nodeName&&"classList"in t&&"contains"in t.classList)}function r(t){t.classList.contains("focus-visible")||(t.classList.add("focus-visible"),t.setAttribute("data-focus-visible-added",""))}function a(t){e=!1}function l(){document.addEventListener("mousemove",c),document.addEventListener("mousedown",c),document.addEventListener("mouseup",c),document.addEventListener("pointermove",c),document.addEventListener("pointerdown",c),document.addEventListener("pointerup",c),document.addEventListener("touchmove",c),document.addEventListener("touchstart",c),document.addEventListener("touchend",c)}function c(t){t.target.nodeName&&"html"===t.target.nodeName.toLowerCase()||(e=!1,document.removeEventListener("mousemove",c),document.removeEventListener("mousedown",c),document.removeEventListener("mouseup",c),document.removeEventListener("pointermove",c),document.removeEventListener("pointerdown",c),document.removeEventListener("pointerup",c),document.removeEventListener("touchmove",c),document.removeEventListener("touchstart",c),document.removeEventListener("touchend",c))}document.addEventListener("keydown",(function(i){i.metaKey||i.altKey||i.ctrlKey||(o(t.activeElement)&&r(t.activeElement),e=!0)}),!0),document.addEventListener("mousedown",a,!0),document.addEventListener("pointerdown",a,!0),document.addEventListener("touchstart",a,!0),document.addEventListener("visibilitychange",(function(t){"hidden"===document.visibilityState&&(i&&(e=!0),l())}),!0),l(),t.addEventListener("focus",(function(t){var i,n,a;o(t.target)&&(e||(n=(i=t.target).type,"INPUT"===(a=i.tagName)&&s[n]&&!i.readOnly||"TEXTAREA"===a&&!i.readOnly||i.isContentEditable))&&r(t.target)}),!0),t.addEventListener("blur",(function(t){var e;o(t.target)&&(t.target.classList.contains("focus-visible")||t.target.hasAttribute("data-focus-visible-added"))&&(i=!0,window.clearTimeout(n),n=window.setTimeout((function(){i=!1}),100),(e=t.target).hasAttribute("data-focus-visible-added")&&(e.classList.remove("focus-visible"),e.removeAttribute("data-focus-visible-added")))}),!0),t.nodeType===Node.DOCUMENT_FRAGMENT_NODE&&t.host?t.host.setAttribute("data-js-focus-visible",""):t.nodeType===Node.DOCUMENT_NODE&&(document.documentElement.classList.add("js-focus-visible"),document.documentElement.setAttribute("data-js-focus-visible",""))}if("undefined"!=typeof window&&"undefined"!=typeof document){var e;window.applyFocusVisiblePolyfill=t;try{e=new CustomEvent("focus-visible-polyfill-ready")}catch(t){(e=document.createEvent("CustomEvent")).initCustomEvent("focus-visible-polyfill-ready",!1,!1,{})}window.dispatchEvent(e)}"undefined"!=typeof document&&t(document)},"object"==typeof exports&&"undefined"!=typeof module?qo():"function"==typeof define&&define.amd?define(qo):qo(),{Alert:K,Button:Y,Carousel:Ft,Collapse:Zt,Dropdown:nn,Modal:Bn,Offcanvas:is,OrangeNavbar:ls,Popover:Ps,QuantitySelector:Rs,ScrollSpy:eo,Tab:To,Toast:Ho,Tooltip:ks}}));
//# sourceMappingURL=boosted.bundle.min.js.map