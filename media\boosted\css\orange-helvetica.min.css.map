{"version": 3, "sources": ["..\\..\\scss\\orange-helvetica.scss"], "names": [], "mappings": "AAkCA,WACE,YAAA,eACA,WAAA,OACA,YAAA,IACA,IAAA,qCAAA,gBACA,aAAA,KAGF,WACE,YAAA,eACA,WAAA,OACA,YAAA,IACA,IAAA,qCAAA,gBACA,aAAA", "sourcesContent": ["// Orange Boosted with Bootstrap\n// Helvetica Neue LT W07 55 Roman\n// <version>1.0</version>\n// <vendor>Monotype Imaging Inc.</vendor>\n// <credits>\n// <name>Fonts.com WebFonts</name>\n// <URL>http://webfonts.fonts.com</URL>\n// </credits>\n// <license>\n// <URL>http://webfonts.fonts.com/Legal</URL>\n// </license>\n// <copyright>Copyright © 2014 Monotype Imaging Inc. All rights reserved.\n// <trademark>Neue Helvetica is a trademark of Monotype Imaging Inc. registered in the U.S. Patent and Trademark Office and may be registered in certain other jurisdictions.\n// Orange has purchased the right to use Helvetica in its websites and mobile applications.\n// Don't use and distribute Helvetica font family if you're not explicitly authorized by Monotype Imaging Inc\n\n// To use local() Helvetica Neue\n// @see https://www.broken-links.com/2009/06/30/checking-for-installed-fonts-with-font-face-and-local/\n// @see https://www.adobe.com/content/dam/acom/en/devnet/font/pdfs/5090.FontNameList.pdf\n//\n// @note Desktop and Web font do not match (at least Orange's version on Windows)\n// @note We'd have to use `font-size-adjust: .5` to ensure they do\n// @note But this is not supported in IE11 nor Edge\n// @see https://caniuse.com/#search=font-size-adjust\n// @see https://developer.mozilla.org/en-US/docs/Web/CSS/font-size-adjust\n//\n// @code `local(\"Helvetica 55 Roman\"), local(\"HelveticaNeue\"),`\n// @code `local(\"Helvetica 75 Bold\"), local(\"HelveticaNeue-Bold\"),`\n\n@import \"functions\";\n@import \"variables\";\n\n$font-path: \"../fonts/\" !default;\n\n@font-face {\n  font-family: HelvNeueOrange#{\"/*rtl:insert:Arabic*/\"};\n  font-style: normal;\n  font-weight: 400;\n  src: url(\"#{$font-path}HelvNeue55_W1G.woff2\") format(\"woff2\") #{\"/*rtl:url('\" + $font-path + \"HelveticaNeueW20-55Roman.woff2') format('woff2')*/\"};\n  font-display: swap;\n}\n\n@font-face {\n  font-family: HelvNeueOrange#{\"/*rtl:insert:Arabic*/\"};\n  font-style: normal;\n  font-weight: 700;\n  src: url(\"#{$font-path}HelvNeue75_W1G.woff2\") format(\"woff2\") #{\"/*rtl:url('\" + $font-path + \"HelveticaNeueW20-75Bold.woff2') format('woff2')*/\"};\n  font-display: swap;\n}\n"]}